<script lang="ts" setup>
import type { VxeGlobalRendererHandles, VxeInputEvents } from 'vxe-pc-ui';
import type { VxeTableDefines } from 'vxe-table';

import type { PropType } from 'vue';

import { computed, ref, watch } from 'vue';

import { Input } from 'vxe-pc-ui';

const props = defineProps({
  renderParams: {
    type: Object as PropType<VxeGlobalRendererHandles.RenderTableFilterParams>,
    default: () => ({}),
  },
});

const currOption = ref<VxeTableDefines.FilterOption>();

const currField = computed(() => {
  const { column } = props.renderParams || {};
  return column ? column.field : '';
});

const load = () => {
  const { renderParams } = props;
  if (renderParams) {
    const { column } = renderParams;
    const option = column.filters[0];
    currOption.value = option;
  }
};

const changeOptionEvent = () => {
  const { renderParams } = props;
  const option = currOption.value;
  if (renderParams && option) {
    const { $table } = renderParams;
    const checked = !!option.data;
    $table.updateFilterOptionStatus(option, checked);
  }
};

const keyupEventVxe: VxeInputEvents.Keyup = ({ $event }) => {
  const { renderParams } = props;
  if (renderParams) {
    const { $table } = renderParams;
    if ($event.key === 'Enter') {
      $table.saveFilterPanel();
    }
  }
};

watch(currField, () => {
  load();
});

load();
</script>

<template>
  <div v-if="currOption" class="my-filter-input">
    <Input
      mode="text"
      v-model="currOption.data"
      placeholder="支持回车筛选"
      @keyup="keyupEventVxe"
      @input="changeOptionEvent"
    />
  </div>
</template>

<style scoped>
.my-filter-input {
  padding: 10px;
}
</style>
