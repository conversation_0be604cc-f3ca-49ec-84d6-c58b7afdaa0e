<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Input, Select, SelectOption, Space } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';

import { addcheckModalSchema } from '../main-form-data';

const emit = defineEmits(['success']);
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: addcheckModalSchema(),
  showDefaultActions: false,
});
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    console.warn(data);

    modalApi.lock();
    try {
      emit('success');

      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      // const data = modalApi.getData<SampleTypeApi.SampleType>();
    }
  },
});
const options = [
  {
    value: 'g',
    label: 'g',
  },
  {
    value: 'mg',
    label: 'mg',
  },
];
const value = ref();
</script>
<template>
  <Modal title="添加校验点">
    <Form class="mx-4">
      <template #EQID="slotProps">
        <Space :size="[4, 0]" wrap>
          <Input v-bind="slotProps" />
          <Select v-model:value="value" style="width: 120px">
            <SelectOption
              v-for="item in options"
              :value="item.value"
              :key="item.value"
            >
              {{ item.label }}
            </SelectOption>
          </Select>
        </Space>
      </template>
    </Form>
  </Modal>
</template>
