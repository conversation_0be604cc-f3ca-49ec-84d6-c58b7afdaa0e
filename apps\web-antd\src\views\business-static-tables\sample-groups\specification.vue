<script lang="ts" setup>
import type { SampleGroupsApi } from '#/api/business-static-tables/sample-groups';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { addSpecToTemplate } from '#/api/business-static-tables/sample-groups';
import { $t } from '#/locales';

import SpecsManagerGrid from '../process-specifications/spec-manager.vue';

const emit = defineEmits(['success']);

const specGridRef = ref();

const spCode = ref<number>(0);

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  showConfirmButton: true,
  confirmDisabled: false,
  onConfirm: async () => {
    const spec = specGridRef.value?.currentSpecRow;
    if (spec === null) {
      message.warn($t('business-static-tables.sampleGroups.NoSpecs'));
      return;
    }

    await addSpecToTemplate(
      'SP_SPECS',
      spCode.value.toString(),
      [spec.SPECNO],
      '',
    );

    emit('success');
    modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData<SampleGroupsApi.IpSampleGroupDetails>();
      if (data) {
        spCode.value = data.SP_CODE;
      }
    }
  },
});
</script>

<template>
  <Modal title="质量标准管理" class="h-[1000px] w-[1200px]">
    <Page>
      <SpecsManagerGrid ref="specGridRef" />
    </Page>
  </Modal>
</template>
