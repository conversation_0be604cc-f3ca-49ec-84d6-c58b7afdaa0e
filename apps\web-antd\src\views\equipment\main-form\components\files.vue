<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { MainFormApi } from '#/api/equipment/main-form';

import { computed, watch } from 'vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getDSCertificateApi } from '#/api/equipment/main-form';
import { useMainFormStore } from '#/store';

import { fileColumns } from '../main-form-data';

const mainFormStore = useMainFormStore();
interface RowType {
  [key: string]: any;
}
const currentRow = computed<RowType>(
  () => mainFormStore.getCurrentRow as unknown as RowType,
);
const eventRow = computed<RowType>(
  () => mainFormStore.getEventRow as unknown as RowType,
);
watch(
  currentRow,
  (newRow) => {
    if (newRow) {
      eventGridApi.query();
    }
  },
  { deep: true },
);
watch(
  eventRow,
  (newRow) => {
    if (newRow) {
      eventGridApi.query();
    }
  },
  { deep: true },
);
const gridOptions: VxeTableGridOptions<MainFormApi.MainForm> = {
  columns: fileColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!currentRow.value) {
          return [];
        }
        const data = await getDSCertificateApi([
          currentRow.value?.ORIGREC,
          eventRow.value?.ORIGREC,
        ]);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: {},
    search: true,
    zoom: true,
  },
};
const [EventGrid, eventGridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: {},
});
</script>
<template>
  <EventGrid />
</template>
