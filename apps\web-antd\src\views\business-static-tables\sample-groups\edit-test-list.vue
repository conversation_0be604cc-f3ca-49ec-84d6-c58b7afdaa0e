<script lang="ts" setup>
import type { DefaultOptionType, SelectValue } from 'ant-design-vue/es/select';

import type { Recordable } from '@vben/types';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message, Select, SelectOption, Transfer } from 'ant-design-vue';

import {
  getTestByMultiChoice,
  getTestCat,
} from '#/api/business-static-tables/sample-groups';
import { $t } from '#/locales';

// 定义Transfer数据项类型
interface TransferItem {
  key: string;
  title: string;
  description?: string;
}

const props = defineProps<{
  aTestCode: string[];
  drawNo: number;
  profile: string;
  spCode: number;
}>();

const emit = defineEmits<{
  (
    e: 'submit',
    data: { selectedItems: TransferItem[]; selectedKeys: string[] },
  ): void;
  (e: 'cancel'): void;
}>();

// interface TransferModalProps {
//   specNo?: number | string;
//   initialSelectedKeys?: string[];
// }

// 数据源与状态
const transferTestData = ref<TransferItem[]>([]);
const targetTestKeys = ref<string[]>([]);
const loading = ref(false);
const error = ref<string>('');

// 计算属性
const selectedItems = computed(() => {
  return transferTestData.value.filter((item) =>
    targetTestKeys.value.includes(item.key),
  );
});

// 组件挂载时展开所有节点
/* onMounted(async () => {
  await getTestCategoryList();
}); */

async function fetchData() {
  const testCategory = selectedTestCategory.value ?? '';
  loading.value = true;
  error.value = '';

  try {
    const result = await getTestByMultiChoice(
      props.spCode,
      props.drawNo,
      testCategory,
      props.profile,
    );
    const data =
      result.items?.map((item: { TEXT: string; VALUE: string }) => ({
        key: item.VALUE,
        title: item.TEXT,
        description: item.TEXT || '',
      })) || [];

    transferTestData.value = data;

    // 设置初始选中项
    targetTestKeys.value =
      props.aTestCode.length > 0 ? [...props.aTestCode] : [];

    // console.log('aTestCodes', props.aTestCodes);
    // console.log('targetKeys', targetKeys.value);
  } catch (error_) {
    error.value = `数据加载失败: ${(error_ as Error).message}`;
    message.error(error.value);
  } finally {
    loading.value = false;
  }
}

// 使用VbenModal
const [TransferTestModal, modalApi] = useVbenModal({
  onOpenChange: async (isOpen: boolean) => {
    if (isOpen) {
      await getTestCatList();
      await fetchData();
    }
  },
  onConfirm: () => {
    handleSubmit();
  },
  onCancel: () => {
    handleCancel();
  },
});

// 处理提交
function handleSubmit() {
  const result = {
    selectedKeys: targetTestKeys.value,
    selectedItems: selectedItems.value,
  };
  if (targetTestKeys.value.length === 0) {
    message.warn($t('business-static-tables.sampleGroups.emptyTests'));
    return;
  }
  // console.log(result);
  emit('submit', result);
  // message.success(`已选择 ${targetKeys.value.length} 项`);
  modalApi.close();
}

// 处理取消
function handleCancel() {
  emit('cancel');
  modalApi.close();
}

// 暴露方法给父组件
defineExpose({
  open: () => modalApi.open(),
  close: () => modalApi.close(),
  setData: (data: any) => modalApi.setData(data),
});

// 组件挂载时展开所有节点
/* onMounted(async () => {
  await getTestCategoryList();
}); */

const selectedTestCategory = ref<string>();

const onSelectChange = (
  _value: SelectValue,
  _option: DefaultOptionType | DefaultOptionType[],
) => {
  fetchData();
};

const testCategoryOptions = ref<Recordable<string>[]>([]);
const getTestCatList = async () => {
  try {
    const res = await getTestCat();
    if (res && res.items && res.items.length > 0) {
      testCategoryOptions.value = res.items;
    } else {
      console.warn('测试类型数据为空，请检查接口返回结果');
    }
  } catch (error) {
    console.error('获取测试类型失败:', error);
  }
};
</script>

<template>
  <TransferTestModal title="编辑测试列表" class="w-[900px]" :loading="loading">
    <div class="mb-2">
      <label class="w-24 pr-2 text-left">测试类型名称：</label>
      <Select
        placeholder="请选择测试类型名称"
        style="width: 70%"
        v-model:value="selectedTestCategory"
        @change="onSelectChange"
      >
        <SelectOption
          v-for="item in testCategoryOptions"
          :key="item.TESTCATCODE"
          :value="item.TESTCATCODE"
        >
          {{ item.TESTCATCODE }}
        </SelectOption>
      </Select>
    </div>
    <Transfer
      v-model:target-keys="targetTestKeys"
      :data-source="transferTestData"
      show-search
      :render="(item) => item.title"
      :list-style="{
        width: '400px',
        height: '400px',
      }"
      :operations="['选择', '移除']"
    />
  </TransferTestModal>
</template>
