import { callServer, getDataSet, getDataSetNoPage } from '#/api/core/witlab';

export namespace EquipmentMgApi {
  export interface EquipmentMg {
    ORIGREC: number;
    MATTYPE: string;
    ISPRODUCEMETRIAL: string;
    DESCRIPTION: string;
    DEPT: string;
  }
  export interface MetaDataEquipment {
    [key: string]: any;
    ORIGREC: number;
    NAME: string;
    EFFECT: string;
    TYPE: string;
    SORTER: number;
  }
  export interface RowType {
    [key: string]: any;
  }
  export interface PageFetchParams {
    [key: string]: any;
    page: number;
    pageSize: number;
  }
  export interface EquipmentData {
    [key: string]: any;
    ORIGREC: number;
    NAME: string;
    EFFECT: string;
    TYPE: string;
  }
}
/**
 * 获取元数据查找表数据
 * @returns 返回包含所有查找表数据的Promise
 */
async function getReportPrintApi() {
  return getDataSet('ReportPrint.frmPrint', ['Label_EquipManage']);
}
async function getLookupsApi() {
  return getDataSet('Common.cbo_lookups', ['OFFLINESTATUS']);
}
async function getEquipmentListApi() {
  return getDataSet('EQUIPMENT_MANAGER.DS_EQUIPMENT_LIST', ['All', '']);
}
async function deleteEquipmentApi(
  data: Omit<EquipmentMgApi.EquipmentData, 'ORIGREC'>,
) {
  return await callServer('EQUIPMENT_MANAGER.DEL_EQUIP', [data.EQID, '']);
}
async function addEquipmentApi(data: Array<string>) {
  return await callServer('EQUIPMENT_MANAGER.ADD_EQUIP', data);
}
async function getEquipmentTypeApi() {
  return getDataSet('EQUIPMENT_MANAGER.DS_EQUIPMENT_TYPE', []);
}
async function getPositionApi() {
  return await getDataSet('EQUIPMENT_MANAGER.dsMyServgrp', []);
}
async function getEquipmentDetailsApi(params: Array<any>) {
  return await getDataSet('EQUIPMENT_MANAGER.DS_EQUIP_DETAILS', params);
}
async function getDsNewMaintSchedApi(params: Array<any>) {
  return await getDataSet('EQUIPMENT_MANAGER.dsNewMaintSched', params);
}
async function updateEquipSchedEventsdApi(params: Array<any>) {
  return await getDataSet('EQUIPMENT_MANAGER.updateEquipSchedEvents', params);
}
async function getEqServGrpsApi(params: Array<any>) {
  return await getDataSet('EQUIPMENT_MANAGER.getEqServGrps', params);
}
async function getEquipManagerListApi() {
  return await getDataSet('EQUIPMENT_MANAGER.DS_GetEquipManagerList', []);
}
async function getEqListApi(params: Array<any>) {
  return await getDataSet('EQUIPMENT_MANAGER.DS_EqList', params);
}
async function getMaintenanceEventsApi(params: Array<any>) {
  return await getDataSet('EQUIPMENT_MANAGER.MaintenanceEvents', params);
}
async function editEquipmentScheduleEventsApi(params: Array<string>) {
  return await callServer(
    'EQUIPMENT_MANAGER.EditEquipmentScheduleEvents',
    params,
  );
}
async function getDsServGroupsApi(params: Array<any>) {
  return await getDataSet('EQUIPMENT_MANAGER.dsServGroups', params);
}
async function editEqServGroupsApi(params: Array<any>) {
  return await callServer('EQUIPMENT_MANAGER.editEqServGroups', params);
}
async function getMaintRecsApi(params: Array<any>) {
  return await getDataSet('EQUIPMENT_MANAGER.DS_MAINT_RECS_NEW', params);
}
async function getMaintTypeApi() {
  return await getDataSetNoPage('EQUIPMENT_MANAGER.DS_GETMAINTTYPE', []);
}
async function getMaintEventsApi(params: Array<any>) {
  return await getDataSetNoPage('EQUIPMENT_MANAGER.getMaintEvents', params);
}
async function getComponentEventsApi(params: Array<any>) {
  return await getDataSetNoPage('EQUIPMENT_MANAGER.getComponentEvents', params);
}
async function getAllEquipmentsApi(params: Array<any>) {
  return await getDataSetNoPage('EQUIPMENT_MANAGER.getAllEquipments', params);
}
async function checkIfELNMethodEQLISTApi(params: Array<any>) {
  return await callServer('EQUIPMENT_MANAGER.checkIfELNMethodEQLIST', params);
}
async function openMaintRecApi(params: Array<any>) {
  return await callServer('EQUIPMENT_MANAGER.OPEN_MAINT_REC', params);
}
async function addOutOfServiceMaintenanceApi(params: Array<any>) {
  return await callServer(
    'EQUIPMENT_MANAGER.AddOutOfServiceMaintenance',
    params,
  );
}
async function checkIfELNMethodApi(params: Array<any>) {
  return await callServer('EQUIPMENT_MANAGER.checkIfELNMethod', params);
}
async function finishMaintRecApi(params: Array<any>) {
  return await callServer('EQUIPMENT_MANAGER.FINISH_MAINT_REC', params);
}
async function getEquipmentUseLogApi(params: Array<any>) {
  return await getDataSet('EQUIPMENT_MANAGER.DS_GetEquipUseLog', params);
}
async function getCertificateApi(params: Array<any>) {
  return await getDataSet('EQUIPMENT_MANAGER.DS_Certificate', params);
}

async function getDocumentationApi(params: Array<any>) {
  return await getDataSet('EQUIPMENT_MANAGER.Documentation', params);
}

async function deleteDocumentationApi(params: Array<any>) {
  return await callServer('EQUIPMENT_MANAGER.DeleteDocumentation', params);
}
async function addEquipUseLogApi(params: Array<any>) {
  return await callServer('EQUIPMENT_MANAGER.AddEquipUseLog', params);
}
async function getUserApi() {
  return await getDataSetNoPage('Common.DS_USERS', []);
}
async function getDsDcuDetailsApi(params: Array<any>) {
  return await getDataSetNoPage('EQUIPMENT_MANAGER.DS_DCU_DETAILS', params);
}
async function getDgCaptrueRecordApi(params: Array<any>) {
  return await getDataSet('EQUIPMENT_MANAGER.dgCaptrueRecord', params);
}
async function getSelectAnalytesApi(params: Array<any>) {
  return await getDataSetNoPage('EQUIPMENT_MANAGER.DS_SELECT_ANALYTES', params);
}
async function getTranslationTableApi(params: Array<any>) {
  return await getDataSet('EQUIPMENT_MANAGER.DS_TRANSLATION_TABLE', params);
}
async function addTranslationAnalytesApi() {
  return await callServer('EQUIPMENT_MANAGER.ADD_TRANSLATION_ANALYTES', []);
}
async function getDCUMethodApi() {
  return await getDataSetNoPage('EQUIPMENT_MANAGER.DCUMethods', []);
}
async function getDCUFileTypeApi() {
  return await getDataSetNoPage(
    'EQUIPMENT_MANAGER.DS_SAMPSPERFILE_COMBOBOX',
    [],
  );
}
async function getRESTScriptsApi() {
  return await getDataSetNoPage('EQUIPMENT_MANAGER.getRESTScripts', []);
}
async function getDsGetMeasureTypesApi() {
  return await getDataSetNoPage('GlobalDataSources.dsGetMeasureTypes', []);
}
async function getDsGetUnitsApi() {
  return await getDataSetNoPage('GlobalDataSources.dsGetUnits', []);
}
async function getFileBasedApi(params: Array<any>) {
  return await getDataSetNoPage('EQUIPMENT_MANAGER.FileBased_gb', params);
}
async function getSerialPortdApi(params: Array<any>) {
  return await getDataSetNoPage('EQUIPMENT_MANAGER.SerialPort_gb', params);
}
async function getRestPnldApi(params: Array<any>) {
  return await getDataSetNoPage('EQUIPMENT_MANAGER.REST_pnl', params);
}
async function getDCUScriptGetApi(params: Array<any>) {
  return await getDataSetNoPage('EQUIPMENT_MANAGER.DCUScriptGet', params);
}
async function getWebSocketApi(params: Array<any>) {
  return await getDataSetNoPage('EQUIPMENT_MANAGER.WebSocket_gd', params);
}

export {
  addEquipmentApi,
  addEquipUseLogApi,
  addOutOfServiceMaintenanceApi,
  addTranslationAnalytesApi,
  checkIfELNMethodApi,
  checkIfELNMethodEQLISTApi,
  deleteDocumentationApi,
  deleteEquipmentApi,
  editEqServGroupsApi,
  editEquipmentScheduleEventsApi,
  finishMaintRecApi,
  getAllEquipmentsApi,
  getCertificateApi,
  getComponentEventsApi,
  getDCUFileTypeApi,
  getDCUMethodApi,
  getDCUScriptGetApi,
  getDgCaptrueRecordApi,
  getDocumentationApi,
  getDsDcuDetailsApi,
  getDsGetMeasureTypesApi,
  getDsGetUnitsApi,
  getDsNewMaintSchedApi,
  getDsServGroupsApi,
  getEqListApi,
  getEqServGrpsApi,
  getEquipManagerListApi,
  getEquipmentDetailsApi,
  getEquipmentListApi,
  getEquipmentTypeApi,
  getEquipmentUseLogApi,
  getFileBasedApi,
  getLookupsApi,
  getMaintenanceEventsApi,
  getMaintEventsApi,
  getMaintRecsApi,
  getMaintTypeApi,
  getPositionApi,
  getReportPrintApi,
  getRestPnldApi,
  getRESTScriptsApi,
  getSelectAnalytesApi,
  getSerialPortdApi,
  getTranslationTableApi,
  getUserApi,
  getWebSocketApi,
  openMaintRecApi,
  updateEquipSchedEventsdApi,
};
