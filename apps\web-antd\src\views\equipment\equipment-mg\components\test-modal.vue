<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';

import { comModalSchema } from '../equipment-mg-data';

const emit = defineEmits(['success']);
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: comModalSchema(),
  showDefaultActions: false,
});
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    console.warn(data);

    modalApi.lock();
    try {
      emit('success');

      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      // const data = modalApi.getData<SampleTypeApi.SampleType>();
    }
  },
});
</script>
<template>
  <Modal title="选择COM端口">
    <Form class="mx-4" />
  </Modal>
</template>
