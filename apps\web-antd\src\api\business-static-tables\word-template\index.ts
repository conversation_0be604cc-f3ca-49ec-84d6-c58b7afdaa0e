import { callServer, getDataSet } from '#/api/core/witlab';

export namespace WordTemplateApi {
  export interface WordTemplate {
    ORIGREC: number;
    TYPE: string;
    TEMP_CODE: number;
    SORTER: number;
    NAME: string;
    LANGUAGE: string;
    MATTYPES: string;
    CLIENTS: string;
    FUNCTION_NAME: string;
    STARDOC_ID: string;
    FILEPATH: string;
    PIC_SIZE: string;
    REMARK: string;
    DEPTNAME: string;
    CREATEDBY: string;
    CREATEDATE: Date;
    STATUS: string;
  }
}

/**
 * 获取报告书模板列表数据
 */
async function getWordTemplateList() {
  return getDataSet('SC_WORD.dsWord_Template', []);
}

/**
 * 根据模板类型获取执行脚本数据
 */
async function getFunctionName(type: string) {
  return getDataSet('SC_WORD.dsFunction_Name', [type]);
}

/**
 * 创建报告书模板
 * @param data 报告书模板数据
 */
async function addWordTemplate(data: WordTemplateApi.WordTemplate) {
  return await callServer('SC_WORD.Add_Word_Template', [
    data.TYPE,
    data.NAME,
    data.FUNCTION_NAME,
    data.STARDOC_ID,
    data.LANGUAGE,
    data.REMARK,
  ]);
}

/**
 * 删除报告书模板
 */
async function deleteWordTemplate(origrec: number[]) {
  return await callServer('Common.DeleteRows', ['WORD_TEMPLATE', origrec]);
}

export {
  addWordTemplate,
  deleteWordTemplate,
  getFunctionName,
  getWordTemplateList,
};
