<script lang="ts" setup>
import type { PropType } from 'vue';

import type { TestManagerApi } from '#/api/business-static-tables';

import { useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import {
  $addPossibleResultApi,
  $delPossibleResultApi,
  $getPossibleResultsApi,
} from '#/api/business-static-tables';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';
import { confirm } from '#/utils/utils';

import AddPassibleResultsForm from './add-passible-results-form.vue';
import {
  usePassibleResultsColumns,
  usePassibleResultsFilterSchema,
} from './data';

const props = defineProps({
  analyte: {
    type: Object as PropType<null | TestManagerApi.Analyte>,
    required: true,
  },
  mode: {
    type: String,
    required: true,
  },
});
const colums = usePassibleResultsColumns();
const filterSchema = usePassibleResultsFilterSchema();
const queryData = async () => {
  if (props.analyte === null) {
    return [];
  }
  return await $getPossibleResultsApi({
    analyte: props.analyte.ANALYTE,
    testCode: props.analyte.TESTCODE,
    mode: props.mode,
  });
};
const gridOpt = {
  pagerConfig: {
    enabled: false,
  },
};
const {
  Grid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
  CurrentCheckRow,
} = useLimsGridsConfig<TestManagerApi.PassibleResults>(
  colums,
  filterSchema,
  queryData,
  gridOpt,
);
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddPassibleResultsForm,
  destroyOnClose: true,
});

async function onRefresh() {
  await gridApi.query();
}
async function onDelete() {
  if (!CurrentCheckRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const checkOrig = CurrentCheckRow.value.ORIGREC;
  if (!props.analyte) {
    message.warning($t('commons.selectAnalyte'));
    return;
  }
  const analyte = props.analyte.ANALYTE;
  const testCode = props.analyte.TESTCODE;
  const result = CurrentCheckRow.value.RESULT;
  await confirm($t('commons.deleteConfirm'), $t('commons.deleteConfirmTitle'));
  const delRes = await $delPossibleResultApi({
    origrec: checkOrig,
    analyte,
    testCode,
    result,
  });
  if (delRes[0]) {
    message.success($t('commons.deleteSuccess'));
  } else {
    message.error($t('commons.deleteFail'));
  }
  onRefresh();
}

function onCreate() {
  formModalApi.setData({ analyte: props.analyte, mode: props.mode }).open();
}

async function addPassFailure() {
  if (!props.analyte) {
    message.warning($t('commons.selectAnalyte'));
    return;
  }
  const dataSuccess: Omit<TestManagerApi.PassibleResults, 'ORIGREC'> = {
    ANALYTE: props.analyte.ANALYTE,
    TESTCODE: props.analyte.TESTCODE,
    RESULT: 'Pass',
    RESULT_STATUS: 'Done',
    SNOMEDCODE: '1',
    CONCLUSION_REPORT: 'The result is in spec',
    mode: props.mode,
  };
  const resSuccess = await $addPossibleResultApi(dataSuccess);

  const dataFailure: Omit<TestManagerApi.PassibleResults, 'ORIGREC'> = {
    ANALYTE: props.analyte.ANALYTE,
    TESTCODE: props.analyte.TESTCODE,
    RESULT: 'Fail',
    RESULT_STATUS: 'OOS',
    SNOMEDCODE: '2',
    CONCLUSION_REPORT: 'The result is Out of spec',
    mode: props.mode,
  };
  const resFailure = await $addPossibleResultApi(dataFailure);
  if (!resSuccess || !resFailure) {
    message.error($t('commons.existsData'));
  }
  onRefresh();
}

async function addNegPos() {
  if (!props.analyte) {
    message.warning($t('commons.selectAnalyte'));
    return;
  }
  const dataNeg: Omit<TestManagerApi.PassibleResults, 'ORIGREC'> = {
    ANALYTE: props.analyte.ANALYTE,
    TESTCODE: props.analyte.TESTCODE,
    RESULT: 'Negative',
    RESULT_STATUS: 'Done',
    SNOMEDCODE: '1',
    CONCLUSION_REPORT: 'The result is Negative',
    mode: props.mode,
  };
  const resNeg = await $addPossibleResultApi(dataNeg);

  const dataPos: Omit<TestManagerApi.PassibleResults, 'ORIGREC'> = {
    ANALYTE: props.analyte.ANALYTE,
    TESTCODE: props.analyte.TESTCODE,
    RESULT: 'Positive',
    RESULT_STATUS: 'Done',
    SNOMEDCODE: '2',
    CONCLUSION_REPORT: 'The result is Positive',
    mode: props.mode,
  };
  const resPos = await $addPossibleResultApi(dataPos);
  if (!resNeg || !resPos) {
    message.error($t('commons.existsData'));
  }
  onRefresh();
}

async function HasPossibleResults() {
  return gridApi.grid.getData().length > 0;
}

defineExpose({
  HasPossibleResults,
});
</script>
<template>
  <FormModal @success="onRefresh" />
  <div class="h-[500px]">
    <Grid>
      <template #toolbar-actions>
        <Space :size="[4, 0]" wrap>
          <Button type="primary" @click="onCreate">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="onDelete">
            {{ $t('ui.actionTitle.delete') }}
          </Button>
          <Button type="default" danger @click="addPassFailure">
            {{ $t('business-static-tables.testManager.addPassFailure') }}
          </Button>
          <Button type="default" @click="addNegPos">
            {{ $t('business-static-tables.testManager.addNegPos') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent(row)">
            {{ $t('commons.save') }}
          </Button>
          <Button type="link" @click="cancelRowEvent(row)">
            {{ $t('commons.cancel') }}
          </Button>
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">
            {{ $t('commons.edit') }}
          </Button>
        </template>
      </template>
    </Grid>
  </div>
</template>
