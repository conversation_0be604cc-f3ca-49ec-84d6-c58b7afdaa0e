<script lang="ts" setup>
import type { PreBatchesApi } from '#/api/login-options/interface-notification';

import { ref } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';

import { getPreBatchesRel } from '#/api/login-options/interface-notification';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import {
  usePreBatchesRelColumns,
  usePreBatchesRelFilterSchema,
} from './request-notification-data';

const requestId = ref<string>('');

const colums = usePreBatchesRelColumns();
const filterSchema = usePreBatchesRelFilterSchema();
const queryData = async () => {
  return getPreBatchesRel(requestId.value);
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
};

const { Grid } = useLimsGridsConfig<PreBatchesApi.PreBatchesRel>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

const [Drawer, drawerApi] = useVbenDrawer({
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<PreBatchesApi.PreBatches>();
      if (data) {
        requestId.value = data.REQUESTID;
        // onRefresh();
      } else {
        // gridApi?.reload();
      }
    }
  },
  showCancelButton: false,
  showConfirmButton: false,
});
</script>

<template>
  <Drawer class="w-full max-w-[800px]" title="指令号">
    <Page auto-content-height>
      <Grid />
    </Page>
  </Drawer>
</template>
