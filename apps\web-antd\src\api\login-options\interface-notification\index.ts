import { callServer, getDataSet } from '#/api/core/witlab';

export namespace PreBatchesApi {
  export interface PreBatches {
    ORIGREC: number;
    DISPSTS: string;
    REQUESTID: string;
    ORDERNO: string;
    MATCODE: string;
    MATNAME: string;
    BATCHNO: string;
    PLANT: string;
    PROCESS: string;
    ESTIMATEDVOL: string;
    ACTUALVOL: string;
    ESTIMATEDVOL_UNITS: string;
    SAMPLE_VOL: string;
    SAMPLE_UNITS: string;
    REQUEST_DATE: Date;
    REQUESTER: string;
    SAMPDATE: Date;
    SAMPLEDBY: string;
    EXPDATE: Date;
    COMMENTS: string;
    STATUS: string;
  }
  export interface PreTests {
    ORIGREC: number;
    TESTCODE: string;
    SAMPLE_NO: string;
    COMMENTS: string;
    REQUESTID: string;
  }

  export interface PreBatchesRel {
    ORIGREC: number;
    REL_REQUESTNO: string;
    REQUESTID: string;
  }
}

async function getPreBatches() {
  return getDataSet('InterfaceNotification.getPreBatches', []);
}

async function getPreTests(sRequestId: string) {
  return getDataSet('InterfaceNotification.dg_Test', [sRequestId]);
}

async function getPreBatchesRel(sRequestId: string) {
  return getDataSet('InterfaceNotification.dg_Bctches_Rel', [sRequestId]);
}

async function getPreBatchInfo(sOrigrec: number) {
  return await callServer('BatchManager.getPreBatchInfo', [sOrigrec]);
}

async function cancelSample(requestId: string) {
  return await callServer('InterfaceNotification.CancelISample', [requestId]);
}

async function UpdatePreBatchesStatus(sOrigrec: number) {
  return await callServer('InterfaceNotification.UpdatePreBatchesStatus', [
    sOrigrec,
  ]);
}

export {
  cancelSample,
  getPreBatches,
  getPreBatchesRel,
  getPreBatchInfo,
  getPreTests,
  UpdatePreBatchesStatus,
};
