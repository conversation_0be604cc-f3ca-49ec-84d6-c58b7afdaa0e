<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { SolutionPreparationApi } from '#/api/dilution-management/solution-preparation';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  materialColumns,
  addMaterialColumns,
  addSolutionColumns,
  addBacteriaColumns,
} from '../solution preparation-data';
import {
  getReagentsApi,
  getCobMatTypeApi,
  getSelectReagentsApi,
  addReagentsByRunApi,
  deleteRowsApi,
  getSoluctionTypeApi,
  getSelectSoluctionApi,
  getBacteriaTypeApi,
  getSelectBacteriaApi,
} from '#/api/dilution-management/solution-preparation';
import {
  Space,
  Button,
  Modal as AModal,
  Select,
  SelectOption,
  Input,
} from 'ant-design-vue';
import { ref } from 'vue';
const emit = defineEmits(['success']);
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    emit('success');
    modalApi.close();
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      clickRow.value = modalApi.getData().clickRow; 
      gridApi.query();
    }
  },
});
const gridOptions: VxeTableGridOptions<SolutionPreparationApi.Form> = {
  columns: materialColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!clickRow.value.BATCHNO) {
          return [];
        }
        const data = await getReagentsApi([
          clickRow.value.BATCHNO,
          'DILUTION',
          modalApi.getData().type,
        ]);

        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
const materialgridOptions: VxeTableGridOptions<SolutionPreparationApi.Form> = {
  columns: addMaterialColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!isSearch.value) {
          return [];
        }
        const data = await getSelectReagentsApi([
          clickRow.value.BATCHNO,
          type.value,
          searchValue.value,
          '',
          'DILUTION',
        ]);
        isSearch.value = false;
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
const solutionGridOptions: VxeTableGridOptions<SolutionPreparationApi.Form> = {
  columns: addSolutionColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!isSearch.value) {
          return [];
        }
        const data = await getSelectSoluctionApi([
          clickRow.value.BATCHNO,
          type.value,
          searchValue.value,
          '',
          'DILUTION',
        ]);
        isSearch.value = false;
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
const bacteriaGridOptions: VxeTableGridOptions<SolutionPreparationApi.Form> = {
  columns: addBacteriaColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!isSearch.value) {
          return [];
        }
        const data = await getSelectBacteriaApi([
          clickRow.value.BATCHNO,
          type.value,
          searchValue.value,
          '',
          'DILUTION',
        ]);
        isSearch.value = false;
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
interface RowType {
  [key: string]: any;
}
const clickRow = ref<RowType>({});
const clickMaterialRow = ref<RowType>({});
const clickCurRow = ref<RowType>({});
const clickSolutionRow = ref<RowType>({});
const clickBacteriaRow = ref<RowType>({});

const gridEvents: VxeGridListeners<SolutionPreparationApi.Form> = {
  currentChange: async ({ row }) => {
    if (row) {
      clickCurRow.value = row;
    }
  },
};
const materialgridEvents: VxeGridListeners<SolutionPreparationApi.Form> = {
  currentChange: async ({ row }) => {
    if (row) {
      clickMaterialRow.value = row;
    }
  },
};
const solutionGridEvents: VxeGridListeners<SolutionPreparationApi.Form> = {
  currentChange: async ({ row }) => {
    if (row) {
      clickSolutionRow.value = row;
    }
  },
};
const bacteriaGridEvents: VxeGridListeners<SolutionPreparationApi.Form> = {
  currentChange: async ({ row }) => {
    if (row) {
      clickBacteriaRow.value = row;
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: gridEvents,
});
const [MaterialGrid, materialGridApi] = useVbenVxeGrid({
  gridOptions: materialgridOptions,
  gridEvents: materialgridEvents,
});
const [SolutionGrid, solutionGridApi] = useVbenVxeGrid({
  gridOptions: solutionGridOptions,
  gridEvents: solutionGridEvents,
});
const [BacteriaGrid, bacteriaGridApi] = useVbenVxeGrid({
  gridOptions: bacteriaGridOptions,
  gridEvents: bacteriaGridEvents,
});
bacteriaGridOptions;
const removeMaterial = async () => {
  AModal.confirm({
    title: '警告',
    content: '是否确认删除所选数据？',
    cancelText: '否',
    okText: '是',
    async onOk() {
      const params = ['OTHER_REAGENTS', [clickCurRow.value.ORIGREC]];
      await deleteRowsApi(params);
      gridApi.query();
    },
  });
};
const type = ref('');
const options = ref<{ label: string; value: string }[]>([]);
const addType = ref('试液');
const searchValue = ref('');
const open = ref(false);
const handleOk = async () => {
  let params: any[] = [];
  if (addType.value === '材料') {
    const checkedList: string[] = materialGridApi.grid.getCheckboxRecords();
    const rowList = checkedList.map((item) => item.INVENTORYID);
    params = [
      'REAGENT',
      'DILUTION',
     modalApi.getData().type,
      clickRow.value.BATCHNO,
      rowList,
    ];
  } else if (addType.value === '溶液') {
    const checkedList: string[] = solutionGridApi.grid.getCheckboxRecords();
    const rowList = checkedList.map((item) => item.ORIGREC);
    params = [
      'SOLUCTION',
      'DILUTION',
      modalApi.getData().type,
      clickRow.value.BATCHNO,
      rowList,
    ];
  } else if (addType.value === '菌种') {
    const checkedList: string[] = bacteriaGridApi.grid.getCheckboxRecords();
    const rowList = checkedList.map((item) => item.BATCHNO);
    params = [
      'BACTERIA',
      'DILUTION',
      modalApi.getData().type,
      clickRow.value.BATCHNO,
      rowList,
    ];
  }

  await addReagentsByRunApi(params);
  open.value = false;
  gridApi.query();
};
const typeChange = () => {};
const addMaterial = async () => {
  searchValue.value = '';

  const res = await getCobMatTypeApi([-1]);
  options.value = res.map((item) => {
    return {
      label: item.Text,
      value: item.Value,
    };
  });
  open.value = true;
  addType.value = '材料';
};
const addSolution = async () => {
  searchValue.value = '';
  const res = await getSoluctionTypeApi([]);
  options.value = res.map((item) => {
    return {
      label: item.Text,
      value: item.Value,
    };
  });
  open.value = true;
  addType.value = '溶液';
};
const addBacteria = async () => {
  searchValue.value = '';
  const res = await getBacteriaTypeApi([]);
  options.value = res.map((item) => {
    return {
      label: item.TEXT,
      value: item.VALUE,
    };
  });
  open.value = true;
  addType.value = '菌种';
};
const isSearch = ref(false);
const materialSearch = () => {
  isSearch.value = true;
  materialGridApi.query();
};
const solutionSearch = () => {
  isSearch.value = true;
  solutionGridApi.query();
};
const bacteriaSearch = () => {
  isSearch.value = true;
  bacteriaGridApi.query();
};
</script>
<template>
  <Modal title="试剂材料" class="h-4/5 w-3/5">
    <AModal v-model:open="open" @ok="handleOk" width="50%" class="h-3/5">
      <MaterialGrid v-if="addType === '材料'" class="h-96">
        <template #toolbar-actions>
          <Space :size="[8, 0]" wrap>
            类型
            <Select
              v-model:value="type"
              style="width: 150px"
              @change="typeChange"
            >
              <SelectOption
                v-for="item in options"
                :value="item.value"
                :key="item.value"
              >
                {{ item.label }}
              </SelectOption>
            </Select>
            查找 <Input v-model:value="searchValue" placeholder="" />
            <Button type="primary" @click="materialSearch"> 查询 </Button>
          </Space>
        </template>
      </MaterialGrid>
      <SolutionGrid v-if="addType === '溶液'" class="h-96">
        <template #toolbar-actions>
          <Space :size="[8, 0]" wrap>
            类型
            <Select
              v-model:value="type"
              style="width: 150px"
              @change="typeChange"
            >
              <SelectOption
                v-for="item in options"
                :value="item.value"
                :key="item.value"
              >
                {{ item.label }}
              </SelectOption>
            </Select>
            查找 <Input v-model:value="searchValue" placeholder="" />
            <Button type="primary" @click="solutionSearch"> 查询 </Button>
          </Space>
        </template>
      </SolutionGrid>
      <BacteriaGrid v-if="addType === '菌种'" class="h-96">
        <template #toolbar-actions>
          <Space :size="[8, 0]" wrap>
            类型
            <Select
              v-model:value="type"
              style="width: 150px"
              @change="typeChange"
            >
              <SelectOption
                v-for="item in options"
                :value="item.value"
                :key="item.value"
              >
                {{ item.label }}
              </SelectOption>
            </Select>
            查找 <Input v-model:value="searchValue" placeholder="" />
            <Button type="primary" @click="bacteriaSearch"> 查询 </Button>
          </Space>
        </template>
      </BacteriaGrid>
    </AModal>

    <Grid>
      <template #toolbar-actions>
        <Space :size="[8, 0]" wrap>
          <Button type="primary" @click="addMaterial"> 添加材料 </Button>
          <Button type="primary" @click="addSolution"> 添加溶液 </Button>
          <Button type="primary" @click="addBacteria"> 添加菌种 </Button>
          <Button type="primary" @click="removeMaterial">
            {{ $t('dilution-management.solution-preparation.remove') }}
          </Button>
        </Space>
      </template>
    </Grid>
  </Modal>
</template>
