<script lang="ts" setup>
import type { XIntervalsApi } from '#/api/basic-static-tables';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import {
  $addSampleDueDateCalcApi,
  $getSampleCalcIntervalsApi,
} from '#/api/basic-static-tables';
import { $t } from '#/locales';

import { useAddTatSchema } from './data';

interface FormArgs {
  deptId: string;
}
const emit = defineEmits(['success']);
const formArgs = ref<FormArgs>();
const [Form, formApi] = useVbenForm({
  commonConfig: {
    labelWidth: 130,
  },
  layout: 'horizontal',
  schema: useAddTatSchema(),
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    if (!formArgs.value?.deptId) {
      console.error('deptId is required');
      return;
    }
    const data = await formApi.getValues<XIntervalsApi.XIntervalTat>();
    const sTempInterval = data.INTERVAL;
    const nPos = sTempInterval.indexOf(' ');
    const sInterval = sTempInterval.slice(0, Math.max(0, nPos));
    const sUnit = sTempInterval.slice(Math.max(0, nPos + 1));
    data.INTERVAL = sInterval;
    data.UNIT = sUnit;
    data.DEPT = formArgs.value.deptId;
    data.eventCode = 'addIntervalTat';
    data.comment = '添加时间间隔样品到期计算';
    modalApi.lock();
    try {
      await $addSampleDueDateCalcApi(data);
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  title: $t('ui.actionTitle.create', [
    $t('basic-static-tables.intervals.intervalTat'),
  ]),
  async onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<FormArgs>();
      formArgs.value = data;
      if (formArgs.value.deptId) {
        const intervals = await $getSampleCalcIntervalsApi({
          deptId: formArgs.value.deptId,
        });
        // 更新表单下拉选项
        formApi.updateSchema([
          {
            fieldName: 'INTERVAL',
            componentProps: {
              options: intervals,
              fieldNames: {
                label: 'INTERVAL',
                value: 'INTERVAL',
              },
            },
          },
        ]);
      }
    }
  },
});
</script>
<template>
  <Modal>
    <Form class="mx-4" />
  </Modal>
</template>
