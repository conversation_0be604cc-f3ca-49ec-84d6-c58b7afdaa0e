<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { LocationTypeApi } from '#/api/basic-static-tables/location-type';

import { computed, ref } from 'vue';

import { confirm, Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteLocationTypeSubLoc,
  getLocationTypeSubLocList,
  updateLocationTypeSubLoc,
} from '#/api/basic-static-tables/location-type';

import AddLocationTypeSubLocForm from './add-location-type-subloc.vue';
import { useLocationTypeSublocEventColumns } from './location-type-data';

const locationTypeData = ref<LocationTypeApi.LocationType>();

const gridOptions: VxeTableGridOptions<LocationTypeApi.LocationTypeSubLoc> = {
  columns: useLocationTypeSublocEventColumns(),
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        const data = drawerApi.getData<LocationTypeApi.LocationType>();
        if (!data) {
          return;
        }
        const LocationTypeID = data.LOCATION_TYPE_ID;
        return await getLocationTypeSubLocList(LocationTypeID);
      },
    },
  },
  showOverflow: true,
  exportConfig: {},
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddLocationTypeSubLocForm,
  destroyOnClose: true,
});

function hasEditStatus(row: LocationTypeApi.LocationTypeSubLoc) {
  return gridApi.grid?.isEditByRow(row);
}

function editRowEvent(row: LocationTypeApi.LocationTypeSubLoc) {
  gridApi.grid?.setEditRow(row);
}

async function saveRowEvent(row: LocationTypeApi.LocationTypeSubLoc) {
  await gridApi.grid?.clearEdit();
  gridApi.setLoading(true);
  updateLocationTypeSubLoc(row);
  setTimeout(() => {
    gridApi.setLoading(false);
    message.success({
      content: `保存成功！`,
    });
  }, 600);
}

const cancelRowEvent = (_row: LocationTypeApi.LocationTypeSubLoc) => {
  gridApi.grid?.clearEdit();
};

const [Drawer, drawerApi] = useVbenDrawer({
  onConfirm: onSubmit,
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<LocationTypeApi.LocationType>();
      if (data) {
        locationTypeData.value = data;
        // gridApi?.query();
      } else {
        // gridApi?.reload();
      }
    }
  },
  showCancelButton: false,
  showConfirmButton: false,
});

async function onSubmit() {
  drawerApi.close();
}

const getDrawerTitle = computed(() => {
  if (locationTypeData.value) {
    return `查看 ${locationTypeData.value.TYPE_NAME}`;
  }
  return '位置布局类型';
});

function onCreate() {
  formModalApi
    .setData({ LOCATION_TYPE_ID: locationTypeData.value?.LOCATION_TYPE_ID })
    .open();
}

async function onDelete() {
  // 获取选中行
  const aLocationTypeSublocId: number[] = gridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.LOCATION_TYPES_SUBLOC_ID);

  if (aLocationTypeSublocId.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  if (aLocationTypeSublocId.length > 1) {
    message.warning('只能选择一条数据！');
    return;
  }

  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${aLocationTypeSublocId.length} 条数据吗？`,
      icon: 'warning',
      centered: false,
    });

    const sLocationTypeSublocId = aLocationTypeSublocId[0] as number;
    await deleteLocationTypeSubLoc(sLocationTypeSublocId);

    message.success('删除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

function onRefresh() {
  gridApi.query();
}
</script>

<template>
  <Drawer class="w-full max-w-[1000px]" :title="getDrawerTitle">
    <FormModal @success="onRefresh" />
    <Page auto-content-height>
      <Grid>
        <template #toolbar-actions>
          <Space>
            <Button type="primary" @click="onCreate">
              {{ $t('ui.actionTitle.create') }}
            </Button>
            <Button type="primary" danger @click="onDelete">
              {{ $t('ui.actionTitle.delete') }}
            </Button>
          </Space>
        </template>
        <template #action="{ row }">
          <template v-if="hasEditStatus(row)">
            <Button type="link" @click="saveRowEvent(row)">
              {{ $t('basic-static-tables.save') }}
            </Button>
            <Button type="link" @click="cancelRowEvent(row)">
              {{ $t('basic-static-tables.cancel') }}
            </Button>
          </template>
          <template v-else>
            <Button type="link" @click="editRowEvent(row)">
              {{ $t('basic-static-tables.edit') }}
            </Button>
          </template>
        </template>
      </Grid>
    </Page>
  </Drawer>
</template>
