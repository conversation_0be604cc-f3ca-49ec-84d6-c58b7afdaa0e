<script lang="ts" setup>
import { onMounted, ref } from 'vue';

interface FormData {
  text: string;
  name: string;
  region: string;
  type: string;
  a: {
    a1: string;
    a2: string;
  };
}

interface Props {
  nodeData: {
    id: string;
    properties?: FormData;
    text?: {
      value: string;
    };
  };
  lf: any;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  onClose: [];
}>();

const text = ref<string>('');
const formData = ref<FormData>({
  text: '',
  name: '',
  region: '',
  type: '',
  a: {
    a1: '',
    a2: '',
  },
});

onMounted(() => {
  const { properties, text: nodeText } = props.nodeData;

  if (properties) {
    formData.value = Object.assign({}, formData.value, properties);
  }

  if (nodeText && nodeText.value) {
    formData.value.text = nodeText.value;
    text.value = nodeText.value;
  }
});

const onSubmit = () => {
  const { id } = props.nodeData;

  props.lf.setProperties(id, {
    ...formData.value,
  });

  props.lf.updateText(id, formData.value.text);
  emit('onClose');
};
</script>

<template>
  <div>
    <el-form label-width="80px" :model="formData">
      <el-form-item label="文案">
        <el-input v-model="formData.text" />
      </el-form-item>
      <el-form-item label="名称">
        <el-input v-model="formData.name" />
      </el-form-item>
      <el-form-item label="活动区域">
        <el-input v-model="formData.region" />
      </el-form-item>
      <el-form-item label="活动形式">
        <el-input v-model="formData.type" />
      </el-form-item>
      <el-form-item label="A">
        <el-input v-model="formData.a.a1" />
        <el-input v-model="formData.a.a2" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped></style>
