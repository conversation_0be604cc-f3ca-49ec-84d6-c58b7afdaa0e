<script lang="ts" setup>
import type { SpecSchemasApi } from '#/api/business-static-tables';

import { computed, ref } from 'vue';

import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import {
  $deleteSpecSchemaCalcApi,
  $getSpecSchemaCalcsApi,
} from '#/api/business-static-tables';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';
import { confirm } from '#/utils/utils';

import AddSpecSchemaCalcs from './add-spec-schema-calc.vue';
import {
  useSpecSchemasCalcsColumns,
  useSpecSchemasCalcsFilterSchema,
} from './data';

const specSchemaData = ref<SpecSchemasApi.SpecSchemas>();
const colums = useSpecSchemasCalcsColumns();
const filterSchema = useSpecSchemasCalcsFilterSchema();
const queryData = async () => {
  return await $getSpecSchemaCalcsApi({
    specSchemaCode: specSchemaData.value?.SPECSCHEMACODE ?? '',
  });
};

const {
  Grid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
} = useLimsGridsConfig<SpecSchemasApi.SpecSchemaCalcs>(
  colums,
  filterSchema,
  queryData,
);
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddSpecSchemaCalcs,
  destroyOnClose: true,
});
const [Drawer, drawerApi] = useVbenDrawer({
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData();
      if (data) {
        specSchemaData.value = data.specSchema;
        gridApi?.query();
      } else {
        gridApi?.reload();
      }
    }
  },
  showCancelButton: false,
  showConfirmButton: false,
  class: '',
});

const getDrawerTitle = computed(() => {
  return `${$t('business-static-tables.specSchema.action')}: ${specSchemaData.value?.SPECSCHEMANAME ?? ''}`;
});

function onRefresh() {
  gridApi.query();
}

async function onDelete() {
  const checkRows = gridApi.grid.getCheckboxRecords();
  const checkOrig: number[] = checkRows.map((item) => item.ORIGREC);

  if (checkOrig.length === 0) {
    message.warning($t('commons.selectOne'));
    return;
  }

  await confirm($t('commons.deleteConfirm'), $t('commons.deleteConfirmTitle'));
  await $deleteSpecSchemaCalcApi(checkOrig);
  message.success($t('commons.deleteSuccess'));
  onRefresh();
}

function onCreate() {
  formModalApi
    .setData({ specSchemaCode: specSchemaData.value?.SPECSCHEMACODE ?? '' })
    .open();
}
</script>
<template>
  <Drawer class="w-full max-w-[1500px]" :title="getDrawerTitle">
    <FormModal @success="onRefresh" />
    <Page auto-content-height>
      <Grid>
        <template #toolbar-actions>
          <Space :size="[4, 0]" wrap>
            <Button type="primary" @click="onCreate">
              {{ $t('ui.actionTitle.create') }}
            </Button>
            <Button type="primary" danger @click="onDelete">
              {{ $t('ui.actionTitle.delete') }}
            </Button>
          </Space>
        </template>
        <template #action="{ row }">
          <template v-if="hasEditStatus(row)">
            <Button type="link" @click="saveRowEvent(row)">
              {{ $t('commons.save') }}
            </Button>
            <Button type="link" @click="cancelRowEvent(row)">
              {{ $t('commons.cancel') }}
            </Button>
          </template>
          <template v-else>
            <Button type="link" @click="editRowEvent(row)">
              {{ $t('commons.edit') }}
            </Button>
          </template>
        </template>
      </Grid>
    </Page>
  </Drawer>
</template>
