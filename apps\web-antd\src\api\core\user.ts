import type { UserInfo } from '@vben/types';

import { backendRequestClient } from '#/api/request';

/**
 * 获取用户信息
 */
export async function getUserInfoApi(id: string, accessToken?: string) {
  return backendRequestClient.get<UserInfo>(`/platform/users/${id}`, {
    headers: accessToken
      ? { Authorization: `Bearer ${accessToken}` }
      : undefined,
  });
}

export async function getUserDepts(userId: string, accessToken?: string) {
  return await backendRequestClient.get(`/platform/users/${userId}/depts`, {
    headers: accessToken
      ? { Authorization: `Bearer ${accessToken}` }
      : undefined,
  });
}

export async function getUserRoles(userId: string, accessToken?: string) {
  return await backendRequestClient.get(`/platform/users/${userId}/roles`, {
    headers: accessToken
      ? { Authorization: `Bearer ${accessToken}` }
      : undefined,
  });
}
