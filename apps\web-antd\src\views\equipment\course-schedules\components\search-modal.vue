<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import { getCboScheduledCoursesApi } from '#/api/equipment/course-schedules';
import { searchSchema } from '../course-schedules-data';
import { ref } from 'vue';
import {
  Select,
  SelectOption,
} from 'ant-design-vue';
const emit = defineEmits(['success']);
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: searchSchema(),
  showDefaultActions: false,
});
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    data.courseName=options.value.find((item)=>item.value===data.course)?.label;
    modalApi.lock();
    try {
      emit('success', data);
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      const data = await getCboScheduledCoursesApi([]);
      options.value = data.map((item) => ({
        label: item.COURSENAME,
        value: item.COURSECODE,
      }));
      // const data = modalApi.getData<SampleTypeApi.SampleType>();
    }
  },
});
const options = ref<{ label: string; value: string }[]>([]);
</script>
<template>
  <Modal title="搜索课程计划">
    <Form class="mx-4">
      <template #course="slotProps">
        <Select v-bind="slotProps" class="w-full">
          <SelectOption
            v-for="item in options"
            :value="item.value"
            :key="item.value"
          >
            {{ item.label }}
          </SelectOption>
        </Select>
      </template>
    </Form>
  </Modal>
</template>
