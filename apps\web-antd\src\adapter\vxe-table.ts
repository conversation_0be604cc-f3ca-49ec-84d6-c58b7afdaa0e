import type { Recordable } from '@vben/types';

import { h } from 'vue';

import { IconifyIcon } from '@vben/icons';
import { $te } from '@vben/locales';
import { setupVbenVxeTable, useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { get, isFunction, isString } from '@vben/utils';

import { objectOmit } from '@vueuse/core';
import { Button, Image, InputNumber, Popconfirm, Tag } from 'ant-design-vue';

import { $t } from '#/locales';

import ApiSelectEdit from './component/vxe-table/api-select-edit.vue';
import TableFilterInput from './component/vxe-table/table-filter-input.vue';
import { extendsDefaultFormatter } from './extend';
import { useVbenForm } from './form';

setupVbenVxeTable({
  configVxeTable: (vxeUI) => {
    vxeUI.setConfig({
      grid: {
        align: 'center',
        border: false,
        columnConfig: {
          resizable: true,
        },
        minHeight: 180,
        formConfig: {
          // 全局禁用vxe-table的表单配置，使用formOptions
          enabled: false,
        },
        proxyConfig: {
          autoLoad: true,
          response: {
            result: 'items',
            total: 'total',
            list: '',
          },
          showActiveMsg: true,
          showResponseMsg: false,
        },
        round: true,
        showOverflow: true,
        size: 'small',
      },
    });

    /**
     * 解决vxeTable在热更新时可能会出错的问题
     */
    vxeUI.renderer.forEach((_item, key) => {
      if (key.startsWith('Cell')) {
        vxeUI.renderer.delete(key);
      }
    });

    // 表格配置项可以用 cellRender: { name: 'CellImage' },
    vxeUI.renderer.add('CellImage', {
      renderTableDefault(_renderOpts, params) {
        const { column, row } = params;
        return h(Image, { src: row[column.field] });
      },
    });

    // 表格配置项可以用 cellRender: { name: 'CellLink' },
    vxeUI.renderer.add('CellLink', {
      renderTableDefault(renderOpts, params) {
        const { props } = renderOpts;
        const { row, column } = params;
        const linkText = row[column.field];
        return h(
          Button,
          { size: 'small', type: 'link', target: '_blank', href: linkText },
          { default: () => props?.text ?? linkText },
        );
      },
    });

    // 单元格渲染： Tag
    vxeUI.renderer.add('CellTag', {
      renderTableDefault({ options, props }, { column, row }) {
        const value = get(row, column.field);
        const tagOptions = options ?? [
          { color: 'success', label: $t('common.enabled'), value: 1 },
          { color: 'error', label: $t('common.disabled'), value: 0 },
        ];
        const tagItem = tagOptions.find((item) => item.value === value);
        return h(
          Tag,
          {
            ...props,
            ...objectOmit(tagItem ?? {}, ['label']),
          },
          { default: () => tagItem?.label ?? value },
        );
      },
    });

    // 单元格渲染： StabCellTag
    vxeUI.renderer.add('StabCellTag', {
      renderTableDefault({ options, props }, { column, row }) {
        const value = get(row, column.field);
        if (value === null) {
          return h('div', {}, value);
        }
        const tagOptions = options ?? [
          { color: 'orchid', label: $t('commons.test'), value: 'test' },
          { color: 'olive', label: $t('commons.backup'), value: 'backup' },
        ];
        const tagItem = tagOptions.find((item) => item.value === value);
        return h(
          Tag,
          {
            ...props,
            ...objectOmit(tagItem ?? {}, ['label']),
          },
          { default: () => tagItem?.label ?? value },
        );
      },
    });

    // 单元格渲染：多个 Tag
    vxeUI.renderer.add('CellTags', {
      renderTableDefault({ options, props }, { column, row }) {
        const value = get(row, column.field);
        const tagOptions = options ?? [];

        // 预设的随机颜色
        const randomColors = [
          'blue',
          'green',
          'cyan',
          'purple',
          'magenta',
          'orange',
          'gold',
          'lime',
          'volcano',
          'geekblue',
        ];

        // 将非数组值转换为数组
        const values = Array.isArray(value) ? value : [value];

        const tags = values.map((val, index) => {
          const tagItem = tagOptions.find((item) => item.value === val);
          // 如果没有指定颜色，则随机选择一个颜色
          const color =
            tagItem?.color ??
            // randomColors[Math.floor(Math.random() * randomColors.length)];
            randomColors[index];

          return h(
            Tag,
            {
              ...props,
              ...objectOmit(tagItem ?? {}, ['label']),
              color,
              bordered: false,
              style: { marginRight: '2px', marginBottom: '2px' },
            },
            { default: () => tagItem?.label ?? val },
          );
        });

        return h(
          'div',
          {
            style: {
              display: 'flex',
              flexWrap: 'wrap',
              gap: '2px',
            },
          },
          tags,
        );
      },
    });

    /**
     * 注册表格的操作按钮渲染器
     */
    vxeUI.renderer.add('CellOperation', {
      renderTableDefault({ attrs, options, props }, { column, row }) {
        const defaultProps = { size: 'small', type: 'link', ...props };
        let align = 'end';
        switch (column.align) {
          case 'center': {
            align = 'center';
            break;
          }
          case 'left': {
            align = 'start';
            break;
          }
          default: {
            align = 'end';
            break;
          }
        }
        const presets: Recordable<Recordable<any>> = {
          delete: {
            danger: true,
            text: $t('common.delete'),
          },
          edit: {
            text: $t('common.edit'),
          },
        };
        const operations: Array<Recordable<any>> = (
          options || ['edit', 'delete']
        )
          .map((opt) => {
            if (isString(opt)) {
              return presets[opt]
                ? { code: opt, ...presets[opt], ...defaultProps }
                : {
                    code: opt,
                    text: $te(`common.${opt}`) ? $t(`common.${opt}`) : opt,
                    ...defaultProps,
                  };
            } else {
              return { ...defaultProps, ...presets[opt.code], ...opt };
            }
          })
          .map((opt) => {
            const optBtn: Recordable<any> = {};
            Object.keys(opt).forEach((key) => {
              optBtn[key] = isFunction(opt[key]) ? opt[key](row) : opt[key];
            });
            return optBtn;
          })
          .filter((opt) => opt.show !== false);

        function renderBtn(opt: Recordable<any>, listen = true) {
          return h(
            Button,
            {
              ...props,
              ...opt,
              icon: undefined,
              onClick: listen
                ? () =>
                    attrs?.onClick?.({
                      code: opt.code,
                      row,
                    })
                : undefined,
            },
            {
              default: () => {
                const content = [];
                if (opt.icon) {
                  content.push(
                    h(IconifyIcon, { class: 'size-5', icon: opt.icon }),
                  );
                }
                content.push(opt.text);
                return content;
              },
            },
          );
        }

        function renderConfirm(opt: Recordable<any>) {
          return h(
            Popconfirm,
            {
              getPopupContainer(el) {
                return (
                  el
                    .closest('.vxe-table--viewport-wrapper')
                    ?.querySelector('.vxe-table--main-wrapper')
                    ?.querySelector('tbody') || document.body
                );
              },
              placement: 'topLeft',
              title: $t('ui.actionTitle.delete', [attrs?.nameTitle || '']),
              ...props,
              ...opt,
              icon: undefined,
              onConfirm: () => {
                attrs?.onClick?.({
                  code: opt.code,
                  row,
                });
              },
            },
            {
              default: () => renderBtn({ ...opt }, false),
              description: () =>
                h(
                  'div',
                  { class: 'truncate' },
                  $t('ui.actionMessage.deleteConfirm', [
                    row[attrs?.nameField || 'name'],
                  ]),
                ),
            },
          );
        }

        const btns = operations.map((opt) =>
          opt.code === 'delete' ? renderConfirm(opt) : renderBtn(opt),
        );
        return h(
          'div',
          {
            class: 'flex table-operations',
            style: { justifyContent: align },
          },
          btns,
        );
      },
    });

    // 创建一个输入框筛选渲染器 filterRender: { name: 'TableFilterInput'}
    vxeUI.renderer.add('TableFilterInput', {
      // 自定义筛选模板
      renderTableFilter(_renderOpts, renderParams) {
        return h(TableFilterInput, {
          renderParams,
        });
      },

      // 自定义重置数据方法
      tableFilterResetMethod(params) {
        const { options } = params;
        options.forEach((option) => {
          option.data = '';
        });
      },

      // 自定义重置筛选复原方法（当未点击确认时，该选项将被恢复为默认值）
      tableFilterRecoverMethod({ option }) {
        option.data = '';
      },

      // 自定义筛选方法
      tableFilterMethod(params) {
        // console.log('tableFilterMethod', params);
        const { option, row, column } = params;
        const { data } = option;
        const cellValue = row[column.field];
        if (cellValue) {
          if (typeof cellValue === 'number') {
            return cellValue.toString().includes(data);
          } else if (typeof cellValue === 'boolean') {
            return cellValue.toString().includes(data);
          } else {
            return cellValue.includes(data);
          }
        }
        return false;
      },
    });

    // 创建一个下拉选项渲染器
    vxeUI.renderer.add('ApiSelectEdit', {
      // 可编辑激活模板
      renderTableEdit(renderOpts, renderParams) {
        const { attrs, props } = renderOpts;
        // console.log(attrs);
        // console.log(props);
        const finallyProps = {
          ...props,
          ...attrs,
          renderParams,
        };
        return h(ApiSelectEdit, finallyProps);
      },
      // 可编辑显示模板
      renderTableCell(_renderOpts, renderParams) {
        const { row, column } = renderParams;
        return h('div', {}, row[column.field]);
      },
    });

    vxeUI.renderer.add('InputNumber', {
      renderTableEdit(renderOpts, renderParams) {
        const { attrs, props } = renderOpts;
        const { row, column } = renderParams;
        const val = row[column.field];
        const finallyProps = {
          ...props,
          ...attrs,
          value: val,
          renderParams,
          onChange: (value: null | number) => {
            row[column.field] = value;
          },
        };
        return h(InputNumber, finallyProps);
      },
    });

    // 扩展一些formatter
    extendsDefaultFormatter(vxeUI);

    // 这里可以自行扩展 vxe-table 的全局配置，比如自定义格式化
  },
  useVbenForm,
});
export { useVbenVxeGrid };

export type OnActionClickParams<T = Recordable<any>> = {
  code: string;
  row: T;
};
export type OnActionClickFn<T = Recordable<any>> = (
  params: OnActionClickParams<T>,
) => void;

export type * from '@vben/plugins/vxe-table';
