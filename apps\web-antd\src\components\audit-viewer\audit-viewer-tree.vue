<script lang="ts" setup>
import type { TreeProps } from 'ant-design-vue';
import type { Key } from 'ant-design-vue/es/_util/type';
import type { DataNode, EventDataNode } from 'ant-design-vue/es/tree';

import { ref, watch } from 'vue';

import { Tree } from 'ant-design-vue';

// 类型定义
interface TreeSelectEvent {
  event: 'select';
  nativeEvent: MouseEvent;
  node: EventDataNode;
  selected: boolean;
  selectedNodes: DataNode[];
}

// Props 定义
const props = defineProps<{
  treeData: TreeProps['treeData'];
}>();

// Emits 定义
const emit = defineEmits<{
  (e: 'treeSelect', selectedKeys: Key[], info: TreeSelectEvent): void;
}>();

// 状态管理
const data = ref(props.treeData);
const selectedKeys = ref<Key[]>([]);
const isInitialized = ref(false);

// 监听 treeData 变化
watch(
  () => props.treeData,
  (newTreeData) => {
    data.value = newTreeData;
    // 只在初始化时自动选择第一个节点
    if (!isInitialized.value && newTreeData?.length) {
      const firstNode = newTreeData[0] as DataNode;
      if (firstNode.key) {
        selectedKeys.value = [firstNode.key];
        emit('treeSelect', selectedKeys.value, {
          event: 'select',
          nativeEvent: new MouseEvent('click'),
          node: firstNode as EventDataNode,
          selected: true,
          selectedNodes: [firstNode],
        });
        isInitialized.value = true;
      }
    }
  },
  { immediate: true },
);

// 事件处理
const handleSelect = async (_selectedKeys: Key[], info: TreeSelectEvent) => {
  selectedKeys.value = _selectedKeys;
  emit('treeSelect', _selectedKeys, info);
};
</script>

<template>
  <div class="audit-tree-panel">
    <Tree
      :tree-data="data"
      :default-expand-all="true"
      :selected-keys="selectedKeys"
      :show-icon="true"
      @select="handleSelect"
    >
      <!-- <template #icon="iconProps">
        <div v-if="logKey(iconProps)" style="display: none"></div>
        <LoaderCircle
          v-if="iconProps.selected && iconProps.children.length === 0"
          class="spinner"
          :size="16"
        />
      </template> -->
    </Tree>
  </div>
</template>

<style lang="less" scoped>
.audit-tree-panel {
  height: 100%;
  padding: 16px 8px;
  margin-right: 8px;
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 4%);
}

:deep(.ant-tree) {
  font-size: 15px;
  color: #333;
  height: 100%;
  overflow: auto;
}

:deep(.ant-tree .ant-tree-node-content-wrapper) {
  padding: 4px 8px;
  border-radius: 4px;
  transition: background 0.2s;

  &:hover {
    color: #1677ff;
    background: #f0f5ff;
  }
}

:deep(.ant-tree .ant-tree-node-selected) {
  color: #1677ff !important;
  background: #e6f7ff !important;
}

.spinner {
  display: inline-block;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>
