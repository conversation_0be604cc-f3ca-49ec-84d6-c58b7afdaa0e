import type { VxeColumnPropTypes } from 'vxe-table';

import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { InspectionItemApi } from '#/api/basic-static-tables/inspection-item';

import { $getLookupValuesSimple } from '#/api/basic-static-tables/generic-meta-data/lookup';
import { $t } from '#/locales';

export function useColumns(
  fileNameEditRender: VxeColumnPropTypes.EditRender,
): VxeTableGridOptions<InspectionItemApi.InspectionItem>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 60 },
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 80,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ITEM_SORT',
      title: $t('commons.sort'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      editRender: { name: 'input', attrs: { type: 'number' } },
    },
    {
      align: 'center',
      field: 'FILENAME',
      title: $t('commons.filename'),
      width: 300,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      editRender: fileNameEditRender,
    },
    {
      align: 'center',
      field: 'ITEM_TYPE',
      width: 200,
      title: $t('basic-static-tables.inspectionItem.itemType'),
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ITEM_CONTENT',
      width: 500,
      title: $t('basic-static-tables.inspectionItem.itemContent'),
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'EDITFLAG',
      title: $t('basic-static-tables.inspectionItem.editFlag'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      cellRender: {
        props: {
          checkedValue: 'Y',
          unCheckedValue: 'N',
          checkedChildren: $t('commons.yes'),
          unCheckedChildren: $t('commons.no'),
          // disabled: true,
        },
        name: 'CellSwitch',
      },
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('commons.action'),
      width: 140,
    },
  ];
}

export function useSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      componentProps: {
        api: $getLookupValuesSimple,
        params: 'InspectionFileName',
        labelField: 'TEXT',
        valueField: 'VALUE',
        class: 'w-full',
      },
      fieldName: 'FILENAME',
      label: $t('commons.filename'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: $getLookupValuesSimple,
        params: 'InspectionItmType',
        labelField: 'TEXT',
        valueField: 'VALUE',
        class: 'w-full',
      },
      fieldName: 'ITEM_TYPE',
      label: $t('basic-static-tables.inspectionItem.itemType'),
      rules: 'required',
    },
    {
      component: 'Textarea',
      componentProps: {
        autoSize: { minRows: 2 },
      },
      fieldName: 'ITEM_CONTENT',
      label: $t('basic-static-tables.inspectionItem.itemContent'),
      rules: 'required',
    },
    {
      component: 'Checkbox',
      componentProps: {
        trueValue: 'Y',
        falseValue: 'N',
      },
      fieldName: 'EDITFLAG',
      label: $t('basic-static-tables.inspectionItem.editFlag'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'ITEM_SORT',
      label: $t('commons.sort'),
      rules: 'required',
    },
  ];
}

export function useFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      componentProps: {
        api: $getLookupValuesSimple,
        params: { key: 'InspectionFileName' },
        labelField: 'TEXT',
        valueField: 'VALUE',
        class: 'w-full',
      },
      fieldName: 'FILENAME',
      label: $t('commons.filename'),
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: $getLookupValuesSimple,
        params: { key: 'InspectionItmType' },
        labelField: 'TEXT',
        valueField: 'VALUE',
        class: 'w-full',
      },
      fieldName: 'ITEM_TYPE',
      label: $t('basic-static-tables.inspectionItem.itemType'),
    },
    {
      component: 'Textarea',
      componentProps: {
        autoSize: { minRows: 2 },
      },
      fieldName: 'ITEM_CONTENT',
      label: $t('basic-static-tables.inspectionItem.itemContent'),
    },
    {
      component: 'Checkbox',
      componentProps: {
        trueValue: 'Y',
        falseValue: 'N',
      },
      fieldName: 'EDITFLAG',
      label: $t('basic-static-tables.inspectionItem.editFlag'),
    },
    {
      component: 'Input',
      fieldName: 'ITEM_SORT',
      label: $t('commons.sort'),
    },
  ];
}
