<script lang="ts" setup>
import type { TestCategoriesApi } from '#/api/basic-static-tables/test-categories';

import { ref } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';

import { Button, Space } from 'ant-design-vue';

import { getSites } from '#/api/basic-static-tables/test-categories';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import TransferModal from './edit-dept.vue';
import { useDeptColumns, useDeptFilterSchema } from './test-categories-data';

const testCategory = ref<null | TestCategoriesApi.TestCategory>(null);

const transferModalRef = ref();

const colums = useDeptColumns();
const filterSchema = useDeptFilterSchema();
const queryData = async () => {
  const data = drawerApi.getData<TestCategoriesApi.TestCategory>();
  if (!data) {
    return;
  }
  const sTestCatCode = data.TESTCATCODE;
  const dataResult = await getSites(sTestCatCode);
  return dataResult.items;
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};

const { Grid, gridApi } = useLimsGridsConfig<TestCategoriesApi.TestCategory>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

function onRefresh() {
  gridApi.query();
}

const [Drawer, drawerApi] = useVbenDrawer({
  onConfirm: onSubmit,
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<TestCategoriesApi.TestCategory>();
      if (data) {
        testCategory.value = data;
        // gridApi?.query();
      } else {
        // gridApi?.reload();
      }
    }
  },
  showCancelButton: false,
  showConfirmButton: false,
});

async function onSubmit() {
  drawerApi.close();
}

function openTransferModal() {
  transferModalRef.value?.open();
}
</script>

<template>
  <Drawer class="w-full max-w-[1000px]" title="实验室">
    <TransferModal
      ref="transferModalRef"
      :current-test-row="testCategory"
      @success="onRefresh"
    />
    <Page auto-content-height>
      <Grid>
        <template #toolbar-actions>
          <Space>
            <Button type="primary" @click="openTransferModal"> 编辑 </Button>
          </Space>
        </template>
      </Grid>
    </Page>
  </Drawer>
</template>
