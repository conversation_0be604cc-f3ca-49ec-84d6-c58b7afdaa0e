<script lang="ts" setup>
import type { VxeGridListeners } from '#/adapter/vxe-table';
import type { SpecSchemasApi } from '#/api/business-static-tables';

import { watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import {
  $delchemaGroupApi,
  $getSchemaGroupApi,
} from '#/api/business-static-tables';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';
import { confirm } from '#/utils/utils';

import AddSchemaGroupForm from './add-schema-group.vue';
import { useColumns, useFilterSchema } from './data';

const emit = defineEmits(['select']);

const colums = useColumns();
const filterSchema = useFilterSchema();
const queryData = async () => {
  return await $getSchemaGroupApi();
};
const event: VxeGridListeners = {};
const gridOptions = {
  // pagerConfig: {
  //   enabled: false,
  // },
};

const { Grid, gridApi, CurrentRow } =
  useLimsGridsConfig<SpecSchemasApi.SpecGroups>(
    colums,
    filterSchema,
    queryData,
    gridOptions,
    event,
  );

watch(CurrentRow, (val) => {
  emit('select', val);
});

async function onDelete() {
  const checkOrig: number[] =
    (gridApi.grid
      ?.getCheckboxRecords()
      .map((item) => item.ORIGREC) as number[]) || [];

  if (checkOrig.length === 0) {
    message.warning($t('commons.selectOne'));
    return;
  }
  await confirm($t('commons.deleteConfirm'), $t('commons.deleteConfirmTitle'));
  await $delchemaGroupApi(checkOrig);
  message.success($t('commons.deleteSuccess'));
  onRefresh();
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddSchemaGroupForm,
  destroyOnClose: true,
});

function onCreate() {
  formModalApi.setData(null).open();
}

function onRefresh() {
  gridApi.query();
}
</script>

<template>
  <FormModal @success="onRefresh" />
  <Grid>
    <template #toolbar-actions>
      <Space :size="[4, 0]" wrap>
        <Button type="primary" @click="onCreate">
          {{ $t('ui.actionTitle.create') }}
        </Button>
        <Button type="primary" danger @click="onDelete">
          {{ $t('ui.actionTitle.delete') }}
        </Button>
      </Space>
    </template>
  </Grid>
</template>
