import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { ChsEngTranslateApi } from '#/api/basic-static-tables/chs-translate';

import { $getTransDataApiTypeApi } from '#/api/basic-static-tables/chs-translate';
import { $t } from '#/locales';

export function useColumns(): VxeTableGridOptions<ChsEngTranslateApi.ChsEngTranslate>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'TYPE',
      title: $t('commons.type'),
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      editRender: { name: 'input' },
    },
    {
      align: 'center',
      field: 'CHSVAL',
      title: $t('basic-static-tables.chsTranslate.chsVal'),
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      editRender: { name: 'input' },
    },
    {
      align: 'center',
      field: 'ENGVAL',
      title: $t('basic-static-tables.chsTranslate.engVal'),
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('commons.action'),
      width: 140,
    },
  ];
}

export function useSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'CHSVAL',
      label: $t('basic-static-tables.chsTranslate.chsVal'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'ENGVAL',
      label: $t('basic-static-tables.chsTranslate.engVal'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: $getTransDataApiTypeApi,
        labelField: 'TEXT',
        valueField: 'VALUE',
        class: 'w-full',
      },
      fieldName: 'TYPE',
      label: $t('commons.type'),
      rules: null,
    },
  ];
}

export function useFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'CHSVAL',
      label: $t('basic-static-tables.chsTranslate.chsVal'),
    },
    {
      component: 'Input',
      fieldName: 'ENGVAL',
      label: $t('basic-static-tables.chsTranslate.engVal'),
    },
  ];
}
