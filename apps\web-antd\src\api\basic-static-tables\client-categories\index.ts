import { callServer, getDataSet } from '#/api/core/witlab';

export namespace CategoriesApi {
  export interface Categories {
    ORIGREC: number;
    CATEGORY: string;
  }
}

/**
 * 获取客户类别列表数据
 */
async function getCategorieList() {
  return getDataSet('CLIENT_CATEGORIES.DS_CLIENTCATEGORIES_DGDCLIENTCAT', []);
}

/**
 * 创建客户类别
 * @param data 客户类别数据
 */
async function addCategorie(data: CategoriesApi.Categories) {
  return await callServer('CLIENT_CATEGORIES.ADD_CATEGORY', [data.CATEGORY]);
}

/**
 * 删除客户类别
 * @param category 客户类别 category
 */
async function deleteCategorie(category: string[]) {
  return await callServer('CLIENT_CATEGORIES.DEL_CATEGORY', [category]);
}

export { addCategorie, deleteCategorie, getCategorieList };
