import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { VirusTypeApi } from '#/api/basic-static-tables/prompt-virus-type';

import { $t } from '#/locales';

export function useColumns(): VxeTableGridOptions<VirusTypeApi.VirusType>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'TYPENAME',
      title: $t('basic-static-tables.virusType.typeName'),
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'AGARVALIDITY',
      title: $t('basic-static-tables.virusType.agarValidity'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      editRender: {
        name: 'input',
        props: {
          type: 'integer',
        },
      },
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('commons.action'),
      width: 140,
    },
  ];
}

export function useSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'TYPENAME',
      label: $t('basic-static-tables.virusType.typeName'),
      rules: 'required',
    },
  ];
}

export function useFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'TYPENAME',
      label: $t('basic-static-tables.virusType.typeName'),
    },
  ];
}

export function useInfoColumns(): VxeTableGridOptions<VirusTypeApi.VirusInfo>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'VIRUSNAME',
      title: $t('basic-static-tables.virusInfo.virusName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}

export function useInfoSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'VIRUSNAME',
      label: $t('basic-static-tables.virusInfo.virusName'),
      rules: 'required',
    },
  ];
}

export function useInfoFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'TYPENAME',
      label: $t('basic-static-tables.virusType.typeName'),
    },
  ];
}
