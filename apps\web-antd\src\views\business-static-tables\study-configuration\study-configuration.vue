<script setup lang="ts">
import type { XFolder<PERSON><PERSON> } from '#/api/business-static-tables';

import { onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { alert, Page, prompt, useVbenModal } from '@vben/common-ui';

import {
  Button,
  message,
  Radio,
  RadioGroup,
  Space,
  TabPane,
  Tabs,
} from 'ant-design-vue';

import {
  $closeStudyConfigurationApi,
  $createXorders_XordTaskApi,
  $deleteProtocolApi,
  $editStabMaterialsApi,
  $executeGeneralWorkFlowStepApi,
  $getStabilityArrayDataApi,
  $getStabilityMaterialsApi,
  $getStudyTemplateApi,
  $scFillMatrixApi,
} from '#/api/business-static-tables';
import { $findNextStepOfWorkFlowApi } from '#/api/common';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';
import { confirm } from '#/utils/utils';
import Signatures from '#/views/run-create-results-entry-run-approv/signatures.vue';

import AddProtocolWizard from './add-protocol-wizard.vue';
import AddStudyWizard from './add-study-wizard.vue';
import CopyTemplateOrProtocolForm from './copy-template-or-protocol-form.vue';
import {
  useColumns,
  useCondIntTestMatrixColumns,
  useMaterialsColumns,
} from './data';
import EditMaterialForm from './edit-material-form.vue';
import EditMaterials from './edit-materials.vue';
import SampleQty from './sample-qty.vue';
import StabnoIntervalsTat from './stabno-intervals-tat.vue';

const mode = ref<string>('TEMPLATECONSOLE'); // 'TEMPLATECONSOLE'|'PROTOCOLCONSOLE'
const spCode = ref<number>(-1);
const matCode = ref<string>('');
const subStudyList = ref<string>('');

const AddedSymbol = 'test';
const BackupSymbol = 'backup';
const matrixType = ref<string>('test');
const SubStudySymbol =
  '|||RUNTIME_SUPPORT.GetImageById.lims?ImageId=75C6C0BC-9A60-4247-B630-2F355DD5EC32';
const oArg = ref({ sMode: 'STABILITY_PROTOCOL', aDsParam: [-1] });
const activeKey = ref('1');

const signaturesRef = ref<InstanceType<typeof Signatures>>();

const route = useRoute();
onMounted(async () => {
  if (route.query.mode) {
    mode.value = route.query.mode as string;
  }
  if (route.query.spCode) {
    spCode.value = Number(route.query.spCode);
  }
  if (route.query.matCode) {
    matCode.value = route.query.matCode as string;
  }
  if (route.query.subStudyList) {
    subStudyList.value = route.query.subStudyList as string;
  }
});

const queryData = async () => {
  return await $getStudyTemplateApi({
    sMode: mode.value,
    nSpcode: spCode.value,
    sMatCode: matCode.value,
    sShowAll: 'false',
    sStabnoList: '',
  });
};
const {
  Grid: StudyTemplatesGrid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
  CurrentRow,
  CurrentCheckRow,
} = useLimsGridsConfig<XFolderApi.XFolder>(useColumns(), [], queryData);

const {
  Grid: CondIntTestMatrixGrid,
  gridApi: condIntTestMatrixGridApi,
  CurrentRow: MatrixCurrentRow,
} = useLimsGridsConfig<XFolderApi.CondIntTestMatrix>(
  useCondIntTestMatrixColumns(),
  [],
  () => {
    return Promise.resolve([]);
  },
  {
    pagerConfig: {
      enabled: false,
    },
    toolbarConfig: {
      export: true,
      zoom: true,
    },
  },
  {
    cellDblclick: async ({ row, column }) => {
      if (mode.value !== 'PROTOCOLCONSOLE') {
        return;
      }
      const folderStatus = CurrentRow.value?.FDISPSTAT;
      if (folderStatus === 'Released' || folderStatus === 'Retired') {
        message.warning(
          $t('business-static-tables.studyConfiguration.cannotEditNotDraft'),
        );
        return;
      }

      // console.log(row, column);
      if (!CurrentRow.value) {
        return;
      }
      const colName = column.field;
      if (colName === 'Col3' || colName === 'Col4') {
        // 阻止点击Col3、Col4列
        return;
      }
      const IntervalAssigned = row[colName];
      const Condition = row.Col4;
      const StabNo = CurrentRow.value.STABNO;
      const Interval = column.title;
      const Testcode = row.Col2;
      const nSubStudyNo = null;
      let Action = '';
      if (
        IntervalAssigned === AddedSymbol ||
        IntervalAssigned === BackupSymbol ||
        IntervalAssigned === SubStudySymbol
      ) {
        Action = 'Delete';
        row[colName] = '';
        condIntTestMatrixGridApi.grid.reloadRow(row, {});
      } else if (
        IntervalAssigned.length === 1 &&
        Testcode !== -101 &&
        matrixType.value === BackupSymbol
      ) {
        Action = 'Backup';
        row[colName] = BackupSymbol;
        condIntTestMatrixGridApi.grid.reloadRow(row, {});
      } else if (
        IntervalAssigned.length === 1 &&
        matrixType.value === BackupSymbol &&
        Testcode === -101
      ) {
        // const aRet = await form.ShowModalDialog(
        //   lims.GetFormSource('StudyConfiguration.selectSubStudy'),
        //   [StabNo],
        // );

        // if (lims.Len(aRet) < 1) return;
        Action = 'Add';
        // nSubStudyNo = aRet[0];
        return;
      } else if (
        IntervalAssigned.length === 1 &&
        matrixType.value === AddedSymbol &&
        Testcode !== -101
      ) {
        Action = 'Add';
        row[colName] = AddedSymbol;
        condIntTestMatrixGridApi.grid.reloadRow(row, {});
      } else if (
        IntervalAssigned.length === 1 &&
        matrixType.value === AddedSymbol &&
        Testcode === -101
      ) {
        // var aRet = await form.ShowModalDialog(lims.GetFormSource("StudyConfiguration.selectSubStudy"),[StabNo]);

        // if (lims.Len(aRet)<1) return;
        Action = 'Add';
        return;
      }

      const NeedRefres = await $createXorders_XordTaskApi({
        stabNo: StabNo,
        condition: Condition,
        interval: Interval,
        testcode: Testcode,
        action: Action,
        reason: 'NOAUDIT',
        mode: null,
        subStudyNo: nSubStudyNo,
      });
      if (NeedRefres) {
        message.success($t('commons.success'));
      }
      // if (NeedRefres || Testcode === -101) {}
    },
  },
);

const {
  Grid: MaterialsGrid,
  gridApi: materialsGridApi,
  CurrentRow: MaterialsCurrentRow,
  saveRowEvent: saveMaterialsRow,
} = useLimsGridsConfig<XFolderApi.XFolderRelations>(
  useMaterialsColumns(),
  [],
  async () => {
    if (!CurrentRow.value) {
      return [];
    }
    return await $getStabilityMaterialsApi({
      stabNo: CurrentRow.value.STABNO,
    });
  },
  {
    pagerConfig: {
      enabled: false,
    },
  },
);

async function onEditMatrixList() {
  if (!CurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const status = CurrentRow.value.FDISPSTAT;
  if (status !== 'Draft') {
    message.warning(
      $t('business-static-tables.studyConfiguration.cannotEditNotDraft'),
    );
    return;
  }
  editMaterialsFormModalApi
    .setData({
      stabNo: CurrentRow.value.STABNO,
    })
    .open();
}
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddStudyWizard,
  destroyOnClose: true,
});

const [EditMaterialsFormModal, editMaterialsFormModalApi] = useVbenModal({
  connectedComponent: EditMaterials,
  destroyOnClose: true,
});

const [CopyModal, copyModalApi] = useVbenModal({
  destroyOnClose: true,
  connectedComponent: CopyTemplateOrProtocolForm,
});
const [IntervalTatModal, intervalTatModalApi] = useVbenModal({
  destroyOnClose: true,
  connectedComponent: StabnoIntervalsTat,
});
const [EditMaterialModal, editMaterialModalApi] = useVbenModal({
  connectedComponent: EditMaterialForm,
  destroyOnClose: true,
});

function onCreate() {
  const openingMode = mode.value;
  const spCode = -1;
  const stabNo = -1;
  const sSite = 'Site1';
  const bShowName = true;
  const bDisableConditions = false;
  formModalApi
    .setData({
      openingMode,
      spCode,
      stabNo,
      sSite,
      bShowName,
      bDisableConditions,
    })
    .open();
}
async function onDelete() {
  if (!CurrentCheckRow.value) {
    message.warning($t('commons.pleaseCheckOneRow'));
    return;
  }
  const status = CurrentCheckRow.value.FDISPSTAT;
  if (status === 'Released' || status === 'Retired') {
    message.warning(
      $t('business-static-tables.studyConfiguration.cannotDeleteNotDraft'),
    );
    return;
  }
  await confirm($t('commons.deleteConfirm'), $t('commons.deleteConfirmTitle'));
  const res = await $deleteProtocolApi({
    stabNo: CurrentCheckRow.value.STABNO,
  });
  if (!res) {
    message.error($t('business-static-tables.studyConfiguration.stabUsed'));
  }
  gridApi.query();
}
async function onRelease() {
  if (!CurrentCheckRow.value) {
    message.warning($t('commons.pleaseCheckOneRow'));
    return;
  }
  const status = CurrentCheckRow.value.FDISPSTAT;
  if (status === 'Released' || status === 'Retired') {
    message.warning(
      $t('business-static-tables.studyConfiguration.cannotReleaseNotDraft'),
    );
    return;
  }
  await confirm(
    $t('commons.releaseConfirm'),
    $t('commons.releaseConfirmTitle'),
  );
  const workFlowCode = CurrentCheckRow.value.WORKFLOWCODE;
  const currStepCode = CurrentCheckRow.value.DISPSTEPCODE;
  const stabNo = CurrentCheckRow.value.STABNO;
  const spCode = CurrentCheckRow.value.SP_CODE;
  const itemORIGREC = CurrentCheckRow.value.ORIGREC;
  const stepParams = await $findNextStepOfWorkFlowApi({
    workFlowCode,
    currStepCode,
  });

  const nextStepCode = stepParams[0];
  // const signatureType = stepParams[1];
  // const goToSteps = stepParams[2];
  // const openingMode = 'TEMPLATECONSOLE';
  const sComment = null;
  const dStartDate = null;
  const dEndDate = null;
  // const eventCode =
  //   nextStepCode !== 'LASTSTEP' || openingMode === 'TEMPLATECONSOLE'
  //     ? 'ReleaseStabilityTemplete'
  //     : 'ReleaseStabilityProtocol';
  await $executeGeneralWorkFlowStepApi({
    currStepCode,
    nextStepCode,
    stabNo,
    sComment,
    dStartDate,
    spCode,
    dEndDate,
    workFlowCode,
    itemORIGREC,
  });

  await $closeStudyConfigurationApi({
    mode: mode.value,
    spCode,
  });

  gridApi.query();
}
async function onCopy() {
  if (!CurrentCheckRow.value) {
    message.warning($t('commons.pleaseCheckOneRow'));
    return;
  }
  copyModalApi
    .setData({
      mode: mode.value,
      dept: CurrentCheckRow.value.DEPT,
      fromStabNo: CurrentCheckRow.value.STABNO,
    })
    .open();
}

watch(CurrentRow, async (newVal) => {
  loadMatrixGridData(newVal);
  materialsGridApi?.query();
  if (newVal) {
    oArg.value.aDsParam = [newVal.STABNO];
    signaturesRef.value?.LoadSignatures(oArg.value);
  }
});

async function loadMatrixGridData(row: null | XFolderApi.XFolder) {
  if (row) {
    const stabilityArrayData = await $getStabilityArrayDataApi({
      stabNo: row.STABNO,
    });
    const testConditionsArray = stabilityArrayData[0];
    const testConditionColumn = stabilityArrayData[0].map(
      (item: string[]) => item[0],
    );
    const intervalColumns = stabilityArrayData[1];
    const captions = intervalColumns.map((item: string[]) => item[0]);
    const dataFills = stabilityArrayData[2];
    if (dataFills !== null) {
      for (const dataFill of dataFills) {
        const TestnoCondition = dataFill[0];
        const Interval = dataFill[1];
        // const DueDate = dataFill[2];
        const TS = dataFill[3];
        const nSubStudyNo = dataFill[11];
        const nOrigRec = dataFill[4];
        const strOrigSts = dataFill[5];
        const y_Pos = testConditionColumn.indexOf(TestnoCondition);
        const x_Pos = captions.indexOf(Interval);
        let TheSymbol = '';
        if (y_Pos !== -1 && x_Pos !== -1) {
          if (TS === 'BACKUP') {
            TheSymbol = BackupSymbol;
          } else if (TS === 'SUBSTUDY') {
            TheSymbol = nSubStudyNo + SubStudySymbol;
          } else {
            TheSymbol = AddedSymbol;
          }
          testConditionsArray[y_Pos][x_Pos + 4] = TheSymbol;
        }
        if (y_Pos >= 0) {
          testConditionsArray[y_Pos][30] = nOrigRec;
          testConditionsArray[y_Pos][31] = strOrigSts;
        }
      }
    }
    const columns = condIntTestMatrixGridApi.grid.columns ?? [];
    for (let j = 0; j < 27; j++) {
      const colName = `Col${j + 5}`;
      const col = columns.find((item) => item.field === colName);
      if (col) {
        if (captions.length > j) {
          col.visible = true;
          col.title = captions[j];
        } else {
          col.visible = false;
        }
      }
    }

    // 将字符串数组转换成对象数组
    const dataArray = [];
    for (const element of testConditionsArray) {
      const obj: Record<string, any> = {};
      for (const column of columns) {
        if (column.field) {
          const index = Number.parseInt(column.field.slice(3)) - 1; // 假设字段名是 "Col1", "Col2" 等
          obj[column.field] = element[index];
        }
      }
      dataArray.push(obj);
    }

    condIntTestMatrixGridApi.setGridOptions({
      columns,
      data: dataArray,
    });
  } else {
    condIntTestMatrixGridApi.setGridOptions({
      columns: useCondIntTestMatrixColumns(),
      data: [],
    });
  }
}

async function onIntervalTat() {
  if (!CurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const status = CurrentRow.value.FDISPSTAT;
  const stabNo = CurrentRow.value.STABNO;
  let openingMode = 'Edit';
  if (status === 'Released') {
    openingMode = 'View';
  }
  intervalTatModalApi
    .setData({
      openingMode,
      stabNo,
    })
    .open();
}
const [ProtocolFormModal, protocolFormModalApi] = useVbenModal({
  connectedComponent: AddProtocolWizard,
  destroyOnClose: true,
});
async function onCreateProtocol() {
  protocolFormModalApi.open();
}
async function showFillMatrixPrompt() {
  // const sCondition	= CondIntTestMatrix_gd.GetCurrentRowData("Col4");
  // const sTestcode	= CondIntTestMatrix_gd.GetCurrentRowData("Col2");
  const nStabno = CurrentRow.value?.STABNO;
  if (!nStabno) {
    return;
  }
  const status = CurrentRow.value?.FDISPSTAT;
  if (status !== 'Draft') {
    message.warning(
      $t('business-static-tables.studyConfiguration.cannotEditNotDraft'),
    );
    return;
  }
  let aParams: any[] | null = null;
  const sCondition = MatrixCurrentRow.value?.Col4;

  let sAction = matrixType.value === BackupSymbol ? 'Backup' : 'Add';

  const options = [
    { label: $t('commons.all'), value: 'All' },
    { label: $t('commons.row'), value: 'Row' },
    { label: $t('commons.column'), value: 'Column' },
    {
      label: $t('business-static-tables.studyConfiguration.clearMatrix'),
      value: 'Delete',
    },
  ];
  prompt({
    async beforeClose(scope) {
      if (scope.isConfirm && !scope.value) {
        alert('请选择一个选项');
        return false;
      }
      return true;
    },
    component: RadioGroup,
    componentProps: {
      options,
      class: 'flex flex-col',
    },
    content: '请选择填充方式',
    icon: 'question',
    modelPropName: 'value',
  })
    .then((val) => {
      const sMode = val; // 'Row' | 'Column' | 'All' | 'Delete';
      switch (sMode) {
        case 'All': {
          aParams = [nStabno, false, sAction, sMode];
          break;
        }

        case 'Column': {
          const currentCol = condIntTestMatrixGridApi.grid.getCurrentColumn();
          if (currentCol === null) {
            message.warning($t('commons.selectOneCol'));
            return;
          }
          const sColumnName = currentCol.field;
          if (sColumnName !== 'Col3' && sColumnName !== 'Col4') {
            const sInterval = currentCol.title;
            aParams = [nStabno, false, sAction, sMode, sInterval];
          }
          break;
        }

        case 'Row': {
          if (!MatrixCurrentRow.value) {
            message.warning($t('commons.selectOne'));
            return;
          }
          const nPosition = MatrixCurrentRow.value.Col4;
          const nTestcode = MatrixCurrentRow.value.Col2;
          if (nTestcode === -101) {
            message.error($t('commons.noFillSubStudy'));
            return;
          }
          aParams = [
            nStabno,
            false,
            sAction,
            sMode,
            sCondition,
            nPosition,
            nTestcode,
          ];
          break;
        }

        case 'Delete': {
          sAction = 'Delete';
          aParams = [nStabno, false, sAction, 'All'];
        }
      }

      if (aParams === null) {
        return;
      }
      return $scFillMatrixApi(aParams);
    })
    .then((res) => {
      if (res) {
        loadMatrixGridData(CurrentRow.value);
      }
    });
}

async function onEditMaterialDetail(row: XFolderApi.XFolderRelations) {
  editMaterialModalApi.setData(row).open();
}

async function updateMaterialDetail(data: XFolderApi.XFolderRelations) {
  if (!MaterialsCurrentRow.value) {
    return;
  }

  MaterialsCurrentRow.value.CONTAINER_MATCODE = data.CONTAINER_MATCODE;
  MaterialsCurrentRow.value.PULL_CONTAINER_MATCODE =
    data.PULL_CONTAINER_MATCODE;
  // console.log(materialsGridApi.grid.getUpdateRecords());
  saveMaterialsRow(MaterialsCurrentRow.value);
}

async function editStabMaterials(materials: string[]) {
  if (!CurrentRow.value) {
    return;
  }
  await $editStabMaterialsApi({ stabNo: CurrentRow.value.STABNO, materials });
  materialsGridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal class="!w-[800px]" @success="gridApi.query()" />
    <CopyModal @success="gridApi.query()" />
    <IntervalTatModal />
    <ProtocolFormModal />
    <EditMaterialModal @success="updateMaterialDetail($event)" />
    <EditMaterialsFormModal
      class="w-[800px]"
      @update-materials="editStabMaterials($event)"
    />
    <div class="flex h-full flex-col">
      <div class="h-1/2 w-full">
        <StudyTemplatesGrid>
          <template #toolbar-actions>
            <Space :size="[4, 0]" wrap>
              <Button
                type="primary"
                @click="onCreate"
                v-if="mode === 'TEMPLATECONSOLE'"
              >
                {{
                  $t('business-static-tables.studyConfiguration.createTemplate')
                }}
              </Button>
              <Button
                type="primary"
                @click="onCreateProtocol"
                v-if="mode === 'PROTOCOLCONSOLE'"
              >
                {{
                  $t('business-static-tables.studyConfiguration.createProtocol')
                }}
              </Button>
              <Button type="primary" danger @click="onDelete">
                {{ $t('ui.actionTitle.delete') }}
              </Button>
              <Button type="primary" @click="onRelease">
                {{ $t('commons.release') }}
              </Button>
              <Button type="default" @click="onCopy">
                {{ $t('commons.copy') }}
              </Button>
            </Space>
          </template>
          <template #action="{ row }">
            <template v-if="hasEditStatus(row)">
              <Button type="link" @click="saveRowEvent(row)">
                {{ $t('commons.save') }}
              </Button>
              <Button type="link" @click="cancelRowEvent(row)">
                {{ $t('commons.cancel') }}
              </Button>
            </template>
            <template v-else>
              <Button type="link" @click="editRowEvent(row)">
                {{ $t('commons.edit') }}
              </Button>
            </template>
          </template>
        </StudyTemplatesGrid>
      </div>
      <div class="h-1/2 flex-1 bg-white px-5">
        <Tabs v-model:active-key="activeKey" class="h-full">
          <TabPane
            key="1"
            :tab="$t('business-static-tables.studyConfiguration.studyConfig')"
          >
            <CondIntTestMatrixGrid class="h-full">
              <template #toolbar-actions>
                <Space :size="[4, 0]" wrap>
                  <Button type="primary" @click="onEditMatrixList">
                    {{ $t('ui.actionTitle.edit') }}
                  </Button>
                  <Button type="default" @click="onIntervalTat">
                    {{
                      $t('business-static-tables.studyConfiguration.interval')
                    }}
                  </Button>
                  <Button type="default" @click="showFillMatrixPrompt">
                    {{
                      $t('business-static-tables.studyConfiguration.fillMatrix')
                    }}
                  </Button>
                  <RadioGroup v-model:value="matrixType" size="small">
                    <Radio :value="AddedSymbol">
                      {{ $t('commons.test') }}
                    </Radio>
                    <Radio :value="BackupSymbol">
                      {{ $t('commons.backup') }}
                    </Radio>
                  </RadioGroup>
                </Space>
              </template>
            </CondIntTestMatrixGrid>
          </TabPane>
          <TabPane
            key="2"
            :tab="$t('business-static-tables.studyConfiguration.materials')"
            v-if="mode === 'PROTOCOLCONSOLE'"
          >
            <MaterialsGrid class="h-full">
              <template #toolbar-actions>
                <Space :size="[4, 0]" wrap>
                  <Button type="primary" @click="onEditMatrixList">
                    {{ $t('commons.editList') }}
                  </Button>
                </Space>
              </template>
              <template #action="{ row }">
                <Button type="link" @click="onEditMaterialDetail(row)">
                  {{ $t('commons.edit') }}
                </Button>
              </template>
            </MaterialsGrid>
          </TabPane>
          <TabPane
            key="3"
            :tab="$t('business-static-tables.studyConfiguration.sampleQty')"
            v-if="mode === 'PROTOCOLCONSOLE'"
          >
            <SampleQty :stab-row="CurrentRow" />
          </TabPane>
          <TabPane
            key="4"
            :tab="$t('business-static-tables.studyConfiguration.signatures')"
            v-if="mode === 'PROTOCOLCONSOLE'"
          >
            <Signatures v-model="oArg" ref="signaturesRef" />
          </TabPane>
        </Tabs>
      </div>
    </div>
  </Page>
</template>

<style>
.ant-tabs-content {
  height: 100%;
}
</style>
