import { callServer, getDataSet } from '#/api/core/witlab';

export namespace NotifySamplersApi {
  export interface NotifySamplers {
    ORIGREC: number;
    TAT: string;
    SAMPLE_TYPE: string;
    FLDISPSTATUS: string;
    DISPSTS: string;
    BATCHNO: string;
    PRODUCTION_DATE: Date;
    ORDNO: string;
    SAMPDATE: Date;
    SAMPLIMIT: string;
    ACTUALVOL: string;
    TOTALNUMOFPACKAGES: string;
    NUMOFPACKAGES: string;
    SAMPLESOURCE: string;
    FOLDERNO: string;
    SP_CODE: number;
    BATCHID: number;
    SAMPLEGROUPCODE: string;
    IFLAG: string;
    CLSAMPNO: string;
    RETEST_BATCHID: string;
    MATNAME: string;
    SAMPLE_NAME: string;
    ELN_ID: string;
    ELN_ORIGREC: number;
    REQUESTID: string;
  }
}

async function getSamplesSelectNew() {
  return getDataSet('NotifySamplers.getSamplesSelectNew', []);
}

async function logSamples(sOrderNoList: string, sMode: string) {
  return await callServer('NotifySamplers.LogSamples', [sOrderNoList, sMode]);
}

async function sendSampTask(
  sOrderNo: string,
  sRequestId: string,
  sComment: string,
  sBatchId: number,
) {
  return await callServer('NotifySamplers.sendSampTask', [
    sOrderNo,
    sRequestId,
    sComment,
    sBatchId,
  ]);
}

export { getSamplesSelectNew, logSamples, sendSampTask };
