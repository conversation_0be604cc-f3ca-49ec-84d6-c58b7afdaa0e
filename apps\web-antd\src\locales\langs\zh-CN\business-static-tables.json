{"title": "业务静态数据", "operation": "操作", "add": "添加", "delete": "删除", "edit": "编辑", "save": "保存", "cancel": "取消", "detail": "详情", "removed": "移除", "copy": "复制", "release": "发布", "retire": "废弃", "process-specifications": {"title": "质量标准管理", "grouping": "分组", "specification": "质量标准名称", "specification_eng": "质量标准名称（英文）", "version": "版本", "inspection_standard": "线下文件编号", "limitfile": "警戒纠偏文件/版本", "status": "状态", "startddate": "生效日期", "expdate": "失效日期", "retireddat": "废弃日期", "matcode": "物料代码", "specno": "质量标准编号", "speccategory": "分类", "testno": "测试名称", "analyte": "分析项", "printflag": "是否显示在报告中", "synonim": "报告显示名称", "specsorter": "报告中显示顺序", "schemaname": "标准设置", "picture": "修约规则", "lowa": "超标低限", "lowb": "纠偏低限", "lowc": "警戒低限", "highc": "警戒高限", "highb": "纠偏高限", "higha": "超标高限", "units": "报告单位", "charlimits": "文本限值（标准）", "charlimits_i": "文本限值（内控）", "pass_text": "标准规定", "pass_text_eng": "标准规定（英文）", "analtype": "类型", "testcode": "测试代码", "edit-testno": "编辑测试", "edit-analyte": "编辑分析项", "footnotes": "描述", "specification-exists": "质量标准名称已存在", "addversion": "新建版本", "spreadDate": "扩期"}, "word-static-fields": {"title": "WORD替换字段", "group_by": "分组", "marking_name": "字段标识", "replace_name": "字段内容", "sorter": "序号"}, "word-template": {"title": "报告书模板管理", "status": "状态", "type": "模板类型", "temp_code": "模板编码", "sorter": "序号", "name": "模板名称", "language": "语言版本", "mattypes": "指定物料类型", "clients": "指定客户", "function_name": "执行脚本", "stardoc_id": "模板", "filepath": "模板路径", "pic_size": "图片尺寸", "remark": "描述", "deptname": "实验室", "createdby": "创建人", "createdate": "创建时间"}, "methodManager": {"menu": "方法管理", "method": "方法编号", "methodCategory": "方法分类", "description": "中文名称", "sopEng": "英文名称", "methodType": "方法类别", "methodNo": "文件编号", "Test Method": "测试方法", "Equipment Method": "设备方法"}, "methodVersionManager": {"uiTitle": "方法版本管理", "fileVersion": "线下版本号", "startDdate": "生效日期", "expDate": "失效日期", "refNo": "参考号", "link": "链接地址", "starDocId": "文件ID", "draftVersionExists": "草稿版本已存在", "methodVersion": "方法版本"}, "specSchema": {"menu": "标准设置", "specSchemaGroup": "标准设置", "schemaName": "设置名称", "specSchemaCode": "设置代码", "action": "动作", "typeOfItem": "项目", "calculation": "审核编号", "fieldName": "字段名称", "fieldCaption": "字段标题", "tableName": "表名称", "dispProperty": "显示属性", "fieldWidth": "字段宽度", "uiSpecSchemaCalc": "动作", "uiSpecSchemaCalcExists": "项目已存在"}, "testManager": {"menu": "测试管理", "testNo": "测试名称", "testCatCode": "测试分类", "testCode": "测试代码", "testDesc": "描述", "testNoEng": "英文名称", "testNoAlias": "别名", "testFlag": "测试类型", "createRunType": "分析批类型", "testType": "结果录入窗口", "scoreType": "分值", "analyte": "分析项", "analyteEng": "分析项英文名称", "schemaName": "标准设置", "calcName": "计算公式", "picture": "修约规则", "low": "低限", "high": "高限", "units": "单位", "noRep": "重复次数", "analPrint": "登录标记", "analType": "结果类型", "defaultMethod": "默认", "method": "方法", "methodStatus": "方法状态", "team": "检测组", "eqType": "设备类型", "allEquipment": "所有设备", "instVerify": "设备授权", "assignType": "方法授权", "startDate": "生效日期", "toDate": "失效日期", "elnId": "ELN模板ID", "maxAllocationSampleQuantity": "分配样品数量", "allocationSampleUnit": "计量单位", "progName": "计划名称", "moveCategory": "移动分类", "usedInTestWarn": "以下选择的测试\n {0}被用于测试计划中，不能被删除！", "usedInSamplesWarn": "以下选择的测试\n {0}被用于样品中，不能被删除！", "usedInStaticData": "以下选择的测试\n {0}被用于静态数据中，不能被删除！", "isAllEq": "所有设备", "checkEqStatus": "设备状态核对", "needCert": "需要认证", "addTestCodeFail": "添加测试失败", "addAnalyteFail": "添加分析项失败", "synonym": "同义词", "result": "结果", "snomedCode": "结果代码", "resultStatus": "状态", "conclusionReport": "报告结论", "possibleResult": "可能的结果", "test": "测试", "selectTestFirst": "请先选择测试", "delAnalyte": "分析项 {0} 用于测试计划，因此无法删除。", "analUsedInSamplesWarn": "以下选择的分析项 {0} 被用于样品中，不能被删除！", "analyteAndMaterials": "分析项{0}已关联物料，不能删除！", "addPassFailure": "通过/失败", "addNegPos": "阴性/阳性", "confirmDelMethod": "确认删除方法 {0} 吗？", "forMethod": "对于方法：", "showMethod": "查看方法", "editEqList": "编辑设备列表", "outSourceLabs": "分包实验室", "loggedByDept": "登记实验室", "dept": "外包实验室", "preference": "优先级", "certifiedTill": "认证到期日", "otherEquipment": "其他设备", "testingEquipment": "检测设备", "timeUsedInMinutes": "使用时间（分钟）", "editOtherEquipment": "编辑其他设备", "selectEquipmentTypes": "选择设备类型", "selectEln": "选择ELN模板", "viewSpec": "查看质量标准", "relatedFile": "关联文件", "docName": "文件名称", "docUrl": "文件链接"}, "menu": "业务静态数据", "sampleGroups": {"title": "样品模板组", "prodgroup": "样品组", "subgroupname": "子组名", "samplegroupname": "模板名称", "samplegroupcode": "模板ID", "matcode": "物料代码", "matname": "物料名称", "sampgrtype": "类型", "dept": "实验室", "plant": "车间", "metadataTemplate": "元数据模板", "active": "激活", "spcode": "代码", "sequence": "序号", "noSamplesToLogin": "# 样品", "progname": "测试计划名称", "version": "版本", "dispstepcode": "下一步", "dispstatus": "状态", "datefrom": "生效日期", "dateto": "失效日期", "profile": "方案", "specification": "质量标准", "autoadd": "批次登录", "skipSampling": "跳过取样", "skipCr": "跳过样品接收", "skipReceive": "跳过实验室接收", "autoDisposition": "自动发布样品", "batchAutoRelease": "自动发布批次", "inspectionCycle": "检验周期（天）", "skipBatchInspection": "跳批检验", "batchNumber": "批次数", "roleSampling": "取样授权", "NoDelAdHocSGT": "常规样品组不能被删除！", "sampleGroupInSamples": "样品组有样品用在批里，不能删除！", "sampleGroupHasTestPlansExistingInOtherSampGrp": "样品组有样品在其它组使用，不能删除！", "sampleGroupHasReleasedTestPlans": "样品组已经发布了样品，不能删除！", "sampleGroupInBatch": "样品模板已经发布了测试计划，不能删除！", "newVersion": "新版本", "usedInOtherProdGrp": "相关样品模板", "spreadStage": "扩期", "dataValidation": "数据验证", "status": "状态", "recipecode": "配方代码", "recipename": "配方名称", "startddate": "生效日期", "expdate": "失效日期", "retireddat": "废弃日期", "samplingposition": "取样位置", "samplesize": "容器类型", "numberofcontainers": "容器数量", "sampleType": "样品类型", "containerqty": "取样数量", "containerUnits": "取样单位", "forlab": "样品用于", "purpose": "用途", "condition": "条件", "createInventoryId": "创建库存", "aliquot": "分样", "sExist": "该采样要求已经存在，请重新输入！", "setUsedByProfile": "请确保您将'配置文件中也使用的'分配给新的取样要求。", "LAST_SAMPLING_REQ": "操作不允许，必须定义至少一个取样要求！", "num": "复制数量", "testno": "测试名称", "addNewSpec": "添加新项", "deleteSpec": "删除", "selectExist": "选择现有的", "viewSpecs": "信息查看", "primarySpec": "现行版", "inspectionStandard": "线下文件编号", "spTestsSorter": "序号", "spTestNo": "专用测试名称", "retestFlag": "复验标记", "sceneFlag": "现场检测", "spSynonym": "专用分析项名称", "testNo": "测试名称", "analyte": "分析项", "noRep": "重复", "sorter": "序号", "defaultMethod": "默认", "method": "方法", "servGrp": "岗位", "elnReference": "ELN引用", "elnId": "ELN模板ID", "testUsrnam": "检验员", "eqType": "设备类型", "matCode": "材料编码", "matName": "品名", "testPlanExistsInOrders": "不能删除测试计划，有样品登录！", "existingDraftVersions": "测试计划已经有新建版本！", "testPlanExists": "样品已经存在！", "NoSpecs": "您必须至少定义一组质量标准!", "sSpecificationAssignedToSample": "无法删除质量标准。它被分配给样品!", "sSpecificationAssignedToTemplate": "无法删除质量标准。它被分配给模板!", "emptyTests": "你必须选择一种测试与这个方法相关联！", "profileExists": "测试名已存在请选择不同的测试名！", "SAMPLES": "不能删除测试,该测试已经登样.", "OTHER_PROFILE": "不能删除测试,测试被引用.", "editTestList": "编辑测试列表", "Synchronization": "同步方法失效日期", "AddTestPlanFromExisting": "新增（从现有样品选择）", "possibleResults": "可能结果"}, "studyConfiguration": {"menu": "稳定性研究模板", "studyTemplate": "稳定性研究模板", "studyConfig": "研究设计", "templateNo": "模板编号", "protocolNo": "草案编号", "dept": "实验室", "folderName": "名称", "version": "版本", "notes1": "备注", "dispStepCode": "下一步", "startDate": "生效日期", "expDate": "失效日期", "conditionType": "类型", "conditionIntervals": "条件与间隔", "selectOneConditionsAndIntervals": "请选择至少一个条件与间隔", "existTemplateName": "名称重复", "cannotDeleteNotDraft": "不能删除非草稿数据", "stabUsed": "不能删除，数据已经在使用中", "cannotReleaseNotDraft": "不能发布非草稿数据", "cannotEditNotDraft": "不能编辑非草稿数据", "copyTemplateOrProtocol": "复制", "Stability": "稳定性", "intervalTat": "各考察时间点的截至日期", "interval": "考察时间点", "tatBeforeAfter": "考察时间点前后的截至日期", "selectintervalTat": "请选择考察时间点的截至日期", "protocolName": "草案名称", "materials": "物料列表", "isAddNew": "新增", "isNotAdd": "使用模板", "createWay": "创建方式", "editMaterials": "编辑物料", "chooseTest": "选择测试", "editTest": "编辑测试", "protocolExist": "草案已存在", "cannotRemAllTest": "不能删除所有测试", "SAMPLES": "不能删除测试,该测试已经登样.", "OTHER_PROFILE": "不能删除测试,测试被引用.", "createTemplate": "创建模板", "createProtocol": "创建草案", "fillMatrix": "填充矩阵", "clearMatrix": "清空矩阵", "matCode": "物料编码", "matName": "物料名称", "containerMatCode": "包装规格", "pullContainerMatCode": "取样容器", "signatures": "签名", "sampleQty": "样品数量", "spTestNo": "测试", "unitsQty": "单位数量", "units": "单位", "storedQty": "存储数量", "qty": "取样数量"}}