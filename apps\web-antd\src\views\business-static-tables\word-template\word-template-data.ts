import type { VxeColumnPropTypes } from 'vxe-table';

import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { WordTemplateApi } from '#/api/business-static-tables/word-template';

import dayjs from 'dayjs';

import { $t } from '#/locales';

export function useWordTemplateFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        // placeholder: 'Please enter code',
      },
      fieldName: 'NAME',
      label: $t('business-static-tables.word-template.name'),
    },
  ];
}

// 客户分类列表
export function useWordTemplateColumns(
  languageOptions: VxeColumnPropTypes.EditRender,
  functionNameEditRender: VxeColumnPropTypes.EditRender,
): VxeTableGridOptions<WordTemplateApi.WordTemplate>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'TEMP_CODE',
      title: $t('business-static-tables.word-template.temp_code'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SORTER',
      title: $t('business-static-tables.word-template.sorter'),
      editRender: { name: 'input' },
      minWidth: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'NAME',
      title: $t('business-static-tables.word-template.name'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'LANGUAGE',
      title: $t('business-static-tables.word-template.language'),
      // editRender: { name: 'input' },
      editRender: languageOptions,
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'MATTYPES',
      title: $t('business-static-tables.word-template.mattypes'),
      editRender: { name: 'input' },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'CLIENTS',
      title: $t('business-static-tables.word-template.clients'),
      editRender: { name: 'input' },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'FUNCTION_NAME',
      title: $t('business-static-tables.word-template.function_name'),
      editRender: functionNameEditRender,
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'STARDOC_ID',
      title: $t('business-static-tables.word-template.stardoc_id'),
      editRender: { name: 'input' },
      minWidth: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'REMARK',
      title: $t('business-static-tables.word-template.remark'),
      editRender: { name: 'input' },
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'DEPTNAME',
      title: $t('business-static-tables.word-template.deptname'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'CREATEDBY',
      title: $t('business-static-tables.word-template.createdby'),
      minWidth: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'CREATEDATE',
      title: $t('business-static-tables.word-template.createdate'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('business-static-tables.operation'),
      minWidth: 140,
    },
  ];
}
