<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { EquipmentMgApi } from '#/api/equipment/equipment-mg';

import { computed, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getTranslationTableApi } from '#/api/equipment/equipment-mg';
import { useEquipmentStore } from '#/store';

import { analysisColumns } from '../equipment-mg-data';
import AnalysisModal from './../components/analysis-modal.vue';

const equipmentStore = useEquipmentStore();
const currentRow: EquipmentMgApi.RowType = computed(
  () => equipmentStore.getCurrentRow,
);
interface RowType {
  [key: string]: any;
}
watch(
  currentRow,
  async (newRow: RowType) => {
    if (newRow) {
      gridApi.query();
    }
  },
  { deep: true },
);
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AnalysisModal,
  destroyOnClose: true,
});
const gridOptions: VxeTableGridOptions<EquipmentMgApi.MetaDataEquipment> = {
  columns: analysisColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},

  proxyConfig: {
    ajax: {
      query: async () => {
        const data = await getTranslationTableApi([currentRow.value.EQID]);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: {},
});
const editAnalysisItemList = async () => {
  formModalApi
    .setData({ tableData: gridApi.grid.getTableData().tableData })
    .open();
};
const onRefresh = () => {
  gridApi.query();
};
</script>
<template>
  <div class="h-[100vh]">
    <FormModal @success="onRefresh" />

    <Grid>
      <template #toolbar-actions>
        <Space :size="[4, 0]" wrap>
          <Button type="primary" @click="editAnalysisItemList">
            {{ $t('equipment.equipment-mg.editAnalysisItemList') }}
          </Button>
        </Space>
      </template>
    </Grid>
  </div>
</template>
