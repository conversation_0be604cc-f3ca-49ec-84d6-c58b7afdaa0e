import { callServer, getDataSet } from '#/api/core/witlab';

export namespace ProcessSpecificationsApi {
  export interface Specifications {
    ORIGREC: number;
    GROUPING: string;
    SPECIFICATION: string;
    SPECIFICATION_ENG: string;
    VERSION: string;
    INSPECTION_STANDARD: string;
    LIMITFILE: string;
    STATUS: string;
    STARTDDATE: Date;
    EXPDATE: Date;
    RETIREDDAT: Date;
    MATCODE: string;
    SPECNO: number;
    SPECCATEGORY: string;
    FOOTNOTES: string;
  }
  export interface SpecAnalytes {
    ORIGREC: number;
    TESTNO: string;
    ANALYTE: string;
    PRINTFLAG: string;
    SYNONIM: string;
    GROUPING: string;
    SPTESTSORTER: number;
    SPECSORTER: number;
    SCHEMANAME: string;
    PICTURE: string;
    LOWA: string;
    LOWB: string;
    LOWC: string;
    HIGHC: string;
    HIGHB: string;
    HIGHA: string;
    UNITS: string;
    CHARLIMITS: string;
    CHARLIMITS_I: string;
    ACCEPTANCE_CRITERIA: string;
    PASS_TEXT: string;
    PASS_TEXT_ENG: string;
    SPN01: string;
    SPN02: string;
    SPN03: string;
    SPN04: string;
    SPN05: string;
    ANALTYPE: string;
    TESTCODE: number;
  }
}

/**
 * 获取质量标准列表数据
 */
async function getSpecificationsList() {
  return getDataSet(
    'ProcessSpecifications.findSpecificationsBySearchParameters',
    [],
  );
}

/**
 * 获取检项列表数据
 */
async function getSpecAnalyteList(sSpecNo: number | string) {
  return getDataSet('ProcessSpecifications.getLimitsBySpecification', [
    sSpecNo,
  ]);
}

/**
 * 获取测试类型
 */
async function getTestCategory() {
  return getDataSet('BatchManager.TestCategories', []);
}

/**
 * 获取检测项目
 */
async function getTestNo(sTestCategory: null | string, aTestCode: string[]) {
  return getDataSet('BatchManager.Tests', [sTestCategory, aTestCode]);
}

/**
 * 获取分析项
 */
async function getAnalytes(sTestCode: null | number, aAnalyte: string[]) {
  return getDataSet('ProcessSpecifications.dsAnalyte', [sTestCode, aAnalyte]);
}

/**
 * 获取分类
 */
async function getSpecCategory() {
  return getDataSet('ProcessSpecifications.getCategories', []);
}

/**
 * 获取分组
 */
async function getGrouping() {
  return getDataSet('ProcessSpecifications.getExistingGroupings', []);
}

/**
 * 获取质量标准名称是否存在
 */
async function IsSpecificationNameExists(sSpecification: string) {
  return await callServer('ProcessSpecifications.Specifications.IsDuplicate', [
    sSpecification,
    -100,
  ]);
}

/**
 * 添加质量标准
 */
async function addSpecification(data: ProcessSpecificationsApi.Specifications) {
  return await callServer(
    'ProcessSpecifications.Specifications.AddSpecFromWizard',
    [
      data.SPECIFICATION,
      'SP_SPECS',
      null,
      '',
      data.MATCODE,
      data.SPECCATEGORY,
      'Y',
      data.GROUPING,
      data.FOOTNOTES,
      false,
      null,
      null,
      data.INSPECTION_STANDARD,
      data.LIMITFILE,
      data.SPECIFICATION_ENG,
    ],
  );
}

/**
 * 复制质量标准
 */
async function copySpecification(
  data: ProcessSpecificationsApi.Specifications,
) {
  return getDataSet('ProcessSpecifications.getTempSpecCopy', [
    data.SPECIFICATION,
    data.MATCODE,
    data.SPECNO,
    data.SPECCATEGORY,
    data.GROUPING,
    data.FOOTNOTES,
    data.INSPECTION_STANDARD,
    data.LIMITFILE,
    data.SPECIFICATION_ENG,
  ]);
}

/**
 * 新建版本
 */
async function addVersion() {
  return getDataSet('ProcessSpecifications.EditWizard.Finalize', []);
}

/**
 * 发布/废弃质量标准
 */
async function updateSpecificationState(sOrigrec: number, sStatus: string) {
  return await callServer('ProcessSpecifications.SpecOperate', [
    sOrigrec,
    sStatus,
  ]);
}
/**
 * 删除质量标准
 */
async function deleteSpecification(aSpecNo: number[]) {
  return await callServer(
    'ProcessSpecifications.Specifications.DeleteBySpecNo',
    [aSpecNo],
  );
}

/**
 * 编辑检项
 */
async function editTestList(
  aSpecNo: number,
  aTestCode: string[],
  sTrackChanges: boolean,
) {
  return await callServer('ProcessSpecifications.Limits.SetTestList', [
    aSpecNo,
    aTestCode,
    sTrackChanges,
  ]);
}

/**
 * 编辑分析项
 */
async function editAnalyteList(
  aSpecNo: number,
  sTestCode: number,
  aAnalyte: string[],
) {
  return await callServer('ProcessSpecifications.Limits.SetAnalyteList', [
    aSpecNo,
    sTestCode,
    aAnalyte,
  ]);
}

export {
  addSpecification,
  addVersion,
  copySpecification,
  deleteSpecification,
  editAnalyteList,
  editTestList,
  getAnalytes,
  getGrouping,
  getSpecAnalyteList,
  getSpecCategory,
  getSpecificationsList,
  getTestCategory,
  getTestNo,
  IsSpecificationNameExists,
  updateSpecificationState,
};
