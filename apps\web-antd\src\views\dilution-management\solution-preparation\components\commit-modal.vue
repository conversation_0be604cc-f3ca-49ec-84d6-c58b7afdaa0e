<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import { commitModalSchema } from '../solution preparation-data';
import { message } from 'ant-design-vue';

import {
  getUsersApi,
  updateApproverApi,
  addTestSubmitRecordApi,
  getTitranSoluteApi,
} from '#/api/dilution-management/solution-preparation';

const emit = defineEmits(['success']);
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: commitModalSchema(),
  showDefaultActions: false,
});
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    console.warn(data);

    modalApi.lock();
    try {
      const clickRow = modalApi.getData().clickRow || [];
      const data = await formApi.getValues();
      const courseNameRef = formApi.getFieldComponentRef('courseName');
      const options = courseNameRef.getOptions();
      const approverName = options.find(
        (item: any) => item.value === data.approver,
      )?.label;
      const params = [approverName, clickRow.ORIGREC, data.approver];
      const res = await updateApproverApi(params);
      if (res !== true) {
        modalApi.close();
        message.error('提交失败');
        return;
      }
      //TODO:电子签名
      const oESig = false;
      if (oESig) {
        modalApi.close();
        message.warning('请先进行电子签名');
        return;
      }
      const addRes = await addTestSubmitRecordApi([]);
      if (addRes !== true) {
        modalApi.close();
        message.error('提交失败');
        return;
      }
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      const type = modalApi.getData().type || '';
      let res = [];
      if (type === '滴定液') {
        const clickRow = modalApi.getData().clickRow || [];
        res = await getTitranSoluteApi([clickRow.AFTERMANCODE]);
      } else {
        res = await getUsersApi();
      }
      const options = res.map((item: any) => {
        return {
          label: item.Text,
          value: item.Value,
        };
      });
      formApi.updateSchema([
        {
          fieldName: 'approver',
          componentProps: {
            options: options,
          },
        },
      ]);
    }
  },
});
</script>
<template>
  <Modal title="审核页面">
    <Form class="mx-4" />
  </Modal>
</template>
