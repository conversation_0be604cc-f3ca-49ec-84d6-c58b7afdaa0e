<script lang="ts" setup>
import type { Recordable } from '@vben/types';

import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';

import { ref, watch } from 'vue';

import { confirm, useVbenModal } from '@vben/common-ui';
import { isEmpty } from '@vben/utils';

import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@vben-core/shadcn-ui';

import { Button, message } from 'ant-design-vue';
import { EditIcon } from 'lucide-vue-next';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  callServer,
  deleteBuilding,
  deleteRoom,
  getBuildingListByDept,
  getRoomListByBuilding,
  updateProvider,
} from '#/api';
import { showAduitViewer } from '#/components/audit-viewer';
import { $t } from '#/locales';
import { saveEditingRowOriginalData } from '#/utils/lims-grids-config';

import BuildingForm from './building-form.vue';
import RoomForm from './room-form.vue';

const props = defineProps<{
  deptCode?: string;
}>();

const currentBuildingRow = ref<any>(null);

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: BuildingForm,
  destroyOnClose: true,
});

const [RoomFormModal, roomFormModalApi] = useVbenModal({
  connectedComponent: RoomForm,
  destroyOnClose: true,
});

const [BuildingGrid, buildingGridApi] = useVbenVxeGrid({
  gridEvents: {
    currentRowChange: (params: any) => {
      currentBuildingRow.value = params.row;
      roomGridApi.grid.clearData();
      roomGridApi.query();
    },
    editActivated: saveEditingRowOriginalData,
    editClosed: updateProvider,
    cellMenu: ({ row, $grid }) => $grid.setCurrentRow(row),
    menuClick: ({ menu }) => {
      if (menu.code === 'viewAudit') {
        const currentRow = buildingGridApi.grid?.getCurrentRecord();
        if (currentRow) {
          showAduitViewer({
            tableName: buildingGridApi.grid.params.tableName,
            origrec: currentRow.ORIGREC,
          });
        }
      }
    },
  },
  gridOptions: {
    columns: [
      {
        align: 'left',
        field: 'ORIGREC',
        title: $t('commons.origrec'),
        visible: false,
        minWidth: 150,
      },
      {
        field: 'DEPT',
        title: $t('system.dept.building.dept'),
        visible: false,
        minWidth: 150,
      },
      {
        field: 'BUILDING_ID',
        title: $t('system.dept.building.buildingId'),
        visible: false,
        minWidth: 100,
      },
      {
        field: 'BUILDING_CODE',
        title: $t('system.dept.building.buildingCode'),
        minWidth: 100,
      },
      {
        field: 'BUILDING_NAME',
        title: $t('system.dept.building.buildingName'),
        minWidth: 100,
        editRender: { name: 'VxeInput' },
      },
      {
        field: 'DESCRIPTION',
        title: $t('system.dept.building.description'),
        minWidth: 200,
        editRender: { name: 'VxeInput' },
      },
      {
        align: 'center',
        cellRender: {
          attrs: {
            nameField: 'name',
            nameTitle: $t('system.dept.building.buildingName'),
            onClick: onActionClick,
          },
          name: 'CellOperation',
          options: [
            {
              code: 'delete',
            },
          ],
        },
        field: 'operation',
        fixed: 'right',
        headerAlign: 'center',
        showOverflow: false,
        title: $t('system.dept.operation'),
        width: 100,
      },
    ],
    height: 'auto',
    pagerConfig: {
      enabled: false,
    },
    proxyConfig: {
      ajax: {
        query: async (_params) => {
          if (props.deptCode) {
            return await getBuildingListByDept(props.deptCode);
          }
          return [];
        },
      },
    },
    toolbarConfig: {
      slots: {
        buttons: 'toolbarButtons',
      },
      custom: true,
      export: false,
      refresh: { code: 'query' },
      zoom: true,
    },
    columnConfig: {
      resizable: true,
    },
    rowConfig: {
      isCurrent: true,
      keyField: 'ORIGREC',
    },
    editConfig: {
      trigger: 'click',
      mode: 'cell',
    },
    menuConfig: {
      body: {
        options: [
          [
            {
              code: 'viewAudit',
              name: '查看历史',
              prefixConfig: { icon: 'vxe-icon-table' },
              visible: true,
              disabled: false,
            },
          ],
        ],
      },
    },
    keepSource: true,
    params: {
      limsControlId: 'dgdBuilding',
      tableName: 'BUILDINGS',
    },
  } as VxeTableGridOptions,
});

const [RoomGrid, roomGridApi] = useVbenVxeGrid({
  gridEvents: {
    currentRowChange: (_params: any) => {},
    editActivated: saveEditingRowOriginalData,
    editClosed: updateProvider,
    cellMenu: ({ row, $grid }) => $grid.setCurrentRow(row),
    menuClick: ({ menu }) => {
      if (menu.code === 'viewAudit') {
        const currentRow = roomGridApi.grid?.getCurrentRecord();
        if (currentRow) {
          showAduitViewer({
            tableName: roomGridApi.grid.params.tableName,
            origrec: currentRow.ORIGREC,
          });
        }
      }
    },
  },
  gridOptions: {
    columns: [
      {
        align: 'left',
        field: 'ORIGREC',
        title: $t('commons.origrec'),
        visible: false,
        minWidth: 150,
      },
      {
        field: 'BUILDING_ID',
        title: $t('system.dept.building.buildingId'),
        visible: false,
        minWidth: 150,
      },
      {
        field: 'ROOM_ID',
        title: $t('system.dept.building.roomId'),
        visible: false,
        minWidth: 100,
      },
      {
        field: 'ROOM_CODE',
        title: $t('system.dept.building.roomCode'),
        minWidth: 100,
      },
      {
        field: 'ROOM_NAME',
        title: $t('system.dept.building.roomName'),
        minWidth: 100,
        editRender: { name: 'VxeInput' },
      },
      {
        field: 'DESCRIPTION',
        title: $t('system.dept.building.description'),
        minWidth: 200,
        editRender: { name: 'VxeInput' },
      },
      {
        field: 'CLASS',
        title: $t('system.dept.building.class'),
        minWidth: 200,
        editRender: { name: 'VxeInput' },
      },
      {
        align: 'center',
        cellRender: {
          attrs: {
            nameField: 'name',
            nameTitle: $t('system.dept.building.room'),
            onClick: onRoomActionClick,
          },
          name: 'CellOperation',
          options: [
            {
              code: 'delete',
            },
          ],
        },
        field: 'operation',
        fixed: 'right',
        headerAlign: 'center',
        showOverflow: false,
        title: $t('system.dept.operation'),
        width: 100,
      },
    ],
    height: 'auto',
    pagerConfig: {
      enabled: false,
    },
    proxyConfig: {
      ajax: {
        query: async (_params) => {
          if (currentBuildingRow.value) {
            return await getRoomListByBuilding(
              currentBuildingRow.value.BUILDING_ID,
            );
          }
          return [];
        },
      },
    },
    toolbarConfig: {
      slots: {
        buttons: 'toolbarButtons',
      },
      custom: true,
      export: false,
      refresh: { code: 'query' },
      zoom: true,
    },
    columnConfig: {
      resizable: true,
    },
    rowConfig: {
      isCurrent: true,
      keyField: 'ORIGREC',
    },
    editConfig: {
      trigger: 'click',
      mode: 'cell',
    },
    menuConfig: {
      body: {
        options: [
          [
            {
              code: 'viewAudit',
              name: '查看历史',
              prefixConfig: { icon: 'vxe-icon-table' },
              visible: true,
              disabled: false,
            },
          ],
        ],
      },
    },
    keepSource: true,
    params: {
      limsControlId: 'dgdRoom',
      tableName: 'ROOMS',
    },
  } as VxeTableGridOptions,
});

async function onRoomCreate() {
  roomFormModalApi
    .setData({
      BUILDING_ID: currentBuildingRow.value.BUILDING_ID,
    })
    .open();
}

function onActionClick({ code, row }: OnActionClickParams) {
  switch (code) {
    case 'delete': {
      onDelete(row);
      break;
    }
  }
}

function onRoomActionClick({ code, row }: OnActionClickParams) {
  switch (code) {
    case 'delete': {
      onDeleteRoom(row);
      break;
    }
  }
}

async function onDelete(row: Recordable<any>) {
  const checkUsed = await callServer('LABS.checkRoomsUsed', [
    [row.BUILDING_ID],
    'BUILDING',
  ]);
  if (checkUsed) {
    message.error({
      content: $t('system.dept.building.buildingIsUsed', [row.BUILDING_NAME]),
      key: 'action_process_msg',
    });
    return;
  }

  const [checkRooms, msg] = await callServer('LABS.scHasRooms', [
    row.BUILDING_ID,
  ]);
  if (checkRooms) {
    await (isEmpty(msg)
      ? confirm({
          title: $t('commons.deleteConfirmTitle'),
          content: $t('system.dept.building.buildingHasRooms'),
          icon: 'warning',
          centered: false,
        })
      : confirm({
          title: $t('commons.deleteConfirmTitle'),
          content: $t('commons.deleteConfirm'),
          icon: 'warning',
          centered: false,
        }));
  } else {
    message.error({
      content: $t('system.dept.building.roomHasLocation'),
      key: 'action_process_msg',
    });
    return;
  }

  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting', [row.BUILDING_NAME]),
    duration: 0,
    key: 'action_process_msg',
  });

  deleteBuilding(row.BUILDING_ID)
    .then(([success]) => {
      if (!success) {
        message.error({
          content: $t('ui.actionMessage.operationFailed', [row.BUILDING_NAME]),
          key: 'action_process_msg',
        });
        return;
      }

      message.success({
        content: $t('ui.actionMessage.deleteSuccess', [row.BUILDING_NAME]),
        key: 'action_process_msg',
      });

      buildingGridApi?.query();
    })
    .catch(() => {
      hideLoading();
    });
}

async function onDeleteRoom(row: Recordable<any>) {
  const checkUsed = await callServer('LABS.checkRoomsUsed', [
    [row.ROOM_ID],
    'ROOMS',
  ]);
  if (checkUsed) {
    message.error({
      content: $t('system.dept.building.roomIsUsed', [row.ROOM_NAME]),
      key: 'action_process_msg',
    });
    return;
  }

  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting', [row.ROOM_NAME]),
    duration: 0,
    key: 'action_process_msg',
  });

  deleteRoom(row.ROOM_ID)
    .then(([success]) => {
      if (!success) {
        message.error({
          content: $t('ui.actionMessage.operationFailed', [row.ROOM_NAME]),
          key: 'action_process_msg',
        });
        return;
      }

      message.success({
        content: $t('ui.actionMessage.deleteSuccess', [row.ROOM_NAME]),
        key: 'action_process_msg',
      });

      roomGridApi?.query();
    })
    .catch(() => {
      hideLoading();
    });
}

function onCreate() {
  formModalApi.setData({ DEPT: props.deptCode }).open();
}

watch(
  () => props.deptCode,
  (currentCode, oldCode) => {
    if (currentCode !== oldCode) {
      buildingGridApi?.grid?.clearData();
      buildingGridApi?.query();
    }
  },
);
</script>

<template>
  <ResizablePanelGroup direction="horizontal">
    <ResizablePanel :default-size="50">
      <FormModal @success="buildingGridApi.query()" />
      <BuildingGrid class="mx-2">
        <template #toolbarButtons>
          <Button type="primary" @click="onCreate" :disabled="!props.deptCode">
            <EditIcon class="size-5" />
            {{
              $t('ui.actionTitle.create', [
                $t('system.dept.building.buildingName'),
              ])
            }}
          </Button>
        </template>
      </BuildingGrid>
    </ResizablePanel>
    <ResizableHandle />
    <ResizablePanel :default-size="50">
      <RoomFormModal @success="roomGridApi.query()" />
      <RoomGrid class="mx-2">
        <template #toolbarButtons>
          <Button
            type="primary"
            @click="onRoomCreate"
            :disabled="!currentBuildingRow"
          >
            <EditIcon class="size-5" />
            {{ $t('ui.actionTitle.create', [$t('system.dept.building.room')]) }}
          </Button>
        </template>
      </RoomGrid>
    </ResizablePanel>
  </ResizablePanelGroup>
</template>
