# 使用Nginx 镜像
FROM nginx:stable-alpine AS production
 
# 设置环境变量，指定项目编码为 UTF-8
ENV LANG=en_US.UTF-8
ENV LC_ALL=en_US.UTF-8
 
# 将 Vue 项目的构建产物拷贝到容器内的 Nginx 静态文件目录
COPY ./apps/web-antd/dist/ /usr/share/nginx/html
 
# 移除默认的nginx配置文件
RUN rm /etc/nginx/conf.d/*.conf
 
# 将本地的 nginx.conf 文件拷贝到容器内的 Nginx 配置目录
COPY ./nginx.conf /etc/nginx/ 
# 暴露 Nginx 默认的 8088 端口
EXPOSE 8088
 
# 启动 Nginx 服务
CMD ["nginx", "-g", "daemon off;"]    