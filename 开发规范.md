# Witlab-LIMS 开发规范 Pending

## 一、工程规范

> 1. 前端开发规范：[遵循Vben代码规范](https://doc.vben.pro/guide/project/standard.html)。

> 2. 后端开发规范：遵循STARLIMS开发规范，STARLIMS API接口统一目录：API_Witlab_Pharm_QC。

> 3. GIT规范：[使用规范](#section-git) & [Commit规范 | Vben Admin](https://doc.vben.pro/guide/project/standard.html#commitlint)

## 二、界面与交互规范

1. 🌈**界面布局与风格**：保持界面布局的一致性，统一风格，包括颜色、字体大小等视觉元素，确保用户体验的一致性。
2. 🎯**筛选表单设计**：使用Vxe Table组件，默认展示：

   1. 列表

      1. 斑马纹
      2. 多选框
      3. 列头：筛选 + 排序 + 分组？
      4. 操作

         1. 编辑——编辑行列

            1. 保存——审计跟踪
            2. 取消

         2. 详情——抽屉式

   2. 右上角

      1. 搜索图标——重构实现类似禅道筛选功能【TODO⁉️】
      2. 导出图标
      3. 刷新图标
      4. 全屏图标
      5. 列编辑图标

   3. 左上角

      1. 功能按钮——注意底色控制，敏感操作红底（如：删除）。

   4. 底部

      1. 分页【TODO⁉️】

3. **筛选与排序功能**：实现表格的筛选功能，考虑前端筛选和服务端筛选的差异，确保分页情况下筛选的有效性；同时，添加排序功能，提升用户对数据的操作便利性。
4. **编辑功能**：编辑功能应允许用户点击后进入编辑模式，而不是直接修改，以确保用户操作的明确性。
5. **批量操作**：界面功能设计需默认支持批量操作。
6. 🚀**权限控制**：

   1. ✨使用**“权限码-指令”**模式：[权限 | Vben Admin](https://doc.vben.pro/guide/in-depth/access.html#指令方式)

   2. ✨通过**角色权限控制功能可见性Visibility**，通过**数据状态控制功能可用性Availability**。

7. 🚀**审计跟踪**：结合**电子签名**，实现表格改动触发审计的功能，考虑自动保存和手动触发审计的方案。【TODO⁉️】
8. **多语言支持**：考虑前端界面的多语言国际化，确保系统能够适应不同语言环境的需求。
9. **提高系统可配置性**：关注功能的可配置性，提高前端配置灵活度，降低维护运维动代码的成本，如：国际化语言设置、角色权限配置。

## 三、代码结构与命名规范

1. **命名规范**：☝️**kebab-case**☝️ 小写字母加短横杠，确保代码的可读性和可维护性。特别地，绑定字段命名与STARLIMS保持一致，大写！

2. **目录结构**：代码目录应统一规划，便于查找和管理。

3. **组件化开发**：鼓励使用组件化的开发方式，提高代码的复用性和模块化程度。

4. **注释规范**：代码中应添加必要的注释，解释代码的功能和逻辑，方便他人理解和维护。

5. **公共代码**：公共代码应放在统一的目录下，便于复用和维护。

## 四、API与数据处理规范

1. **API设计**：设计统一规范的API接口，确保前后端数据交互的一致性和稳定性。

   1. STARLIMS REST API转发
   2. STARLIMS CallServer
   3. STARLIMS GetDataSource

   4. STARLIMS FileUpload【TODO⁉️】
   5. STARLIMS FileDownload【TODO⁉️】

2. **数据处理**：对于返回的数据，应进行必要的处理和转换，确保数据的正确性和可用性。

3. **错误处理**：对于API请求的错误，应有明确的处理机制，如返回错误码和错误信息，便于前端进行相应的处理。

## 五、性能优化规范

1. **减少HTTP请求**：合并CSS和JavaScript文件，减少页面加载时的HTTP请求次数。

2. **使用CDN**：将静态资源放在CDN上，加快资源的加载速度。

3. **代码分割**：对于大型项目，应进行代码分割，按需加载，提高页面的加载速度。

4. **缓存策略**：合理设置缓存策略，减少不必要的数据加载，提高系统性能。

## 六、安全性规范

1. **输入验证**：对用户输入的数据进行验证，防止SQL注入等安全风险。

2. **权限控制**：实现细粒度的权限控制，确保用户只能访问和操作自己权限范围内的数据和功能。

3. **数据加密**：对敏感数据进行加密存储和传输，保护用户数据的安全。

4. **日志记录**：记录系统操作日志，便于追踪和审计系统的运行情况。

## 七、GIT使用规范<a id='section-git'></a>

1. 开发主分支DEV。

2. 各开发人员开发时自行拉取并创建独立分支进行。

3. 开发人员应定期（如每天）拉取DEV分支，确保差异不过大。

   ```bash
   # 1. 暂存当前分支未提交的修改
   git stash

   # 2. 执行rebase操作拉取dev分支
   git pull --rebase origin dev

   # 3. 恢复当前分支暂存的修改
   git stash pop
   ```

4. **每周发布一次DEV版本**。

5. 遵循分支命名规范。

6. 分支边界最小化，避免长时间大量开发一个分支。

7. 小步快跑，及时提交合并请求。

8. 遇冲突先本地解决再提交合并请求。
