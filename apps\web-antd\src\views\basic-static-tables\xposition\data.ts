import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { XPositionApi } from '#/api/basic-static-tables';

import { $t } from '#/locales';
import { createColumn } from '#/utils/lims-grids-config';

export function useColumns(): VxeTableGridOptions<XPositionApi.XPosition>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 60 },
    createColumn({
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 80,
    }),
    createColumn({
      field: 'POSITION',
      title: $t('basic-static-tables.xposition.position'),
      minWidth: 120,
    }),
    // {
    //   field: 'action',
    //   title: $t('commons.action'),
    //   minWidth: 150,
    //   slots: { default: 'action' },
    // },
  ];
}

export function useSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'POSITION',
      label: $t('basic-static-tables.xposition.position'),
      rules: 'required',
    },
  ];
}

export function useFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'POSITION',
      label: $t('basic-static-tables.xposition.position'),
    },
  ];
}
