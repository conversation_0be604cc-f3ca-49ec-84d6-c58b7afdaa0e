<script setup lang="ts">
import type {
  // VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { RpswaCalendarApi } from '#/api/basic-static-tables/rps-calendar';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, message, Modal, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { $getLookupValuesSimple } from '#/api/basic-static-tables/generic-meta-data/lookup';
import {
  $deleteAdditionalHoursApi,
  $getAdditionalHoursApi,
} from '#/api/basic-static-tables/rps-calendar';
import { $t } from '#/locales';

import AddAdditionalHoursForm from './add-additional-hours-form.vue';
import { useAddtionalColumns } from './rps-calendar-data';

const data = ref<Record<string, any>>();

const weekDayData = await $getLookupValuesSimple('Weekdays');
const weekdaysMap: { label: string; value: string }[] = [];
weekDayData.forEach((cur) => {
  weekdaysMap.push({
    label: cur.TEXT,
    value: cur.VALUE,
  });
});

const gridOptions: VxeTableGridOptions<RpswaCalendarApi.RpswaCalendarHours> = {
  columns: useAddtionalColumns(weekdaysMap),
  stripe: true,
  border: true,
  height: 'auto',
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!data.value) return;
        return await $getAdditionalHoursApi(
          data.value.calendarId,
          data.value.type,
        );
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
  },
  treeConfig: {
    parentField: 'Parent',
    rowField: 'Value',
    transform: true,
  },
  rowConfig: {
    // isCurrent: true,
    // isHover: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});

const [AddtionHoursModal, modalApi] = useVbenModal({
  draggable: true,
  footer: false,
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
    }
  },
});

const getTitle = computed(() => {
  return $t('ui.actionTitle.create');
});

function onRefresh() {
  gridApi.query();
}

function onCreate() {
  addAdditionalHoursFormApi.setData(data.value).open();
}

async function onDelete() {
  const checkOirgrec: number[] = gridApi.grid
    ?.getCheckboxRecords()
    .map((item) => item.ORIGREC as number);
  if (!checkOirgrec || checkOirgrec.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }
  await confirm('是否确认删除选中的数据？', '删除确认');
  await $deleteAdditionalHoursApi(checkOirgrec);
  message.success('删除成功！');
  onRefresh();
}

function confirm(content: string, title: string) {
  return new Promise((reslove, reject) => {
    Modal.confirm({
      content,
      onCancel() {
        reject(new Error('已取消'));
      },
      onOk() {
        reslove(true);
      },
      title,
    });
  });
}

const [AddAdditionalHoursFormModal, addAdditionalHoursFormApi] = useVbenModal({
  connectedComponent: AddAdditionalHoursForm,
});
</script>

<template>
  <AddtionHoursModal :title="getTitle" class="h-[600px] w-[1000px]">
    <AddAdditionalHoursFormModal @success="onRefresh" />
    <Grid>
      <template #toolbar-actions>
        <Space :size="[4, 0]" wrap>
          <Button type="primary" @click="onCreate">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="onDelete">
            {{ $t('ui.actionTitle.delete') }}
          </Button>
        </Space>
      </template>
    </Grid>
  </AddtionHoursModal>
</template>
