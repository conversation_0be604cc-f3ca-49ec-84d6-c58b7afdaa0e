import type { Dayjs } from 'dayjs';

import { callServer, getDataSet, getDataSetNoPage } from '../core';

export namespace SpecSchemasApi {
  export interface SpecGroups {
    [key: string]: any;
    ORIGREC: number;
    GROUPDESC: string;
    SPECSCHEMAGROUP: string;
  }

  export interface SpecSchemas {
    [key: string]: any;
    ORIGREC: number;
    SCHEMANAME: string;
    EXPDATE: Dayjs;
    STARTDDATE: Dayjs;
    VER: number;
    SPECSCHEMACODE: string;
    SPECSCHEMAGROUP: string;
  }

  export interface SpecSchemaCalcs {
    [key: string]: any;
    ORIGREC: number;
    TYPEOFITEM: string;
    CALCULATION: string;
    SPECSCHEMACODE: string;
    STATUS: string;
  }

  // SPECSCHEMAFIELDS
  export interface SpecSchemaFields {
    [key: string]: any;
    ORIGREC: number;
    FIELDNAME: string;
    FIELDCAPTION: string;
    DISP_PROPERTY: string;
    FIELDWIDTH: number;
    TABLENAME: string;
  }
}

const $getSchemaGroupApi = async () => {
  return await getDataSet('SPEC_SCHEMA.GET_SCHEMA_GROUPS', []);
};

const $addSchemaGroupApi = async (
  data: Omit<SpecSchemasApi.SpecGroups, 'ORIGREC'>,
): Promise<boolean> => {
  const result = await callServer('SPEC_SCHEMA.ADD_GROUP', [
    data.SPECSCHEMAGROUP,
  ]);
  return result.result as boolean;
};

// SPEC_SCHEMA.DELETE_GROUP
const $delchemaGroupApi = async (delOrigrecs: number[]) => {
  return await callServer('SPEC_SCHEMA.DELETE_GROUP', [delOrigrecs]);
};

// SPEC_SCHEMA.GET_SPEC_SCHEMA
const $getSpecSchemasApi = async (data: { specSchemaGroup: string }) => {
  return await getDataSet('SPEC_SCHEMA.GET_SPEC_SCHEMA', [
    data.specSchemaGroup,
  ]);
};

// SPEC_SCHEMA.ADD_SCHEMA_NAME
const $addSchemaNameApi = async (
  data: Omit<SpecSchemasApi.SpecSchemas, 'ORIGREC'>,
) => {
  return await callServer('SPEC_SCHEMA.ADD_SCHEMA_NAME', [
    data.SCHEMANAME,
    data.SPECSCHEMAGROUP,
  ]);
};

// SPEC_SCHEMA.DELETE_SCHEMA_NAME
const $deleteSchemaNameApi = async (origrecs: number[]) => {
  return await callServer('SPEC_SCHEMA.DELETE_SCHEMA_NAME', [origrecs]);
};

// getSpecSchemaCalcs
const $getSpecSchemaCalcsApi = async (data: { specSchemaCode: string }) => {
  return await getDataSet('SPEC_SCHEMA.GET_SCHEMA_ACTIONS', [
    data.specSchemaCode,
  ]);
};

// addSpecSchemaCalc
const $addSpecSchemaCalcApi = async (
  data: Omit<SpecSchemasApi.SpecSchemaCalcs, 'ORIGREC'>,
) => {
  return await callServer('SPEC_SCHEMA.ADD_ACTION', [
    data.SPECSCHEMACODE,
    data.TYPEOFITEM,
    data.STATUS,
    data.CALCULATION,
  ]);
};

// deleteSpecSchemaCalc
const $deleteSpecSchemaCalcApi = async (origrecs: number[]) => {
  return await callServer('SPEC_SCHEMA.DELETE_ACTION', [origrecs]);
};

const $getSpecSchemaFieldApi = async (data: { specSchemaCode: string }) => {
  return await getDataSet('SPEC_SCHEMA.GET_SCHEMA_FIELDS', [
    data.specSchemaCode,
  ]);
};

const $getComboSpecSchemaFiledDispPropertyApi = async () => {
  const result = await getDataSetNoPage('SPEC_SCHEMA.CB_PROPERTY', []);
  return result;
};
const $getComboSpecSchemaCalcStatusApi = async () => {
  const result = await getDataSetNoPage('SPEC_SCHEMA.CB_STATUS', []);
  return result;
};

const $getComboSpecSchemaCalcItemApi = async () => {
  const result = await getDataSetNoPage('SPEC_SCHEMA.CB_CALC_ITEM', []);
  return result;
};

export {
  $addSchemaGroupApi,
  $addSchemaNameApi,
  $addSpecSchemaCalcApi,
  $delchemaGroupApi,
  $deleteSchemaNameApi,
  $deleteSpecSchemaCalcApi,
  $getComboSpecSchemaCalcItemApi,
  $getComboSpecSchemaCalcStatusApi,
  $getComboSpecSchemaFiledDispPropertyApi,
  $getSchemaGroupApi,
  $getSpecSchemaCalcsApi,
  $getSpecSchemaFieldApi,
  $getSpecSchemasApi,
};
