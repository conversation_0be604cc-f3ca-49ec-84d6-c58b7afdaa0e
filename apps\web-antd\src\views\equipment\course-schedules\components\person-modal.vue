<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';
import {
  getDeptListApi,
  getParticPantsListApi,
  addParticiPantApi,
} from '#/api/equipment/course-schedules';
import { SelectOption, Select, Button } from 'ant-design-vue';
import { ref } from 'vue';
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from 'lucide-vue-next';

const emit = defineEmits(['success']);
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const clickRow = modalApi.getData().clickRow;
    modalApi.lock();
    try {
      const list = rightItems.value.map((item) => item.VALUE);
      const params = [clickRow.COURSENO, list];
      await addParticiPantApi(params);
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },

  async onOpenChange(isOpen) {
    if (isOpen) {
      getListData();
      // const data = modalApi.getData<SampleTypeApi.SampleType>();
    }
  },
});
const getListData = async (value: string = 'ALL') => {
  const res = await getDeptListApi();
  options.value = res.map((item: { DEPT: string }) => {
    return {
      value: item.DEPT,
      label: item.DEPT,
    };
  });
  const clickRow = modalApi.getData().clickRow;
  const tableData = modalApi.getData().tableData;
  options.value.unshift({ label: 'All', value: 'All' });
  const params = [clickRow.COURSENO, clickRow.COURSENO, value];
  const list = await getParticPantsListApi(params);
  rightItems.value = tableData.map((item) => {
    return {
      TEXT: item.FULLNAME,
      VALUE: item.USRNAM,
    };
  });

  leftItems.value = list
    .map((item) => {
      return {
        TEXT: item.FULLNAME,
        VALUE: item.USRNAM,
      };
    })
    .filter((item) => {
      const isExist = rightItems.value.some((i) => i.VALUE === item.VALUE);
      if (!isExist) {
        return item;
      }
    });
};
interface PersonItem {
  TEXT: string;
  VALUE: string;
}
const rightItems = ref<PersonItem[]>([]);
const leftItems = ref<PersonItem[]>([]);
const leftSelectedItem = ref<PersonItem>({
  TEXT: '',
  VALUE: '',
});
const rightSelectedItem = ref<PersonItem>({
  TEXT: '',
  VALUE: '',
});
const clickLeftItem = (item: PersonItem) => {
  leftSelectedItem.value = item;

  console.warn(item);
};
const clickRightItem = (item: PersonItem) => {
  rightSelectedItem.value = item;

  console.warn(item);
};
const removeToRight = () => {
  if (leftSelectedItem.value.VALUE) {
    leftItems.value = leftItems.value.filter(
      (item) => item.VALUE !== leftSelectedItem.value.VALUE,
    );

    rightItems.value.push(leftSelectedItem.value);
  }
};
const removeToLeft = () => {
  if (rightSelectedItem.value.VALUE) {
    rightItems.value = rightItems.value.filter(
      (item) => item.VALUE !== rightSelectedItem.value.VALUE,
    );
    leftItems.value.push(rightSelectedItem.value);
  }
};
const removeAllToRight = () => {
  rightItems.value.push(...leftItems.value);
  leftItems.value = [];
};
const removeAllToLeft = () => {
  leftItems.value.push(...rightItems.value);
  rightItems.value = [];
};
const type = ref('All');
const options = ref<{ label: string; value: string }[]>([]);
const typeChange = (value) => {
  getListData(value);
};
</script>
<template>
  <Modal title="选择参与者" class="h-1/2 w-2/5">
    <Space>
      实验室
      <Select v-model:value="type" style="width: 150px" @change="typeChange">
        <SelectOption
          v-for="item in options"
          :value="item.value"
          :key="item.value"
        >
          {{ item.label }}
        </SelectOption>
      </Select>
    </Space>

    <div class="flex h-[90%] justify-around overflow-hidden">
      <ul class="h-full w-2/5 overflow-auto">
        <li
          v-for="item in leftItems"
          :key="item.VALUE"
          class="list-item hover:bg-blue-200"
          :style="{
            backgroundColor:
              leftSelectedItem.VALUE === item.VALUE ? '#f0f0f0' : 'white',
          }"
          @click="clickLeftItem(item)"
        >
          {{ item.TEXT }}
        </li>
      </ul>

      <div class="flex flex-col justify-evenly">
        <div>
          <Button type="primary" @click="removeToRight">
            <ChevronRight class="ml-auto h-6 w-6" />
          </Button>
        </div>
        <div>
          <Button type="primary" @click="removeToLeft">
            <ChevronLeft class="ml-auto h-6 w-6" />
          </Button>
        </div>

        <div>
          <Button type="primary" @click="removeAllToRight">
            <ChevronsRight class="ml-auto h-6 w-6" />
          </Button>
        </div>
        <div>
          <Button type="primary" @click="removeAllToLeft">
            <ChevronsLeft class="ml-auto h-6 w-6" />
          </Button>
        </div>
      </div>
      <ul class="h-full w-2/5 overflow-auto">
        <li
          v-for="item in rightItems"
          :key="item.VALUE"
          class="list-item hover:bg-blue-200"
          :style="{
            backgroundColor:
              rightSelectedItem.VALUE === item.VALUE ? '#f0f0f0' : 'white',
          }"
          @click="clickRightItem(item)"
        >
          {{ item.TEXT }}
        </li>
      </ul>
    </div>
  </Modal>
</template>
<style>
.list {
  padding: 0;
  list-style-type: none;
}

.list-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #ccc;
  transition: background-color 0.3s;
}

.list-item:hover {
  background-color: #f0f0f0;
}

.list-item.bg-blue-100 {
  color: #000;
}
</style>
