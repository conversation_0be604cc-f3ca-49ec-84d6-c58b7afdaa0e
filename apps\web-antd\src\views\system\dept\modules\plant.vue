<script lang="ts" setup>
import type { Recordable } from '@vben/types';

import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';

import { ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@vben-core/shadcn-ui';

import { Button, message } from 'ant-design-vue';
import { EditIcon } from 'lucide-vue-next';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deletePlant,
  getPlantListByDept,
  getPlantMemberListByPlant,
  getSiteAreas,
  updateProvider,
} from '#/api';
import { showAduitViewer } from '#/components/audit-viewer';
import { $t } from '#/locales';
import { saveEditingRowOriginalData } from '#/utils/lims-grids-config';

import PlantForm from './plant-form.vue';
import PlantMemberForm from './plant-member-form.vue';

const props = defineProps<{
  deptCode?: string;
}>();

const currentPlantRow = ref<any>(null);

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: PlantForm,
  destroyOnClose: true,
});

const [MemberForm, memberFormApi] = useVbenModal({
  connectedComponent: PlantMemberForm,
  destroyOnClose: true,
});

const [PlantGrid, plantGridApi] = useVbenVxeGrid({
  gridEvents: {
    currentRowChange: (params: any) => {
      currentPlantRow.value = params.row;
      plantMemberGridApi.grid.clearData();
      plantMemberGridApi.query();
    },
    editActivated: saveEditingRowOriginalData,
    editClosed: updateProvider,
    cellMenu: ({ row, $grid }) => $grid.setCurrentRow(row),
    menuClick: ({ menu }) => {
      if (menu.code === 'viewAudit') {
        const currentRow = plantGridApi.grid?.getCurrentRecord();
        if (currentRow) {
          showAduitViewer({
            tableName: plantGridApi.grid.params.tableName,
            origrec: currentRow.ORIGREC,
          });
        }
      }
    },
  },
  gridOptions: {
    columns: [
      {
        align: 'left',
        field: 'ORIGREC',
        title: $t('commons.origrec'),
        visible: false,
        minWidth: 150,
      },
      {
        field: 'DEPT',
        title: $t('system.dept.plant.dept'),
        visible: false,
        minWidth: 150,
      },
      {
        field: 'AREA_NAME',
        title: $t('system.dept.plant.areaName'),
        minWidth: 100,
        editRender: {
          name: 'ApiSelectEdit',
          props: {
            api: () =>
              props.deptCode
                ? getSiteAreas(props.deptCode)
                : Promise.resolve([]),
            labelField: 'AREA_NAME',
            valueField: 'AREA_NAME',
          },
        },
      },
      {
        field: 'PLANT',
        title: $t('system.dept.plant.plant'),
        minWidth: 100,
      },
      {
        field: 'DESCRIPTION',
        title: $t('system.dept.plant.plantDescription'),
        minWidth: 200,
        editRender: { name: 'VxeInput' },
      },
      {
        align: 'center',
        cellRender: {
          attrs: {
            nameField: 'name',
            nameTitle: $t('system.dept.plant.plant'),
            onClick: onActionClick,
          },
          name: 'CellOperation',
          options: [
            {
              code: 'delete',
            },
          ],
        },
        field: 'operation',
        fixed: 'right',
        headerAlign: 'center',
        showOverflow: false,
        title: $t('system.dept.operation'),
        width: 100,
      },
    ],
    height: 'auto',
    pagerConfig: {
      enabled: false,
    },
    proxyConfig: {
      ajax: {
        query: async (_params) => {
          if (props.deptCode) {
            return await getPlantListByDept(props.deptCode);
          }
          return [];
        },
      },
    },
    toolbarConfig: {
      slots: {
        buttons: 'toolbarButtons',
      },
      custom: true,
      export: false,
      refresh: { code: 'query' },
      zoom: true,
    },
    columnConfig: {
      resizable: true,
    },
    rowConfig: {
      isCurrent: true,
      keyField: 'ORIGREC',
    },
    editConfig: {
      trigger: 'click',
      mode: 'cell',
    },
    menuConfig: {
      body: {
        options: [
          [
            {
              code: 'viewAudit',
              name: '查看历史',
              prefixConfig: { icon: 'vxe-icon-table' },
              visible: true,
              disabled: false,
            },
          ],
        ],
      },
    },
    keepSource: true,
    params: {
      limsControlId: 'dgdPlants',
      tableName: 'PLANTS',
    },
  } as VxeTableGridOptions,
});

const [PlantMemberGrid, plantMemberGridApi] = useVbenVxeGrid({
  gridEvents: {
    currentRowChange: (_params: any) => {},
  },
  gridOptions: {
    columns: [
      {
        align: 'left',
        field: 'ORIGREC',
        title: $t('commons.origrec'),
        visible: false,
        minWidth: 150,
      },
      {
        field: 'FULLNAME',
        title: $t('system.dept.plant.plantMember'),
        minWidth: 100,
      },
    ],
    height: 'auto',
    keepSource: true,
    pagerConfig: {
      enabled: false,
    },
    proxyConfig: {
      ajax: {
        query: async (_params) => {
          if (currentPlantRow.value) {
            return await getPlantMemberListByPlant(
              currentPlantRow.value.DEPT,
              currentPlantRow.value.PLANT,
            );
          }
          return [];
        },
      },
    },
    toolbarConfig: {
      slots: {
        buttons: 'toolbarButtons',
      },
      custom: true,
      export: false,
      refresh: { code: 'query' },
      zoom: true,
    },
    columnConfig: {
      resizable: true,
    },
    rowConfig: {
      isCurrent: true,
      keyField: 'ORIGREC',
    },
  } as VxeTableGridOptions,
});

async function onMemberEdit() {
  memberFormApi.setData(currentPlantRow.value).open();
}

function onActionClick({ code, row }: OnActionClickParams) {
  switch (code) {
    case 'delete': {
      onDelete(row);
      break;
    }
  }
}

function onDelete(row: Recordable<any>) {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting', [row.name]),
    duration: 0,
    key: 'action_process_msg',
  });

  deletePlant(row.DEPT, row.PLANT)
    .then(([success, errorCode]) => {
      if (!success) {
        const errorMessages: Record<string, string> = {
          '-100': $t('system.dept.plant.cannotDeletePlant', [
            row.DEPT,
            row.PLANT,
          ]),
          '-101': $t('system.dept.plant.cannotDeletePlantHasSamples', [
            row.DEPT,
            row.PLANT,
          ]),
          '-102': $t('system.dept.plant.cannotDeletePlantHasSamplePoints', [
            row.DEPT,
            row.PLANT,
          ]),
        };

        message.error({
          content: errorMessages[errorCode.toString()],
          key: 'action_process_msg',
        });
        return;
      }

      message.success({
        content: $t('ui.actionMessage.deleteSuccess', [row.name]),
        key: 'action_process_msg',
      });
      plantGridApi?.query();
    })
    .catch(() => {
      hideLoading();
    });
}

function onCreate() {
  formModalApi.setData({ DEPT: props.deptCode }).open();
}

watch(
  () => props.deptCode,
  (currentCode, oldCode) => {
    if (currentCode !== oldCode) {
      plantGridApi?.grid?.clearData();
      plantGridApi?.query();
    }
  },
);
</script>

<template>
  <ResizablePanelGroup direction="horizontal">
    <ResizablePanel :default-size="50">
      <FormModal @success="plantGridApi.query()" />
      <PlantGrid class="mx-2">
        <template #toolbarButtons>
          <Button type="primary" @click="onCreate" :disabled="!props.deptCode">
            <EditIcon class="size-5" />
            {{ $t('ui.actionTitle.create', [$t('system.dept.plant.plant')]) }}
          </Button>
        </template>
      </PlantGrid>
    </ResizablePanel>
    <ResizableHandle />
    <ResizablePanel :default-size="50">
      <MemberForm @success="plantMemberGridApi.query()" />
      <PlantMemberGrid class="mx-2">
        <template #toolbarButtons>
          <Button
            type="primary"
            @click="onMemberEdit"
            :disabled="!currentPlantRow"
          >
            <EditIcon class="size-5" />
            {{
              $t('ui.actionTitle.edit2', [$t('system.dept.plant.plantMember')])
            }}
          </Button>
        </template>
      </PlantMemberGrid>
    </ResizablePanel>
  </ResizablePanelGroup>
</template>
