<script lang="ts" setup>
import type { MethodManagerApi } from '#/api/business-static-tables';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import { $addTestMethodApi } from '#/api/business-static-tables/method-manager';
import { $t } from '#/locales';

import { useSchema } from './data';

const emit = defineEmits(['success']);

const methodData = ref<MethodManagerApi.TestMethod>();
const getTitle = computed(() => {
  return $t('ui.actionTitle.create', [
    $t('business-static-tables.methodManager.method'),
  ]);
});

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: useSchema(),
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    modalApi.lock();
    try {
      await $addTestMethodApi(data);
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<MethodManagerApi.TestMethod>();
      if (data) {
        methodData.value = data;
        formApi.setValues(methodData.value);
      }
    }
  },
});
</script>

<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
  </Modal>
</template>
