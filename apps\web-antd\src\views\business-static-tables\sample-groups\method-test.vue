<script setup lang="ts">
import type { SampleGroupsApi } from '#/api/business-static-tables/sample-groups';

import { watch } from 'vue';

import { Button, Space } from 'ant-design-vue';

import {
  getMethods,
  updateDefaultMethodsRelSpTest,
} from '#/api/business-static-tables/sample-groups';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import {
  useMethodsRelSpTestsColumns,
  useMethodsRelSpTestsFilterSchema,
} from './sample-groups-data';

const props = defineProps<{
  currentTestRow: null | SampleGroupsApi.SpTests;
}>();

watch(
  () => props.currentTestRow,
  (_val) => {
    if (props.currentTestRow !== null) {
      updateDefaultMethodsRelSpTest(
        props.currentTestRow.SP_CODE,
        props.currentTestRow.TESTCODE,
        props.currentTestRow.PROFILE,
      );
    }
    onRefresh();
  },
);
const colums = useMethodsRelSpTestsColumns();
const filterSchema = useMethodsRelSpTestsFilterSchema();
const queryData = async () => {
  if (!props.currentTestRow) return [];
  return getMethods(
    props.currentTestRow.SP_CODE,
    props.currentTestRow.TESTCODE,
    props.currentTestRow.PROFILE,
  );
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};

const {
  Grid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
  CurrentRow,
} = useLimsGridsConfig<SampleGroupsApi.MethodsRelSpTests>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

function onRefresh() {
  gridApi.query();
}

defineExpose({ CurrentRow }); // Vue 3 的 expose 语法
</script>

<template>
  <FormModal @success="onRefresh" />
  <Grid>
    <template #toolbar-actions>
      <Space :size="[4, 0]" wrap>
        <Button type="primary">
          {{ $t('business-static-tables.edit') }}
        </Button>
      </Space>
    </template>
    <template #action="{ row }">
      <template v-if="hasEditStatus(row)">
        <Button type="link" @click="saveRowEvent(row)">
          {{ $t('business-static-tables.save') }}
        </Button>
        <Button type="link" @click="cancelRowEvent(row)">
          {{ $t('business-static-tables.cancel') }}
        </Button>
      </template>
      <template v-else>
        <Button type="link" @click="editRowEvent(row)">
          {{ $t('business-static-tables.edit') }}
        </Button>
      </template>
    </template>
  </Grid>
</template>
