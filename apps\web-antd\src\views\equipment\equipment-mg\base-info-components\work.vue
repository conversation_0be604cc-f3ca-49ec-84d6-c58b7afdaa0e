<script lang="ts" setup>
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { EquipmentMgApi } from '#/api/equipment/equipment-mg';

import { computed, ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  getDsNewMaintSchedApi,
  updateEquipSchedEventsdApi,
} from '#/api/equipment/equipment-mg';
import { useEquipmentStore } from '#/store';

import EventsModal from '../components/events-modal.vue';
import { eventColumns } from '../equipment-mg-data';

const xTable = ref(null);

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: EventsModal,
  destroyOnClose: true,
});
const equipmentStore = useEquipmentStore();
const currentRow: EquipmentMgApi.RowType = computed(
  () => equipmentStore.getCurrentRow,
);
interface RowType {
  [key: string]: any;
}
watch(
  currentRow,
  async (newRow: RowType) => {
    if (newRow) {
      gridApi.query();
    }
  },
  { deep: true },
);
const gridEvents: VxeGridListeners<EquipmentMgApi.MetaDataEquipment> = {
  editClosed: async ({ column, row }) => {
    const sComment = '电子签名';
    gridApi.setLoading(true);
    const params = [
      row.origrec,
      column.field,
      row[column.field],
      sComment,
      row.EQID,
    ];
    await updateEquipSchedEventsdApi(params); // TODO: 电子签名
    gridApi.setLoading(false);
  },
};
const tableData = ref<EquipmentMgApi.MetaDataEquipment[]>([]);
const gridOptions: VxeTableGridOptions<EquipmentMgApi.MetaDataEquipment> = {
  columns: eventColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'cell',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!currentRow.value) {
          return [];
        }
        const data = await getDsNewMaintSchedApi([currentRow.value.EQID]);
        tableData.value = data;
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents,
});
const editCalendarEvent = () => {
  formModalApi.setData({ tableData: tableData.value }).open();
};
const onRefresh = () => {
  gridApi.query();
};
</script>
<template>
  <FormModal @success="onRefresh" />
  <Grid height="auto" ref="xTable">
    <template #toolbar-actions>
      <Space :size="[0, 4]">
        <Button type="primary" @click="editCalendarEvent()">
          {{ $t('equipment.editCalendarEvent') }}
        </Button>
      </Space>
    </template>
  </Grid>
</template>
