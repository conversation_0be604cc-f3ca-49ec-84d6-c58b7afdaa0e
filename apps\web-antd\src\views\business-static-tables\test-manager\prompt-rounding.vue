<script lang="ts" setup>
import { Page } from '@vben/common-ui';

import { Card } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';

// const { Title, Paragraph, Text } = Typography;

const [Form, formApi] = useVbenForm({
  schema: [
    {
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: 'ISO', value: 'ISO' },
          { label: 'EPS', value: 'EPS' },
          { label: 'EPA', value: 'EPA' },
          { label: 'FDA', value: 'FDA' },
          { label: 'RDP', value: 'RDP' },
          { label: 'RDD', value: 'RDD' },
        ],
      },
      fieldName: 'PICTURE',
      label: '修约规则',
      rules: 'required',
    },
    {
      component: 'Select',
      fieldName: 'ROUNDING',
      label: '修约位数',
      componentProps: {
        options: [
          { label: '0', value: '0' },
          { label: '1', value: '1' },
          { label: '2', value: '2' },
          { label: '3', value: '3' },
          { label: '4', value: '4' },
          { label: '5', value: '5' },
          { label: '6', value: '6' },
        ],
      },
      rules: 'required',
    },
    {
      component: 'Textarea',
      fieldName: 'PICTURE_DESC',
      label: '修约规则说明',
      componentProps: {
        rows: 4,
        readonly: true,
      },
      dependencies: {
        triggerFields: ['PICTURE'],
        trigger: (values, form) => {
          form.setFieldValue(
            'PICTURE_DESC',
            rulesList.find((item) => item.key === values.PICTURE)?.desc || '',
          );
        },
      },
    },
  ],
  showDefaultActions: false,
  submitOnChange: true,
});

const rulesList = [
  {
    key: 'ISO',
    label: 'ISO',
    desc: '四舍五入小数位数。在需要保留小数位次的后一位，逢五进，逢四舍。例如: 如果n = 3，则：150.2637 = 150.264 和 174.2834 = 174.283。',
  },
  {
    key: 'EPS',
    label: 'EPS',
    desc: '四舍五入有效位数。需要保留的有效数字的位数。逢五进，逢四舍。如果要丢弃的数字<5，则保留的数字不会改变；如果>=5，保留的最后一位数字增加1。例如：如果n = 2，那么：1.63 = 1.6，1.35 = 1.4，1.25 = 1.3，265 = 270。',
  },
  {
    key: 'EPA',
    label: 'EPA',
    desc: '四舍六入有效位数。需要保留的有效数字的位数。逢六进，逢四舍，逢五再判断。如果要丢弃的数字<5，则保留的数字不会改变；如果>5，保留的最后一位数字增加1；如果=5，并且在之后没有0以外的数字，则遵循奇偶平整规则。例如：如果n = 2，那么：1.63 = 1.6，1.35 = 1.4，1.25 = 1.2，1.251=1.3，265 = 270。',
  },
  {
    key: 'FDA',
    label: 'FDA',
    desc: '四舍六入小数位数。在需要保留小数位次的后一位，逢六进，逢四舍，逢五再判断。例如: 如果n = 2，那么：99.8850 = 99.88，99.89500 = 99.90，99.88501 = 99.89。',
  },
  {
    key: 'RDP',
    label: 'RDP',
    desc: '只进不舍小数位数。只进不舍，强进位，保留指定小数位数。例如：设置保留小数点后2位强进位修约，当结果为0.00357时，则修约为0.01。',
  },
  {
    key: 'RDD',
    label: 'RDD',
    desc: '只舍不进小数位数。只舍不进，保留指定小数位数，例如：0.997，保留2位只舍不进修约得：0.99；0.9997，保留3位只舍不进修约得：0.999。',
  },
];

async function getResultFormat() {
  const { valid } = await formApi.validate();
  if (!valid) return;

  const values = await formApi.getValues();
  const rule = values.PICTURE;
  const round = values.ROUNDING;
  const format = `${rule},${round}`;
  return format;
}

defineExpose({
  getResultFormat,
});
</script>

<template>
  <Page>
    <template #description>
      <!-- <Descriptions
        bordered
        :column="1"
        size="middle"
        style="background: #fff; border-radius: 8px; margin-bottom: 16px"
      >
        <Descriptions.Item
          v-for="item in rulesList"
          :key="item.key"
          :label="item.label"
        >
          <Paragraph style="margin-bottom: 0">{{ item.desc }}</Paragraph>
        </Descriptions.Item>
      </Descriptions> -->
    </template>
    <Card class="mt-4">
      <Form />
    </Card>
  </Page>
</template>
