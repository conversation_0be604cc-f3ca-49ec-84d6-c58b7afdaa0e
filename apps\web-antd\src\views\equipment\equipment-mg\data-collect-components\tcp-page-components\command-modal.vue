<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';

import { commandModalSchema } from '../../equipment-mg-data';

const emit = defineEmits(['success']);

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: commandModalSchema(),
  showDefaultActions: false,
});
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    console.warn(data);

    modalApi.lock();
    try {
      emit('success');

      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      // const data = modalApi.getData<SampleTypeApi.SampleType>();
    }
  },
});
</script>
<template>
  <Modal title="添加发送信息">
    <Form class="mx-4" />
    <span class="ml-4 text-xs">提示:你可以为特殊字符CR LF键入\r\n。</span>
  </Modal>
</template>
