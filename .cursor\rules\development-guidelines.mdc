---
description: 
globs: *.vue
alwaysApply: false
---
# 开发规范指南

## 代码风格

- 使用 ESLint 和 Prettier 进行代码格式化
- 遵循 [.prettierrc.mjs](mdc:.prettierrc.mjs) 中的格式化规则
- 使用 TypeScript 进行开发，确保类型安全

## 项目结构规范

### 1. 目录结构
```
src/
├── api/            # API 接口定义
├── views/          # 页面组件
│   └── module/     # 业务模块
│       ├── data.ts # 数据定义
│       ├── list.vue # 列表页面
│       └── modules/ # 子模块组件
├── components/     # 公共组件
├── hooks/          # 公共 hooks
├── utils/          # 工具函数
└── locales/        # 国际化文件
```

### 2. 文件命名规范
- 组件文件：使用 PascalCase（如 `UserList.vue`）
- 工具文件：使用 camelCase（如 `formatDate.ts`）
- 样式文件：与组件同名（如 `UserList.less`）
- 类型定义：使用 `.d.ts` 后缀

## 组件开发规范

### 1. 组件结构
```vue
<script lang="ts" setup>
// 1. 类型导入
import type { Xxx } from '#/types';

// 2. 组件导入
import { Component } from 'ant-design-vue';

// 3. 工具函数导入
import { useXxx } from '#/hooks';

// 4. 状态定义
const state = ref();

// 5. 计算属性
const computed = computed(() => {});

// 6. 方法定义
function handleXxx() {}

// 7. 生命周期
onMounted(() => {});
</script>

<template>
  <!-- 模板内容 -->
</template>

<style lang="less" scoped>
/* 样式定义 */
</style>
```

### 2. 组件通信
- 使用 `defineProps` 和 `defineEmits` 进行组件通信
- 复杂状态使用 Pinia 管理
- 避免过度使用事件总线

## 表单开发规范

### 1. 表单配置
```typescript
const schema: VbenFormSchema[] = [
  {
    component: 'Input',
    fieldName: 'name',
    label: $t('xxx.name'),
    rules: z.string().required(),
  }
];
```

### 2. 表单验证
- 使用 `z` 进行表单验证
- 必填字段使用 `rules: 'required'`
- 复杂验证使用 `z.refine()`
- 异步验证使用 `z.refine()` 的异步版本

### 3. 表单提交
```typescript
async function onSubmit() {
  const { valid } = await formApi.validate();
  if (valid) {
    const values = await formApi.getValues();
    // 处理提交逻辑
  }
}
```

## 表格开发规范

### 1. 表格配置
```typescript
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useColumns(),
    proxyConfig: {
      ajax: {
        query: async (params) => {
          return await getList(params);
        },
      },
    },
  },
});
```

### 2. 表格功能
- 支持分页查询
- 支持条件搜索
- 支持数据刷新
- 支持数据导出
- 支持列配置

## API 开发规范

### 1. API 定义
```typescript
// api/system/user.ts
export interface UserApi {
  getList: (params: QueryParams) => Promise<PageResult<User>>;
  create: (data: CreateUserDto) => Promise<void>;
  update: (id: string, data: UpdateUserDto) => Promise<void>;
  delete: (id: string) => Promise<void>;
}
```

### 2. API 调用
- 使用 TypeScript 类型定义
- 统一的错误处理
- 支持请求取消
- 支持请求重试

## 国际化规范

### 1. 翻译键命名
- 模块级：`module.xxx`
- 页面级：`module.page.xxx`
- 组件级：`module.component.xxx`
- 通用文本：`common.xxx`

### 2. 使用方式
```typescript
// 在 JS/TS 中
$t('module.xxx')

// 在模板中
{{ $t('module.xxx') }}
```

## 权限控制规范

### 1. 权限指令
```vue
<Button v-access:code="['Module.Action']">
```

### 2. 权限码命名
- 格式：`模块名.操作名`
- 示例：`System.User.Create`

## 样式开发规范

### 1. 类名命名
- 使用 BEM 命名规范
- 使用 Tailwind CSS 工具类
- 避免使用全局样式

### 2. 样式作用域
- 组件样式使用 `scoped`
- 全局样式放在 `styles` 目录
- 主题变量使用 CSS 变量

## 错误处理规范

### 1. 错误提示
```typescript
try {
  await action();
  message.success($t('common.success'));
} catch (error) {
  message.error($t('common.error'));
}
```

### 2. 错误日志
- 使用统一的日志服务
- 记录关键错误信息
- 支持错误追踪

## 性能优化规范

### 1. 代码分割
- 路由懒加载
- 组件按需加载
- 第三方库按需引入

### 2. 缓存策略
- 合理使用 `keep-alive`
- 数据缓存
- 请求缓存

## 测试规范

### 1. 单元测试
- 使用 Vitest 进行测试
- 测试文件放在 `__tests__` 目录
- 测试用例命名规范

### 2. 测试覆盖率
- 核心功能 100% 覆盖
- 工具函数 100% 覆盖
- 组件测试 80% 覆盖
