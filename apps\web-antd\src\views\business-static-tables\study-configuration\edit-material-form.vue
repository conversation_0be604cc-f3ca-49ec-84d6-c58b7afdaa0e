<script lang="ts" setup>
import type { XFolderApi } from '#/api/business-static-tables';

import { useVbenForm, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import {
  $getChildContainersApi,
  $getStoredContainersApi,
} from '#/api/business-static-tables';

const emit = defineEmits(['success']);
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: [
    {
      fieldName: 'CONTAINER_MATCODE',
      label: $t('business-static-tables.studyConfiguration.containerMatCode'),
      component: 'Select',
      componentProps: {
        options: [],
        class: 'w-full',
      },
      rules: 'required',
    },
    {
      fieldName: 'PULL_CONTAINER_MATCODE',
      label: $t(
        'business-static-tables.studyConfiguration.pullContainerMatCode',
      ),
      component: 'Select',
      componentProps: {
        options: [],
        class: 'w-full',
      },
      rules: 'required',
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    modalApi.lock();
    try {
      emit('success', data);
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      modalApi.lock(true);
      const data = modalApi.getData<XFolderApi.XFolderRelations>();
      if (data) {
        const matOptions = await $getStoredContainersApi({
          matCode: data.MATCODE,
        });

        const pullMatOptions = await $getChildContainersApi({
          matCode: data.MATCODE,
          storedContainer: data.CONTAINER_MATCODE,
        });
        formApi.updateSchema([
          {
            fieldName: 'CONTAINER_MATCODE',
            componentProps: {
              options: matOptions,
              fieldNames: {
                label: 'PARENT_CONTAINER_MATCODE',
                value: 'PARENT_CONTAINER_MATCODE',
              },
            },
          },
          {
            fieldName: 'PULL_CONTAINER_MATCODE',
            componentProps: {
              options: pullMatOptions,
              fieldNames: {
                label: 'CONTAINER_MATCODE',
                value: 'CONTAINER_MATCODE',
              },
            },
          },
        ]);
        formApi.setValues(data);
      }
      modalApi.lock(false);
    }
  },
});
</script>

<template>
  <Modal :title="$t('business-static-tables.studyConfiguration.editMaterials')">
    <Form class="mx-4" />
  </Modal>
</template>
