<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { EquipmentMgApi } from '#/api/equipment/equipment-mg';

import { useVbenModal } from '@vben/common-ui';

import { Button, message, Modal, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { $t } from '#/locales';

import { commandColumns } from '../../equipment-mg-data';
import SendCommandModal from './command-modal.vue';

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: SendCommandModal,
  destroyOnClose: true,
});
const gridOptions: VxeTableGridOptions<EquipmentMgApi.MetaDataEquipment> = {
  columns: commandColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  data: [
    {
      COMMANDNAME: 'ID1',
      Command_Display: '类型一',
      COMMAND: '名称一',
      STATUS: '状态一',
      ORIGREC: 0,
      NAME: '',
      EFFECT: '',
      TYPE: '',
      SORTER: 0,
    },
    {
      COMMANDNAME: 'ID1',
      Command_Display: '类型一',
      COMMAND: '名称一',
      ORIGREC: 0,
      NAME: '',
      EFFECT: '',
      TYPE: '',
      SORTER: 0,
    },
    {
      COMMANDNAME: 'ID1',
      Command_Display: '类型一',
      COMMAND: '名称一',
      ORIGREC: 0,
      NAME: '',
      EFFECT: '',
      TYPE: '',
      SORTER: 0,
    },
  ],

  // proxyConfig: {
  //   ajax: {
  //     query: async () => {
  //       return;
  //     },
  //   },
  // },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: {},
    search: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: {},
});

const onAdd = () => {
  formModalApi.setData(null).open();
};
const onDelete = () => {
  const checkLookupNames: string[] = gridApi.grid
    .getCheckboxRecords()
    .map((row) => row.NAME) as string[];
  if (checkLookupNames.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }
  if (checkLookupNames.length > 1) {
    message.warning('只能选择一条数据！');
    return;
  }
  Modal.confirm({
    title: '询问',
    content: '确实要删除选定的命令吗？',
    cancelText: '否',
    okText: '是',
    onOk() {
      console.warn('删除');
    },
    onCancel() {},
  });
};
const onRefresh = () => {
  console.warn('refresh');
};
</script>
<template>
  <FormModal @success="onRefresh" />
  <Grid height="auto">
    <template #toolbar-actions>
      <Space :size="[8, 0]" wrap>
        <Button type="primary" @click="onAdd">
          {{ $t('equipment.equipment-mg.add') }}
        </Button>
        <Button type="primary" danger @click="onDelete">
          {{ $t('ui.actionTitle.delete') }}
        </Button>
      </Space>
    </template>
  </Grid>
</template>
