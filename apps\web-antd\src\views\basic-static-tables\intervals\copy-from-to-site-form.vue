<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import {
  $copyFromSiteApi,
  $getSitesAndDefault_Ssl,
} from '#/api/basic-static-tables';
import { $t } from '#/locales';

interface FormArgs {
  fromDept: string;
}

interface FormData {
  fromDept: string;
  toDept: string;
}
const emit = defineEmits(['success']);
const formArgs = ref<FormArgs>();
const [Form, formApi] = useVbenForm({
  commonConfig: {
    labelWidth: 130,
  },
  layout: 'horizontal',
  schema: [
    {
      component: 'ApiSelect',
      componentProps: {
        api: $getSitesAndDefault_Ssl,
        params: 'FROM',
        labelInValue: true,
        labelField: 'DEPT',
        valueField: 'DEPT',
        class: 'w-full',
      },
      fieldName: 'fromDept',
      label: $t('basic-static-tables.intervals.fromDept'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: $getSitesAndDefault_Ssl,
        params: 'TO',
        labelField: 'DEPT',
        valueField: 'DEPT',
        class: 'w-full',
      },
      fieldName: 'toDept',
      label: $t('basic-static-tables.intervals.toDept'),
      rules: 'required',
    },
  ],
  showDefaultActions: false,
});
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues<FormData>();
    modalApi.lock();
    try {
      await $copyFromSiteApi(data);
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  title: $t('ui.actionTitle.create', [
    $t('basic-static-tables.intervals.intervalTat'),
  ]),
  async onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<FormArgs>();
      formArgs.value = data;
      if (data?.fromDept) {
        formApi.setValues({
          fromDept: data.fromDept,
        });
      }
    }
  },
});
</script>

<template>
  <Modal>
    <Form class="mx-4" />
  </Modal>
</template>
