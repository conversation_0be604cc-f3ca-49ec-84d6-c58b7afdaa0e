<script lang="ts" setup>
import type { VxeColumnPropTypes } from 'vxe-table';

import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { SubLocationTypeApi } from '#/api/';

import { onMounted, reactive } from 'vue'; // 添加了 onMounted 和 reactive 的导入

import { confirm, Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Button, message, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteSubLocationType,
  getNumMethod,
  getSubLocationTypeList,
  updateSubLocationType,
} from '#/api/';

import AddSubLocationTypeForm from './add-sub-location-type.vue';
import {
  useSubLocationTypeColumns,
  useSubLocationTypeFilterSchema,
} from './sub-location-type-data';

function createEditRender(): VxeColumnPropTypes.EditRender {
  return {
    name: 'select',
    options: [],
  };
}

const methodEditRender = reactive(createEditRender());

onMounted(async () => {
  // 绑定编码方法数据
  const methodResult = await getNumMethod();
  methodEditRender.options = methodResult.items.map((item) => ({
    label: item.TEXT,
    value: item.VALUE,
  }));
});

const gridOptions: VxeTableGridOptions<SubLocationTypeApi.SubLocationType> = {
  columns: useSubLocationTypeColumns(methodEditRender),
  stripe: true,
  border: true,
  keepSource: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getSubLocationTypeList();
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const formOptions: VbenFormProps = {
  schema: useSubLocationTypeFilterSchema(),
  submitOnChange: false,
  collapsed: true,
  showCollapseButton: true,
  submitButtonOptions: {
    content: '查询',
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  formOptions,
});

function hasEditStatus(row: SubLocationTypeApi.SubLocationType) {
  return gridApi.grid?.isEditByRow(row);
}

function editRowEvent(row: SubLocationTypeApi.SubLocationType) {
  gridApi.grid?.setEditRow(row);
}

async function saveRowEvent(row: SubLocationTypeApi.SubLocationType) {
  await gridApi.grid?.clearEdit();
  updateSubLocationType(row);
  gridApi.setLoading(true);
  setTimeout(() => {
    gridApi.setLoading(false);
    message.success({
      content: `保存成功！`,
    });
  }, 600);
}

const cancelRowEvent = (row: SubLocationTypeApi.SubLocationType) => {
  gridApi.grid?.clearEdit().then(() => {
    // 还原行数据
    gridApi.grid.revertData(row);
  });
};

function onRefresh() {
  gridApi.query();
}

// 添加子位置类型
const [AddSubLocationTypeFormModal, subLocationTypeModalApi] = useVbenModal({
  connectedComponent: AddSubLocationTypeForm,
});

async function addSubLocationTypeFun() {
  subLocationTypeModalApi.setData({}).open();
}

// 删除QC类型
async function deleteSubLocationTypeFun() {
  // 获取选中行
  const aOrigrec: number[] = gridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.ORIGREC) as number[];

  if (aOrigrec.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${aOrigrec.length} 条数据吗？`,
      icon: 'warning',
      centered: false,
    });

    await deleteSubLocationType(aOrigrec);

    message.success('删除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}
</script>
<template>
  <Page auto-content-height>
    <FormDrawer @success="onRefresh" />
    <AddSubLocationTypeFormModal @success="onRefresh" />
    <Grid>
      <template #toolbar-actions>
        <Space>
          <Button type="primary" @click="addSubLocationTypeFun">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="deleteSubLocationTypeFun">
            {{ $t('ui.actionTitle.delete') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent(row)">
            {{ $t('basic-static-tables.save') }}
          </Button>
          <Button type="link" @click="cancelRowEvent(row)">
            {{ $t('basic-static-tables.cancel') }}
          </Button>
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">
            {{ $t('basic-static-tables.edit') }}
          </Button>
        </template>
      </template>
    </Grid>
  </Page>
</template>
