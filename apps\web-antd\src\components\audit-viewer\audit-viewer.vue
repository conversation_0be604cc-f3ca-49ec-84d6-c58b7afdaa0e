<script lang="ts" setup>
import type { Key } from 'ant-design-vue/es/_util/type';
import type { DataNode, EventDataNode } from 'ant-design-vue/es/tree';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { computed, onMounted, ref } from 'vue';

import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@vben-core/shadcn-ui';

import { Spin } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { callServer, DSFromString } from '#/api';
import { $t } from '#/locales';
import { isNullOrWhiteSpace } from '#/utils';

import AuditViewerTree from './audit-viewer-tree.vue';

// 类型定义
interface AuditNode {
  title: string;
  key: string;
  children: AuditNode[];
  tag: string;
  parentKey?: string;
  mainDisplayField?: string;
  whereClause?: string;
  displayColumns?: string[][];
}

// Props 定义
const props = defineProps<{
  origrec: number | string;
  tableName: string;
}>();

// 状态管理
const whereClause = computed(
  () =>
    ` AUDITTRL.TABLENAME = '${props.tableName}' AND AUDITTRL.ORIGINAL_ORIGREC in (${props.origrec})`,
);

const auditTableArray = ref<AuditNode[]>([]);
const loading = ref(true);
const error = ref<null | string>(null);
const auditDataGridSpinning = ref(false);

// 数据加载函数
const loadAuditData = async () => {
  try {
    const auditTableList: string[] = await callServer(
      'Audit.scGetAuditTableList',
      [whereClause.value],
    );

    const newAuditTableArray = await Promise.all(
      auditTableList.map(async (tableName) => {
        const [title] = await callServer('AuditViewer.scGetRootTableDetails', [
          tableName,
          whereClause.value,
          $t('components.auditViewer.audit_trail'),
        ]);

        return {
          title: String(title),
          key: tableName,
          children: [],
          tag: 'Table' as const,
          mainDisplayField: 'ORIGREC',
        } satisfies AuditNode;
      }),
    );

    // 使用新数组替换旧数组，确保触发响应式更新
    auditTableArray.value = newAuditTableArray;
  } catch (error_) {
    error.value =
      error_ instanceof Error
        ? error_.message
        : $t('components.auditViewer.error');
    throw error_;
  }
};

// 计算属性
const treeData = computed(() => {
  const mapToNode = (node: AuditNode): DataNode => ({
    title: node.title,
    key: node.key,
    children: node.children.map((element) => mapToNode(element)),
  });
  return auditTableArray.value.map((element) => mapToNode(element));
});

// 工具函数
const findNodeByKey = (
  key: number | string,
  findArray: AuditNode[] = auditTableArray.value,
): AuditNode | null => {
  for (const node of findArray) {
    if (node.key === key) return node;
    if (node.children && node.children.length > 0) {
      for (const child of node.children) {
        if (child.key === key) return child;
        if (child.children && child.children.length > 0) {
          const result = findNodeByKey(key, child.children);
          if (result) return result;
        }
      }
    }
  }
  return null;
};

// 事件处理函数
const handleTreeSelect = async (
  selectedKeys: Key[],
  info: {
    event: 'select';
    nativeEvent: MouseEvent;
    node: EventDataNode;
    selected: boolean;
    selectedNodes: DataNode[];
  },
) => {
  const { key } = info.node;
  const node = findNodeByKey(key);

  if (!node) return;

  switch (node.tag) {
    case 'Item': {
      auditDataGridApi.grid?.clearFilter();
      auditDataGridApi.grid?.setFilter(
        'fORIGREC',
        [
          {
            label: node.key.toString(),
            value: node.key.toString(),
            checked: true,
          },
        ],
        true,
      );
      await loadRelatedTables(node.parentKey || '', node.key);
      break;
    }
    case 'Table': {
      await loadDetail(key as string);
      break;
    }
  }
};

// 加载详细信息
const loadDetail = async (tableName: string) => {
  try {
    const node = findNodeByKey(tableName) as AuditNode;
    if (!node) return;

    auditDataGridSpinning.value = true;
    const [auditDataSet, _displayColumns] = await Promise.all([
      callServer('Audit.GetAuditDataSet', [
        node.whereClause || whereClause.value,
        'default',
        null,
        false,
      ]),
      callServer('AuditViewer.scGetDisplayStructure', [
        tableName,
        'Default',
        node.whereClause || whereClause.value,
      ]),
    ]);

    const existingColumns = auditDataSet.Tables[0].Columns.filter(
      (column: { ColumnName: string }) => !column.ColumnName.startsWith('f'),
    );

    const displayColumns = node.displayColumns || _displayColumns;

    // 更新主显示字段
    if (
      displayColumns.some(
        ([
          _tableName,
          _fieldName,
          _interface,
          _onChangeFormat,
          _isSystem,
          _dataSource,
          isTableDisplayField,
          _caption,
        ]: string[]) => isTableDisplayField === 'Y',
      )
    ) {
      const [, mainDisplayField] = displayColumns.find(
        ([
          _tableName,
          _fieldName,
          _interface,
          _onChangeFormat,
          _isSystem,
          _dataSource,
          isTableDisplayField,
          _caption,
        ]: string[]) => isTableDisplayField === 'Y',
      );
      if (mainDisplayField) {
        node.mainDisplayField = mainDisplayField;
      }
    }

    // 只在没有子节点时加载
    if (!node.children?.length) {
      const originalOrigrecs = await callServer('Audit.scGetOriginalOrigrecs', [
        tableName,
        whereClause.value,
        `{fn convert(${tableName}.${node.mainDisplayField}, SQL_VARCHAR)}`,
      ]);

      // 创建新的子节点数组
      const newChildren = originalOrigrecs.map(
        ([key, title]: [string, string]) => {
          const node = findNodeByKey(key) as AuditNode;
          if (node) return null;
          return {
            key: String(key),
            title: String(title),
            tag: 'Item' as const,
            parentKey: tableName,
            children: [],
          };
        },
      );

      // 更新节点的子节点
      node.children = newChildren;
    }

    auditDataGridApi.grid?.clearData();
    const columns = [
      ...existingColumns.map((column: { ColumnName: string }) => ({
        field: column.ColumnName,
        title: $t(`audit-trail.${column.ColumnName}`),
        minWidth: 160,
        visible: column.ColumnName !== 'aCELL_ATTRIBS',
      })),
      ...displayColumns.map((column: string[]) => ({
        field: `f${column[1]}`,
        title: column[7],
        minWidth: 200,
        filters:
          column[1] === 'ORIGREC' && node && 'children' in node
            ? node.children?.map((child: AuditNode) => ({
                label: child.key.toString(),
                value: child.key.toString(),
              }))
            : null,
      })),
    ];

    auditDataGridApi.setGridOptions({
      data: auditDataSet.Tables[0].Rows,
      columns,
    });
  } catch (error) {
    console.error($t('components.auditViewer.load_detail_failed'), error);
    throw error;
  } finally {
    auditDataGridSpinning.value = false;
  }
};

const loadRelatedTables = async (parent: string, key: string) => {
  const node = findNodeByKey(key) as AuditNode;
  if (!node) return;
  const navigators: any[] = await callServer('Audit.scGetAuditNavigator', [
    parent,
    key,
    -1,
    null,
    'Default',
  ]);
  const childrenNodes: AuditNode[] = [];
  for (const navigator of navigators) {
    const [
      tableName,
      title,
      // eslint-disable-next-line no-unused-vars, unused-imports/no-unused-vars
      icon,
      dataSetStr,
      whereClause,
      // eslint-disable-next-line no-unused-vars
      _auditCount,
      // eslint-disable-next-line no-unused-vars
      _rowCaption,
      displayColumns,
    ] = navigator;
    const ds = await DSFromString(dataSetStr || '');
    const childrenTableRows = ds.Tables[0].Rows;
    const tableNode = {
      title: String(title),
      key: String(tableName),
      children: [],
      tag: 'Table' as const,
      whereClause: String(whereClause),
      displayColumns,
    };
    tableNode.children = childrenTableRows.map(
      (row: { DISPLAY_MEMBER: string; VALUE_MEMBER: string }) => ({
        title: String(row.DISPLAY_MEMBER),
        key: String(row.VALUE_MEMBER),
        children: [],
        tag: 'Item' as const,
        parentKey: tableName,
      }),
    );
    childrenNodes.push(tableNode);
  }
  node.children = [...childrenNodes];
};

// 表格配置
const auditDataGridOptions: VxeTableGridOptions<any> = {
  columns: [],
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async () => {},
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'fORIGREC',
    isCurrent: true,
  },
  rowStyle: ({ row }) =>
    row.aCELL_ATTRIBS ? { color: row.aCELL_ATTRIBS } : null,
  cellStyle: ({ row, $rowIndex, column }) => {
    const nextRow = auditDataGridApi.grid.getData($rowIndex + 1);
    if (!nextRow || !column.field.startsWith('f')) return null;

    const curVal = row[column.field];
    const nextVal = nextRow[column.field];

    if (isNullOrWhiteSpace(curVal) && isNullOrWhiteSpace(nextVal)) return null;
    return curVal === nextVal ? null : { backgroundColor: 'yellow' };
  },
};

const [AuditDataGrid, auditDataGridApi] = useVbenVxeGrid({
  gridOptions: auditDataGridOptions,
});

// 生命周期
onMounted(async () => {
  try {
    await loadAuditData();
  } catch (error) {
    console.error($t('components.auditViewer.init_failed'), error);
  } finally {
    loading.value = false;
  }
});
</script>

<template>
  <div class="h-[60vh]">
    <div v-if="loading" class="flex h-full items-center justify-center">
      <span class="text-gray-500">{{
        $t('components.auditViewer.loading')
      }}</span>
    </div>
    <div
      v-else-if="error"
      class="flex h-full items-center justify-center text-red-500"
    >
      {{ error }}
    </div>
    <ResizablePanelGroup v-else direction="horizontal">
      <ResizablePanel :default-size="30">
        <AuditViewerTree
          :tree-data="treeData"
          @tree-select="handleTreeSelect"
        />
      </ResizablePanel>
      <ResizableHandle />
      <ResizablePanel :default-size="70">
        <div class="h-full w-full">
          <Spin :spinning="auditDataGridSpinning" class="h-full w-full">
            <AuditDataGrid class="h-full w-full" />
          </Spin>
        </div>
      </ResizablePanel>
    </ResizablePanelGroup>
  </div>
</template>

<style lang="less" scoped>
.audit-viewer {
  &-loading {
    @apply flex h-full items-center justify-center;
  }

  &-error {
    @apply flex h-full items-center justify-center text-red-500;
  }
}

:deep(.ant-spin-nested-loading) {
  height: 100%;
}

:deep(.ant-spin-container) {
  height: 100%;
}
</style>
