<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Select, SelectOption } from 'ant-design-vue';
import {
  getCertifyEQApi,
  chkEQCertApi,
  addEQCertApi,
  getEQtypeApi,
} from '#/api/equipment/persons-manage';
import DoubleList from './double-list.vue';
import { deviceModalSchema } from '../persons-manage-data';
import { useVbenForm } from '#/adapter/form';
import dayjs from 'dayjs';

const emit = defineEmits(['success']);
interface PersonItem {
  TEXT: string;
  VALUE: string;
}
import { message } from 'ant-design-vue';
const rightItems = ref<PersonItem[]>([]);
const leftItems = ref<PersonItem[]>([]);
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    if (rightItems.value.length === 0) {
      modalApi.close();
      return;
    }
    modalApi.lock();
    try {
      const clickRow = modalApi.getData().clickRow;
      let context = [];
      for (const item of rightItems.value) {
        const checkRes = await chkEQCertApi([item.VALUE, clickRow.USRNAM]);
        if (checkRes !== '') {
          context.push(checkRes);
          continue;
        } else {
          const data = await formApi.getValues();
          const date = dayjs(data.authorizationDate).format('YYYY-MM-DD');
          await addEQCertApi([item.VALUE, clickRow.USRNAM, date]);
        }
      }
      if (context.length > 0) {
        message.warning(`已拥有设备：${context.join(', ')}`);
      }
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },

  async onOpenChange(isOpen) {
    if (isOpen) {
      const res = await getEQtypeApi([]);
      equipmentOptions.value = res.map((item: any) => ({
        value: item.Value,
        label: item.Text,
      }));
      formApi.setFieldValue('authorizationDate', dayjs());
      getListData();
    }
  },
});
const leftSelectedItem = ref<PersonItem>({
  TEXT: '',
  VALUE: '',
});
const rightSelectedItem = ref<PersonItem>({
  TEXT: '',
  VALUE: '',
});
const removeToRight = () => {
  if (leftSelectedItem.value.VALUE) {
    leftItems.value = leftItems.value.filter(
      (item) => item.VALUE !== leftSelectedItem.value.VALUE,
    );

    rightItems.value.push(leftSelectedItem.value);
  }
};
const removeToLeft = () => {
  if (rightSelectedItem.value.VALUE) {
    rightItems.value = rightItems.value.filter(
      (item) => item.VALUE !== rightSelectedItem.value.VALUE,
    );
    leftItems.value.push(rightSelectedItem.value);
  }
};
const removeAllToRight = () => {
  rightItems.value.push(...leftItems.value);
  leftItems.value = [];
};
const removeAllToLeft = () => {
  leftItems.value.push(...rightItems.value);
  rightItems.value = [];
};
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: deviceModalSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

const equipmentOptions = ref<{ value: string; label: string }[]>([]);

const getListData = async () => {
  const data = await formApi.getValues();
  const clickRow = modalApi.getData().clickRow;
  let params = [data.equipmentType, clickRow.USRNAM];
  const res = await getCertifyEQApi(params);
  leftItems.value = res.map((item: any) => ({
    TEXT: item.Text,
    VALUE: item.Value,
  }));
};
const equipmentChange = () => {
  getListData();
};
</script>
<template>
  <Modal title="方法授权" class="h-1/2 w-2/5">
    <Form class="mx-4">
      <template #equipmentType="slotProps">
        <Select v-bind="slotProps" class="w-full" @change="equipmentChange">
          <SelectOption
            v-for="item in equipmentOptions"
            :value="item.value"
            :key="item.value"
          >
            {{ item.label }}
          </SelectOption>
        </Select>
      </template>
    </Form>
    <DoubleList
      :leftItems="leftItems"
      :rightItems="rightItems"
      :leftSelectedItem="leftSelectedItem"
      :rightSelectedItem="rightSelectedItem"
      @update:leftSelectedItem="leftSelectedItem = $event"
      @update:rightSelectedItem="rightSelectedItem = $event"
      @removeToRight="removeToRight"
      @removeToLeft="removeToLeft"
      @removeAllToRight="removeAllToRight"
      @removeAllToLeft="removeAllToLeft"
    />
  </Modal>
</template>
