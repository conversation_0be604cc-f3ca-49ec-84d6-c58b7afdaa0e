import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { PreBatchesApi } from '#/api/login-options/interface-notification';

import dayjs from 'dayjs';

import { $t } from '#/locales';

export function usePreBatchesFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'BATCHNO',
      label: $t('login-options.interface-notification.batchno'),
    },
    {
      component: 'Input',
      fieldName: 'MATCODE',
      label: $t('login-options.interface-notification.matcode'),
    },
    {
      component: 'Select',
      fieldName: 'STATUS',
      label: $t('login-options.interface-notification.status'),
      componentProps: {
        options: [
          { value: 'Draft', label: '新建' },
          { value: 'Done', label: '完成' },
          { value: 'Cancelled', label: '取消' },
        ],
        class: 'w-full',
      },
    },
  ];
}
export function usePreBatchesColumns(): VxeTableGridOptions<PreBatchesApi.PreBatches>['columns'] {
  return [
    // { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'origrec',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'DISPSTS',
      title: $t('login-options.interface-notification.dispsts'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'REQUESTID',
      title: $t('login-options.interface-notification.requestid'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ORDERNO',
      title: $t('login-options.interface-notification.orderno'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MATCODE',
      title: $t('login-options.interface-notification.matcode'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MATNAME',
      title: $t('login-options.interface-notification.matname'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'BATCHNO',
      title: $t('login-options.interface-notification.batchno'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'PLANT',
      title: $t('login-options.interface-notification.plant'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'PROCESS',
      title: $t('login-options.interface-notification.process'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ESTIMATEDVOL',
      title: $t('login-options.interface-notification.estimatedvol'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ACTUALVOL',
      title: $t('login-options.interface-notification.actualvol'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ESTIMATEDVOL_UNITS',
      title: $t('login-options.interface-notification.estimatedvol_units'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SAMPLE_VOL',
      title: $t('login-options.interface-notification.sample_vol'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SAMPLE_UNITS',
      title: $t('login-options.interface-notification.sample_units'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'REQUEST_DATE',
      title: $t('login-options.interface-notification.request_date'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      align: 'center',
      field: 'REQUESTER',
      title: $t('login-options.interface-notification.requester'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SAMPDATE',
      title: $t('login-options.interface-notification.sampdate'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      align: 'center',
      field: 'SAMPLEDBY',
      title: $t('login-options.interface-notification.sampledby'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'EXPDATE',
      title: $t('login-options.interface-notification.expdate'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      align: 'center',
      field: 'COMMENTS',
      title: $t('login-options.interface-notification.comments'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'STATUS',
      title: $t('login-options.interface-notification.status'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('login-options.operation'),
      minWidth: 180,
    },
  ];
}

export function usePreTestsFilterSchema(): VbenFormSchema[] {
  return [];
}

export function usePreTestsColumns(): VxeTableGridOptions<PreBatchesApi.PreTests>['columns'] {
  return [
    {
      align: 'center',
      field: 'origrec',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'TESTCODE',
      title: $t('login-options.interface-notification.testcode'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'REQUESTID',
      title: $t('login-options.interface-notification.requestid'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'SAMPLE_NO',
      title: $t('login-options.interface-notification.sample_no'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'COMMENTS',
      title: $t('login-options.interface-notification.comments'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}

export function usePreBatchesRelFilterSchema(): VbenFormSchema[] {
  return [];
}

export function usePreBatchesRelColumns(): VxeTableGridOptions<PreBatchesApi.PreBatchesRel>['columns'] {
  return [
    {
      align: 'center',
      field: 'origrec',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'REQUESTID',
      title: $t('login-options.interface-notification.requestid'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'REL_REQUESTNO',
      title: $t('login-options.interface-notification.rel_requestno'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}
