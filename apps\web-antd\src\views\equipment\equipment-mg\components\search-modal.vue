<script lang="ts" setup>
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { EquipmentMgApi } from '#/api/equipment/equipment-mg';

import { useVbenModal } from '@vben/common-ui';

import { Button, Modal, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { $t } from '#/locales';

import { searchConditionColumns } from '../equipment-mg-data';

const emit = defineEmits(['success']);

const gridOptions: VxeTableGridOptions<EquipmentMgApi.MetaDataEquipment> = {
  columns: searchConditionColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'cell',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  data: [
    {
      EQID: 'ID1',
      EQTYPE: '类型一',
      EQNAME: '名称一',
      STATUS: '状态一',
      ORIGREC: 0,
      NAME: '',
      EFFECT: '',
      TYPE: '',
      SORTER: 0,
    },
    {
      EQID: 'ID1',
      EQTYPE: '类型一',
      EQNAME: '名称一',
      STATUS: '状态一',
      ORIGREC: 0,
      NAME: '',
      EFFECT: '',
      TYPE: '',
      SORTER: 0,
    },
    {
      EQID: 'ID1',
      EQTYPE: '类型一',
      EQNAME: '名称一',
      STATUS: '状态一',
      ORIGREC: 0,
      NAME: '',
      EFFECT: '',
      TYPE: '',
      SORTER: 0,
    },
  ],

  // proxyConfig: {
  //   ajax: {
  //     query: async () => {
  //       return;
  //     },
  //   },
  // },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: {},
    search: true,
    zoom: true,
  },
};
const selectData = [];
const gridEvents: VxeGridListeners<EquipmentMgApi.MetaDataEquipment> = {
  checkboxChange: async (data) => {
    selectData.push({
      row: data.row,
      rowIndex: data.rowIndex,
    });
  },
};
const [Grid] = useVbenVxeGrid({
  gridOptions,
  gridEvents,
});

const [FormModal, modalApi] = useVbenModal({
  async onConfirm() {
    try {
      emit('success');

      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      // const data = modalApi.getData<SampleTypeApi.SampleType>();
    }
  },
});
const addItem = () => {
  console.warn('add');
};
const deleteItem = () => {
  console.warn('delete');
};
const clearItems = () => {
  Modal.confirm({
    title: '提醒',
    content: '确定清楚所有条件?',
    cancelText: '否',
    okText: '是',
    onOk() {
      console.warn('删除');
    },
    onCancel() {},
  });
  console.warn('clear');
};
</script>
<template>
  <FormModal title="选择原因" class="h-2/3 w-1/3">
    <Grid class="h-full">
      <template #toolbar-actions>
        <Space>
          <Space :size="[4, 0]">
            <Button type="primary" primary @click="addItem">
              {{ $t('equipment.add') }}
            </Button>
            <Button @click="deleteItem">
              {{ $t('equipment.delete') }}
            </Button>
            <Button @click="clearItems">
              {{ $t('equipment.clear') }}
            </Button>
          </Space>
        </Space>
      </template>
    </Grid>
  </FormModal>
</template>
