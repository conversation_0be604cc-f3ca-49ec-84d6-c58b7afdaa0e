<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { SupplierApi } from '#/api/basic-static-tables/supplier';

import { confirm, Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Button, message, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteSupplier,
  getSupplierList,
  updateSupplier,
} from '#/api/basic-static-tables/supplier';

import AddSupplierForm from './add-supplier.vue';
import { useSupplierColumns, useSupplierFilterSchema } from './supplier-data';

const gridOptions: VxeTableGridOptions<SupplierApi.Supplier> = {
  columns: useSupplierColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getSupplierList();
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const formOptions: VbenFormProps = {
  schema: useSupplierFilterSchema(),
  submitOnChange: false,
  collapsed: true,
  showCollapseButton: true,
  submitButtonOptions: {
    content: '查询',
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  formOptions,
});

function hasEditStatus(row: SupplierApi.Supplier) {
  return gridApi.grid?.isEditByRow(row);
}

function editRowEvent(row: SupplierApi.Supplier) {
  gridApi.grid?.setEditRow(row);
}

async function saveRowEvent(row: SupplierApi.Supplier) {
  await gridApi.grid?.clearEdit();
  updateSupplier(row);
  gridApi.setLoading(true);
  setTimeout(() => {
    gridApi.setLoading(false);
    message.success({
      content: `保存成功！`,
    });
  }, 600);
}

const cancelRowEvent = (_row: SupplierApi.Supplier) => {
  gridApi.grid?.clearEdit();
};

function onRefresh() {
  gridApi.query();
}

// 添加厂商
const [AddSupplierFormModal, supplierModalApi] = useVbenModal({
  connectedComponent: AddSupplierForm,
});

async function addSupplierFun() {
  supplierModalApi.setData(null).open();
}

// 删除厂商
async function deleteSupplierFun() {
  // 获取选中行
  const aSuppCode: string[] = gridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.SUPPCODE);

  if (aSuppCode.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  const aOrigrec: number[] = gridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.ORIGREC);

  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${aSuppCode.length} 条数据吗？`,
      icon: 'warning',
      centered: false,
    });

    await deleteSupplier(aSuppCode, aOrigrec);

    message.success('删除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}
</script>
<template>
  <Page auto-content-height>
    <FormDrawer @success="onRefresh" />
    <AddSupplierFormModal @success="onRefresh" />
    <Grid>
      <template #toolbar-actions>
        <Space>
          <Button type="primary" @click="addSupplierFun">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="deleteSupplierFun">
            {{ $t('ui.actionTitle.delete') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent(row)">
            {{ $t('basic-static-tables.save') }}
          </Button>
          <Button type="link" @click="cancelRowEvent(row)">
            {{ $t('basic-static-tables.cancel') }}
          </Button>
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">
            {{ $t('basic-static-tables.edit') }}
          </Button>
        </template>
      </template>
    </Grid>
  </Page>
</template>
