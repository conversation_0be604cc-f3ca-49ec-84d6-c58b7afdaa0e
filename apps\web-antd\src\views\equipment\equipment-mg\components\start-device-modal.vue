<script lang="ts" setup>
import type { EquipmentMgApi } from '#/api/equipment/equipment-mg';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Modal as AModal, message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  checkIfELNMethodApi,
  finishMaintRecApi,
} from '#/api/equipment/equipment-mg';
import { useEquipmentStore } from '#/store';

import { reasonModalSchema } from '../equipment-mg-data';

const emit = defineEmits(['success']);
const equipmentStore = useEquipmentStore();
const currentRow: EquipmentMgApi.RowType = computed(
  () => equipmentStore.getCurrentRow,
);
const [Form, formApi] = useVbenForm({
  layout: 'horizontal',
  schema: reasonModalSchema(),
  showDefaultActions: false,
});
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    console.warn(data);

    modalApi.lock();
    try {
      const rowData = modalApi.getData().currentEventRow;
      if (rowData.value.MAINTENANCETYPE === 'EQUIPMENT EVENT') {
        const data = await checkIfELNMethodApi([
          currentRow.value.EQID,
          rowData.value.MAINTENANCETYPE,
        ]);
        if (data) {
          open.value = true;
          return;
        } else {
          // TODO:电子签名判断
          await handleOk();
        }
      }
      emit('success');
    } finally {
      modalApi.lock(false);
    }
  },

  onOpenChange(isOpen) {
    if (isOpen) {
      // const data = modalApi.getData<SampleTypeApi.SampleType>();
    }
  },
});
const handleOk = async () => {
  // TODO:电子签名判断

  open.value = false;
  const formData = await formApi.getValues();
  const rowData = modalApi.getData().currentEventRow;

  const params = [
    currentRow.value.EQID,
    rowData.value.MAINTENANCETYPE,
    formData.OPENREASON,
    rowData.value.ORIGREC,
    currentRow.value.MAINTENANCEEVENT,
    'Done',
    formData.ACTION_TAKEN,
    'InService',
    formData.MAINTDATE,
  ];
  const data = await finishMaintRecApi(params);
  if (data === null) {
    modalApi.close();
    emit('success');
  }
  if (data === 'ISEventExists') {
    message.error('无法添加事件：引用此记录ID的运行事件已存在！');
    return;
  }
  if (data === 'ISDate') {
    message.error('运行维护时间不能早于停用的维护时间');
  }
};
const open = ref(false);
</script>
<template>
  <Modal title="选择原因">
    <AModal v-model:open="open" @ok="handleOk" class="h-[100px]">
      <div>此维护事件需要完成ELN。你确定要完成它吗？</div>
    </AModal>
    <Form class="mx-4" />
  </Modal>
</template>
