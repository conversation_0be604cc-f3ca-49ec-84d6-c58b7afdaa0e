<script lang="ts" setup>
import type { SpecCategoriesApi } from '#/api/basic-static-tables/spec-categories';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { addSpecCategory } from '#/api/basic-static-tables/spec-categories';

const emit = defineEmits(['success']);

const formData = ref<SpecCategoriesApi.SpecCategorries>();

const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 提交函数
  handleSubmit: onSubmit,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'vertical',
  schema: [
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Input',
      // 对应组件的参数
      componentProps: {
        placeholder: '请输入',
      },
      // 字段名
      fieldName: 'SPECCATEGORY',
      // 界面显示的label
      label: '类型',
      rules: 'required',
    },
  ],
  wrapperClass: 'grid-cols-1',
  showDefaultActions: false,
});

/* function resetForm() {
  formApi.resetForm();
  formApi.setValues(formData.value || {});
} */

const [Modal, modalApi] = useVbenModal({
  draggable: true,
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
    // modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData<SpecCategoriesApi.SpecCategorries>();
      if (data) {
        formData.value = data;
        formApi.setValues(formData.value);
      }
    }
  },
  title: '新增质量标准分类',
});

async function onSubmit() {
  try {
    message.loading({
      content: '正在提交中...',
      duration: 0,
      key: 'is-form-submitting',
    });
    modalApi.lock();

    const data =
      (await formApi.getValues()) as SpecCategoriesApi.SpecCategorries;
    // 调用添加分类 API
    await addSpecCategory(data);
    emit('success');
    modalApi.close();
    message.success({
      content: '新增成功',
      duration: 2,
      key: 'is-form-submitting',
    });
  } catch (error) {
    message.error({
      content: `新增失败：${(error as Error).message}`,
      key: 'is-form-submitting',
    });
    modalApi.close();
  } finally {
    modalApi.unlock();
  }
}
</script>

<template>
  <Modal>
    <Form />
    <template #prepend-footer>
      <div class="flex-auto">
        <!--      <Button type="primary" danger @click="resetForm">
          {{ $t('common.reset') }}
        </Button> -->
      </div>
    </template>
  </Modal>
</template>
