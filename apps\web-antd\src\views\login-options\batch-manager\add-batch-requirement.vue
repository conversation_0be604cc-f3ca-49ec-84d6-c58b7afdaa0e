<script lang="ts" setup>
import type { BatcheManagerApi } from '#/api/login-options/batch-manager';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  getCondition,
  getSampleForLab,
  getSamplePosition,
  getSampleReqUnit,
  getSampleSize,
  getSampleType,
} from '#/api/business-static-tables/sample-groups';
import { addAdHocSamplingRequirement } from '#/api/login-options/batch-manager';
import { $t } from '#/locales';

const emit = defineEmits(['success']);

const formData = ref<BatcheManagerApi.BatchSamplingRequirement>();

const sBatchId = ref<string>('');
const sampleGroupCode = ref<string>('');

const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 提交函数
  handleSubmit: onSubmit,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'vertical',
  schema: [
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: getSamplePosition,
        labelField: 'Text',
        valueField: 'Text',
      },
      fieldName: 'SAMPLINGPOSITION',
      label: $t('login-options.batchManager.samplingPosition'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: getSampleSize,
        labelField: 'Text',
        valueField: 'Value',
      },
      fieldName: 'SAMPLESIZE',
      label: $t('login-options.batchManager.sampleSize'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'NUMBEROFCONTAINERS',
      label: $t('login-options.batchManager.numberOfContainers'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: getSampleType,
        labelField: 'TEXT',
        valueField: 'VALUE',
      },
      fieldName: 'SAMPLE_TYPE',
      label: $t('login-options.batchManager.sampleType'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'CONTAINERQTY',
      label: $t('login-options.batchManager.containerQty'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: getSampleReqUnit,
        labelField: 'UNIT_NAME',
        valueField: 'UNIT_CODE',
      },
      fieldName: 'CONTAINER_UNITS',
      label: $t('login-options.batchManager.containerUnits'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: getSampleForLab.bind(null, 'SITE1'),
        labelField: 'SERVGRP',
        valueField: 'SERVGRP',
      },
      fieldName: 'FORLAB',
      label: $t('login-options.batchManager.forLab'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      // 对应组件的参数
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: getCondition,
        labelField: 'CONDITION',
        valueField: 'CONDITION',
      },
      fieldName: 'CONDITION',
      label: $t('login-options.batchManager.condition'),
      rules: 'required',
    },
    {
      component: 'Checkbox',
      fieldName: 'CREATEINVENTORYID',
      label: $t('business-static-tables.sampleGroups.createInventoryId'),
    },
  ],
  // wrapperClass: 'grid-cols-1',
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  draggable: true,
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
    // modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data =
        modalApi.getData<BatcheManagerApi.BatchSamplingRequirement>();
      if (data) {
        sBatchId.value = data.BATCHID;
        sampleGroupCode.value = data.SAMPLEGROUPCODE;
        formData.value = data;
        formApi.setValues(formData.value);
      }
    }
  },
  title: '新增取样要求',
});

async function onSubmit() {
  try {
    message.loading({
      content: '正在提交中...',
      duration: 0,
      key: 'is-form-submitting',
    });
    modalApi.lock();

    const data =
      (await formApi.getValues()) as BatcheManagerApi.BatchSamplingRequirement;
    // 调用添加分类 API
    data.SAMPLEGROUPCODE = sampleGroupCode.value;
    data.BATCHID = sBatchId.value;
    data.CREATEINVENTORYID = data.CREATEINVENTORYID ? 'Y' : 'N';
    data.NUMBEROFCONTAINERS = Number(data.NUMBEROFCONTAINERS);
    await addAdHocSamplingRequirement(data);

    emit('success');
    modalApi.close();
    message.success({
      content: '新增成功',
      duration: 2,
      key: 'is-form-submitting',
    });
  } catch (error) {
    message.error({
      content: `新增失败：${(error as Error).message}`,
      key: 'is-form-submitting',
    });
    modalApi.close();
  } finally {
    modalApi.unlock();
  }
}
</script>

<template>
  <Modal>
    <Form />
    <template #prepend-footer>
      <div class="flex-auto"></div>
    </template>
  </Modal>
</template>
