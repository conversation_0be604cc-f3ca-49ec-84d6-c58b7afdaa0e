<script setup lang="ts">
import type { SampleGroupsApi } from '#/api/business-static-tables/sample-groups';

import { ref, watch } from 'vue';

import { confirm, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import {
  deleteRecipe,
  getRecipeList,
  retireRecipe,
} from '#/api/business-static-tables/sample-groups';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';
import CopyRecipeForm from '#/views/materials-management/material-manager/copy-recipe.vue';
import RecipeDetails from '#/views/materials-management/material-manager/recipe-details.vue';

import AddRecipeForm from './add-recipe.vue';
import { useRecipeColumns, useRecipeFilterSchema } from './sample-groups-data';

const props = defineProps<{
  currentTestRow: null | SampleGroupsApi.IpSampleGroups;
}>();

const isRowEditDisabled = ref(false);

watch(
  () => props.currentTestRow,
  (_val) => {
    onRefresh();
  },
);
const colums = useRecipeColumns();
const filterSchema = useRecipeFilterSchema();
const queryData = async () => {
  if (!props.currentTestRow) return [];
  const data = await getRecipeList(
    props.currentTestRow.MATCODE,
    props.currentTestRow.SAMPLEGROUPCODE,
  );
  return data.items;
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};

const {
  Grid: RecipeGrid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
  CurrentRow,
} = useLimsGridsConfig<SampleGroupsApi.Recipes>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

function onRefresh() {
  gridApi.query();
}

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: RecipeDetails,
  destroyOnClose: true,
  class: 'w-[900px]',
});

function viewRecipeDetails(row: SampleGroupsApi.Recipes) {
  formDrawerApi.setData(row).open();
}

// 添加配方
const [RecipeFormModal, recipeFormModalApi] = useVbenModal({
  connectedComponent: AddRecipeForm,
});

async function onCreate() {
  if (props.currentTestRow === null) return;
  const matcode = props.currentTestRow.MATCODE;
  const sampleGroupCode = props.currentTestRow.SAMPLEGROUPCODE;
  recipeFormModalApi
    .setData({ MATCODE: matcode, SAMPLEGROUPCODE: sampleGroupCode })
    .open();
}

// 删除配方
async function onDelete() {
  // 获取选中行
  const recipe = gridApi.grid?.getCurrentRecord();
  if (!recipe) return;

  const sOrigrec = recipe.REF_ORIGREC;
  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的配方数据吗？`,
      icon: 'warning',
      centered: false,
    });

    await deleteRecipe(sOrigrec);

    message.success('删除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

// 复制配方
const [CopyRecipeFormModal, copyRecipeFormModalApi] = useVbenModal({
  connectedComponent: CopyRecipeForm,
});
async function onCopy() {
  const recipe = gridApi.grid?.getCurrentRecord();
  if (!recipe) return;
  copyRecipeFormModalApi.setData(recipe).open();
}

// 废弃配方
async function onRetired() {
  // 获取选中行
  const recipe = gridApi.grid?.getCurrentRecord();
  if (!recipe) return;
  const recipeCode = recipe.RECIPECODE;

  try {
    await confirm({
      title: '确认废弃',
      content: `确定要废弃选中的配方吗？`,
      icon: 'warning',
      centered: false,
    });
    await retireRecipe(recipeCode);

    message.success('废弃成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

const isReleaseRecipeDisabled = ref(false);
const isDeleteRecipeDisabled = ref(false);
const isRetireRecipeDisabled = ref(false);

watch(
  () => CurrentRow,
  (_val) => {
    const status = getStatusOfRecipe();
    switch (status) {
      case 'Active': {
        isRetireRecipeDisabled.value = false;
        isDeleteRecipeDisabled.value = true;
        isReleaseRecipeDisabled.value = true;
        break;
      }

      case 'Expired': {
        isRetireRecipeDisabled.value = false;
        isDeleteRecipeDisabled.value = true;
        isReleaseRecipeDisabled.value = true;
        break;
      }

      case 'Retired': {
        isRetireRecipeDisabled.value = true;
        isDeleteRecipeDisabled.value = true;
        isReleaseRecipeDisabled.value = true;
        break;
      }
      default: {
        isRetireRecipeDisabled.value = true;
        isDeleteRecipeDisabled.value = false;
        isReleaseRecipeDisabled.value = false;
      }
    }
  },
);

function getStatusOfRecipe() {
  const row = CurrentRow.value;
  if (!row) return;
  const expDate = new Date(row.EXPDATE);

  if (row.RETIREDDAT) {
    return 'Retired';
  }
  if (row.STARTDDATE && expDate && expDate > new Date()) {
    return 'Active';
  }
  if (row.STARTDDATE && expDate && expDate <= new Date()) {
    return 'Expired';
  }
  return 'Unknown';
}
</script>

<template>
  <RecipeFormModal @success="onRefresh" />
  <CopyRecipeFormModal @success="onRefresh" />
  <FormDrawer @success="onRefresh" />
  <div class="h-[350px] w-full">
    <RecipeGrid>
      <template #toolbar-actions>
        <Space :size="[4, 0]" wrap>
          <Button type="primary" @click="onCreate">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button
            type="primary"
            danger
            @click="onDelete"
            :disabled="isDeleteRecipeDisabled"
          >
            {{ $t('ui.actionTitle.delete') }}
          </Button>
          <Button type="default" @click="onCopy">
            {{ $t('business-static-tables.copy') }}
          </Button>
          <Button type="default" :disabled="isReleaseRecipeDisabled">
            {{ $t('business-static-tables.release') }}
          </Button>
          <Button
            type="default"
            @click="onRetired"
            :disabled="isRetireRecipeDisabled"
          >
            {{ $t('business-static-tables.retire') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent(row)">
            {{ $t('business-static-tables.save') }}
          </Button>
          <Button type="link" @click="cancelRowEvent(row)">
            {{ $t('business-static-tables.cancel') }}
          </Button>
        </template>
        <template v-else>
          <Button
            type="link"
            @click="editRowEvent(row)"
            :disabled="isRowEditDisabled"
          >
            {{ $t('business-static-tables.edit') }}
          </Button>
        </template>
        <Button type="link" @click="viewRecipeDetails(row)">
          {{ $t('business-static-tables.detail') }}
        </Button>
      </template>
    </RecipeGrid>
  </div>
</template>

<style scoped></style>
