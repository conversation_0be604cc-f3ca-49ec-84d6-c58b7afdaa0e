<script lang="ts" setup>
import type { BatcheManagerApi } from '#/api/login-options/batch-manager';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import { getParentBatchSamplingRequirement } from '#/api/receive-inlab/receive-inlab';
import { $t } from '#/locales';

// const emit = defineEmits(['success']);

const formData = ref<BatcheManagerApi.BatchSamplingRequirement>();

const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // handleSubmit: onSubmit,
  layout: 'horizontal',
  schema: [
    {
      component: 'Input',
      fieldName: 'INVENTORYID',
      label: $t('login-options.batchManager.inventoryId'),
    },
    {
      component: 'Input',
      fieldName: 'SAMPLINGPOSITION',
      label: $t('login-options.batchManager.samplingPosition'),
    },
    {
      component: 'Input',
      fieldName: 'FORLAB',
      label: $t('login-options.batchManager.forLab'),
    },
    {
      component: 'Input',
      fieldName: 'SAMPLESIZE',
      label: $t('login-options.batchManager.sampleSize'),
    },
    {
      component: 'Input',
      fieldName: 'CONTAINERQTY',
      label: $t('login-options.batchManager.containerQty'),
    },
    {
      component: 'Input',
      fieldName: 'CONTAINER_UNITS',
      label: $t('login-options.batchManager.containerUnits'),
    },
    {
      component: 'Input',
      fieldName: 'RECEIVEDBY',
      label: $t('login-options.batchManager.receivedBy'),
    },
    {
      component: 'Input',
      fieldName: 'RECEIVEDDATE',
      label: $t('login-options.batchManager.receivedDate'),
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  draggable: true,
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    // await formApi.validateAndSubmitForm();
    modalApi.close();
  },
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data =
        modalApi.getData<BatcheManagerApi.BatchSamplingRequirement>();
      const returnData = await getParentBatchSamplingRequirement(
        data.FROMINVENTORYID,
      );
      formData.value = returnData
        .items[0] as BatcheManagerApi.BatchSamplingRequirement;
      formApi.setValues(formData.value);
      formApi.setState({ commonConfig: { disabled: true } });
    }
  },
  title: '父取样要求',
});
</script>

<template>
  <Modal>
    <Form />
    <template #prepend-footer>
      <div class="flex-auto"></div>
    </template>
  </Modal>
</template>
