import type { VxeColumnPropTypes } from 'vxe-table';

import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { MaterialTypesApi } from '#/api/basic-static-tables/material-types';

import { $t } from '#/locales';

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'MATTYPE',
      label: $t('basic-static-table.material-types.mattype'),
    },
    {
      component: 'Input',
      fieldName: 'DESCRIPTION',
      label: $t('basic-static-table.material-types.description'),
    },
  ];
}

// 材料类型详情列表
export function mattypeDetailColumns(
  batchcrysReportIdEditRender: VxeColumnPropTypes.EditRender,
  requestFormCryIdEditRender: VxeColumnPropTypes.EditRender,
  labelSampleEditRender: VxeColumnPropTypes.EditRender,
  sampleElnIdEditRender: VxeColumnPropTypes.EditRender,
  sigModeEditRender: VxeColumnPropTypes.EditRender,
): VxeTableGridOptions<MaterialTypesApi.MaterialTypesDetail>['columns'] {
  return [
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'MATTYPE',
      title: $t('basic-static-tables.material-types-detail.mattype'),
      minWidth: 180,
      visible: false,
    },
    {
      field: 'DEPT',
      title: $t('basic-static-tables.material-types-detail.dept'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'BATCHCRYSREPORTID',
      title: $t('basic-static-tables.material-types-detail.batchcrysreportid'),
      minWidth: 200,
      editRender: batchcrysReportIdEditRender,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'REQUESTFORM_CRYID',
      title: $t('basic-static-tables.material-types-detail.requestform_cryid'),
      minWidth: 200,
      editRender: requestFormCryIdEditRender,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'CERT_SAMPLING',
      title: $t('basic-static-tables.material-types-detail.cert_sampling'),
      minWidth: 200,
      editRender: labelSampleEditRender,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'LABEL_SAMPLE_TEST',
      title: $t('basic-static-tables.material-types-detail.label_sample_test'),
      width: 200,
      editRender: labelSampleEditRender,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'LABEL_SAMPLE_RETAIN',
      title: $t(
        'basic-static-tables.material-types-detail.label_sample_retain',
      ),
      minWidth: 200,
      editRender: labelSampleEditRender,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SAMPLE_ELNID',
      title: $t('basic-static-tables.material-types-detail.sample_elnid'),
      minWidth: 200,
      editRender: sampleElnIdEditRender,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SAMPLECYCLE',
      title: $t('basic-static-tables.material-types-detail.samplecycle'),
      minWidth: 200,
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'NEEDAPPEARANCEINSPECTION',
      title: $t(
        'basic-static-tables.material-types-detail.needappearanceinspection',
      ),
      minWidth: 200,
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'MATINVENTORY_SIGMODE',
      title: $t(
        'basic-static-tables.material-types-detail.matinventory_sigmode',
      ),
      minWidth: 200,
      editRender: sigModeEditRender,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'ADDINVENTORY_SIGMODE',
      title: $t(
        'basic-static-tables.material-types-detail.addinventory_sigmode',
      ),
      minWidth: 200,
      editRender: sigModeEditRender,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'STOCKTAKING_SIGMODE',
      title: $t(
        'basic-static-tables.material-types-detail.stocktaking_sigmode',
      ),
      minWidth: 200,
      editRender: sigModeEditRender,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'DESTRUCTION_SIGMODE',
      title: $t(
        'basic-static-tables.material-types-detail.destruction_sigmode',
      ),
      minWidth: 200,
      editRender: sigModeEditRender,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'RESERVED_RECEIVE_SIGN_MODEL',
      title: $t(
        'basic-static-tables.material-types-detail.reserve_receive_sign_model',
      ),
      minWidth: 200,
      editRender: sigModeEditRender,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'RESERVED_DESTROY_SIGN_MODEL',
      title: $t(
        'basic-static-tables.material-types-detail.reserved_destroy_sign_model',
      ),
      minWidth: 200,
      editRender: sigModeEditRender,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'RESERVED_OBSERVE_SIGN_MODEL',
      title: $t(
        'basic-static-tables.material-types-detail.reserved_observe_sign_model',
      ),
      width: 200,
      editRender: sigModeEditRender,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      slots: { default: 'action' },
      align: 'center',
      field: 'operation',
      fixed: 'right',
      title: $t('basic-static-tables.operation'),
      minWidth: 200,
    },
  ];
}

// 材料类型列表
export function mattypeColumns(): VxeTableGridOptions<MaterialTypesApi.MaterialTypes>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'origrec',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'MATTYPE',
      title: $t('basic-static-tables.material-types.mattype'),
      minWidth: 400,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'ISPRODUCEMETRIAL',
      title: $t('basic-static-tables.material-types.isproducematrial'),
      minWidth: 200,
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'DEPT',
      title: $t('basic-static-tables.material-types.dept'),
      visible: false,
    },
    {
      align: 'center',
      field: 'DESCRIPTION',
      title: $t('basic-static-tables.material-types.description'),
      minWidth: 100,
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      field: 'operation',
      fixed: 'right',
      title: $t('basic-static-tables.operation'),
      width: 180,
    },
  ];
}

// 子类型列表
export function childTypeColumns(): VxeTableGridOptions<MaterialTypesApi.ChildType>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      align: 'center',
      field: 'MATTYPE',
      title: $t('basic-static-tables.material-types.mattype'),
      minWidth: 400,
      visible: false,
    },
    {
      align: 'center',
      field: 'TYPENAME',
      title: $t('basic-static-tables.material-child-types.typename'),
      minWidth: 400,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'TYPEDESC',
      title: $t('basic-static-tables.material-child-types.typedesc'),
      minWidth: 500,
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      field: 'operation',
      fixed: 'right',
      title: $t('basic-static-tables.operation'),
      minWidth: 400,
    },
  ];
}

// 外观验收项目列表
export function materialAppearanceItemColumns(): VxeTableGridOptions<MaterialTypesApi.AppearanceItem>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 50,
      visible: false,
    },
    {
      align: 'center',
      field: 'MATTYPE',
      title: $t('basic-static-tables.material-types.mattype'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'DEPT',
      title: $t('basic-static-tables.material-types.dept'),
      minWidth: 200,
      visible: false,
    },
    {
      align: 'center',
      field: 'SORTER',
      title: $t('basic-static-tables.material-appearance-item.sorter'),
      minWidth: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ITEMNAME',
      title: $t('basic-static-tables.material-appearance-item.itemname'),
      minWidth: 200,
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      field: 'operation',
      fixed: 'right',
      title: $t('basic-static-tables.operation'),
      minWidth: 200,
    },
  ];
}

// 外观验收项目结果列表
export function materialAppearanceItemResultColumns(): VxeTableGridOptions<MaterialTypesApi.AppearanceItemResult>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('basic-static-tables.origrec'),
      minWidth: 50,
      visible: false,
    },
    {
      field: 'SORTER',
      title: $t('basic-static-tables.material-appearance-item-result.sorter'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'ITEMID',
      title: $t('basic-static-tables.material-appearance-item-result.itemid'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'FILLTYPE',
      title: $t('basic-static-tables.material-appearance-item-result.filltype'),
      minWidth: 150,
      editRender: {
        name: 'select',
        options: [
          { value: 'COMBOBOX', label: 'COMBOBOX' },
          { value: 'TEXTBOX', label: 'TEXTBOX' },
          { value: 'DATETIME', label: 'DATETIME' },
        ],
      },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'RESULTSVALUE',
      title: $t(
        'basic-static-tables.material-appearance-item-result.resultsvalue',
      ),
      minWidth: 200,
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      field: 'operation',
      fixed: 'right',
      title: $t('basic-static-tables.operation'),
      minWidth: 200,
    },
  ];
}
