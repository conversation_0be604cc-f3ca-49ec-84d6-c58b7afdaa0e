<script lang="ts" setup>
import type { HolidaysApi } from '#/api/basic-static-tables/holidays';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { $addHolidayApi } from '#/api/basic-static-tables/holidays';
import { $t } from '#/locales';

import { useAddHolidaySchema } from './holidays-calendar-data';

const emit = defineEmits(['success']);

const getTitle = computed(() => {
  return $t('ui.actionTitle.create');
});

const formData = ref<HolidaysApi.Holidays>();

const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'vertical',
  schema: useAddHolidaySchema(),
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    modalApi.lock();
    try {
      if (typeof data.ISWORKDAY === 'boolean') {
        data.ISWORKDAY = data.ISWORKDAY ? 'Y' : 'N';
      }
      await $addHolidayApi(data);
      emit('success');
      message.success($t('ui.actionMessage.operationSuccess'));
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<HolidaysApi.Holidays>();
      if (data) {
        formData.value = data;
        formApi.setValues(formData.value);
      }
    }
  },
});
</script>
<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
  </Modal>
</template>
