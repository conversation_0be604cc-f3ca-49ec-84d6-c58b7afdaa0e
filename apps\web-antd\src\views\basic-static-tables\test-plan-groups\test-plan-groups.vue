<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { TestPlanGroupsApi } from '#/api/basic-static-tables/test-plan-groups';

import { confirm, Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Button, message, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteTestPlanGroup,
  getTestPlanGroupList,
} from '#/api/basic-static-tables/test-plan-groups';

import AddTestPlanGroupForm from './add-test-plan-group.vue';
import Detail from './dept.vue';
import {
  useTestPlanGroupsColumns,
  useTestPlanGroupsFilterSchema,
} from './test-plan-groups-data';

const gridOptions: VxeTableGridOptions<TestPlanGroupsApi.TestPlanGroup> = {
  columns: useTestPlanGroupsColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getTestPlanGroupList();
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const formOptions: VbenFormProps = {
  schema: useTestPlanGroupsFilterSchema(),
  submitOnChange: false,
  collapsed: true,
  showCollapseButton: true,
  submitButtonOptions: {
    content: '查询',
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  formOptions,
});

function hasEditStatus(row: TestPlanGroupsApi.TestPlanGroup) {
  return gridApi.grid?.isEditByRow(row);
}

function editRowEvent(row: TestPlanGroupsApi.TestPlanGroup) {
  gridApi.grid?.setEditRow(row);
}

async function saveRowEvent() {
  await gridApi.grid?.clearEdit();
  // updateSubLocationType(row);
  gridApi.setLoading(true);
  setTimeout(() => {
    gridApi.setLoading(false);
    message.success({
      content: `保存成功！`,
    });
  }, 600);
}

const cancelRowEvent = (_row: TestPlanGroupsApi.TestPlanGroup) => {
  gridApi.grid?.clearEdit();
};

function onRefresh() {
  gridApi.query();
}

// 添加质量标准分类
const [FormModal, ModalApi] = useVbenModal({
  connectedComponent: AddTestPlanGroupForm,
});

async function addTestPlanGroup() {
  ModalApi.setData(null).open();
}

// 删除质量标准分类
async function deleteTestPlanGroupConfirm() {
  // 获取选中行
  const aProdGroup: string[] = gridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.PRODGROUP);

  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${aProdGroup.length} 条数据吗？`,
      icon: 'warning',
      centered: false,
    });

    await deleteTestPlanGroup(aProdGroup);

    message.success('删除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: Detail,
  destroyOnClose: true,
  class: 'w-[900px]',
});

function onDetail(row: TestPlanGroupsApi.TestPlanGroup) {
  formDrawerApi.setData(row).open();
}
</script>
<template>
  <Page auto-content-height>
    <FormDrawer @success="onRefresh" />
    <FormModal @success="onRefresh" />
    <Grid>
      <template #toolbar-actions>
        <Space>
          <Button type="primary" @click="addTestPlanGroup">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="deleteTestPlanGroupConfirm">
            {{ $t('ui.actionTitle.delete') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent()">
            {{ $t('basic-static-tables.save') }}
          </Button>
          <Button type="link" @click="cancelRowEvent(row)">
            {{ $t('basic-static-tables.cancel') }}
          </Button>
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">
            {{ $t('basic-static-tables.edit') }}
          </Button>
        </template>
        <Button type="link" @click="onDetail(row)">
          {{ $t('basic-static-tables.dept') }}
        </Button>
      </template>
    </Grid>
  </Page>
</template>
