<script lang="ts" setup>
import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message, Transfer } from 'ant-design-vue';

import { getAnalytesByMultiChoice } from '#/api/business-static-tables/sample-groups';

// 定义Transfer数据项类型
interface TransferItem {
  key: string;
  title: string;
  description?: string;
}

const props = defineProps<{
  aAnalytes: string[];
  drawNo: number;
  profile: string;
  spCode: number;
  testCode: number;
}>();

const emit = defineEmits<{
  (
    e: 'submit',
    data: { selectedItems: TransferItem[]; selectedKeys: string[] },
  ): void;
  (e: 'cancel'): void;
}>();

// 数据源与状态
const transferAnalyteData = ref<TransferItem[]>([]);
const targetAnalyteKeys = ref<string[]>([]);
const loading = ref(false);
const error = ref<string>('');

// 计算属性
const selectedItems = computed(() => {
  return transferAnalyteData.value.filter((item) =>
    targetAnalyteKeys.value.includes(item.key),
  );
});

// 获取数据
async function fetchData() {
  loading.value = true;
  error.value = '';

  try {
    const result = await getAnalytesByMultiChoice(
      props.spCode,
      props.drawNo,
      props.testCode,
      props.profile,
    );
    const data =
      result.items?.map((item: { TEXT: string; VALUE: string }) => ({
        key: item.VALUE,
        title: item.TEXT,
        description: item.TEXT || '',
      })) || [];

    transferAnalyteData.value = data;

    // 设置初始选中项
    targetAnalyteKeys.value =
      props.aAnalytes.length > 0 ? [...props.aAnalytes] : [];
  } catch (error_) {
    error.value = `数据加载失败: ${(error_ as Error).message}`;
    message.error(error.value);
  } finally {
    loading.value = false;
  }
}

// 使用VbenModal
const [TransferAnalyteModal, modalApi] = useVbenModal({
  onOpenChange: async (isOpen: boolean) => {
    // console.log(props.testCode);
    // console.log(props.aAnalytes);
    if (isOpen && props.testCode !== null && props.aAnalytes.length > 0) {
      await fetchData();
    }
  },
  onConfirm: () => {
    handleSubmit();
  },
  onCancel: () => {
    handleCancel();
  },
});

// 处理提交
function handleSubmit() {
  const result = {
    selectedKeys: targetAnalyteKeys.value,
    selectedItems: selectedItems.value,
  };
  // console.log(result);
  emit('submit', result);
  // message.success(`已选择 ${targetKeys.value.length} 项`);
  modalApi.close();
}

// 处理取消
function handleCancel() {
  emit('cancel');
  modalApi.close();
}

// 暴露方法给父组件
defineExpose({
  open: () => modalApi.open(),
  close: () => modalApi.close(),
  setData: (data: any) => modalApi.setData(data),
});
</script>

<template>
  <TransferAnalyteModal title="编辑分析项" class="w-[800px]" :loading="loading">
    <Transfer
      v-model:target-keys="targetAnalyteKeys"
      :data-source="transferAnalyteData"
      show-search
      :render="(item) => item.title"
      :list-style="{
        width: '400px',
        height: '400px',
      }"
      :operations="['选择', '移除']"
    />
  </TransferAnalyteModal>
</template>
