import { callServer, getDataSet, getDataSetNoPage } from '#/api/core/witlab';

export namespace HolidaysApi {
  export interface NonWorkingDays {
    [key: string]: any;
    ORIGREC: number;
    MON: string;
    TUE: string;
    WED: string;
    THU: string;
    FRI: string;
    SAT: string;
    SUN: string;
  }

  export interface Holidays {
    [key: string]: any;
    ORIGREC: number;
    DEPT: string;
    HOL_DATE: string;
    HOL_NAME: string;
    HOL_DESCRIPTION: string;
    HOL_YEAR: string;
    ISWORKDAY: string;
  }
}

/**
 * 获取非工作日
 * @returns {HolidaysApi.NonWorkingDays[]} 返回非工作日的数据数组
 */
async function $getNonWorkingDaysApi({
  site,
}: {
  site: string;
}): Promise<HolidaysApi.NonWorkingDays[]> {
  return await getDataSetNoPage('Holidays.getNonWorkingDays', [site]);
}

/**
 * 获取假期
 * @returns {HolidaysApi.Holidays[]} 返回假期数据数组
 */
async function $getHolidaysApi({ site }: { site: string }) {
  return await getDataSet('Holidays.getHolidays', [site]);
}

async function $addHolidayApi(
  data: Omit<HolidaysApi.Holidays, 'ORIGREC'>,
  eventCode: string = 'AddHoliday',
  comment: string = '',
) {
  return await callServer('Holidays.addHoliday', [
    data.DEPT,
    data.HOL_DATE,
    data.HOL_NAME,
    data.HOL_DESCRIPTION,
    data.ISWORKDAY,
    eventCode,
    comment,
  ]);
}

async function $deleteHoliday(origrecs: number[] | string[]) {
  return await callServer('Holidays.deleteHoliday', [origrecs]);
}

async function $copyHoliday({
  fromSite,
  fromYear,
  toSite,
  toYear,
}: {
  fromSite: string;
  fromYear: string;
  toSite: string;
  toYear: string;
}) {
  return await callServer('Holidays.copyHoliday', [
    fromSite,
    fromYear,
    toSite,
    toYear,
  ]);
}

async function $getSitesApi() {
  return await getDataSetNoPage('Holidays.getSites', []);
}

async function $getYearsApi(data: { dept: string; fromSysData: string }) {
  return await getDataSetNoPage('Holidays.getYears', [
    data.fromSysData,
    data.dept,
  ]);
}

export {
  $addHolidayApi,
  $copyHoliday,
  $deleteHoliday,
  $getHolidaysApi,
  $getNonWorkingDaysApi,
  $getSitesApi,
  $getYearsApi,
};
