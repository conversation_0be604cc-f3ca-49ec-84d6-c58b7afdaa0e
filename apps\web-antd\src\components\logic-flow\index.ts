/**
 * 审批工作流组件库入口文件
 */

// 子组件
export { default as WorkflowNodePanel } from './components/WorkflowNodePanel.vue';
export { default as WorkflowPropertyPanel } from './components/WorkflowPropertyPanel.vue';

// 配置和工具
export {
  APPROVAL_TYPE_OPTIONS,
  CONDITION_OPERATORS,
  DEFAULT_WORKFLOW_DATA,
  getAllNodeTypes,
  getNodeConfig,
  getNodePanelConfig,
  WORKFLOW_NODE_CONFIGS,
  WORKFLOW_TEMPLATES,
} from './config/nodes';
// 兼容旧版本的导出
export { default as LogicFlow } from './logic-flow.vue';

export { default as TurboAdapter } from './turbo-adapter.vue';

// 类型定义
export type {
  Approver,
  ConditionRule,
  NodeConfig,
  NodeProperties,
  NodeType,
  ValidationResult,
  WorkflowComponentEvents,
  WorkflowComponentMethods,
  WorkflowComponentProps,
  WorkflowData,
  WorkflowEdge,
  WorkflowMode,
  WorkflowNode,
  WorkflowTemplate,
} from './types/workflow';

export { validateWorkflow, WorkflowValidator } from './utils/validator';

// 主要组件
export { default as WorkflowDesigner } from './WorkflowDesigner.vue';
export { default as WorkflowExample } from './WorkflowExample.vue';
