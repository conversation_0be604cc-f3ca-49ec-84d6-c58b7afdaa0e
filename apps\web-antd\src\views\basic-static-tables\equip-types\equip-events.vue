<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { EquipmentTypeApi } from '#/api/basic-static-tables/equip-types';

import { computed, ref } from 'vue';

import { confirm, Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addEquipmentEvent,
  deleteEquipmentEvent,
  getEquipmentEventList,
} from '#/api/basic-static-tables/equip-types';

import AddEquipEventForm from './add-equip-event.vue';
import { useEquipmentEventColumns } from './equip-types-data';

const equipTypeData = ref<EquipmentTypeApi.EquipmentType>();

const gridOptions: VxeTableGridOptions<EquipmentTypeApi.EquipmentEvent> = {
  columns: useEquipmentEventColumns(),
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        const data = drawerApi.getData<EquipmentTypeApi.EquipmentType>();
        if (!data) {
          return;
        }
        const EQTYPE = data.EQTYPE;
        return await getEquipmentEventList(EQTYPE);
      },
    },
  },
  showOverflow: true,
  exportConfig: {},
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddEquipEventForm,
  destroyOnClose: true,
});

function hasEditStatus(row: EquipmentTypeApi.EquipmentEvent) {
  return gridApi.grid?.isEditByRow(row);
}

function editRowEvent(row: EquipmentTypeApi.EquipmentEvent) {
  gridApi.grid?.setEditRow(row);
}

async function saveRowEvent(row: EquipmentTypeApi.EquipmentEvent) {
  await gridApi.grid?.clearEdit();
  gridApi.setLoading(true);
  addEquipmentEvent(row);
  setTimeout(() => {
    gridApi.setLoading(false);
    message.success({
      content: `保存成功！`,
    });
  }, 600);
}

const cancelRowEvent = (_row: EquipmentTypeApi.EquipmentEvent) => {
  gridApi.grid?.clearEdit();
};

const [Drawer, drawerApi] = useVbenDrawer({
  onConfirm: onSubmit,
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<EquipmentTypeApi.EquipmentType>();
      if (data) {
        equipTypeData.value = data;
        gridApi?.query();
      } else {
        gridApi?.reload();
      }
    }
  },
  showCancelButton: false,
  showConfirmButton: false,
});

async function onSubmit() {
  drawerApi.close();
}

const getDrawerTitle = computed(() => {
  if (equipTypeData.value) {
    return `查看 ${equipTypeData.value.EQTYPE}`;
  }
  return '设备维护事件';
});

function onCreate() {
  formModalApi.setData({ EQTYPE: equipTypeData.value?.EQTYPE }).open();
}

async function onDelete() {
  // 获取选中行
  const aEqipType: string[] = gridApi.grid
    .getCheckboxRecords()
    .map((row) => row.EQTYPE) as string[];

  if (aEqipType.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  if (aEqipType.length > 1) {
    message.warning('只能选择一条数据！');
    return;
  }

  const aOrigrec: number[] = gridApi.grid
    .getCheckboxRecords()
    .map((row) => row.ORIGREC) as number[];

  const aMaintenanceEvent: string[] = gridApi.grid
    .getCheckboxRecords()
    .map((row) => row.MAINTENANCEEVENT) as string[];

  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${aEqipType.length} 条数据吗？`,
      icon: 'warning',
      centered: false,
    });

    const sOrigrec = aOrigrec[0] as number;
    const sMaintenanceEvent = aMaintenanceEvent[0] as string;
    const sEqipType = aEqipType[0] as string;
    await deleteEquipmentEvent(sOrigrec, sMaintenanceEvent, sEqipType);

    message.success('删除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

function onRefresh() {
  gridApi.query();
}
</script>

<template>
  <Drawer class="w-full max-w-[1150px]" :title="getDrawerTitle">
    <FormModal @success="onRefresh" />
    <Page auto-content-height>
      <Grid>
        <template #toolbar-actions>
          <Space>
            <Button type="primary" @click="onCreate">
              {{ $t('ui.actionTitle.create') }}
            </Button>
            <Button type="primary" danger @click="onDelete">
              {{ $t('ui.actionTitle.delete') }}
            </Button>
          </Space>
        </template>
        <template #action="{ row }">
          <template v-if="hasEditStatus(row)">
            <Button type="link" @click="saveRowEvent(row)">
              {{ $t('basic-static-tables.save') }}
            </Button>
            <Button type="link" @click="cancelRowEvent(row)">
              {{ $t('basic-static-tables.cancel') }}
            </Button>
          </template>
          <template v-else>
            <Button type="link" @click="editRowEvent(row)">
              {{ $t('basic-static-tables.edit') }}
            </Button>
          </template>
        </template>
      </Grid>
    </Page>
  </Drawer>
</template>
