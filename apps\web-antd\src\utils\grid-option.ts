import type { VxeTableGridOptions } from '#/adapter/vxe-table';

export function createGridOptions<T>(): VxeTableGridOptions<T> {
  return {
    stripe: true,
    border: true,
    keepSource: true,
    checkboxConfig: {
      highlight: true,
      range: true,
      labelField: 'select',
    },
    editConfig: {
      mode: 'row',
      trigger: 'manual',
    },
    height: 'auto',
    pagerConfig: {},
    exportConfig: {},
    showOverflow: true,
    rowConfig: {},
    toolbarConfig: {
      custom: true,
      export: true,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  };
}
