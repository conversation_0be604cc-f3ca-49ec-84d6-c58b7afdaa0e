import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { ConditionsApi } from '#/api/basic-static-tables/conditions';

import { $t } from '#/locales';

export function useConditionsColumns(): VxeTableGridOptions<ConditionsApi.Conditions>['columns'] {
  return [
    { field: 'select', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'CONDITION',
      title: $t('basic-static-tables.conditions.condition'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'TEMPERATURE',
      title: $t('basic-static-tables.conditions.temperature'),
      minWidth: 200,
      visible: false,
    },
    {
      align: 'center',
      field: 'TEMPERATURE_MAX',
      title: $t('basic-static-tables.conditions.temperature_max'),
      minWidth: 200,
      visible: false,
    },
    {
      align: 'center',
      field: 'TEMPERATURE_STD',
      title: $t('basic-static-tables.conditions.temperature_std'),
      minWidth: 200,
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'HUMIDITY',
      title: $t('basic-static-tables.conditions.humidity'),
      minWidth: 200,
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'LUMINOSITY',
      title: $t('basic-static-tables.conditions.luminosity'),
      minWidth: 200,
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'OTHER',
      title: $t('basic-static-tables.conditions.other'),
      minWidth: 200,
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'POSITION',
      title: $t('basic-static-tables.conditions.position'),
      minWidth: 200,
      visible: false,
    },
    {
      align: 'center',
      field: 'ELAPSE_TIME_RETURN_SAMPLE',
      title: $t('basic-static-tables.conditions.elapse_time_return_sample'),
      minWidth: 200,
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('basic-static-tables.operation'),
      minWidth: 140,
    },
  ];
}

export function useConditionsFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        // placeholder: 'Please enter code',
      },
      fieldName: 'CONDITION',
      label: $t('basic-static-tables.conditions.condition'),
    },
  ];
}

export function useConditionsTatColumns(): VxeTableGridOptions<ConditionsApi.ConditionsTat>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 100,
      visible: false,
    },
    {
      align: 'center',
      field: 'DEPT',
      title: $t('basic-static-tables.conditions-tat.dept'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'CONDITION',
      title: $t('basic-static-tables.conditions-tat.condition'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'TAT',
      title: $t('basic-static-tables.conditions-tat.tat'),
      editRender: { name: 'input' },
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'CONDITION_TAT_UNITS',
      title: $t('basic-static-tables.conditions-tat.condition_tat_units'),
      editRender: {
        name: 'select',
        options: [
          { value: 'Hour', label: 'Hour' },
          { value: 'Day', label: 'Day' },
          { value: 'Month', label: 'Month' },
          { value: 'Week', label: 'Week' },
        ],
      },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('basic-static-tables.operation'),
      minWidth: 140,
    },
  ];
}
