<script setup lang="ts">
import type { TestManagerApi } from '#/api/business-static-tables/test-manager';

import { watch } from 'vue';

import { Button, Space } from 'ant-design-vue';

import { $getTestPlanListApi } from '#/api/business-static-tables/test-manager';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import { useMethodTestPlanSchema, useTestPlanColumns } from './data';

const props = defineProps<{
  currentTestRow: null | TestManagerApi.Test;
}>();

watch(
  () => props.currentTestRow,
  (_val) => {
    onRefresh();
  },
);
const colums = useTestPlanColumns();
const filterSchema = useMethodTestPlanSchema();
const queryData = async () => {
  if (!props.currentTestRow) {
    return [];
  }
  return await $getTestPlanListApi({ testCode: props.currentTestRow.TESTCODE });
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};

const { Grid: MethedRelateTestGrid, gridApi } =
  useLimsGridsConfig<TestManagerApi.TestPlan>(
    colums,
    filterSchema,
    queryData,
    girdOption,
  );

function viewSpec() {
  formModalApi.setData(null).open();
}

function onRefresh() {
  gridApi.query();
}
</script>

<template>
  <MethedRelateTestGrid>
    <template #toolbar-actions>
      <Space :size="[4, 0]" wrap>
        <Button type="default" @click="viewSpec">
          {{ $t('business-static-tables.testManager.viewSpec') }}
        </Button>
      </Space>
    </template>
  </MethedRelateTestGrid>
</template>
