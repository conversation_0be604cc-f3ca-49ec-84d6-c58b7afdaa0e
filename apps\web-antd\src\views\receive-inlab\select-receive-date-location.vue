<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  dsSubLocationGet,
  getLocationList,
} from '#/api/materials-management/location-management';
import {
  getSampleData,
  receiveInLabMulti,
  updReceiveAll,
} from '#/api/receive-inlab/receive-inlab';
import { $t } from '#/locales';

const emit = defineEmits(['success']);

// const formData = ref();
const sOpeningMode = ref<string>('RECEIVEINCR');

// 树形数据处理
const treeData = ref<any[]>([]);
const isLoading = ref(false);
const aOrigrec = ref<number[]>([]);

// 数据转换函数：将API返回的扁平数据转换为树形结构
const transformToTreeData = (flatData: any[]) => {
  if (!flatData || flatData.length === 0) {
    console.warn('transformToTreeData: 输入数据为空');
    return [];
  }

  // console.log('transformToTreeData: 原始数据', flatData);

  const map = new Map();
  const roots: any[] = [];

  // 第一遍遍历：创建所有节点
  flatData.forEach((item) => {
    const node = {
      // 确保字段名称正确映射
      label: item.TEXTMEMBER || '未知位置',
      value: item.NAMEMEMBER || item.ORIGREC,
      key: item.NAMEMEMBER || item.ORIGREC,
      title: item.TEXTMEMBER || '未知位置', // TreeSelect 可能需要 title 字段
      children: [],

      // 保留原始数据用于调试
      _original: item,
      _parentId: item.PARENTMEMBER,
    };

    // 使用 NAMEMEMBER 作为 map 的键
    if (node.value) {
      map.set(node.value, node);
    } else {
      console.warn('transformToTreeData: 节点缺少有效的 value', item);
    }
  });

  // console.log('transformToTreeData: 节点映射表', map);

  // 第二遍遍历：建立父子关系
  flatData.forEach((item) => {
    const nodeValue = item.NAMEMEMBER || item.ORIGREC;
    const parentValue = item.PARENTMEMBER;

    const node = map.get(nodeValue);
    if (!node) return;

    // 修复父子关系判断逻辑
    if (parentValue && parentValue !== '' && map.has(parentValue)) {
      // 有父节点，添加到父节点的children中
      const parent = map.get(parentValue);
      if (parent && parent.children) {
        parent.children.push(node);
      }
    } else {
      // 没有父节点或父节点不存在，作为根节点
      roots.push(node);
    }
  });

  // console.log('transformToTreeData: 转换后的树形数据', roots);

  // 验证数据结构
  const validateTreeData = (nodes: any[], level = 0) => {
    nodes.forEach((node) => {
      // console.log(`Level ${level}, Node ${index}:`, {
      //   label: node.label,
      //   value: node.value,
      //   hasChildren: node.children && node.children.length > 0,
      //   childrenCount: node.children ? node.children.length : 0,
      // });

      if (node.children && node.children.length > 0) {
        validateTreeData(node.children, level + 1);
      }
    });
  };

  if (roots.length > 0) {
    // console.log('=== 树形数据结构验证 ===');
    validateTreeData(roots);
  }

  return roots;
};

// 异步加载位置数据
const loadLocationData = async (parentValue?: string) => {
  try {
    isLoading.value = true;
    const result = await getLocationList('SITE1');

    if (result && result.items) {
      if (parentValue) {
        // 动态加载子节点
        return transformToTreeData(
          result.items.filter((item: any) => item.NAMEMEMBER === parentValue),
        );
      } else {
        // 初始加载所有数据
        const transformedData = transformToTreeData(result.items);
        treeData.value = transformedData;
        // console.log('transformedData', transformedData);
        return transformedData;
      }
    }
    return [];
  } catch (error) {
    console.error('加载位置数据失败:', error);
    message.error('加载位置数据失败');
    return [];
  } finally {
    isLoading.value = false;
  }
};

const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  handleSubmit: onSubmit,
  layout: 'vertical',
  schema: [
    {
      component: 'DatePicker',
      fieldName: 'COMPDAT',
      label: $t('receive-inlab.compdat'),
      rules: 'required',
    },
    {
      component: 'ApiTreeSelect',
      fieldName: 'LOCATIONCODE',
      label: $t('receive-inlab.locationcode'),
      rules: 'required',
      componentProps: {
        allowClear: true,
        placeholder: '请选择位置',
        showSearch: true,
        treeDefaultExpandAll: false,
        treeDefaultExpandedKeys: [],
        showIcon: true,
        // API配置
        // api: loadLocationData,
        treeData,
        immediate: true,

        // 字段映射配置 - 使用转换后的标准字段
        labelField: 'label',
        valueField: 'value',
        childrenField: 'children',

        // TreeSelect特有配置
        fieldNames: {
          label: 'label',
          value: 'value',
          children: 'children',
        },

        // 搜索配置 - 使用转换后的标准字段
        treeNodeFilterProp: 'label',
        filterTreeNode: (inputValue: string, treeNode: any) => {
          if (!inputValue) return true;
          const label = treeNode.label || treeNode.title || '';
          return label.toLowerCase().includes(inputValue.toLowerCase());
        },

        // 动态加载配置
        loadData: async (treeNode: any) => {
          // 动态加载子节点
          // console.log('treeNode.value', treeNode.value);
          if (treeNode.children && treeNode.children.length > 0) {
            // console.log('treeNode.children', treeNode.children);
            if (treeNode.children[0].label.includes('expand')) {
              const str = treeNode.value;
              const sId = str.split('*')[0];
              const childrenData = await dsSubLocationGet(sId);
              const result = transformToTreeData(childrenData.items);
              treeNode.children.push(...result);
              // this.$set(treeNode, 'children', result);
            } else {
              // 已有子节点，不需要加载
            }
          } else {
            try {
              const str = treeNode.value;
              const sId = str.split('*')[0];
              const childrenData = await dsSubLocationGet(sId);
              const result = transformToTreeData(childrenData.items);
              treeNode.children.push(...result);
              // treeNode.children = children;
            } catch (error) {
              console.error('动态加载子节点失败:', error);
            }
          }
        },

        // 事件处理
        // onSelect: (value: string, node: any) => {
        //   console.log('选中节点:', { value, node });
        // },

        // onTreeExpand: async (expandedKeys: string[]) => {
        //   console.log('展开的节点:', expandedKeys);
        // },

        // 加载状态
        loading: isLoading,
      },
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  draggable: true,
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
    // modalApi.close();
  },
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData<Record<string, any>>();
      // data.COMPDAT = new Date();
      // formData.value = data;
      aOrigrec.value = data.AORIGREC;
      // 初始化加载树形数据
      await loadLocationData();
    }
  },
  title: '接收样品',
});

async function onSubmit() {
  try {
    message.loading({
      content: '正在提交中...',
      duration: 0,
      key: 'is-form-submitting',
    });
    modalApi.lock();

    const data = await formApi.getValues();
    const aSampleData = await getSampleData(
      sOpeningMode.value,
      aOrigrec.value,
      data.LOCATIONCODE,
    );
    if (aSampleData === null) return;
    const aORDNOs = await receiveInLabMulti(
      'SAMPLE',
      aSampleData,
      data.COMPDAT,
      sOpeningMode.value,
    );
    if (aORDNOs) {
      await updReceiveAll(aORDNOs);
    }
    emit('success');
    modalApi.close();
    message.success({
      content: '接收成功',
      duration: 2,
      key: 'is-form-submitting',
    });
  } catch (error) {
    message.error({
      content: `接收失败：${(error as Error).message}`,
      key: 'is-form-submitting',
    });
    modalApi.close();
  } finally {
    modalApi.unlock();
  }
}
</script>

<template>
  <Modal>
    <Form />
    <template #prepend-footer>
      <div class="flex-auto"></div>
    </template>
  </Modal>
</template>
