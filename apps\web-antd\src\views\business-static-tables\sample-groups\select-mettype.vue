<script lang="ts" setup>
import type { SampleGroupsApi } from '#/api/business-static-tables/sample-groups';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message, Transfer } from 'ant-design-vue';

import { getMetTypes } from '#/api/business-static-tables/sample-groups';

// 定义Transfer数据项类型
interface TransferItem {
  key: string;
  title: string;
  description?: string;
}

const props = defineProps<{
  aMetTypes: string[];
  currentTestRow: null | SampleGroupsApi.MethodsRelSpTests;
  metTypes: string;
}>();

const emit = defineEmits<{
  (
    e: 'submit',
    data: { selectedItems: TransferItem[]; selectedKeys: string[] },
  ): void;
  (e: 'cancel'): void;
}>();

// 数据源与状态
const transferData = ref<TransferItem[]>([]);
const targetKeys = ref<string[]>([]);
const loading = ref(false);
const error = ref<string>('');

// 计算属性
const selectedItems = computed(() => {
  return transferData.value.filter((item) =>
    targetKeys.value.includes(item.key),
  );
});

// 获取数据
async function fetchData() {
  loading.value = true;
  error.value = '';

  if (props.currentTestRow === null) return;

  try {
    const result = await getMetTypes(
      props.currentTestRow.SP_CODE,
      props.currentTestRow.TESTCODE,
      props.currentTestRow.METHOD,
      props.currentTestRow.DEPT,
      props.currentTestRow.PROFILE,
      props.currentTestRow.SERVGRP,
      props.currentTestRow.DRAWNO,
      props.metTypes,
    );
    const data =
      result.items?.map((item: { TEXT: string; VALUE: string }) => ({
        key: item.VALUE,
        title: item.TEXT,
        description: item.TEXT || '',
      })) || [];

    transferData.value = data;

    // 设置初始选中项
    targetKeys.value = props.aMetTypes.length > 0 ? [...props.aMetTypes] : [];
  } catch (error_) {
    error.value = `数据加载失败: ${(error_ as Error).message}`;
    message.error(error.value);
  } finally {
    loading.value = false;
  }
}

// 使用VbenModal
const [TransferModal, modalApi] = useVbenModal({
  onOpenChange: async (isOpen: boolean) => {
    // console.log(props.testCode);
    // console.log(props.aAnalytes);
    if (isOpen) {
      await fetchData();
    }
  },
  onConfirm: () => {
    handleSubmit();
  },
  onCancel: () => {
    handleCancel();
  },
});

// 处理提交
function handleSubmit() {
  const result = {
    selectedKeys: targetKeys.value,
    selectedItems: selectedItems.value,
  };
  // console.log(result);
  emit('submit', result);
  // message.success(`已选择 ${targetKeys.value.length} 项`);
  modalApi.close();
}

// 处理取消
function handleCancel() {
  emit('cancel');
  modalApi.close();
}

// 暴露方法给父组件
defineExpose({
  open: () => modalApi.open(),
  close: () => modalApi.close(),
  setData: (data: any) => modalApi.setData(data),
});
</script>

<template>
  <TransferModal title="选择材料类型" class="w-[800px]" :loading="loading">
    <Transfer
      v-model:target-keys="targetKeys"
      :data-source="transferData"
      show-search
      :render="(item) => item.title"
      :list-style="{
        width: '400px',
        height: '400px',
      }"
      :operations="['选择', '移除']"
    />
  </TransferModal>
</template>
