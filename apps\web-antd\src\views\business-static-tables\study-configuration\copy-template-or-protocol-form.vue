<script lang="ts" setup>
import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  $copyTemplateOrProtocolApi,
  $getSitesApi,
} from '#/api/business-static-tables';
import { $t } from '#/locales';

interface FormArgs {
  mode: string;
  dept: string;
  fromStabNo: number;
}

interface FormSchema {
  Name: string;
  ToDept: string;
}
const emit = defineEmits(['success']);

const formArgs = ref<FormArgs>();

const [Form, formApi] = useVbenForm({
  layout: 'horizontal',
  schema: [
    {
      fieldName: 'Name',
      label: $t('business-static-tables.studyConfiguration.folderName'),
      component: 'Input',
      rules: 'required',
    },
    {
      fieldName: 'ToDept',
      label: $t('commons.dept'),
      component: 'ApiSelect',
      componentProps: {
        api: $getSitesApi,
        labelField: 'DEPT',
        valueField: 'DEPT',
        class: 'w-full',
      },
      rules: 'required',
    },
  ],
  showDefaultActions: false,
});
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    if (!formArgs.value) return;
    const data = await formApi.getValues<FormSchema>();
    modalApi.lock();
    try {
      const nRet = await $copyTemplateOrProtocolApi({
        bNewVersion: false,
        sOpeningMode: formArgs.value.mode,
        sName: data.Name,
        sSite: data.ToDept,
        sProdGroup: $t('business-static-tables.studyConfiguration.Stability'),
        nFromStabNo: formArgs.value.fromStabNo,
      });
      if (nRet === -100) {
        message.error(
          $t('business-static-tables.studyConfiguration.existTemplateName'),
        );
        return;
      }
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<FormArgs>();
      if (data) {
        formArgs.value = data;
      }
    }
  },
});
const getTitle = computed(() => {
  return $t('business-static-tables.studyConfiguration.copyTemplateOrProtocol');
});
</script>
<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
  </Modal>
</template>
