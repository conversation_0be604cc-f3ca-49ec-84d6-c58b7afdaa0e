import { callServer, getDataSet } from '#/api/core/witlab';
import { requestClient } from '#/api/request';

export namespace LocationTypeApi {
  export interface PageFetchParams {
    [key: string]: any;
    page: number;
    pageSize: number;
  }

  export interface LocationType {
    ORIGREC: number;
    LOCATION_TYPE_ID: number;
    TYPE_NAME: string;
    DESCRIPTION: string;
    IMAGEREF: string;
  }

  export interface LocationTypeSubLoc {
    ORIGREC: number;
    LOCATION_TYPES_SUBLOC_ID: number;
    LOCATION_TYPE_ID: number;
    NAME: string;
    PREFIX: string;
    LOCTYPE_SIZE: number;
    LOCTYPE_ORDER: string;
    NUMBERING_METHOD: string;
    IS_STORABLE: string;
    IMAGEREF: string;
  }
}

/**
 * 获取位置类型列表数据
 */
async function getLocationTypeList() {
  return getDataSet('LocationTypeManagement.dsGetLocationType', []);
}

/**
 * 添加位置类型
 * @param data 位置类型数据
 */
async function addLocationType(data: LocationTypeApi.LocationType) {
  return await callServer('LocationTypeManagement.scAddLocationType', [
    data.TYPE_NAME,
    data.IMAGEREF,
    data.DESCRIPTION,
    'AddLocationType2',
  ]);
}

/**
 * 更新位置类型
 *
 * @param data 位置类型数据
 * @returns boolean
 */
async function updateLocationType(data: LocationTypeApi.LocationType) {
  return requestClient.post<boolean>('/location-type/updateLocationType', data);
}

/**
 * 删除位置类型
 *
 * @param origrec 位置类型数据
 * @returns boolean
 */
async function deleteLocationType(sTypeId: number) {
  return await callServer('LocationTypeManagement.scDeleteLocationType', [
    sTypeId,
  ]);
}

/**
 * 获取位置布局类型列表数据
 */
async function getLocationTypeSubLocList(sTypeId: number) {
  return getDataSet('LocationTypeManagement.dsGetLocationTypeLayout', [
    sTypeId,
  ]);
}

/**
 * 获取子位置类型
 */
async function getLayoutTemplate() {
  return getDataSet('LocationTypeManagement.dsGetLayoutTemplate', []);
}

/**
 * 添加位置布局类型
 * @param data 位置布局类型数据
 */
async function addLocationTypeSubLoc(data: LocationTypeApi.LocationTypeSubLoc) {
  return await callServer('LocationTypeManagement.scAddLocationLayout', [
    data.LOCATION_TYPE_ID,
    data.NAME,
    data.LOCTYPE_SIZE,
    data.LOCTYPE_ORDER,
    data.IS_STORABLE,
  ]);
}

/**
 * 更新位置布局类型
 *
 * @param data 位置布局类型数据
 * @returns boolean
 */
async function updateLocationTypeSubLoc(
  data: LocationTypeApi.LocationTypeSubLoc,
) {
  return requestClient.post<boolean>(
    '/location-type/updateLocationTypeSubLoc',
    data,
  );
}

/**
 * 删除位置布局类型
 *
 * @param origrec 位置布局类型数据
 * @returns boolean
 */
async function deleteLocationTypeSubLoc(sLayoutId: number) {
  return await callServer('LocationTypeManagement.scDeleteLocationLayout', [
    sLayoutId,
  ]);
}

export {
  addLocationType,
  addLocationTypeSubLoc,
  deleteLocationType,
  deleteLocationTypeSubLoc,
  getLayoutTemplate,
  getLocationTypeList,
  getLocationTypeSubLocList,
  updateLocationType,
  updateLocationTypeSubLoc,
};
