import type { VxeColumnPropTypes } from 'vxe-table';

import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { QcTypeApi } from '#/api/basic-static-tables/qc-types';

import { $t } from '#/locales';

export function useQcTypeColumns(
  qcGroupOptions: VxeColumnPropTypes.EditRender,
): VxeTableGridOptions<QcTypeApi.QcType>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'origrec',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'QCTYPE',
      title: $t('basic-static-tables.qc-types.qctype'),
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'QCGROUP',
      title: $t('basic-static-tables.qc-types.qcgroup'),
      editRender: qcGroupOptions,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'UPDPARENT',
      title: $t('basic-static-tables.qc-types.updparent'),
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue === 'Y' ? '是' : '否';
      },
    },
    {
      align: 'center',
      field: 'AUTOSELECT',
      title: $t('basic-static-tables.qc-types.autoselect'),
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'INSTRUMENTQC',
      title: $t('basic-static-tables.qc-types.instrumentqc'),
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue === 'Y' ? '是' : '否';
      },
    },
    {
      align: 'center',
      field: 'COLOR',
      title: $t('basic-static-tables.qc-types.color'),
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'EMPW_FUNCTION',
      title: $t('basic-static-tables.qc-types.empw_function'),
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('basic-static-tables.operation'),
      width: 140,
    },
  ];
}

export function useQcTypeFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        // placeholder: 'Please enter code',
      },
      fieldName: 'QCTYPE',
      label: $t('basic-static-tables.qc-types.qctype'),
    },
    {
      component: 'Input',
      fieldName: 'QCGROUP',
      componentProps: {
        // placeholder: 'Please enter name',
      },
      label: $t('basic-static-tables.qc-types.qcgroup'),
    },
  ];
}
