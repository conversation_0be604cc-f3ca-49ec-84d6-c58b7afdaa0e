<script lang="ts" setup>
import type { DefaultOptionType, SelectValue } from 'ant-design-vue/es/select';

import type { Recordable } from '@vben/types';

import type { ProcessSpecificationsApi } from '#/api/business-static-tables/process-specifications';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message, Select, SelectOption, Transfer } from 'ant-design-vue';

import {
  getTestCategory,
  getTestNo,
} from '#/api/business-static-tables/process-specifications';

// 定义Transfer数据项类型
interface TransferItem {
  key: string;
  title: string;
  description?: string;
}

const props = defineProps<{
  aTestCodes: string[];
  specNo: null | number;
}>();

// const props = withDefaults(defineProps<TransferModalProps>(), {
//   specNo: 0,
//   initialSelectedKeys: () => [],
// });

const emit = defineEmits<{
  (
    e: 'submit',
    data: { selectedItems: TransferItem[]; selectedKeys: string[] },
  ): void;
  (e: 'cancel'): void;
}>();

// 定义组件Props类型
// interface TransferModalProps {
//   specNo?: number | string;
//   initialSelectedKeys?: string[];
// }

// 数据源与状态
const transferTestData = ref<TransferItem[]>([]);
const targetTestKeys = ref<string[]>([]);
const loading = ref(false);
const error = ref<string>('');

// 计算属性
const selectedItems = computed(() => {
  return transferTestData.value.filter((item) =>
    targetTestKeys.value.includes(item.key),
  );
});

// 组件挂载时展开所有节点
/* onMounted(async () => {
  await getTestCategoryList();
}); */

async function fetchData() {
  const testCategory = selectedTestCategory.value ?? '';
  loading.value = true;
  error.value = '';

  try {
    const result = await getTestNo(testCategory, props.aTestCodes);
    const data =
      result.items?.map((item: ProcessSpecificationsApi.SpecAnalytes) => ({
        key: item.TESTCODE,
        title: item.TESTNO,
        description: item.TESTNO || '',
      })) || [];

    transferTestData.value = data;

    // 设置初始选中项
    // targetKeys.value =
    //   props.aTestCodes && props.aTestCodes.length > 0
    //     ? [...props.aTestCodes]
    //     : [];
    targetTestKeys.value =
      props.aTestCodes.length > 0 ? [...props.aTestCodes] : [];

    // console.log('aTestCodes', props.aTestCodes);
    // console.log('targetKeys', targetKeys.value);
  } catch (error_) {
    error.value = `数据加载失败: ${(error_ as Error).message}`;
    message.error(error.value);
  } finally {
    loading.value = false;
  }
}

// 使用VbenModal
const [TransferTestModal, modalApi] = useVbenModal({
  onOpenChange: async (isOpen: boolean) => {
    if (isOpen) {
      await getTestCategoryList();
      await fetchData();
    }
  },
  onConfirm: () => {
    handleSubmit();
  },
  onCancel: () => {
    handleCancel();
  },
});

// 处理提交
function handleSubmit() {
  const result = {
    selectedKeys: targetTestKeys.value,
    selectedItems: selectedItems.value,
  };
  // console.log(result);
  emit('submit', result);
  // message.success(`已选择 ${targetKeys.value.length} 项`);
  modalApi.close();
}

// 处理取消
function handleCancel() {
  emit('cancel');
  modalApi.close();
}

// 暴露方法给父组件
defineExpose({
  open: () => modalApi.open(),
  close: () => modalApi.close(),
  setData: (data: any) => modalApi.setData(data),
});

// 组件挂载时展开所有节点
/* onMounted(async () => {
  await getTestCategoryList();
}); */

const selectedTestCategory = ref<string>();

const onSelectChange = (
  _value: SelectValue,
  _option: DefaultOptionType | DefaultOptionType[],
) => {
  fetchData();
};

const testCategoryOptions = ref<Recordable<string>[]>([]);
const getTestCategoryList = async () => {
  try {
    const res = await getTestCategory();
    if (res && res.items && res.items.length > 0) {
      testCategoryOptions.value = res.items;
    } else {
      console.warn('测试类型数据为空，请检查接口返回结果');
    }
  } catch (error) {
    console.error('获取测试类型失败:', error);
  }
};
</script>

<template>
  <TransferTestModal title="选择检项" class="w-[900px]" :loading="loading">
    <div class="mb-2">
      <label class="w-24 pr-2 text-left">测试类型名称：</label>
      <Select
        placeholder="请选择测试类型名称"
        style="width: 70%"
        v-model:value="selectedTestCategory"
        @change="onSelectChange"
      >
        <SelectOption
          v-for="item in testCategoryOptions"
          :key="item.TESTCATCODE"
          :value="item.TESTCATCODE"
        >
          {{ item.TESTCATCODE }}
        </SelectOption>
      </Select>
    </div>
    <Transfer
      v-model:target-keys="targetTestKeys"
      :data-source="transferTestData"
      show-search
      :render="(item) => item.title"
      :list-style="{
        width: '400px',
        height: '400px',
      }"
      :operations="['选择', '移除']"
    />
  </TransferTestModal>
</template>
