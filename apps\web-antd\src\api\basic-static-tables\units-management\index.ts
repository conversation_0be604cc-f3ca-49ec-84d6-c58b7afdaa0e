import { callServer, getDataSet } from '#/api/core/witlab';

export namespace UnitsManagementApi {
  export interface PageFetchParams {
    [key: string]: any;
    page: number;
    pageSize: number;
  }

  export interface MeasureTypes {
    ORIGREC: number;
    MEASURE_TYPE: string;
  }

  export interface UnitsOfMeasure {
    ORIGREC: number;
    UNIT_CODE: string;
    UNIT_NAME: string;
    UOM_LONG_NAME: string;
    UOM_SHORT_NAME: string;
    IS_SI: string;
    MEASURE_TYPE: string;
  }

  export interface UnitsConversion {
    ORIGREC: number;
    MEASURE_TYPE: string;
    UNIT_CODE: string;
    FACTOR: string;
    TO_UNIT_CODE: string;
    IS_NA: string;
  }
}

/**
 * 获取单位类型列表数据
 */
async function getMeasureTypeList() {
  return getDataSet('UNITS_MANAGEMENT.DS_MEASURE_TYPE', []);
}

/**
 * 添加单位类型
 * @param data 单位类型数据
 */
async function addMeasureType(data: UnitsManagementApi.MeasureTypes) {
  return await callServer('UNITS_MANAGEMENT.ADD_MEASURE_TYPE', [
    data.MEASURE_TYPE,
    'AddMeasuerType',
  ]);
}

/**
 * 删除单位类型
 *
 * @param origrec 质单位类型数据
 * @returns boolean
 */
// 使用async/await语法实现异步操作，确保函数执行时不会阻塞后续代码
async function deleteMeasureType(origrec: number[]) {
  // 调用服务器端的删除功能，传递单位类型数据作为参数
  return await callServer('UNITS_MANAGEMENT.DEL_MEASURE_TYPE', [origrec]);
}

/**
 * 获取计量单位列表数据
 */
async function getUnitsOfMeasureList(sMeasureType: string) {
  return getDataSet('UNITS_MANAGEMENT.DS_UNITS_OF_MEASURE', [sMeasureType]);
}

/**
 * 添加计量单位
 * @param data 计量单位数据
 */
async function addUnitsOfMeasure(data: UnitsManagementApi.UnitsOfMeasure) {
  return await callServer('UNITS_MANAGEMENT.ADD_UNIT_OF_MEAS', [
    data.MEASURE_TYPE,
    [
      data.UNIT_CODE,
      data.UNIT_NAME,
      data.UOM_LONG_NAME,
      data.UOM_SHORT_NAME,
      data.IS_SI,
    ],
    'AddUnitOfMeas',
  ]);
}

/**
 * 删除计量单位
 *
 * @param origrec 计量单位数据
 * @returns boolean
 */
async function deleteUnitsOfMeasure(origrec: number[]) {
  return await callServer('UNITS_MANAGEMENT.DEL_UNIT_OF_MEAS', [origrec]);
}

/**
 * 重置计量单位
 *
 * @param origrec 计量单位数据
 * @returns boolean
 */
async function resetUnitsOfMeasure(sMeasureType: string, sUnitCode: string) {
  return await callServer('UNITS_MANAGEMENT.SetUnitConversionCascade', [
    sMeasureType,
    sUnitCode,
    null,
    null,
    true,
  ]);
}

/**
 * 获取转换因子列表数据
 */
async function getUnitsConversionList(sUnitCode: string, sMeasureType: string) {
  return getDataSet('UNITS_MANAGEMENT.DS_CONVERSION_MAP', [
    sUnitCode,
    sMeasureType,
  ]);
}

export {
  addMeasureType,
  addUnitsOfMeasure,
  deleteMeasureType,
  deleteUnitsOfMeasure,
  getMeasureTypeList,
  getUnitsConversionList,
  getUnitsOfMeasureList,
  resetUnitsOfMeasure,
};
