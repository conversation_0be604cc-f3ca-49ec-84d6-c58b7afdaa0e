<script lang="ts" setup>
import type { SpecSchemasApi } from '#/api/business-static-tables';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { $addSpecSchemaCalcApi } from '#/api/business-static-tables';
import { $t } from '#/locales';

import { useSpecSchemasCalcsSchema } from './data';

const emit = defineEmits(['success']);
const specSchemaCode = ref<string>('');

const getTitle = computed(() => {
  return $t('ui.actionTitle.create', [
    $t('business-static-tables.specSchema.uiSpecSchemaCalc'),
  ]);
});

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: useSpecSchemasCalcsSchema(),
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    modalApi.lock();
    try {
      data.SPECSCHEMACODE = specSchemaCode.value;
      const res = await $addSpecSchemaCalcApi(data);
      if (res === 'EXISTS') {
        message.error(
          $t('business-static-tables.specSchema.uiSpecSchemaCalcExists'),
        );
        return;
      }
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<SpecSchemasApi.SpecSchemas>();
      if (data) {
        specSchemaCode.value = data.specSchemaCode;
      }
    }
  },
});
</script>

<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
  </Modal>
</template>
