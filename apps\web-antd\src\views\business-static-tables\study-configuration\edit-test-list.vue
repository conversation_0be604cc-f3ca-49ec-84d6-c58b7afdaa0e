<script lang="ts" setup>
import type { Recordable } from '@vben/types';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { message, Select, Skeleton, Transfer } from 'ant-design-vue';

import {
  $cbTestCatApi,
  $checkDefaultApi,
  $defaultProfileHasHiddenTestsApi,
  $getTestMutiChoiceApi,
  $insertTestApi,
} from '#/api/business-static-tables';

interface FormArgs {
  spCode: number;
  drawNo: number;
  callingApp: string;
  profile: string;
  isWizard: 'N' | 'Y';
  sTemplateCode: string;
  bAddbySpecs: boolean | undefined;
  sSpecNo: string;
}
const emit = defineEmits(['success']);
const defaultExists = ref<string>();
const formArgs = ref<FormArgs>(); // 表单参数
const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  onConfirm: async () => {
    if (!formArgs.value) return;
    const tests = targetKeys.value;
    if (
      tests.length === 0 &&
      formArgs.value.profile === 'Default' &&
      !(await $defaultProfileHasHiddenTestsApi({
        spCode: formArgs.value.spCode,
        drawNo: formArgs.value.drawNo,
      }))
    ) {
      message.error(
        $t('business-static-tables.studyConfiguration.cannotRemAllTest'),
      );
      return;
    }
    // const strProfile = '';
    if (
      defaultExists.value !== 'Default' &&
      formArgs.value.callingApp !== 'Edit'
    ) {
      message.warning(
        $t('business-static-tables.studyConfiguration.notSupportedParameter'),
      );
    } else if (defaultExists.value === 'Default') {
      if (!tests || tests.length === 0) {
        message.warning(
          $t('business-static-tables.studyConfiguration.cannotRemAllTest'),
        );
        return;
      }
      const addTests = await $insertTestApi({
        spCode: formArgs.value.spCode,
        drawNo: formArgs.value.drawNo,
        profile: formArgs.value.profile,
        elements: tests,
        NODELSYNC: 'Y',
        callingApp: 'Add',
        sTemplateCode: formArgs.value.sTemplateCode,
      });

      // check if profile exists
      if (addTests === 'EXISTS') {
        message.warning(
          $t('business-static-tables.studyConfiguration.profileExists'),
        );
      } else {
        if (addTests === 'SAMPLES') {
          message.warning(
            $t('business-static-tables.studyConfiguration.SAMPLES'),
          );
        }
        if (addTests === 'OTHER_PROFILE') {
          message.warning(
            $t('business-static-tables.studyConfiguration.otherProfile'),
          );
        }
        emit('success');
        modalApi.close();
      }
    } else if (formArgs.value.callingApp === 'Edit') {
      //   if(!await CheckConfirmIfDeletedTestsUsedInTriggers(strProfile, elements, spCode))
      // {
      // 	return;
      // }
      const addTests = await $insertTestApi({
        spCode: formArgs.value.spCode,
        drawNo: formArgs.value.drawNo,
        profile: formArgs.value.profile,
        elements: tests,
        NODELSYNC: 'Y',
        callingApp: formArgs.value.callingApp,
        sTemplateCode: formArgs.value.sTemplateCode,
      });
      if (addTests === 'SAMPLES') {
        message.warning(
          $t('business-static-tables.studyConfiguration.SAMPLES'),
        );
      }
      if (addTests === 'OTHER_PROFILE') {
        message.warning(
          $t('business-static-tables.studyConfiguration.otherProfile'),
        );
      }
      emit('success');
      modalApi.close();
    }
  },
  onCancel: () => {
    modalApi.close();
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<FormArgs>();
      if (data) {
        formArgs.value = data;
        defaultExists.value = await $checkDefaultApi({
          spCode: data.spCode,
          drawNo: data.drawNo,
        });
        // targetKeys.value = data.materials;
        typeList.value = await $cbTestCatApi();
        getMatList();
      }
    }
  },
});

const targetKeys = ref<string[]>([]); // 目标列表
const dataList = ref<Recordable<any>[]>([]); // 物料列表
const typeList = ref<{ label: string; value: string }[]>([]);
const selectType = ref<string>('');
const showIntervalTreeSkeleton = ref<boolean>(true);
const getMatList = async () => {
  if (!formArgs.value) return;
  showIntervalTreeSkeleton.value = true;
  dataList.value = await $getTestMutiChoiceApi({
    testCat: selectType.value,
    spCode: formArgs.value.spCode,
    drawNo: formArgs.value.drawNo,
    profile: formArgs.value.profile,
    aSelTests: targetKeys.value,
  });
  showIntervalTreeSkeleton.value = false;
};
</script>
<template>
  <Modal :title="$t('business-static-tables.studyConfiguration.editTest')">
    <div
      class="flex h-full w-full items-center justify-center"
      style="width: 100%; min-height: 500px; padding: 20px"
    >
      <div class="flex h-full w-full flex-col gap-4">
        <Select
          :options="typeList"
          v-model:value="selectType"
          class="mb-4 w-[200px]"
          :field-names="{ label: 'TESTCATCODE', value: 'TESTCATCODE' }"
          @select="getMatList"
        />
        <Skeleton
          :loading="showIntervalTreeSkeleton"
          :paragraph="{ rows: 8 }"
          active
          class="flex-1 p-[8px]"
        >
          <Transfer
            v-model:target-keys="targetKeys"
            :data-source="dataList"
            show-search
            :list-style="{
              width: '100%',
              height: '500px',
            }"
            :render="(item) => `${item.TEXT}`"
            :row-key="(record) => record.VALUE"
            class="w-full"
          >
            <template #notFoundContent>
              <span>没数据</span>
            </template>
          </Transfer>
        </Skeleton>
      </div>
    </div>
  </Modal>
</template>
