<script lang="ts" setup>
import type { TestCategoriesApi } from '#/api/basic-static-tables/test-categories';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message, Transfer } from 'ant-design-vue';

import {
  editSiteList,
  mcSelectSites,
} from '#/api/basic-static-tables/test-categories';

// 定义Transfer数据项类型
interface TransferItem {
  key: string;
  title: string;
  description?: string;
}

const props = defineProps<{
  currentTestRow: null | TestCategoriesApi.TestCategory;
}>();

const emit = defineEmits(['success']);

// 数据源与状态
const transferData = ref<TransferItem[]>([]);
const targetKeys = ref<string[]>([]);
const loading = ref(false);
const error = ref<string>('');

// 获取数据
async function fetchData() {
  loading.value = true;
  error.value = '';

  if (props.currentTestRow === null) return;

  try {
    const result = await mcSelectSites(props.currentTestRow.TESTCATCODE);
    const data =
      result.map((item: { Text: string; Value: string }) => ({
        key: item.Value,
        title: item.Text,
        description: item.Text || '',
      })) || [];

    transferData.value = data;

    const selectKeys = result
      .filter((item: { selected: string }) => item.selected === 'true')
      .map((item: { Value: number }) => item.Value);

    targetKeys.value = selectKeys;

    // 设置初始选中项
    // targetKeys.value = props.aAnalytes.length > 0 ? [...props.aAnalytes] : [];
  } catch (error_) {
    error.value = `数据加载失败: ${(error_ as Error).message}`;
    message.error(error.value);
  } finally {
    loading.value = false;
  }
}

// 使用VbenModal
const [TransferAnalyteModal, modalApi] = useVbenModal({
  onOpenChange: async (isOpen: boolean) => {
    // console.log(props.testCode);
    // console.log(props.aAnalytes);
    if (isOpen && props.currentTestRow !== null) {
      await fetchData();
    }
  },
  onConfirm: () => {
    handleSubmit();
  },
  onCancel: () => {
    handleCancel();
  },
});

// 处理提交
async function handleSubmit() {
  if (props.currentTestRow === null) return;

  await editSiteList(props.currentTestRow.TESTCATCODE, targetKeys.value);

  emit('success');
  // message.success(`已选择 ${targetKeys.value.length} 项`);
  modalApi.close();
}

// 处理取消
function handleCancel() {
  modalApi.close();
}

// 暴露方法给父组件
defineExpose({
  open: () => modalApi.open(),
  close: () => modalApi.close(),
  setData: (data: any) => modalApi.setData(data),
});
</script>

<template>
  <TransferAnalyteModal title="编辑实验室" class="w-[800px]" :loading="loading">
    <Transfer
      v-model:target-keys="targetKeys"
      :data-source="transferData"
      show-search
      :render="(item) => item.title"
      :list-style="{
        width: '400px',
        height: '400px',
      }"
      :operations="['选择', '移除']"
    />
  </TransferAnalyteModal>
</template>
