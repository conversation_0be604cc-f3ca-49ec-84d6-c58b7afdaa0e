import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { SpecCategoriesApi } from '#/api/basic-static-tables/spec-categories';

import { $t } from '#/locales';

export function useSpecCategoryColumns(): VxeTableGridOptions<SpecCategoriesApi.SpecCategorries>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'origrec',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'SPECCATEGORY',
      title: $t('basic-static-tables.spec-categories.speccategory'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'REGIONCODE',
      title: $t('basic-static-tables.spec-categories.regioncode'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
      visible: false,
    },
  ];
}

export function useSpecCategoryFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        // placeholder: 'Please enter code',
      },
      fieldName: 'SPECCATEGORY',
      label: $t('basic-static-tables.spec-categories.speccategory'),
    },
  ];
}
