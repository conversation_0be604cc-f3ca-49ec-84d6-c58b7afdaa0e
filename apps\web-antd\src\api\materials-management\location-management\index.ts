import { callServer, getDataSet } from '#/api/core/witlab';

export namespace LocationManageApi {
  export interface LocationManage {
    ORIGREC: number;
    TEXTMEMBER: string;
    VALUEMEMBER: string;
    PARENTMEMBER: string;
    NAMEMEMBER: string;
    SEQ: number;
    LOCATIONID: number;
    LOCATIONCODE: number;
    LOCATION_NAME: string;
    DESCRIPTION: string;
    IS_STORABLE: string;
    IS_GXP: string;
    CONDITION: string;
    TEMPERATURE: string;
    LUMINOSITY: string;
    TEMPERATURE_MAX: string;
    OTHER: string;
    HUMIDITY: string;
    NAME: string;
    SEQUENCE: string;
    NUMBERING_METHOD: string;
    SUBLOCATION_SIZE: string;
    STCONDITION_CODE: string;
    PREFIX: string;
    LOCTYPE_SIZE: string;
    LOCTYPE_ORDER: string;
    LOCATION_TYPES_SUBLOC_ID: number;
    LOCATION_TYPE_ID: number;
  }

  export interface DeptManage {
    ORIGREC: number;
    DEPTCODE: string;
    DEPT: string;
  }

  export interface BuildingManage {
    ORIGREC: number;
    BUILDING_CODE: string;
    BUILDING_NAME: string;
    DESCRIPTION: string;
  }

  export interface RoomManage {
    ORIGREC: number;
    ROOM_CODE: string;
    ROOM_NAME: string;
    CLASS: string;
    DESCRIPTION: string;
  }

  export interface RoomManage {
    ORIGREC: number;
    ROOM_CODE: string;
    ROOM_NAME: string;
    CLASS: string;
    DESCRIPTION: string;
  }

  export interface StorageCondition {
    ORIGREC: number;
    STCONDITION_CODE: string;
    TEMPERATURE: string;
    TEMPERATURE_MAX: string;
    HUMIDITY: string;
    LUMINOSITY: string;
    OTHER: string;
  }

  export interface LayoutConfig {
    NAME: string;
    PREFIX: string;
    LOCTYPE_SIZE: string;
    LOCTYPE_ORDER: string;
    IS_STORABLE: string;
    LOCATIONCODE: string;
    NUMBERING_METHOD: string;
  }

  export interface LocationConfig {
    LOCATIONCODE: string;
    LOCATIONTYPEID: string;
    ROOMID: number | undefined;
    CONDITION: string | undefined;
    LOCATION_NAME: string;
    LOCATIONID: string;
    LOCTYPE_SIZE: number;
    DESCRIPTION: string;
    IS_STORABLE: string;
    IS_GXP: string;
  }
}

/**
 * 获取存储位置树形列表数据
 */
async function getLocationList(sDept: string) {
  return await getDataSet('LocationManagement.dsLocationsGet', [sDept]);
}

async function dsSubLocationGet(sId: string) {
  return await getDataSet('LocationManagement.dsSubLocationGet', [sId]);
}

/**
 * 获取站点数据
 */
async function getSiteList(sUserNam: string) {
  return await getDataSet('LocationManagement.dsSiteSearch', [sUserNam]);
}

// /**
//  * 获取位置详情数据
//  */
// async function getLocationDetail(sLocationId: string) {
//   return await getDataSet('LocationManagement.dsLocationGet', [sLocationId]);
// }

/**
 * 获取位置详情数据
 */
async function getLocationDetail(sLocationCode: string) {
  return await getDataSet('LocationManagement.dsGetLocationByLocationCode', [
    sLocationCode,
  ]);
}
/**
 * 获取站点详情数据
 */
async function getDeptDetail(sOrigrec: number) {
  return await getDataSet('LocationManagement.dsSiteGet', [sOrigrec]);
}

/**
 * 获取建筑详情数据
 */
async function getBuildingDetail(sBuildingId: string) {
  return await getDataSet('LocationManagement.dsBuildingGet', [sBuildingId]);
}

/**
 * 获取房间详情数据
 */
async function getRoomDetail(sRoomId: string) {
  return await getDataSet('LocationManagement.dsRoomGet', [sRoomId]);
}

/**
 * 获取位置类型
 */
async function getLocationType() {
  return await getDataSet('GlobalDataSources.dsGetLocationType', []);
}

/**
 * 获取存储条件列表数据
 */
async function getStorageConditionList() {
  return await getDataSet('GlobalDataSources.dsGetStorageConditions', []);
}

/**
 * 获取位置布局列表数据
 */
async function getStorageConditionLayoutList(sLocationType: string) {
  return await getDataSet('GlobalDataSources.dsGetStorageConditionLayout', [
    sLocationType,
  ]);
}

/**
 * 添加位置
 * @param data 位置数据
 */
async function addLocation(
  aLocation: LocationManageApi.LocationConfig,
  sMode: string,
) {
  return await callServer('LocationManagement.scAddLocation', [
    [
      aLocation.LOCATIONCODE,
      aLocation.LOCATION_NAME,
      aLocation.LOCATIONTYPEID,
      aLocation.DESCRIPTION,
      aLocation.ROOMID,
      aLocation.CONDITION,
      aLocation.IS_STORABLE,
      aLocation.IS_GXP,
    ],
    sMode,
  ]);
}

/**
 * 添加位置
 * @param data 位置数据
 */
async function addLocationInfo(
  arSubLocation: string[][],
  aLocation: LocationManageApi.LocationConfig,
) {
  return await callServer('LocationManagement.scAddSubLocationInfo', [
    arSubLocation,
    'Location',
    [
      aLocation.LOCATIONCODE,
      aLocation.LOCATIONTYPEID,
      aLocation.ROOMID,
      aLocation.CONDITION,
      aLocation.LOCATION_NAME,
      aLocation.LOCATIONID,
      aLocation.LOCTYPE_SIZE,
    ],
  ]);
}

/**
 * 删除位置
 */
async function deleteLocation(sLocationId: number) {
  return await callServer('LocationManagement.scDeleteLocation', [sLocationId]);
}

export {
  addLocation,
  addLocationInfo,
  deleteLocation,
  dsSubLocationGet,
  getBuildingDetail,
  getDeptDetail,
  getLocationDetail,
  getLocationList,
  getLocationType,
  getRoomDetail,
  getSiteList,
  getStorageConditionLayoutList,
  getStorageConditionList,
};
