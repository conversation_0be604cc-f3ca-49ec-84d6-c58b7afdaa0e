<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { EquipmentMgApi } from '#/api/equipment/equipment-mg';

import { computed, ref, watch } from 'vue';

import { Button, DatePicker, Space } from 'ant-design-vue';
import dayjs from 'dayjs';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getDgCaptrueRecordApi } from '#/api/equipment/equipment-mg';
import { $t } from '#/locales';
import { useEquipmentStore } from '#/store';

import { logColumns } from '../equipment-mg-data';

const equipmentStore = useEquipmentStore();
const currentRow = computed<EquipmentMgApi.RowType | null>(
  () => equipmentStore.getCurrentRow,
);
interface RowType {
  [key: string]: any;
}
const startTime = ref(dayjs().subtract(1, 'month'));
const endTime = ref(dayjs());
watch(
  currentRow,
  async (newRow: RowType) => {
    if (newRow) {
      gridApi.query();
    }
  },
  { deep: true },
);
const gridOptions: VxeTableGridOptions<EquipmentMgApi.MetaDataEquipment> = {
  columns: logColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!currentRow.value) {
          return [];
        }
        const params = [
          currentRow.value?.EQID,
          dayjs(startTime.value).format('YYYY-MM-DD HH:mm:ss'),
          dayjs(endTime.value).format('YYYY-MM-DD HH:mm:ss'),
        ];
        const data = await getDgCaptrueRecordApi(params);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
  virtualXConfig: {
    enabled: true,
  },
  scrollX: {
    enabled: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: {},
});
const hasEditStatus = (row: EquipmentMgApi.RowType) => {
  return gridApi.grid?.isEditByRow(row);
};
const editRowEvent = (row: EquipmentMgApi.RowType) => {
  gridApi.grid?.setEditRow(row);
};

const saveRowEvent = async (row: EquipmentMgApi.RowType) => {
  console.warn(`保存成功！${row}`);
  await gridApi.grid?.clearEdit();
  gridApi.setLoading(true);
  setTimeout(() => {
    gridApi.setLoading(false);
  }, 600);
};
const cancelRowEvent = (_row: EquipmentMgApi.RowType) => {
  gridApi.grid?.clearEdit();
};
const Search = () => {
  gridApi.query();
};
const viewSpectrum = () => {
  // TODO：文件下载
  console.warn('saveRowEvent');
};
</script>
<template>
  <div class="h-[36vh] w-[40%]">
    <Grid>
      <template #toolbar-actions>
        <Space :size="[4, 0]" wrap>
          查询起始时间&nbsp;<DatePicker v-model:value="startTime" />
          查询终止时间&nbsp;<DatePicker v-model:value="endTime" />
          <Button type="primary" @click="Search">
            {{ $t('equipment.equipment-mg.search') }}
          </Button>
          <Button type="primary" @click="viewSpectrum">
            {{ $t('equipment.equipment-mg.viewSpectrum') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent(row)">
            {{ $t('basic-static-tables.save') }}
          </Button>
          <Button type="link" @click="cancelRowEvent(row)">
            {{ $t('equipment.cancel') }}
          </Button>
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">
            {{ $t('basic-static-tables.edit') }}
          </Button>
        </template>
      </template>
    </Grid>
  </div>
</template>
