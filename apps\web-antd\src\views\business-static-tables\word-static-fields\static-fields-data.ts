import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { WordStaticFieldsApi } from '#/api/business-static-tables/word-static-fields';

import { $t } from '#/locales';

export function useWordStaticFieldsFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        // placeholder: 'Please enter code',
      },
      fieldName: 'MARKING_NAME',
      label: $t('business-static-tables.word-static-fields.marking_name'),
    },
  ];
}

// 客户分类列表
export function useWordStaticFieldsColumns(): VxeTableGridOptions<WordStaticFieldsApi.WordStaticFields>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'GROUP_BY',
      title: $t('business-static-tables.word-static-fields.group_by'),
      editRender: { name: 'input' },
      minWidth: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'MARKING_NAME',
      title: $t('business-static-tables.word-static-fields.marking_name'),
      editRender: { name: 'input' },
      minWidth: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'REPLACE_NAME',
      title: $t('business-static-tables.word-static-fields.replace_name'),
      editRender: { name: 'input' },
      minWidth: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SORTER',
      title: $t('business-static-tables.word-static-fields.sorter'),
      editRender: { name: 'input' },
      minWidth: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('business-static-tables.operation'),
      minWidth: 140,
    },
  ];
}
