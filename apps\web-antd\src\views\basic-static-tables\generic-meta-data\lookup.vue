<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { LookupTableApi } from '#/api/basic-static-tables/generic-meta-data';

import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { Button, message, Modal, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addLookupApi,
  deleteLookupApi,
  getLookupTableApi,
} from '#/api/basic-static-tables/generic-meta-data';

import AddLookupForm from './add-lookup-form.vue';
import { useColumns, useFilterLookupSchema } from './lookup-data';
import LookupValues from './lookup-values.vue';

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: LookupValues,
  destroyOnClose: true,
  class: 'w-[900px]',
});

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddLookupForm,
  destroyOnClose: true,
});

const gridOptions: VxeTableGridOptions<LookupTableApi.MetaDataLookups> = {
  columns: useColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        const data = await getLookupTableApi();
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const formOptions: VbenFormProps = {
  schema: useFilterLookupSchema(),
  submitOnChange: false,
  collapsed: true,
  showCollapseButton: true,
  submitButtonOptions: {
    content: '查询',
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: {},
  formOptions,
});

function hasEditStatus(row: LookupTableApi.MetaDataLookups) {
  return gridApi.grid?.isEditByRow(row);
}

function editRowEvent(row: LookupTableApi.MetaDataLookups) {
  gridApi.grid?.setEditRow(row);
}

function viewDetails(row: LookupTableApi.MetaDataLookups) {
  formDrawerApi.setData(row).open();
}

async function saveRowEvent(row: LookupTableApi.MetaDataLookups) {
  await gridApi.grid?.clearEdit();
  addLookupApi(row);
  gridApi.setLoading(true);
  setTimeout(() => {
    gridApi.setLoading(false);
    message.success({
      content: `保存成功！`,
    });
  }, 600);
}

const cancelRowEvent = (_row: LookupTableApi.MetaDataLookups) => {
  gridApi.grid?.clearEdit();
};

function onRefresh() {
  gridApi.query();
}

function onCreate() {
  formModalApi.setData(null).open();
}

async function onDelete() {
  const checkLookupNames: string[] = gridApi.grid
    .getCheckboxRecords()
    .map((row) => row.NAME) as string[];
  if (checkLookupNames.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }
  if (checkLookupNames.length > 1) {
    message.warning('只能选择一条数据！');
    return;
  }
  await confirm('是否确认删除选中的数据？', '删除确认');

  gridApi.grid?.getCheckboxRecords().forEach((row) => {
    gridApi.grid?.remove(row);
  });
  const delName = checkLookupNames[0] as string;
  await deleteLookupApi(delName);
  message.success('删除成功！');
  onRefresh();
}

function confirm(content: string, title: string) {
  return new Promise((reslove, reject) => {
    Modal.confirm({
      content,
      onCancel() {
        reject(new Error('已取消'));
      },
      onOk() {
        reslove(true);
      },
      title,
    });
  });
}
</script>

<template>
  <Page auto-content-height>
    <FormDrawer @success="onRefresh" />
    <FormModal @success="onRefresh" class="w-[600px]" />
    <Grid>
      <template #toolbar-actions>
        <Space :size="[4, 0]" wrap>
          <Button type="primary" @click="onCreate">
            <!-- <Plus class="size-4" /> -->
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="onDelete">
            <!-- <Delete class="size-4" /> -->
            {{ $t('ui.actionTitle.delete') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button
            type="link"
            @click="saveRowEvent(row)"
            :text="$t('commons.save')"
          />
          <Button
            type="link"
            @click="cancelRowEvent(row)"
            :text="$t('commons.cancel')"
          />
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">编辑</Button>
          <Button type="link" @click="viewDetails(row)">详情</Button>
        </template>
      </template>
    </Grid>
  </Page>
</template>
