<script lang="ts" setup>
import type { MethodManagerApi } from '#/api/business-static-tables';
import type { EsigResult } from '#/types/types';

import { computed, ref } from 'vue';

import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import {
  $delTestMethodVersionApi,
  $extendExpDateApi,
  $getTestMethodVersionListApi,
  $releaseTestMethodVersionApi,
} from '#/api/business-static-tables';
import EsigForm from '#/components/esig/esig-form.vue';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';
import { confirm } from '#/utils/utils';

import AddMethodVersionForm from './add-method-version-form.vue';
import { useFilterSchema, useMethodVersionColumns } from './data';

const methodData = ref<MethodManagerApi.TestMethod>();
const colums = useMethodVersionColumns();
const filterSchema = useFilterSchema();
const queryData = async () => {
  return await $getTestMethodVersionListApi(methodData.value?.METHOD ?? '');
};

const {
  Grid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
  CurrentCheckRow,
  SelectedCheckRows,
} = useLimsGridsConfig<MethodManagerApi.TestMethodVersion>(
  colums,
  filterSchema,
  queryData,
);

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddMethodVersionForm,
  destroyOnClose: true,
});

const [EsigFormModal, esigFormModalApi] = useVbenModal({
  connectedComponent: EsigForm,
  destroyOnClose: true,
});

const [Drawer, drawerApi] = useVbenDrawer({
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<MethodManagerApi.TestMethod>();
      if (data) {
        methodData.value = data;
        gridApi?.query();
      } else {
        gridApi?.reload();
      }
    }
  },
  showCancelButton: false,
  showConfirmButton: false,
  class: '',
});
const getDrawerTitle = computed(() => {
  return `${$t('business-static-tables.methodVersionManager.uiTitle')}: ${methodData.value?.METHOD}`;
});

function onRefresh() {
  gridApi.query();
}

async function onDelete() {
  const checkRows = gridApi.grid.getCheckboxRecords();
  const checkOrig: number[] = checkRows.map((item) => item.ORIGREC);

  if (checkOrig.length === 0) {
    message.warning($t('commons.selectOne'));
    return;
  }
  // const arrOrigrec = deleteData.map((item) => item.ORIGREC);
  const method = checkRows[0].METHOD;
  const arrVersion = checkRows.map((item) => item.VERSION);
  const arrDocIds = checkRows.map((item) => item.STARDOC_ID);

  await confirm($t('commons.deleteConfirm'), $t('commons.deleteConfirmTitle'));
  await $delTestMethodVersionApi(checkOrig, method, arrVersion, arrDocIds);
  message.success($t('commons.deleteSuccess'));
  onRefresh();
}

function onCreate() {
  formModalApi.setData(methodData.value).open();
}

function extentExpDateRowEvent() {
  const checkRow = CurrentCheckRow.value;
  if (!checkRow) {
    message.warning($t('commons.selectOne'));
    return;
  }
  esigFormModalApi
    .setData({
      EventCode: 'TestMethodRelease',
      ConfirmSuccessCallback: async (esigResult: EsigResult) => {
        await $extendExpDateApi({
          ORIGREC: checkRow.ORIGREC,
          EXPDATE: esigResult.EndDate,
        });
        onRefresh();
      },
    })
    .open();
}
async function releaseRowEvent() {
  const checkRow = CurrentCheckRow.value;
  if (!checkRow) {
    message.warning($t('commons.selectOne'));
    return;
  }
  esigFormModalApi
    .setData({
      EventCode: 'TestMethodRelease',
      ConfirmSuccessCallback: async (esigResult: EsigResult) => {
        await $releaseTestMethodVersionApi({
          method: checkRow?.METHOD ?? '',
          version: checkRow?.VERSION ?? 0,
          startDate: esigResult.StartDate,
          expDate: esigResult.EndDate,
        });
        onRefresh();
      },
    })
    .open();
}

const disabledRelBtn = computed(() => {
  return CurrentCheckRow.value?.STATUS !== 'Draft';
});

const disabledExtendBtn = computed(() => {
  return CurrentCheckRow.value?.STATUS !== 'Approved';
});

const disabledUploadFileBtn = computed(() => {
  return CurrentCheckRow.value?.STATUS !== 'Draft';
});

const disableDelBtn = computed(() => {
  const checkRows = SelectedCheckRows.value;
  return (
    checkRows.length === 0 || checkRows.some((item) => item.STATUS !== 'Draft')
  );
});

const disabledViewFileBtn = computed(() => {
  return !CurrentCheckRow.value?.STARDOC_ID;
});

const uploadFile = () => {
  const checkRow = CurrentCheckRow.value;
  if (!checkRow) {
    message.warning($t('commons.selectOne'));
  }
  // TODO: 上传文件
  // console.log('上传文件');
};

const viewFile = () => {
  const checkRow = CurrentCheckRow.value;
  if (!checkRow) {
    message.warning($t('commons.selectOne'));
  }
  // TODO: 查看文件
  // console.log('查看文件');
};
</script>

<template>
  <Drawer class="w-full max-w-[1500px]" :title="getDrawerTitle">
    <FormModal @success="onRefresh" />
    <EsigFormModal />
    <Page auto-content-height>
      <Grid>
        <template #toolbar-actions>
          <Space :size="[4, 0]" wrap>
            <Button type="primary" @click="onCreate">
              {{ $t('ui.actionTitle.create') }}
            </Button>
            <Button
              type="primary"
              danger
              @click="onDelete"
              :disabled="disableDelBtn"
            >
              {{ $t('ui.actionTitle.delete') }}
            </Button>
            <Button
              type="default"
              @click="releaseRowEvent()"
              :disabled="disabledRelBtn"
            >
              {{ $t('commons.release') }}
            </Button>
            <Button
              type="default"
              @click="uploadFile()"
              :disabled="disabledUploadFileBtn"
            >
              {{ $t('commons.uploadFile') }}
            </Button>
            <Button
              type="default"
              @click="viewFile()"
              :disabled="disabledViewFileBtn"
            >
              {{ $t('commons.viewFile') }}
            </Button>
            <Button
              type="default"
              @click="extentExpDateRowEvent()"
              :disabled="disabledExtendBtn"
            >
              {{ $t('commons.extendExpDate') }}
            </Button>
          </Space>
        </template>
        <template #action="{ row }">
          <template v-if="hasEditStatus(row)">
            <Button type="link" @click="saveRowEvent(row)">
              {{ $t('commons.save') }}
            </Button>
            <Button type="link" @click="cancelRowEvent(row)">
              {{ $t('commons.cancel') }}
            </Button>
          </template>
          <template v-else>
            <Button type="link" @click="editRowEvent(row)">
              {{ $t('commons.edit') }}
            </Button>
          </template>
        </template>
      </Grid>
    </Page>
  </Drawer>
</template>
