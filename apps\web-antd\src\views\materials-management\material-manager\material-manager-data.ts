import type { VxeColumnPropTypes } from 'vxe-table';

import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { MaterialManagerApi } from '#/api/materials-management/material-manager';

import dayjs from 'dayjs';

import { $t } from '#/locales';

export function useMaterialManagerFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        // placeholder: 'Please enter code',
      },
      fieldName: 'MATCODE',
      label: $t('materials-management.material-manager.matcode'),
    },
    {
      component: 'Input',
      componentProps: {
        // placeholder: 'Please enter code',
      },
      fieldName: 'MATNAME',
      label: $t('materials-management.material-manager.matname'),
    },
  ];
}

// 物料列表
export function useMaterialManagerColumns(
  matTypeEditRender: VxeColumnPropTypes.EditRender,
  inspectionLevelResult: VxeColumnPropTypes.EditRender,
): VxeTableGridOptions<MaterialManagerApi.MaterialManager>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'MATCODE',
      title: $t('materials-management.material-manager.matcode'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'MATNAME',
      title: $t('materials-management.material-manager.matname'),
      editRender: { name: 'input' },
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'MATTYPE',
      title: $t('materials-management.material-manager.mattype'),
      minWidth: 200,
      editRender: matTypeEditRender,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'DEFAULT_UNIT_CODE',
      title: $t('materials-management.material-manager.default_unit_code'),
      editRender: { name: 'input' },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SPEC',
      title: $t('materials-management.material-manager.spec'),
      editRender: { name: 'input' },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'PACKING_SPEC',
      title: $t('materials-management.material-manager.packing_spec'),
      editRender: { name: 'input' },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'DOSAGEFORM',
      title: $t('materials-management.material-manager.dosageform'),
      editRender: { name: 'input' },
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'INSPECTION_LEVEL',
      title: $t('materials-management.material-manager.inspection_level'),
      editRender: inspectionLevelResult,
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'PRODUCT_NAME',
      title: $t('materials-management.material-manager.product_name'),
      editRender: { name: 'input' },
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'GENERIC_NAME',
      title: $t('materials-management.material-manager.generic_name'),
      editRender: { name: 'input' },
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'RETESTDATE_M',
      title: $t('materials-management.material-manager.retestdate_m'),
      editRender: { name: 'input' },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'EXDATE_M',
      title: $t('materials-management.material-manager.exdate_m'),
      editRender: { name: 'input' },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SAVECONDITION',
      title: $t('materials-management.material-manager.savecondition'),
      editRender: { name: 'input' },
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SAMPLEPERIOD',
      title: $t('materials-management.material-manager.sampleperiod'),
      editRender: { name: 'input' },
      minWidth: 170,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('business-static-tables.operation'),
      minWidth: 140,
    },
  ];
}

// 同义词列表
export function useSynonimsColumns(): VxeTableGridOptions<MaterialManagerApi.MaterialsSynonims>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'MATCODE',
      title: $t('materials-management.material-manager.matcode'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      field: 'MATSYNONIM',
      title: $t('materials-management.material-manager.matsynonims'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}

// 厂商列表
export function useProdSuppliersColumns(): VxeTableGridOptions<MaterialManagerApi.ProdSuppliers>['columns'] {
  return [
    // { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'MATCODE',
      title: $t('materials-management.material-manager.matcode'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      field: 'SUPTYPE',
      title: $t('materials-management.material-manager.suptype'),
      minWidth: 120,
      editRender: {
        name: 'select',
        options: [
          { value: 'GY', label: '供应商' },
          { value: 'SC', label: '生产商' },
        ],
      },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SUPPCODE',
      title: $t('materials-management.material-manager.suppcode'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SUPPNAM',
      title: $t('materials-management.material-manager.suppnam'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}

// 配方列表
export function useRecipesColumns(): VxeTableGridOptions<MaterialManagerApi.Recipes>['columns'] {
  return [
    // { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'MATCODE',
      title: $t('materials-management.material-manager.matcode'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      field: 'RECIPECODE',
      title: $t('materials-management.material-manager.recipecode'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'RECIPENAME',
      title: $t('materials-management.material-manager.recipename'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'STARTDDATE',
      title: $t('materials-management.material-manager.startddate'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      field: 'EXPDATE',
      title: $t('materials-management.material-manager.expdate'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      field: 'STATUS',
      title: $t('materials-management.material-manager.status'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      field: 'CREATED_BY',
      title: $t('materials-management.material-manager.created_by'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'APPROVEDAT',
      title: $t('materials-management.material-manager.approvedat'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      field: 'RETIREDDAT',
      title: $t('materials-management.material-manager.retireddat'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      field: 'STARDOC_ID',
      title: $t('materials-management.material-manager.stardoc_id'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('materials-management.operation'),
      minWidth: 140,
    },
  ];
}

// 配方详情
export function useRecipesDetailColumns(): VxeTableGridOptions<MaterialManagerApi.Recipes>['columns'] {
  return [
    // { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'MATCODE',
      title: $t('materials-management.material-manager.matcode'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'MATNAME',
      title: $t('materials-management.material-manager.matname'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'RECIPECODE',
      title: $t('materials-management.material-manager.recipecode'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
  ];
}

// 配方所选物料列表
export function useRecipesMaterialColumns(): VxeTableGridOptions<MaterialManagerApi.MaterialManager>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'MATTYPE',
      title: $t('materials-management.material-manager.mattype'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'MATCODE',
      title: $t('materials-management.material-manager.matcode'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'MATNAME',
      title: $t('materials-management.material-manager.matcode'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}

// 容器单位
export function useMaterialUnitConvColumns(): VxeTableGridOptions<MaterialManagerApi.MaterialUnitConversion>['columns'] {
  return [
    // { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'TEXT',
      title: $t('materials-management.material-manager.text'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      treeNode: true,
    },
    {
      field: 'VALUE',
      title: $t('materials-management.material-manager.value'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      field: 'MATNAME',
      title: $t('materials-management.material-manager.matname'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'FACTOR',
      title: $t('materials-management.material-manager.factor'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'PARENT',
      title: $t('materials-management.material-manager.parent'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
  ];
}
