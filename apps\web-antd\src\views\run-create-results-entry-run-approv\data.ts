import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { Audittrl } from '#/api/run-create-results-entry-run-approv';

import { $t } from '@vben/locales';

import { createColumn } from '#/utils/lims-grids-config';

export function useColumns(): VxeTableGridOptions<Audittrl>['columns'] {
  return [
    createColumn({
      field: 'EVENT_LEVEL',
      title: $t('run-create-results-entry-run-approv.signatures.eventLevel'),
      width: 120,
    }),
    createColumn({
      field: 'EVENT_TYPE',
      title: $t('run-create-results-entry-run-approv.signatures.eventType'),
      width: 120,
    }),
    createColumn({
      field: 'EVENT',
      title: $t('run-create-results-entry-run-approv.signatures.event'),
      width: 200,
    }),
    createColumn({
      field: 'FULLNAME',
      title: $t('run-create-results-entry-run-approv.signatures.fullName'),
      width: 120,
    }),
    createColumn({
      field: 'AUDIT_DT',
      title: $t('run-create-results-entry-run-approv.signatures.auditDt'),
      width: 150,
    }),
    createColumn({
      field: 'CHANGE_COMMENT',
      title: $t('run-create-results-entry-run-approv.signatures.changeComment'),
      width: 300,
    }),
    createColumn({
      field: 'ESIG',
      title: $t('run-create-results-entry-run-approv.signatures.esig'),
      width: 120,
      cellRender: {
        props: {
          checkedValue: 'Y',
          unCheckedValue: 'N',
          checkedChildren: $t('commons.yes'),
          unCheckedChildren: $t('commons.no'),
        },
        name: 'CellSwitch',
      },
    }),
    createColumn({
      field: 'ESIG_COMMENT',
      title: $t('run-create-results-entry-run-approv.signatures.esigComment'),
      width: 150,
      cellRender: {
        props: {
          checkedValue: 'Y',
          unCheckedValue: 'N',
          checkedChildren: $t('commons.yes'),
          unCheckedChildren: $t('commons.no'),
        },
        name: 'CellSwitch',
      },
    }),
    createColumn({
      field: 'ESIG_PASSWORD',
      title: $t('run-create-results-entry-run-approv.signatures.esigPassword'),
      width: 150,
      cellRender: {
        props: {
          checkedValue: 'Y',
          unCheckedValue: 'N',
          checkedChildren: $t('commons.yes'),
          unCheckedChildren: $t('commons.no'),
        },
        name: 'CellSwitch',
      },
    }),
    createColumn({
      field: 'ESIG_WITNESS',
      title: $t('run-create-results-entry-run-approv.signatures.esigWitness'),
      width: 150,
      cellRender: {
        props: {
          checkedValue: 'Y',
          unCheckedValue: 'N',
          checkedChildren: $t('commons.yes'),
          unCheckedChildren: $t('commons.no'),
        },
        name: 'CellSwitch',
      },
    }),
    {
      field: 'ESIG_WITNESS_NAME',
      title: $t(
        'run-create-results-entry-run-approv.signatures.esigWitnessName',
      ),
      width: 150,
    },
    createColumn({
      field: 'ESIG_AGREEMENT',
      title: $t('run-create-results-entry-run-approv.signatures.esigAgreement'),
      width: 150,
      cellRender: {
        props: {
          checkedValue: 'Y',
          unCheckedValue: 'N',
          checkedChildren: $t('commons.yes'),
          unCheckedChildren: $t('commons.no'),
        },
        name: 'CellSwitch',
      },
    }),
    createColumn({
      field: 'ESIG_AGREEMENT_TEXT',
      title: $t(
        'run-create-results-entry-run-approv.signatures.esigAgreementText',
      ),
      width: 150,
    }),
  ];
}
