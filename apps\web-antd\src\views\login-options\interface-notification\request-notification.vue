<script lang="ts" setup>
import type { PreBatchesApi } from '#/api/login-options/interface-notification';

// 添加了 onMounted 和 reactive 的导入
import { confirm, Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Button, message, Space } from 'ant-design-vue';

import {
  cancelSample,
  getPreBatches,
} from '#/api/login-options/interface-notification';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import PreBatchesRel from './pre-batches-rel.vue';
import PreTests from './pre-tests.vue';
import AddLotSampleForm from './prompt-for-product-step.vue';
import {
  usePreBatchesColumns,
  usePreBatchesFilterSchema,
} from './request-notification-data';

const colums = usePreBatchesColumns();
const filterSchema = usePreBatchesFilterSchema();
const queryData = async () => {
  return getPreBatches();
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
};

const { Grid, gridApi } = useLimsGridsConfig<PreBatchesApi.PreBatches>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

function onRefresh() {
  gridApi.query(); // 主动触发一次查询
}

// 启动
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddLotSampleForm,
});

async function onStart() {
  const request = gridApi.grid?.getCurrentRecord();
  if (!request) return;
  const sOrigrec = request.ORIGREC;
  const strMode = 'REQUEST';
  formModalApi.setData({ Mode: strMode, ORIGREC: sOrigrec }).open();
}

// 关闭
async function onClose() {
  // 获取选中行
  const request = gridApi.grid?.getCurrentRecord();
  if (!request) return;

  const requestId = request.REQUESTID;
  try {
    await confirm({
      title: '确认关闭',
      content: `确定要关闭请验单 ${requestId} 吗？`,
      icon: 'warning',
      centered: false,
    });

    await cancelSample(requestId);
    message.success('操作成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

const [TestFormDrawer, testFormDrawerApi] = useVbenDrawer({
  connectedComponent: PreTests,
  destroyOnClose: true,
  class: 'w-[900px]',
});

function onPreTestsView(row: PreBatchesApi.PreBatches) {
  testFormDrawerApi.setData(row).open();
}

const [RelFormDrawer, relFormDrawerApi] = useVbenDrawer({
  connectedComponent: PreBatchesRel,
  destroyOnClose: true,
  class: 'w-[900px]',
});

function onPreBatchesRelView(row: PreBatchesApi.PreBatches) {
  relFormDrawerApi.setData(row).open();
}
</script>
<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <TestFormDrawer />
    <RelFormDrawer />
    <Grid>
      <template #toolbar-actions>
        <Space>
          <Button type="primary" @click="onStart">
            {{ $t('login-options.start') }}
          </Button>
          <Button type="primary" danger @click="onClose">
            {{ $t('login-options.close') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <Button type="link" @click="onPreTestsView(row)">
          {{ $t('login-options.interface-notification.preTests') }}
        </Button>
        <Button type="link" @click="onPreBatchesRelView(row)">
          {{ $t('login-options.interface-notification.preBatchesRel') }}
        </Button>
      </template>
    </Grid>
  </Page>
</template>
