<script lang="ts" setup>
import type { VxeGridListeners } from '#/adapter/vxe-table';
import type { StabilityPurposesApi } from '#/api/basic-static-tables';

import { Page, useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import {
  $delStabilityPurposeApi,
  $getStabilityPurposeApi,
} from '#/api/basic-static-tables';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';
import { confirm } from '#/utils/utils';

import AddStabilityPurposeForm from './add-stability-purpose-form.vue';
import { useColumns, useFilterSchema } from './data';

const columns = useColumns();
const filterSchema = useFilterSchema();
const queryData = async () => {
  return await $getStabilityPurposeApi();
};
const event: VxeGridListeners = {};
const gridOptions = {};

const {
  Grid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  cancelRowEvent,
  saveRowEvent,
} = useLimsGridsConfig<StabilityPurposesApi.XStabilityPurpose>(
  columns,
  filterSchema,
  queryData,
  gridOptions,
  event,
);

async function onDelete() {
  const checkRec = gridApi.grid?.getCheckboxRecords();

  if (checkRec.length === 0) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const purposes = checkRec.map((item) => item.PURPOSE);
  await confirm($t('commons.deleteConfirm'), $t('commons.deleteConfirmTitle'));
  const res = await $delStabilityPurposeApi(purposes);
  if (res) {
    message.success($t('commons.deleteSuccess'));
    onRefresh();
  } else {
    message.error($t('basic-static-tables.stabilityPurpose.nodel'));
  }
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddStabilityPurposeForm,
  destroyOnClose: true,
});

function onCreate() {
  formModalApi.setData(null).open();
}
function onRefresh() {
  gridApi.query();
}
</script>

<template>
  <FormModal @success="onRefresh" />
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Space :size="[4, 0]" wrap>
          <Button type="primary" @click="onCreate">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="onDelete">
            {{ $t('ui.actionTitle.delete') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent(row)">保存</Button>
          <Button type="link" @click="cancelRowEvent(row)">取消</Button>
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">编辑</Button>
        </template>
      </template>
    </Grid>
  </Page>
</template>
