<script lang="ts" setup>
import { ref } from 'vue';

import { confirm, useVbenModal } from '@vben/common-ui';

// import { getMaterialTypesApi } from '@/api/core/material';
import {
  Button,
  Checkbox,
  message,
  Space,
  TabPane,
  Tabs,
} from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

import AddChildTypeModal from './add-childtype.vue';
import AddMatTypeModal from './add-mattype.vue';

// import { MOCK_TABLE_DATA } from '../table-data';

/* interface MatTypeRowType {
  MATTYPE: string;
  ISPRODUCEMETRIAL: string;
  DESCRIPTION: string;
} */

const materialData = ref([
  { MATTYPE: '中间产品', ISPRODUCEMETRIAL: true, DESCRIPTION: '中间产品' },
  { MATTYPE: '包材', ISPRODUCEMETRIAL: false, DESCRIPTION: '包材' },
  { MATTYPE: '原料', ISPRODUCEMETRIAL: false, DESCRIPTION: '原料' },
  { MATTYPE: '对照品', ISPRODUCEMETRIAL: false, DESCRIPTION: '对照品' },
  { MATTYPE: '试剂', ISPRODUCEMETRIAL: false, DESCRIPTION: '试剂' },
  { MATTYPE: '制药用水', ISPRODUCEMETRIAL: false, DESCRIPTION: '制药用水' },
]);

const [MatTypeGrid] = useVbenVxeGrid({
  gridOptions: {
    columns: [
      { title: '类型名称', field: 'MATTYPE' },
      {
        title: '生产物料？',
        field: 'ISPRODUCEMETRIAL',
        type: 'checkbox', // 设置为 checkbox 类型
        editRender: { name: 'input' },
      },
      { title: '描述', field: 'DESCRIPTION', editRender: { name: 'input' } },
    ],
    data: materialData.value,
    editConfig: {
      mode: 'cell',
      trigger: 'click',
    },
    pagerConfig: {
      enabled: true,
    },
    sortConfig: {
      multiple: true,
    },
    height: '100%',
    stripe: true, // 斑马纹
    toolbarConfig: {
      custom: true,
      export: true,
      // import: true,
      refresh: true,
      zoom: true,
    },
    // rowKey: 'MATTYPE',
    // rowId: 'MATTYPE',
  },
});

// const detailData = ref([]); // 存储下面 Grid 的数据

const detailData = ref([
  { DEPT: 'SITE1' },
  { DEPT: 'SITE2' },
  { DEPT: 'SITE3' },
  { DEPT: 'SITE4' },
  { DEPT: 'SITE5' },
  { DEPT: 'SITE6' },
]);

const [DetailGrid] = useVbenVxeGrid({
  gridOptions: {
    columns: [
      { title: '实验室', field: 'DEPT', width: 180 },
      {
        title: 'COA报告模板',
        field: 'BATCHCRYSREPORTID',
        width: 200,
        editRender: {
          name: 'select', // 设置为下拉框
          options: [
            {
              label: '产品（BatchCOAS.ProductReport）',
              value: '产品（BatchCOAS.ProductReport）',
            },
            {
              label: '原材料（BatchCOAS.RawReport）',
              value: '原材料（BatchCOAS.RawReport）',
            },
          ],
        },
      },
      { title: '请验单模板', field: 'REQUESTFORM_CRYID', width: 210 },
      { title: '取样证模板', field: 'CERT_SAMPLING', width: 200 },
      { title: '检验样标签模板', field: 'LABEL_SAMPLE_TEST', width: 200 },
      { title: '留样标签模板', field: 'LABEL_SAMPLE_RETAIN', width: 200 },
      { title: '取样记录ELN模板', field: 'SAMPLE_ELNID', width: 200 },
      { title: '留样观察周期(月)', field: 'SAMPLECYCLE', width: 120 },
      { title: '需要外观验收', field: 'NEEDAPPEARANCEINSPECTION', width: 120 },
      { title: '领用发放签名模式', field: 'MATINVENTORY_SIGMODE', width: 120 },
      { title: '新增库存签名模式', field: 'ADDINVENTORY_SIGMODE', width: 120 },
      { title: '库存盘存签名模式', field: 'STOCKTAKING_SIGMODE', width: 120 },
      { title: '库存销毁签名模式', field: 'DESTRUCTION_SIGMODE', width: 120 },
      {
        title: '留样领用签名模式',
        field: 'RESERVED_RECEIVE_SIGN_MODEL',
        width: 120,
      },
      {
        title: '留样销毁签名模式',
        field: 'RESERVED_DESTROY_SIGN_MODEL',
        width: 120,
      },
      {
        title: '留样观察签名模式',
        field: 'RESERVED_OBSERVE_SIGN_MODEL',
        width: 120,
      },
    ],
    data: detailData.value,
    editConfig: {
      mode: 'cell',
      trigger: 'click',
    },
    pagerConfig: {
      enabled: true,
    },
    sortConfig: {
      multiple: true,
    },
    height: '100%',
    stripe: true, // 斑马纹
    toolbarConfig: {
      custom: true,
      export: true,
      // import: true,
      refresh: true,
      zoom: true,
    },
  },
});

const [ChildTypeGrid] = useVbenVxeGrid({
  gridOptions: {
    columns: [
      { title: '类型名称', field: 'TYPENAME' },
      { title: '类型描述', field: 'TYPEDESC', width: 200 },
    ],
    // data: MOCK_TABLE_DATA,
    pagerConfig: {
      enabled: true,
    },
    sortConfig: {
      multiple: true,
    },
    height: '100%',
    stripe: true, // 斑马纹
    toolbarConfig: {
      custom: true,
      export: true,
      // import: true,
      refresh: true,
      zoom: true,
    },
  },
});

// 添加材料类型
const [MatTypeFormModal, matTypeModalApi] = useVbenModal({
  connectedComponent: AddMatTypeModal,
});

function addMatTypeFormModal() {
  matTypeModalApi.setData({}).open();
}

// 添加子单位类型
const [ChildFormModal, childTypeModalApi] = useVbenModal({
  connectedComponent: AddChildTypeModal,
});

function addChildTypeFormModal() {
  childTypeModalApi.setData({}).open();
}

// 废弃材料类型
function deleteMatTypeConfirm() {
  confirm({
    beforeClose({ isConfirm }) {
      if (!isConfirm) return;
      // 这里可以做一些异步操作
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(true);
        }, 1000);
      });
    },
    centered: false,
    content: '你确定要废弃这条数据吗',
    icon: 'question',
  })
    .then(() => {
      message.success('废弃成功');
    })
    .catch(() => {
      // message.error('用户取消了操作');
    });
}

// 删除子单位类型
function deleteChildTypeConfirm() {
  confirm({
    beforeClose({ isConfirm }) {
      if (!isConfirm) return;
      // 这里可以做一些异步操作
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(true);
        }, 1000);
      });
    },
    centered: false,
    content: '你确定要删除这条数据吗',
    icon: 'question',
  })
    .then(() => {
      message.success('删除成功');
    })
    .catch(() => {
      // message.error('用户取消了操作');
    });
}

const activeKey = ref('1');

const checked = ref(false);

// 调用 API 获取数据
/* onMounted(async () => {
  try {
    const response = await getMaterialTypesApi();
    materialData.value = response.data; // 将 API 数据绑定到 Grid
  } catch {
    message.error('获取物料数据失败');
  }
}); */
</script>
<template>
  <div class="vp-raw h-[400px] w-full">
    <MatTypeGrid>
      <template #toolbar-actions>
        <MatTypeFormModal />
        <Space>
          <Button type="primary" @click="addMatTypeFormModal"> 添加 </Button>
          <Button @click="deleteMatTypeConfirm"> 废弃 </Button>
          <Button> 外观验收标准 </Button>
          <Button> 更新实验室 </Button>
          <Checkbox v-model:checked="checked">显示全部</Checkbox>
        </Space>
      </template>
    </MatTypeGrid>
  </div>
  <Tabs v-model:active-key="activeKey" style="margin-left: 10px">
    <TabPane key="1" tab="详细设置" style="height: 100%">
      <div class="vp-raw h-[500px] w-full">
        <DetailGrid />
      </div>
    </TabPane>
    <TabPane key="2" tab="子类型" style="height: 100%" force-render>
      <div class="vp-raw h-[500px] w-full">
        <ChildTypeGrid>
          <template #toolbar-actions>
            <ChildFormModal />
            <Space>
              <Button type="primary" @click="addChildTypeFormModal">
                添加
              </Button>
              <Button @click="deleteChildTypeConfirm"> 删除 </Button>
            </Space>
          </template>
        </ChildTypeGrid>
      </div>
    </TabPane>
  </Tabs>
</template>
