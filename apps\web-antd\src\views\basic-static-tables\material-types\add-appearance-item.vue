<script lang="ts" setup>
import type { MaterialTypesApi } from '#/api/basic-static-tables/material-types';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { addAppearanceItem } from '#/api/basic-static-tables/material-types';

const emit = defineEmits(['success']);

const matType = ref<string>('');

const formData = ref<MaterialTypesApi.AppearanceItem>();

const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 提交函数
  handleSubmit: onSubmit,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'vertical',
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入',
      },
      fieldName: 'SORTER',
      label: '排序',
      rules: 'required',
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Input',
      // 对应组件的参数
      componentProps: {
        placeholder: '请输入',
      },
      // 字段名
      fieldName: 'ITEMNAME',
      // 界面显示的label
      label: '检查项目',
      rules: 'required',
    },
  ],
  wrapperClass: 'grid-cols-1',
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  draggable: true,
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
    // modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData<MaterialTypesApi.AppearanceItem>();
      if (data) {
        matType.value = data.MATTYPE;
        formData.value = data;
        formApi.setValues(formData.value);
      }
    }
  },
  title: '添加验收项',
});

async function onSubmit() {
  try {
    message.loading({
      content: '正在提交中...',
      duration: 0,
      key: 'is-form-submitting',
    });
    modalApi.lock();

    const data = (await formApi.getValues()) as MaterialTypesApi.AppearanceItem;
    // 调用添加分类 API
    data.MATTYPE = matType.value;
    await addAppearanceItem(data);
    emit('success');
    modalApi.close();
    message.success({
      content: '新增成功',
      duration: 2,
      key: 'is-form-submitting',
    });
  } catch (error) {
    message.error({
      content: `新增失败：${(error as Error).message}`,
      key: 'is-form-submitting',
    });
    modalApi.close();
  } finally {
    modalApi.unlock();
  }
}
</script>

<template>
  <Modal>
    <Form />
  </Modal>
</template>
