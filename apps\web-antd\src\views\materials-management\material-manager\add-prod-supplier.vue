<script lang="ts" setup>
import type { MaterialManagerApi } from '#/api/materials-management/material-manager';

// 在 script setup 中添加 watch
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  addProdSupplier,
  getSupplierCodeAndName,
} from '#/api/materials-management/material-manager';
import { $t } from '#/locales';

const emit = defineEmits(['success']);

const formData = ref<MaterialManagerApi.ProdSuppliers>();

const matCode = ref<string>('');

const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 提交函数
  handleSubmit: onSubmit,
  /*   handleValuesChange(_values, fieldsChanged) {
    // message.info(`表单以下字段发生变化：${fieldsChanged.join('，')}`);
  }, */
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'vertical',
  schema: [
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Select',
      // 对应组件的参数
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        options: [
          {
            label: '生产商',
            value: 'SC',
          },
          {
            label: '供应商',
            value: 'GY',
          },
        ],
        onChange: async (value: string) => {
          // message.success(`当前页面${value}`);
          const res = await getSupplierCodeAndName(value, '');
          const options = res.items.map((item) => ({
            label: item.SUPPCODE,
            value: item.SUPPCODE,
          }));
          formApi.updateSchema([
            {
              fieldName: 'SUPPCODE', // 精准更新字段
              componentProps: {
                options,
              },
            },
          ]);
        },
      },
      // 字段名
      fieldName: 'SUPTYPE',
      // 界面显示的label
      label: $t('materials-management.material-manager.suptype'),
      rules: 'required',
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'ApiSelect',
      // 对应组件的参数
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        // autoSelect: 'first',
        immediate: true,
        onChange: async (value: string) => {
          // 获取整个表单的值
          const formValues = await formApi.getValues();
          // 获取 SUPTYPE 的值
          const suptype = formValues.SUPTYPE;

          const res = await getSupplierCodeAndName(suptype, value);
          const options = res.items.map((item) => ({
            label: item.SUPPNAM,
            value: item.SUPPNAM,
          }));
          formApi.updateSchema([
            {
              fieldName: 'SUPPNAM', // 精准更新字段
              componentProps: {
                options,
              },
            },
          ]);
        },
      },
      // 字段名
      fieldName: 'SUPPCODE',
      // 界面显示的label
      label: $t('materials-management.material-manager.suppcode'),
      rules: 'required',
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'ApiSelect',
      // 对应组件的参数
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        /* api: async () => {
          const supType = formData.value?.SUPTYPE;
          if (!supType) {
            return []; // 或者抛出错误、提示用户选择类型
          }
          const res = await getSupplierCodeAndName(supType);
          return res.items.map((item) => ({
            label: item.SUPPNAM,
            value: item.SUPPNAM,
          }));
        }, */
        // autoSelect: 'first',
        immediate: true,
      },
      // 字段名
      fieldName: 'SUPPNAM',
      // 界面显示的label
      label: $t('materials-management.material-manager.suppnam'),
    },
  ],
  showDefaultActions: false,
});

/* function resetForm() {
  formApi.resetForm();
  formApi.setValues(formData.value || {});
} */

const [Modal, modalApi] = useVbenModal({
  draggable: true,
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
    // modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData<MaterialManagerApi.ProdSuppliers>();
      if (data) {
        matCode.value = data.MATCODE;
        formData.value = data;
        formApi.setValues(formData.value);
      }
    }
  },
  title: '新增厂商',
});

async function onSubmit() {
  try {
    message.loading({
      content: '正在提交中...',
      duration: 0,
      key: 'is-form-submitting',
    });
    modalApi.lock();

    const data =
      (await formApi.getValues()) as MaterialManagerApi.ProdSuppliers;
    // 调用添加分类 API
    data.MATCODE = matCode.value;
    await addProdSupplier(data);
    emit('success');
    modalApi.close();
    message.success({
      content: '新增成功',
      duration: 2,
      key: 'is-form-submitting',
    });
  } catch (error) {
    message.error({
      content: `新增失败：${(error as Error).message}`,
      key: 'is-form-submitting',
    });
    modalApi.close();
  } finally {
    modalApi.unlock();
  }
}
</script>

<template>
  <Modal>
    <Form />
    <template #prepend-footer>
      <div class="flex-auto">
        <!--      <Button type="primary" danger @click="resetForm">
          {{ $t('common.reset') }}
        </Button> -->
      </div>
    </template>
  </Modal>
</template>
