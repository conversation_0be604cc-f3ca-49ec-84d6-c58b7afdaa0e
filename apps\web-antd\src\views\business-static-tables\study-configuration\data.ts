import type { VxeColumnPropTypes } from 'vxe-table';

import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { XFolderApi } from '#/api/business-static-tables';

import { $t } from '@vben/locales';

import { createColumn } from '#/utils/lims-grids-config';

export function useColumns(): VxeTableGridOptions<XFolderApi.XFolder>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 60 },
    createColumn({
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 80,
    }),
    createColumn({
      field: 'FDISPSTAT',
      title: $t('commons.status'),
      width: 80,
    }),
    createColumn({
      field: 'DISP_TEMPLATENO',
      title: $t('business-static-tables.studyConfiguration.templateNo'),
      width: 120,
    }),
    createColumn({
      field: 'DISP_PROTOCOLNO',
      title: $t('business-static-tables.studyConfiguration.protocolNo'),
      width: 120,
    }),
    createColumn({
      field: 'DEPT',
      title: $t('business-static-tables.studyConfiguration.dept'),
      width: 100,
    }),
    createColumn({
      field: 'FOLDERNAM',
      title: $t('business-static-tables.studyConfiguration.folderName'),
      width: 300,
      editRender: {
        name: 'input',
      },
    }),
    createColumn({
      field: 'VERSION',
      title: $t('business-static-tables.studyConfiguration.version'),
      width: 80,
    }),
    createColumn({
      field: 'FNOTES1',
      title: $t('business-static-tables.studyConfiguration.notes1'),
      width: 180,
      editRender: {
        name: 'input',
      },
    }),
    createColumn({
      field: 'DISPSTEPCODE',
      title: $t('business-static-tables.studyConfiguration.dispStepCode'),
      width: 100,
    }),
    createColumn({
      field: 'STARTDDATE',
      title: $t('business-static-tables.studyConfiguration.startDate'),
      width: 120,
    }),
    createColumn({
      field: 'EXPDATE',
      title: $t('business-static-tables.studyConfiguration.expDate'),
      width: 120,
    }),
    createColumn({
      field: 'CONDITION_TYPE',
      title: $t('business-static-tables.studyConfiguration.conditionType'),
      width: 100,
      editRender: {
        name: 'input',
      },
    }),
    {
      field: 'action',
      title: $t('commons.action'),
      minWidth: 150,
      slots: {
        default: 'action',
      },
      fixed: 'right',
    },
  ];
}

export function useCondIntTestMatrixColumns(): VxeTableGridOptions<XFolderApi.CondIntTestMatrix>['columns'] {
  const columns: VxeTableGridOptions<XFolderApi.CondIntTestMatrix>['columns'] =
    [];
  for (let i = 1; i <= 31; i++) {
    if (i === 3) {
      columns.push({
        field: `Col${i}`,
        title: '测试名称',
        width: 150,
      });
    } else if (i === 4) {
      columns.push({
        field: `Col${i}`,
        title: '条件',
        width: 200,
        align: 'left',
      });
    } else if (i < 5) {
      columns.push({
        field: `Col${i}`,
        title: $t(`Col${i}`),
        width: 80,
        visible: false,
      });
    } else {
      columns.push({
        field: `Col${i}`,
        title: $t(`Col${i}`),
        width: 80,
        cellRender: {
          name: 'StabCellTag',
        },
      });
    }
  }
  // 调换columns中Col3和Col4的位置;
  const col3 = columns[2];
  const col4 = columns[3];
  if (col3 && col4) {
    columns[2] = col4;
    columns[3] = col3;
  }
  return columns;
}

export function useMaterialsColumns(): VxeTableGridOptions<XFolderApi.XFolderRelations>['columns'] {
  return [
    createColumn({
      field: 'MATCODE',
      title: $t('business-static-tables.studyConfiguration.matCode'),
      width: '20%',
    }),
    createColumn({
      field: 'MATNAME',
      title: $t('business-static-tables.studyConfiguration.matName'),
      width: '20%',
    }),
    createColumn({
      field: 'CONTAINER_MATCODE',
      title: $t('business-static-tables.studyConfiguration.containerMatCode'),
      width: '20%',
    }),
    createColumn({
      field: 'PULL_CONTAINER_MATCODE',
      title: $t(
        'business-static-tables.studyConfiguration.pullContainerMatCode',
      ),
      width: '20%',
    }),
    {
      field: 'action',
      title: $t('commons.action'),
      minWidth: 120,
      slots: {
        default: 'action',
      },
    },
  ];
}

export function useStabTestQtyColumns(
  unitEditRander: VxeColumnPropTypes.EditRender,
): VxeTableGridOptions<XFolderApi.XStabTestQty>['columns'] {
  return [
    createColumn({
      field: 'SP_TESTNO',
      title: $t('business-static-tables.studyConfiguration.spTestNo'),
      width: 120,
    }),
    createColumn({
      field: 'UNITS_QTY',
      title: $t('business-static-tables.studyConfiguration.unitsQty'),
      width: 120,
      editRender: {
        name: 'InputNumber',
      },
    }),
    createColumn({
      field: 'UNITS',
      title: $t('business-static-tables.studyConfiguration.units'),
      width: 180,
      editRender: unitEditRander,
    }),
    createColumn({
      field: 'STORED_QTY',
      title: $t('business-static-tables.studyConfiguration.storedQty'),
      width: 120,
    }),
    createColumn({
      field: 'CONTAINER_MATCODE',
      title: $t('business-static-tables.studyConfiguration.containerMatCode'),
      width: 150,
    }),
    createColumn({
      field: 'QTY',
      title: $t('business-static-tables.studyConfiguration.qty'),
      width: 120,
    }),
    createColumn({
      field: 'PULL_CONTAINER_MATCODE',
      title: $t(
        'business-static-tables.studyConfiguration.pullContainerMatCode',
      ),
    }),
    {
      field: 'action',
      title: $t('commons.action'),
      minWidth: 150,
      slots: { default: 'action' },
      fixed: 'right',
    },
  ];
}

export function useXFolderIntervalTatColumns(): VxeTableGridOptions<XFolderApi.XFolderIntervalTat>['columns'] {
  return [
    createColumn({
      field: 'INTERVAL',
      title: $t('business-static-tables.studyConfiguration.interval'),
      width: 120,
    }),
    createColumn({
      field: 'TATBEFOREAFTER',
      title: $t('business-static-tables.studyConfiguration.tatBeforeAfter'),
      width: 220,
    }),
    {
      field: 'action',
      title: $t('commons.action'),
      minWidth: 60,
      slots: {
        default: 'action',
      },
    },
  ];
}
