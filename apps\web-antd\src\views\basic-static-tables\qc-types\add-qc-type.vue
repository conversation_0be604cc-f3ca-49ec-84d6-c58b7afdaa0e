<script lang="ts" setup>
import type { QcTypeApi } from '#/api/';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { addQcType, getQcGroup } from '#/api/basic-static-tables/qc-types';

const emit = defineEmits(['success']);

const formData = ref<QcTypeApi.QcType>();

const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 提交函数
  handleSubmit: onSubmit,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'vertical',
  schema: [
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Input',
      // 字段名
      fieldName: 'QCTYPE',
      // 界面显示的label
      label: 'QC类型',
      rules: 'required',
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'ApiSelect',
      // 对应组件的参数
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: async () => {
          const res = await getQcGroup();
          return res.items.map((item) => ({
            label: item.DISPLAY_TEXT,
            value: item.QC_GROUP,
          }));
        },
        // autoSelect: 'first',
        immediate: true,
      },
      // 字段名
      fieldName: 'QCGROUP',
      // 界面显示的label
      label: 'QC组',
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Select',
      // 对应组件的参数
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        options: [
          {
            label: '是',
            value: 'Y',
          },
          {
            label: '否',
            value: 'N',
          },
        ],
      },
      // 字段名
      fieldName: 'UPDPARENT',
      // 界面显示的label
      label: '更新父样品编号',
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Input',
      // 字段名
      fieldName: 'AUTOSELECT',
      // 界面显示的label
      label: '自动选择组',
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'ApiSelect',
      // 对应组件的参数
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        options: [
          {
            label: '是',
            value: 'Y',
          },
          {
            label: '否',
            value: 'N',
          },
        ],
      },
      // 字段名
      fieldName: 'INSTRUMENTQC',
      // 界面显示的label
      label: '设备QC',
    },
  ],
  wrapperClass: 'grid-cols-1',
  showDefaultActions: false,
});

/* function resetForm() {
  formApi.resetForm();
  formApi.setValues(formData.value || {});
} */

const [Modal, modalApi] = useVbenModal({
  draggable: true,
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
    // modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const Data = modalApi.getData<QcTypeApi.QcType>();
      if (Data) {
        formData.value = Data;
        formApi.setValues(formData.value);
      }
    }
  },
  title: '新增QC类型',
});

async function onSubmit() {
  try {
    message.loading({
      content: '正在提交中...',
      duration: 0,
      key: 'is-form-submitting',
    });
    modalApi.lock();
    const data = (await formApi.getValues()) as QcTypeApi.QcType;
    await addQcType(data);
    emit('success');
    modalApi.close();
    message.success({
      content: '新增成功',
      duration: 2,
      key: 'is-form-submitting',
    });
  } catch (error) {
    message.error({
      content: `新增失败：${(error as Error).message}`,
      key: 'is-form-submitting',
    });
    modalApi.close();
  } finally {
    modalApi.unlock();
  }
}
</script>

<template>
  <Modal>
    <Form />
    <template #prepend-footer>
      <div class="flex-auto">
        <!--      <Button type="primary" danger @click="resetForm">
          {{ $t('common.reset') }}
        </Button> -->
      </div>
    </template>
  </Modal>
</template>
