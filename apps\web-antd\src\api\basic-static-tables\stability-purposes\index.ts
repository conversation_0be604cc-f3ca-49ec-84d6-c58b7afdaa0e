import { callServer, getDataSet } from '#/api/core';

export namespace StabilityPurposesApi {
  export interface XStabilityPurpose {
    [key: string]: any;
    PURPOSE: string;
    DESCRIPTION: string;
  }
}

const $delStabilityPurposeApi = async (purposes: string[]) => {
  return await callServer('stabilityPurposes.deletePurpose', [purposes]);
};
const $getStabilityPurposeApi = async () => {
  return await getDataSet('stabilityPurposes.getStabPurposes', []);
};
const $addStabilityPurposeApi = async (
  data: Omit<StabilityPurposesApi.XStabilityPurpose, 'ORIGREC'>,
) => {
  return await callServer('stabilityPurposes.addPurpose', [
    data.PURPOSE,
    data.DESCRIPTION,
    data.eventCode,
    data.comment,
  ]);
};

export {
  $addStabilityPurposeApi,
  $delStabilityPurposeApi,
  $getStabilityPurposeApi,
};
