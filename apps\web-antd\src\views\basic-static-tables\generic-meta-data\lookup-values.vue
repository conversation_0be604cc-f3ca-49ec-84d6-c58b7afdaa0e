<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { LookupTableApi } from '#/api/basic-static-tables/generic-meta-data';

import { computed, ref } from 'vue';

import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { Button, message, Modal, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { deleteLookupValueApi, getLookupValuesTableApi } from '#/api';
import { addLookupValueApi } from '#/api/basic-static-tables/generic-meta-data/lookup';

import AddLookupValueForm from './add-lookup-value-form.vue';
import { useLookupValuesColumns } from './lookup-data';

const lookupData = ref<LookupTableApi.MetaDataLookups>();
const gridOptions: VxeTableGridOptions<LookupTableApi.MetaDataLookupValue> = {
  columns: useLookupValuesColumns(),
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  // pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }) => {
        const data = drawerApi.getData<LookupTableApi.MetaDataLookups>();
        if (!data) {
          return;
        }
        const { NAME } = data;
        return await getLookupValuesTableApi({
          page: page.currentPage,
          pageSize: page.pageSize,
          key: NAME,
        });
      },
    },
  },
  showOverflow: true,
  exportConfig: {},
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddLookupValueForm,
  destroyOnClose: true,
});

function hasEditStatus(row: LookupTableApi.MetaDataLookupValue) {
  return gridApi.grid?.isEditByRow(row);
}

function editRowEvent(row: LookupTableApi.MetaDataLookupValue) {
  gridApi.grid?.setEditRow(row);
}

async function saveRowEvent(row: LookupTableApi.MetaDataLookupValue) {
  await gridApi.grid?.clearEdit();
  gridApi.setLoading(true);
  addLookupValueApi(row);
  setTimeout(() => {
    gridApi.setLoading(false);
    message.success({
      content: `保存成功！`,
    });
  }, 600);
}

const cancelRowEvent = (_row: LookupTableApi.MetaDataLookupValue) => {
  gridApi.grid?.clearEdit();
};

const [Drawer, drawerApi] = useVbenDrawer({
  onConfirm: onSubmit,
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<LookupTableApi.MetaDataLookups>();
      if (data) {
        lookupData.value = data;
        gridApi?.query();
      } else {
        gridApi?.reload();
      }
    }
  },
  showCancelButton: false,
  showConfirmButton: false,
});

async function onSubmit() {
  drawerApi.close();
}

const getDrawerTitle = computed(() => {
  if (lookupData.value) {
    return `${lookupData.value.NAME}`;
  }
  return '';
});

function onCreate() {
  formModalApi.setData({ LOOKUP_NAME: lookupData.value?.NAME }).open();
}

async function onDelete() {
  const checkLookupNames: string[][] = gridApi.grid
    .getCheckboxRecords()
    .map((row) => [row.LOOKUP_NAME, row.VALUE]) as string[][];
  if (checkLookupNames.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }
  if (checkLookupNames.length > 1) {
    message.warning('只能选择一条数据！');
    return;
  }
  await confirm('是否确认删除选中的数据？', '删除确认');
  gridApi.grid?.getCheckboxRecords().forEach((row) => {
    gridApi.grid?.remove(row);
  });
  const delName = checkLookupNames[0] as string[];
  await deleteLookupValueApi(delName);
  message.success('删除成功！');
  onRefresh();
}

function onRefresh() {
  gridApi.query();
}

function confirm(content: string, title: string) {
  return new Promise((reslove, reject) => {
    Modal.confirm({
      content,
      onCancel() {
        reject(new Error('已取消'));
      },
      onOk() {
        reslove(true);
      },
      title,
    });
  });
}
</script>

<template>
  <Drawer class="w-full max-w-[800px]" :title="getDrawerTitle">
    <FormModal @success="onRefresh" />
    <Page auto-content-height>
      <Grid>
        <template #toolbar-actions>
          <Space :size="[4, 0]" wrap>
            <Button type="primary" @click="onCreate">
              <!-- <Plus class="size-4" /> -->
              {{ $t('ui.actionTitle.create') }}
            </Button>
            <Button type="primary" danger @click="onDelete">
              <!-- <Delete class="size-4" /> -->
              {{ $t('ui.actionTitle.delete') }}
            </Button>
          </Space>
        </template>
        <template #action="{ row }">
          <template v-if="hasEditStatus(row)">
            <Button type="link" @click="saveRowEvent(row)">保存</Button>
            <Button type="link" @click="cancelRowEvent(row)">取消</Button>
          </template>
          <template v-else>
            <Button type="link" @click="editRowEvent(row)">编辑</Button>
          </template>
        </template>
      </Grid>
    </Page>
  </Drawer>
</template>
