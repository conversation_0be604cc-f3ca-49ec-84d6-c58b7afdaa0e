<script lang="ts" setup>
import { ref } from 'vue';

import { Tab<PERSON>ane, Tabs } from 'ant-design-vue';

import Connect from './tcp-page-components/connect-page.vue';
import ReceiveData from './tcp-page-components/receive-data.vue';
import SendCommand from './tcp-page-components/send-command.vue';

const components = [
  {
    value: '连接',
    page: Connect,
  },
  {
    value: '接收数据',
    page: ReceiveData,
  },
  {
    value: '发送命令',
    page: SendCommand,
  },
];
const activeKey = ref('连接');
</script>
<template>
  <div>
    <Tabs v-model:active-key="activeKey" class="1/3">
      <TabPane v-for="item in components" :key="item.value" :tab="item.value">
        <div class="h-[500px]">
          <component
            :is="
              components.find((item) => item.value === activeKey)?.page ||
              Connect
            "
          />
        </div>
      </TabPane>
    </Tabs>
  </div>
</template>
