import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { MethodManagerApi } from '#/api/business-static-tables';

import { $t } from '#/locales';
import { createColumn } from '#/utils/lims-grids-config';

export function useMethodRelateTestColumns(): VxeTableGridOptions<MethodManagerApi.TestMethodVersion>['columns'] {
  return [
    createColumn({
      field: 'METHOD',
      title: $t('business-static-tables.methodManager.method'),
      width: 300,
    }),
    createColumn({
      field: 'VERSION',
      title: $t('commons.version'),
      minWidth: 80,
    }),
  ];
}
