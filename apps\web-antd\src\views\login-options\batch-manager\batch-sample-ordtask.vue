<script setup lang="ts">
import type { BatcheManagerApi } from '#/api/login-options/batch-manager';

import { watch } from 'vue';

import { Button, Space } from 'ant-design-vue';

import { getSampleTestsAnalytesList } from '#/api/login-options/batch-manager';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

// import AddRecipeForm from './add-recipe.vue';
import {
  useBatchOrdtaskColumns,
  useBatchOrdtaskFilterSchema,
} from './batch-manager-data';

const props = defineProps<{
  currentTestRow: BatcheManagerApi.BatchOrders | null;
  mode: '' | string;
}>();

watch(
  () => props.currentTestRow,
  (_val) => {
    onRefresh();
  },
);
const colums = useBatchOrdtaskColumns();
const filterSchema = useBatchOrdtaskFilterSchema();
const queryData = async () => {
  if (!props.currentTestRow) return [];
  const data = await getSampleTestsAnalytesList(
    props.currentTestRow.ORDNO,
    props.mode,
    '',
  );
  return data.items;
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};

const {
  Grid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
  CurrentRow,
} = useLimsGridsConfig<BatcheManagerApi.BatchOrdtask>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

function onRefresh() {
  gridApi.query();
}

defineExpose({ CurrentRow }); // Vue 3 的 expose 语法

// 添加配方
// const [FormModal, formModalApi] = useVbenModal({
//   connectedComponent: AddRecipeForm,
// });

// async function onCreate() {
//   if (props.currentTestRow === null) return;
//   const matcode = props.currentTestRow.MATCODE;
//   const sampleGroupCode = props.currentTestRow.SAMPLEGROUPCODE;
//   formModalApi
//     .setData({ MATCODE: matcode, SAMPLEGROUPCODE: sampleGroupCode })
//     .open();
// }
</script>

<template>
  <FormModal @success="onRefresh" />
  <div class="h-[350px] w-full">
    <Grid>
      <template #toolbar-actions>
        <Space>
          <Button type="primary">
            {{ $t('login-options.batchManager.editTest') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent(row)">
            {{ $t('login-options.save') }}
          </Button>
          <Button type="link" @click="cancelRowEvent(row)">
            {{ $t('login-options.cancel') }}
          </Button>
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">
            {{ $t('login-options.edit') }}
          </Button>
        </template>
      </template>
    </Grid>
  </div>
</template>
