<script lang="ts" setup>
import type { BatcheManagerApi } from '#/api/login-options/batch-manager';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { copySampleAndAudit } from '#/api/login-options/batch-manager';
import { $t } from '#/locales';

const emit = defineEmits(['success']);

const formData = ref<BatcheManagerApi.BatchOrders>();
const sBatchId = ref<number>(0);
const sProfile = ref<string>('');
const sSpCode = ref<number>(0);
const sOrdno = ref<string>('');

const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 提交函数
  handleSubmit: onSubmit,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'vertical',
  schema: [
    {
      component: 'Input',
      fieldName: 'NUM',
      label: $t('login-options.batchManager.num'),
      rules: 'required',
    },
  ],
  // wrapperClass: 'grid-cols-1',
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  draggable: true,
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
    // modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData<BatcheManagerApi.BatchOrders>();
      if (data) {
        sBatchId.value = data.BATCHID;
        sProfile.value = data.PROFILE;
        sSpCode.value = data.SP_CODE;
        sOrdno.value = data.ORDNO;
        formData.value = data;
        formApi.setValues(formData.value);
      }
    }
  },
  title: '复制样品',
});

async function onSubmit() {
  try {
    message.loading({
      content: '正在提交中...',
      duration: 0,
      key: 'is-form-submitting',
    });
    modalApi.lock();

    const data = await formApi.getValues();
    // 调用添加分类 API
    const num: number = Number(data?.NUM);
    await copySampleAndAudit(
      sSpCode.value,
      sProfile.value,
      sBatchId.value,
      num,
      sOrdno.value,
    );

    emit('success');
    modalApi.close();
    message.success({
      content: '操作成功',
      duration: 2,
      key: 'is-form-submitting',
    });
  } catch (error) {
    message.error({
      content: `操作失败：${(error as Error).message}`,
      key: 'is-form-submitting',
    });
    modalApi.close();
  } finally {
    modalApi.unlock();
  }
}
</script>

<template>
  <Modal>
    <Form />
    <template #prepend-footer>
      <div class="flex-auto"></div>
    </template>
  </Modal>
</template>
