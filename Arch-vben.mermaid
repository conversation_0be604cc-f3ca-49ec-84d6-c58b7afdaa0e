flowchart TD
    %% Frontend Applications
    subgraph "Frontend Applications"
        A1("apps/web-antd (Web UI Variant)"):::frontend
        A2("apps/web-ele (Web UI Variant)"):::frontend
        A3("apps/web-naive (Web UI Variant)"):::frontend
        A4("playground (Demo/Testing)"):::frontend
    end

    %% Backend API Layer
    subgraph "Backend API Layer"
        B1("apps/backend-mock (Backend API Mocks)"):::backend
    end

    %% Shared Modules
    subgraph "Shared Modules"
        S1("packages/@core (Core & UI Kit)"):::shared
        S2("packages/constants (Constants)"):::shared
        S3("packages/utils (Utilities)"):::shared
        S4("packages/types (Type Definitions)"):::shared
        S5("packages/stores (State Management)"):::shared
    end

    %% Documentation
    subgraph "Documentation"
        D1("docs (VitePress Documentation)"):::docs
    end

    %% Tooling & CI/CD Infrastructure
    subgraph "Tooling & CI/CD"
        T1("internal (Monorepo Tooling & Config)"):::tooling
        T2("scripts (Deployment & Build Scripts)"):::tooling
        T3(".github/workflows (CI/CD Pipelines)"):::tooling
    end

    %% Data Flow from Frontend to Backend API
    A1 -->|"HTTP_Requests"| B1
    A2 -->|"HTTP_Requests"| B1
    A3 -->|"HTTP_Requests"| B1
    A4 -->|"HTTP_Requests"| B1

    %% Frontend usage of Shared Modules
    A1 -->|"Uses"| S1
    A1 -->|"Uses"| S2
    A1 -->|"Uses"| S3
    A1 -->|"Uses"| S4
    A1 -->|"Uses"| S5

    A2 -->|"Uses"| S1
    A2 -->|"Uses"| S2
    A2 -->|"Uses"| S3
    A2 -->|"Uses"| S4
    A2 -->|"Uses"| S5

    A3 -->|"Uses"| S1
    A3 -->|"Uses"| S2
    A3 -->|"Uses"| S3
    A3 -->|"Uses"| S4
    A3 -->|"Uses"| S5

    A4 -->|"Uses"| S1
    A4 -->|"Uses"| S2
    A4 -->|"Uses"| S3
    A4 -->|"Uses"| S4
    A4 -->|"Uses"| S5

    %% Documentation links to Frontend and Shared Modules
    D1 -->|"Guides"| A1
    D1 -->|"Guides"| A2
    D1 -->|"Guides"| A3
    D1 -->|"Guides"| A4
    D1 -->|"Explains"| S1

    %% Tooling & CI/CD connections to Frontend Applications
    T1 -->|"Configures"| A1
    T1 -->|"Configures"| A2
    T1 -->|"Configures"| A3
    T1 -->|"Configures"| A4

    T2 -->|"Build_Deploy"| A1
    T2 -->|"Build_Deploy"| A2
    T2 -->|"Build_Deploy"| A3
    T2 -->|"Build_Deploy"| A4

    T3 -->|"Triggers"| A1
    T3 -->|"Triggers"| A2
    T3 -->|"Triggers"| A3
    T3 -->|"Triggers"| A4

    %% Click Events
    click A1 "https://github.com/vbenjs/vue-vben-admin/tree/main/apps/web-antd"
    click A2 "https://github.com/vbenjs/vue-vben-admin/tree/main/apps/web-ele"
    click A3 "https://github.com/vbenjs/vue-vben-admin/tree/main/apps/web-naive"
    click A4 "https://github.com/vbenjs/vue-vben-admin/tree/main/playground"
    click B1 "https://github.com/vbenjs/vue-vben-admin/tree/main/apps/backend-mock"
    click S1 "https://github.com/vbenjs/vue-vben-admin/tree/main/packages/@core"
    click S2 "https://github.com/vbenjs/vue-vben-admin/tree/main/packages/constants"
    click S3 "https://github.com/vbenjs/vue-vben-admin/tree/main/packages/utils"
    click S4 "https://github.com/vbenjs/vue-vben-admin/tree/main/packages/types"
    click S5 "https://github.com/vbenjs/vue-vben-admin/tree/main/packages/stores"
    click D1 "https://github.com/vbenjs/vue-vben-admin/tree/main/docs"
    click T1 "https://github.com/vbenjs/vue-vben-admin/tree/main/internal"
    click T2 "https://github.com/vbenjs/vue-vben-admin/tree/main/scripts"
    click T3 "https://github.com/vbenjs/vue-vben-admin/tree/main/.github/workflows"

    %% Styles
    classDef frontend fill:#AEDFF7,stroke:#333,stroke-width:2px;
    classDef backend fill:#F7A1A1,stroke:#333,stroke-width:2px;
    classDef shared fill:#C7F7C7,stroke:#333,stroke-width:2px,stroke-dasharray: 5 5;
    classDef docs fill:#F7F7A1,stroke:#333,stroke-width:2px;
    classDef tooling fill:#D1C4E9,stroke:#333,stroke-width:2px;
