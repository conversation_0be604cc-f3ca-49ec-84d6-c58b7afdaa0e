<script lang="ts" setup>
import { computed } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { $addSchemaGroupApi } from '#/api/business-static-tables';
import { $t } from '#/locales';

import { useSchema } from './data';

const emit = defineEmits(['success']);

const getTitle = computed(() => {
  return $t('ui.actionTitle.create', [
    $t('business-static-tables.specSchema.specSchemaGroup'),
  ]);
});

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: useSchema(),
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    modalApi.lock();
    try {
      const res = await $addSchemaGroupApi(data);
      if (!res) {
        message.error($t(''));
        return;
      }
      if (res) emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
});
</script>

<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
  </Modal>
</template>
