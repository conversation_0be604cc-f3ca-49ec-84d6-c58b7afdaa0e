import type { VxeGridPropTypes } from 'vxe-table';

import type { VbenFormProps } from '#/adapter/form';
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';

import { ref } from 'vue';

import { cloneDeep } from '@vben/utils';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { updateProvider } from '#/api';
import { showAduitViewer } from '#/components/audit-viewer';

export function useLimsGridsConfig<T = any>(
  useColumns: VxeTableGridOptions<T>['columns'],
  useFilterSchema: null | VbenFormProps['schema'],
  queryData: (...args: any[]) => Promise<any>,
  customGridOptions: Partial<VxeTableGridOptions<T>> = {},
  customGridEvents: Partial<VxeGridListeners<T>> = {},
  customFormOptions: Partial<VbenFormProps> = {},
) {
  const CurrentCheckRow = ref<null | T>(null); // 当前唯一勾选行
  const SelectedCheckRows = ref<T[]>([]); // 当前所有勾选行
  const CurrentRow = ref<null | T>(null); // 当前行
  const gridOptions: VxeTableGridOptions<T> = {
    columns: useColumns,
    stripe: true,
    border: true,
    keepSource: true,
    editConfig: {
      mode: 'row',
      trigger: 'manual',
      showStatus: true,
      autoClear: false,
    },
    height: 'auto',
    pagerConfig: {},
    proxyConfig: {
      ajax: {
        query: async (params) => {
          // debugger;
          // 此处必须手动执行一下CurrentChange行切换事件，否则在右上角刷新时，或者重新加载数据时，当前行清除了但是未触发CurrentChange事件问题导致关联数据未清除
          CurrentCheckRow.value = null; // 清空选中行
          SelectedCheckRows.value = []; // 清空选中行
          CurrentRow.value = null; // 清空当前行
          return await queryData(params);
        },
      },
    },
    exportConfig: {},
    showOverflow: true,
    rowConfig: {
      isCurrent: true,
      // drag: true, // 开启行拖拽
    },
    rowDragConfig: {
      trigger: 'row',
      showGuidesStatus: true,
    },
    currentRowConfig: {},
    columnConfig: {
      drag: true,
      isCurrent: true,
    },
    columnDragConfig: {
      isCrossDrag: true,
      showGuidesStatus: true,
      showIcon: false,
      trigger: 'cell',
    },
    checkboxConfig: {
      range: true,
    },
    resizableConfig: {
      isDblclickAutoWidth: true,
    },
    toolbarConfig: {
      custom: true,
      export: true,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
    menuConfig: {
      body: {
        options: [
          [
            {
              code: 'copy',
              name: '复制内容（Ctrl+C）',
              prefixConfig: { icon: 'vxe-icon-copy' },
              visible: true,
              disabled: false,
            },
          ],
          [
            {
              code: 'viewAudit',
              name: '查看历史',
              prefixConfig: { icon: 'vxe-icon-table' },
              visible: true,
              disabled: false,
            },
          ],
        ],
      },
    },
  };
  // 合并自定义配置
  if (customGridOptions) {
    Object.assign(gridOptions, customGridOptions);
  }

  const gridEvents: VxeGridListeners<T> = {
    cellMenu({ row, column }) {
      const $grid = gridApi.grid;
      if ($grid) {
        $grid.setCurrentRow(row);
        $grid.setCurrentColumn(column);
      }
    },
    menuClick({ menu }) {
      if (menu.code === 'copy') {
        const col = gridApi.grid.getCurrentColumn();
        const row = gridApi.grid.getCurrentRecord();
        if (col && row) {
          navigator.clipboard.writeText(row[col.field]);
        }
      }
      if (menu.code === 'viewAudit') {
        const currentRow = gridApi.grid?.getCurrentRecord();
        if (currentRow) {
          showAduitViewer({
            tableName: gridApi.grid.params.tableName,
            origrec: currentRow.ORIGREC,
          });
        }
      }
    },
    checkboxChange: (_params) => {
      // debugger;
      // 如果选中多行则设置当前行为空
      const checkRecords = gridApi.grid.getCheckboxRecords();
      CurrentCheckRow.value =
        checkRecords.length > 1 ? null : (checkRecords[0] as T); // 选中行
      SelectedCheckRows.value = checkRecords; // 选中行
    },
    currentRowChange: (_params) => {
      // console.log('defaultcurrentChange');
      CurrentRow.value = gridApi.grid.getCurrentRecord(); // 当前行
    },
    editActivated: saveEditingRowOriginalData,
    editClosed: updateProvider,
  };

  // 合并自定义事件
  if (customGridEvents) {
    // 特殊处理 currentChange，先执行默认的再执行自定义的
    if (customGridEvents.currentRowChange) {
      const defaultCurrentChange = gridEvents.currentRowChange;
      gridEvents.currentRowChange = (params) => {
        defaultCurrentChange?.(params);
        customGridEvents.currentRowChange?.(params);
      };
    }
    // 其余事件直接替换
    Object.entries(customGridEvents).forEach(([key, handler]) => {
      if (key !== 'currentRowChange' && handler) {
        (gridEvents as any)[key] = handler;
      }
    });
  }

  let formOptions: undefined | VbenFormProps;
  if (Array.isArray(useFilterSchema) && useFilterSchema.length > 0) {
    formOptions = {
      schema: useFilterSchema,
      submitOnChange: false,
      collapsed: true,
      showCollapseButton: true,
    };
    if (customFormOptions) {
      // 合并自定义表单配置
      Object.assign(formOptions, customFormOptions);
    }
  }

  const [Grid, gridApi] = useVbenVxeGrid({
    gridOptions,
    gridEvents,
    formOptions,
  });

  function hasEditStatus(row: T) {
    return gridApi.grid?.isEditByRow(row);
  }

  function editRowEvent(row: T) {
    gridApi.grid?.setEditRow(row);
  }

  async function saveRowEvent(row: T) {
    await gridApi.grid?.clearEdit();
    gridApi.grid.reloadRow(row); // 清除编辑状态
    gridApi.setLoading(true);
    setTimeout(() => {
      gridApi.setLoading(false);
      message.success({
        content: `保存成功！`,
      });
    }, 600);
  }

  const cancelRowEvent = (row: T) => {
    gridApi.grid?.clearEdit().then(() => {
      // 还原行数据
      gridApi.grid.revertData(row);
    });
  };
  return {
    Grid,
    gridApi,
    hasEditStatus,
    editRowEvent,
    saveRowEvent,
    cancelRowEvent,
    CurrentCheckRow,
    SelectedCheckRows,
    CurrentRow,
  } as const;
}

const baseColumn = {
  align: 'center' as const,
  filterRender: { name: 'TableFilterInput' },
  filters: [{ data: '' }],
  sortable: true,
};

// 工厂函数生成列
export function createColumn(options: Partial<VxeGridPropTypes.Column>) {
  return { ...baseColumn, ...options };
}

export async function saveEditingRowOriginalData(params: any) {
  const { row, $grid } = params;
  if (!row.ORIGREC) return;
  if (!$grid.props.params) {
    $grid.props.params = {};
  }
  if (!$grid.props.params.editingRows) {
    $grid.props.params.editingRows = [];
  }
  const editingRows = $grid.props.params.editingRows;
  if (
    editingRows.some(
      (item: { ORIGREC: number }) => item.ORIGREC === row.ORIGREC,
    )
  ) {
    editingRows.splice(
      editingRows.findIndex(
        (item: { ORIGREC: number }) => item.ORIGREC === row.ORIGREC,
      ),
      1,
    );
  }

  editingRows.push(cloneDeep(row));
}
