<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { CategoriesApi } from '#/api/';

import { confirm, Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Button, message, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { deleteCategorie, getCategorieList } from '#/api/';

import AddCategoriesForm from './add-categories.vue';
import { clientCategoriesColumns, useCategoryFilterSchema } from './data';

const gridOptions: VxeTableGridOptions<CategoriesApi.Categories> = {
  columns: clientCategoriesColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getCategorieList();
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const formOptions: VbenFormProps = {
  schema: useCategoryFilterSchema(),
  submitOnChange: false,
  collapsed: true,
  showCollapseButton: true,
  submitButtonOptions: {
    content: '查询',
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  formOptions,
});

function onRefresh() {
  gridApi.query();
}

// 添加客户类别
const [AddCategoriesFormModal, categorieModalApi] = useVbenModal({
  connectedComponent: AddCategoriesForm,
});

function addCategory() {
  categorieModalApi.setData(null).open();
}

// 删除客户类别
async function deleteCategory() {
  // 获取选中行
  const aCategory: string[] = gridApi.grid
    .getCheckboxRecords()
    .map((row) => row.CATEGORY) as string[];
  if (aCategory.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${aCategory.length} 条数据吗？`,
      icon: 'warning',
      centered: false,
    });

    await deleteCategorie(aCategory);
    message.success('删除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}
</script>
<template>
  <Page auto-content-height>
    <FormDrawer @success="onRefresh" />
    <AddCategoriesFormModal @success="onRefresh" />
    <Grid>
      <template #toolbar-actions>
        <Space>
          <Button type="primary" @click="addCategory">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="deleteCategory">
            {{ $t('ui.actionTitle.delete') }}
          </Button>
        </Space>
      </template>
    </Grid>
  </Page>
</template>
