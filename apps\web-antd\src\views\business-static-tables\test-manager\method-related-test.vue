<script setup lang="ts">
import type { TestManagerApi } from '#/api/business-static-tables/test-manager';

import { watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import {
  $checkEquipOtherServGrpApi,
  $delMethodRelatedApi,
  $getMethodRelatedListApi,
} from '#/api/business-static-tables/test-manager';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';
import { confirm } from '#/utils/utils';
import MethodVersionInfo from '#/views/runbuild-resent-approve/method-version-info.vue';

import AddMethodRelatedForm from './add-method-related-form.vue';
import { useMethodRelateTestColumns, useMethodRelateTestSchema } from './data';
import ManageOtherEquipment from './manage-other-equipment.vue';
import OutSourceLabs from './out-source-labs.vue';
import PromptEditEln from './prompt-edit-eln.vue';
import PromptEditEquipments from './prompt-edit-equipments.vue';
import RequiredDocuments from './required-documents.vue';

const props = defineProps<{
  currentTestRow: null | TestManagerApi.Test;
}>();

watch(
  () => props.currentTestRow,
  (_val) => {
    onRefresh();
  },
);

const colums = useMethodRelateTestColumns();
const filterSchema = useMethodRelateTestSchema();
const queryData = async () => {
  if (!props.currentTestRow?.TESTCODE) return [];
  return $getMethodRelatedListApi(props.currentTestRow.TESTCODE);
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};
// const events: VxeGridListeners<TestManagerApi.MethodRelatedToTests> = {
//   // 这里可以添加其他事件监听
//   currentChange: (params) => {
//     console.log('Current row changed:', params);
//   },
// };
const {
  Grid: MethedRelateTestGrid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
  CurrentRow,
} = useLimsGridsConfig<TestManagerApi.MethodRelatedToTests>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

async function onDelete() {
  const checkRecords: TestManagerApi.MethodRelatedToTests[] =
    gridApi.grid?.getCheckboxRecords();
  if (checkRecords.length === 0) {
    message.warning($t('commons.selectOne'));
    return;
  }
  await confirm($t('commons.deleteConfirm'), $t('commons.deleteConfirmTitle'));
  const arrDelData = checkRecords.map((item) => item.ORIGREC);
  const aMethods = checkRecords.map((item) => item.METHOD);
  const aSelServGrp = checkRecords.map((item) => item.SERVGRP);
  const aDept = checkRecords.map((item) => item.DEPT);
  const aEqTypes = checkRecords.map((item) => item.EQTYPE);
  const nTestCode = props.currentTestRow?.TESTCODE || 0;
  const aSelectedEq = null;
  let sParam = '';
  for (const [i, sMethod] of aMethods.entries()) {
    const sCrtServGrp = aSelServGrp[i] || '';
    const sDept = aDept[i] || '';
    const sEqType = aEqTypes[i] || '';
    const aEquipOtherSGrp = await $checkEquipOtherServGrpApi({
      nTestCode,
      sMethod,
      aSelectedEq,
      sCrtServGrp,
      sDept,
      sEqType,
      arrDelData,
    });
    if (aEquipOtherSGrp && aEquipOtherSGrp.length > 1) {
      const sEq = aEquipOtherSGrp[0].ToString();
      const sMethods = aEquipOtherSGrp[1].join('\n');
      sParam += `\n${sEq}${$t('business-static-tables.testManager.forMethod')}\n${sMethods}`;
    }
  }
  if (sParam) {
    const sContinue = await confirm(
      $t('business-static-tables.testManager.confirmDelMethod', [sParam]),
      $t('commons.question'),
    );
    if (!sContinue) return;
  }

  const deleteOk = await $delMethodRelatedApi({
    arrDelData,
    nTestCode,
    aSelServGrp,
    aDept,
  });
  if (deleteOk) {
    message.success($t('commons.deleteSuccess'));
    onRefresh();
  } else {
    message.warning($t('business-static-tables.testManager.methodRel'));
  }
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddMethodRelatedForm,
  destroyOnClose: true,
});
function onCreate() {
  formModalApi.setData({ testData: props.currentTestRow }).open();
}

function onRefresh() {
  gridApi.query();
}
const [MethodVersionInfoModal, methodVersionInfoModalApi] = useVbenModal({
  connectedComponent: MethodVersionInfo,
  showConfirmButton: false,
  cancelText: $t('commons.close'),
  showCancelButton: true,
  destroyOnClose: true,
  onCancel() {
    methodVersionInfoModalApi.close();
  },
});

const showMethod = () => {
  if (!CurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const method = CurrentRow.value.METHOD;
  methodVersionInfoModalApi.setData({ method }).open();
};

const [EqListModal, eqListModalApi] = useVbenModal({
  connectedComponent: PromptEditEquipments,
  destroyOnClose: true,
});

const editEqList = () => {
  if (!CurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }

  // var eqId 	 = lims.CallServer("TEST_MANAGER.FIND_EQID", [ testCode ,Method]);
  const nTestCode = props.currentTestRow?.TESTCODE || 0;
  const strMethod = CurrentRow.value.METHOD;
  const strServiceGroup = CurrentRow.value.SERVGRP || '';
  const strEquipmentType = CurrentRow.value.EQTYPE || '';
  const strDepartment = CurrentRow.value.DEPT || '';
  eqListModalApi.setData({
    nTestCode,
    strMethod,
    strServiceGroup,
    strEquipmentType,
    strDepartment,
  });

  eqListModalApi.open();
};

const [OutSourceLabsModal, outSourceLabsModalApi] = useVbenModal({
  connectedComponent: OutSourceLabs,
  destroyOnClose: true,
  showConfirmButton: false,
  cancelText: $t('commons.close'),
});

const openOutSourceLabs = () => {
  if (!CurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const TestCode = props.currentTestRow?.TESTCODE || 0;
  const TestNo = props.currentTestRow?.TESTNO || '';
  const Method = CurrentRow.value.METHOD;
  outSourceLabsModalApi
    .setData({
      TestCode,
      Method,
      TestNo,
    })
    .open();
};
const [OtherEquipmentModal, otherEquipmentModalApi] = useVbenModal({
  connectedComponent: ManageOtherEquipment,
  destroyOnClose: true,
  showConfirmButton: false,
  cancelText: $t('commons.close'),
});
const openOtherEquipment = () => {
  if (!CurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const TestCode = props.currentTestRow?.TESTCODE || 0;
  const Method = CurrentRow.value.METHOD;
  otherEquipmentModalApi
    .setData({
      TestCode,
      Method,
    })
    .open();
};

const [EditElnModal, editElnModalApi] = useVbenModal({
  connectedComponent: PromptEditEln,
  destroyOnClose: true,
});
const openEditEln = () => {
  if (!CurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const origrec = CurrentRow.value.ORIGREC;
  const elnId = CurrentRow.value.ELN_ID;
  editElnModalApi
    .setData({
      origrec,
      elnId,
    })
    .open();
};

const openRelatedFile = () => {
  if (!CurrentRow.value) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const testCode = props.currentTestRow?.TESTCODE || 0;
  const method = CurrentRow.value.METHOD;
  documentModalApi
    .setData({
      testCode,
      method,
    })
    .open();
};
const [DocumentModal, documentModalApi] = useVbenModal({
  connectedComponent: RequiredDocuments,
  destroyOnClose: true,
});
</script>
<template>
  <FormModal @success="onRefresh" />
  <MethodVersionInfoModal />
  <EqListModal class="w-[800px]" />
  <OutSourceLabsModal class="h-[600px] w-[800px]" />
  <OtherEquipmentModal class="h-[600px] w-[800px]" />
  <EditElnModal @success="onRefresh" class="h-[600px] w-[1000px]" />
  <DocumentModal class="h-[600px] w-[800px]" />

  <MethedRelateTestGrid>
    <template #toolbar-actions>
      <Space :size="[4, 0]" wrap>
        <Button type="primary" @click="onCreate">
          {{ $t('ui.actionTitle.create') }}
        </Button>
        <Button type="primary" danger @click="onDelete">
          {{ $t('ui.actionTitle.delete') }}
        </Button>
        <Button type="default" @click="showMethod">
          {{ $t('business-static-tables.testManager.showMethod') }}
        </Button>
        <Button type="default" @click="editEqList">
          {{ $t('business-static-tables.testManager.editEqList') }}
        </Button>
        <Button type="default" @click="openOutSourceLabs">
          {{ $t('business-static-tables.testManager.outSourceLabs') }}
        </Button>
        <Button type="default" @click="openOtherEquipment">
          {{ $t('business-static-tables.testManager.otherEquipment') }}
        </Button>
        <Button type="default" @click="openEditEln">
          {{ $t('business-static-tables.testManager.selectEln') }}
        </Button>
        <Button type="default" @click="openRelatedFile">
          {{ $t('business-static-tables.testManager.relatedFile') }}
        </Button>
      </Space>
    </template>
    <template #action="{ row }">
      <template v-if="hasEditStatus(row)">
        <Button type="link" @click="saveRowEvent(row)">
          {{ $t('commons.save') }}
        </Button>
        <Button type="link" @click="cancelRowEvent(row)">
          {{ $t('commons.cancel') }}
        </Button>
      </template>
      <template v-else>
        <Button type="link" @click="editRowEvent(row)">
          {{ $t('commons.edit') }}
        </Button>
      </template>
    </template>
  </MethedRelateTestGrid>
</template>
