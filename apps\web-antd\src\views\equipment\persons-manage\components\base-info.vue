<script setup lang="ts">
import { baseInfoVbenSchema } from '../persons-manage-data';
import { useVbenForm } from '#/adapter/form';
import { getPersonnelInfoApi } from '#/api/equipment/persons-manage';
import { watch } from 'vue';
interface RowType {
  [key: string]: any;
}
const props = defineProps<{
  clickRow: RowType | null;
}>();
watch(
  () => props.clickRow,
  (row) => {
    if (row) {
      getFormData(row);
    }
  },
);
const getFormData = async (row: RowType) => {
  const res = await getPersonnelInfoApi([row.USRNAM]);
  formApi.setValues(res[0]);
};
const [Form, formApi] = useVbenForm({
  layout: 'horizontal',
  schema: baseInfoVbenSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3',
});
</script>
<template>
  <Form class=""> </Form>
</template>
