import { callServer, getDataSet, getDataSetNoPage } from '#/api/core/witlab';

export namespace TestCategoriesApi {
  export interface PageFetchParams {
    [key: string]: any;
    page: number;
    pageSize: number;
  }

  export interface TestCategory {
    ORIGREC: number;
    TESTCATCODE: string;
    TESTCATDESC: string;
    DEPT: string;
  }
}

/**
 * 获取测试分类列表数据
 */
async function getTestCategoryList() {
  return getDataSet('TEST_CATEGORIES.dgdTestCategories_DataSource', []);
}

async function getSites(sTestCatCode: string) {
  return getDataSet('TEST_CATEGORIES.dgdSites', [sTestCatCode]);
}

async function mcSelectSites(sTestCatCode: string) {
  return getDataSetNoPage('TEST_CATEGORIES.mcSelectSites', [sTestCatCode]);
}

async function editSiteList(sTestCatCode: string, aSite: string[]) {
  return await callServer('TEST_CATEGORIES.EditSiteList', [
    sTestCatCode,
    aSite,
  ]);
}

/**
 * 添加测试分类
 * @param data 测试分类数据
 */
async function addTestCategory(data: TestCategoriesApi.TestCategory) {
  return await callServer('TEST_CATEGORIES.ADD_CATEGORY', [
    data.TESTCATCODE,
    data.TESTCATDESC,
    'StaticalDataAddCategory',
  ]);
}

/**
 * 删除测试分类
 *
 * @param origrec 测试分类数据
 * @returns boolean
 */
async function deleteTestCategory(sTestCatCode: string[]) {
  return await callServer('TEST_CATEGORIES.DEL_CATEGORY', [sTestCatCode]);
}

export {
  addTestCategory,
  deleteTestCategory,
  editSiteList,
  getSites,
  getTestCategoryList,
  mcSelectSites,
};
