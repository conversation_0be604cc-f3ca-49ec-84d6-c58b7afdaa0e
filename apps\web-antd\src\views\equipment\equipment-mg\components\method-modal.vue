<script lang="ts" setup>
import type { EquipmentMgApi } from '#/api/equipment/equipment-mg';

import { computed } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { addEquipUseLogApi } from '#/api/equipment/equipment-mg';
import { useEquipmentStore } from '#/store';

import { addUseRecordSchema } from '../equipment-mg-data';

const emit = defineEmits(['success']);
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: addUseRecordSchema(),
  showDefaultActions: false,
});
const equipmentStore = useEquipmentStore();
const currentRow: EquipmentMgApi.RowType = computed(
  () => equipmentStore.getCurrentRow,
);
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    console.warn(data);

    modalApi.lock();
    try {
      const params = [
        data.EQID,
        data.DT_USAGE,
        data.EQSTARTDATE,
        data.EQENDDATE,
        data.REL_ORDERS,
        data.REL_BATCHNO,
        data.REL_SPECIFICATION,
        data.REL_TEST,
        data.INSPECTIONITEM,
        'HUANGWH',
        data.COMMENTS,
      ];
      await addEquipUseLogApi(params);
      message.success('添加成功');
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      formApi.setFieldValue('EQID', currentRow.value.EQID);
      // const data = modalApi.getData<SampleTypeApi.SampleType>();
    }
  },
});
</script>
<template>
  <Modal title="添加使用记录">
    <Form class="mx-4" />
  </Modal>
</template>
