import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { BatcheManagerApi } from '#/api/login-options/batch-manager';

import dayjs from 'dayjs';

import { $t } from '#/locales';

export function useBatchesFilterSchema(): VbenFormSchema[] {
  return [
    // {
    //   component: 'Input',
    //   fieldName: 'BATCHNO',
    //   label: $t('login-options.batchManager.batchno'),
    // },
    // {
    //   component: 'Input',
    //   fieldName: 'MATCODE',
    //   label: $t('login-options.batchManager.matcode'),
    // },
  ];
}
export function useBatchesColumns(): VxeTableGridOptions<BatcheManagerApi.Batches>['columns'] {
  return [
    // { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'origrec',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'FLDISPSTATUS',
      title: $t('login-options.batchManager.flDispStatus'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'REPORTFLAG',
      title: $t('login-options.batchManager.reportFlag'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'TAT',
      title: $t('login-options.batchManager.tat'),
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'NEED_REPORT',
      title: $t('login-options.batchManager.needReport'),
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'TYPE',
      title: $t('login-options.batchManager.type'),
      editRender: {
        name: 'select',
        options: [
          { value: 'RAW', label: '原辅包' },
          { value: 'FP', label: '产品/中间产品' },
          { value: 'EPIM', label: '免检物料' },
        ],
      },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'OPENDATE',
      title: $t('login-options.batchManager.openDate'),
      editRender: { name: 'input', attrs: { type: 'date' } },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      align: 'center',
      field: 'DISPSTS',
      title: $t('login-options.batchManager.dispsts'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'PLANT',
      title: $t('login-options.batchManager.plant'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'PROCESS',
      title: $t('login-options.batchManager.process'),
      editRender: { name: 'input' },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'BATCHID',
      title: $t('login-options.batchManager.batchid'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'BATCHNO',
      title: $t('login-options.batchManager.batchno'),
      editRender: { name: 'input' },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SUPPBATCHNO',
      title: $t('login-options.batchManager.suppbatchno'),
      editRender: { name: 'input' },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'PRODGROUP',
      title: $t('login-options.batchManager.prodGroup'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SAMPLEGROUPNAME',
      title: $t('login-options.batchManager.sampleGroupName'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MATCODE',
      title: $t('login-options.batchManager.matcode'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'BATCHNAME',
      title: $t('login-options.batchManager.batchname'),
      editRender: { name: 'input' },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'COMMENTS',
      title: $t('login-options.batchManager.comments'),
      editRender: { name: 'input' },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MFG',
      title: $t('login-options.batchManager.mfg'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SUPPNAM',
      title: $t('login-options.batchManager.suppnam'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'NUM_ARRIVAL',
      title: $t('login-options.batchManager.num_arrival'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'NUM_UNPACK',
      title: $t('login-options.batchManager.num_unpack'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ESTIMATEDVOL',
      title: $t('login-options.batchManager.estimatedVol'),
      editRender: { name: 'input' },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ACTUALVOL',
      title: $t('login-options.batchManager.actualVol'),
      editRender: { name: 'input' },
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ESTIMATEDVOL_UNITS',
      title: $t('login-options.batchManager.estimatedVolUnits'),
      editRender: { name: 'input' },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SAMPLE_VOL',
      title: $t('login-options.batchManager.sample_vol'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'SAMPLE_UNITS',
      title: $t('login-options.batchManager.sample_units'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'PACKING_SPEC',
      title: $t('login-options.batchManager.packingSpec'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'STORAGE_CONDITION',
      title: $t('login-options.batchManager.storageCondition'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ORDERNO',
      title: $t('login-options.batchManager.orderNo'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'VERSION_NO',
      title: $t('login-options.batchManager.version_no'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'PRODUCTGRADE',
      title: $t('login-options.batchManager.productgrade'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'PRODUCTION_DATE',
      title: $t('login-options.batchManager.productionDate'),
      editRender: { name: 'input', attrs: { type: 'date' } },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      align: 'center',
      field: 'EXPDATE',
      title: $t('login-options.batchManager.expdate'),
      editRender: { name: 'input', attrs: { type: 'date' } },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      align: 'center',
      field: 'RETEST_DATE',
      title: $t('login-options.batchManager.retest_date'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      align: 'center',
      field: 'REQUEST_DATE',
      title: $t('login-options.batchManager.requestDate'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      align: 'center',
      field: 'REQUESTER',
      title: $t('login-options.batchManager.requester'),
      editRender: { name: 'input' },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'REQUEST_DEPT',
      title: $t('login-options.batchManager.requestDept'),
      editRender: { name: 'input' },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RETEST_BATCHID',
      title: $t('login-options.batchManager.retestBatchId'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RETESTORDNO',
      title: $t('login-options.batchManager.retestOrderNo'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'REQUEST_REASON',
      title: $t('login-options.batchManager.requestReason'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('login-options.operation'),
      minWidth: 180,
    },
  ];
}

export function useBatchOrdersFilterSchema(): VbenFormSchema[] {
  return [];
}

export function useBatchOrdersColumns(): VxeTableGridOptions<BatcheManagerApi.BatchOrders>['columns'] {
  return [
    // { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'origrec',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'FLDISPSTATUS',
      title: $t('login-options.batchManager.flDispStatus'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'STATUS',
      title: $t('login-options.batchManager.status'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'ORDNO',
      title: $t('login-options.batchManager.ordno'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MATNAME',
      title: $t('login-options.batchManager.matname'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SPECIFICATION',
      title: $t('login-options.batchManager.specification'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'DUEDAT',
      title: $t('login-options.batchManager.dueDate'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      align: 'center',
      field: 'MATCODE',
      title: $t('login-options.batchManager.matcode'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SP_CODE',
      title: $t('login-options.batchManager.spCode'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'SAMPDESC',
      title: $t('login-options.batchManager.sampdesc'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}

export function useBatchOrdtaskFilterSchema(): VbenFormSchema[] {
  return [];
}

export function useBatchOrdtaskColumns(): VxeTableGridOptions<BatcheManagerApi.BatchOrdtask>['columns'] {
  return [
    // { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'origrec',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'PROFILE',
      title: $t('login-options.batchManager.profile'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'ORDNO',
      title: $t('login-options.batchManager.ordno'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'OTTESTNO',
      title: $t('login-options.batchManager.otTestNo'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MATNAME',
      title: $t('login-options.batchManager.matname'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'SERVGRP',
      title: $t('login-options.batchManager.servgrp'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'DEPT',
      title: $t('login-options.batchManager.dept'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'TS',
      title: $t('login-options.batchManager.ts'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'TESTNO',
      title: $t('login-options.batchManager.testno'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'METHOD',
      title: $t('login-options.batchManager.method'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'VERSION',
      title: $t('login-options.batchManager.version'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ANALYSISDUEDATE',
      title: $t('login-options.batchManager.analysisDueDate'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      align: 'center',
      field: 'SPTESTSORTER',
      title: $t('login-options.batchManager.spTestSorter'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'NEED_REPORT',
      title: $t('login-options.batchManager.needreport'),
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'REP',
      title: $t('login-options.batchManager.rep'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SINONYM',
      title: $t('login-options.batchManager.sinonym'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'FINAL',
      title: $t('login-options.batchManager.final'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'S',
      title: $t('login-options.batchManager.s'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'CHARLIMITS',
      title: $t('login-options.batchManager.charLimits'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'LOWA',
      title: $t('login-options.batchManager.lowa'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'LOWB',
      title: $t('login-options.batchManager.lowb'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'LOWC',
      title: $t('login-options.batchManager.lowc'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'HIGHC',
      title: $t('login-options.batchManager.highc'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'HIGHB',
      title: $t('login-options.batchManager.highb'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'HIGHA',
      title: $t('login-options.batchManager.higha'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'PASS_TEXT',
      title: $t('login-options.batchManager.passText'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'PASS_TEXT_ENG',
      title: $t('login-options.batchManager.passTextEng'),
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('login-options.operation'),
      minWidth: 180,
    },
  ];
}

export function useBatchRecipeFilterSchema(): VbenFormSchema[] {
  return [];
}

export function useBatchRecipeColumns(): VxeTableGridOptions<BatcheManagerApi.BatchRecipe>['columns'] {
  return [
    // { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'origrec',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'MATCODE',
      title: $t('login-options.batchManager.matcode'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MATNAME',
      title: $t('login-options.batchManager.matname'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'COMPONENTLOTNUMBER',
      title: $t('login-options.batchManager.componentLotNumber'),
      editRender: { name: 'input' },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SAMPLEGROUPNAME',
      title: $t('login-options.batchManager.sampleGroupName'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'QTY',
      title: $t('login-options.batchManager.qty'),
      editRender: { name: 'input' },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'UNITS',
      title: $t('login-options.batchManager.units'),
      editRender: { name: 'input' },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'BATCHIDFROMBATCHES',
      title: $t('login-options.batchManager.batchIdFromBatches'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('login-options.operation'),
      minWidth: 180,
    },
  ];
}

export function useBatchRequirementFilterSchema(): VbenFormSchema[] {
  return [];
}

export function useBatchRequirementColumns(): VxeTableGridOptions<BatcheManagerApi.BatchSamplingRequirement>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'origrec',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'ORDNO',
      title: $t('login-options.batchManager.ordno'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'INVENTORYID',
      title: $t('login-options.batchManager.inventoryId'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'EXTERNAL_ID',
      title: $t('login-options.batchManager.externalId'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SAMPLE_TYPE',
      title: $t('login-options.batchManager.sampleType'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'CONTAINERQTY',
      title: $t('login-options.batchManager.containerQty'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'CONTAINER_UNITS',
      title: $t('login-options.batchManager.containerUnits'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SAMPLINGPOSITION',
      title: $t('login-options.batchManager.samplingPosition'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'FORLAB',
      title: $t('login-options.batchManager.forLab'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SAMPLESIZE',
      title: $t('login-options.batchManager.sampleSize'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'NUMBEROFCONTAINERS',
      title: $t('login-options.batchManager.numberOfContainers'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'CONDITION',
      title: $t('login-options.batchManager.condition'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'STATUS',
      title: $t('login-options.batchManager.status'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('login-options.operation'),
      minWidth: 100,
    },
  ];
}

export function useBatchReqTestsFilterSchema(): VbenFormSchema[] {
  return [];
}

export function useBatchReqTestsColumns(): VxeTableGridOptions<BatcheManagerApi.BatchSamplingRequTests>['columns'] {
  return [
    // { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'origrec',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'ORDNO',
      title: $t('login-options.batchManager.ordno'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'INVENTORYID',
      title: $t('login-options.batchManager.inventoryId'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'TESTCODE',
      title: $t('login-options.batchManager.testCode'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'TESTNO',
      title: $t('login-options.batchManager.testNo'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}

export function useBatchAttachFilterSchema(): VbenFormSchema[] {
  return [];
}

export function useBatchAttachColumns(): VxeTableGridOptions<BatcheManagerApi.BatchAttach>['columns'] {
  return [
    {
      align: 'center',
      field: 'origrec',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'ATTACHMENTNAME',
      title: $t('login-options.batchManager.attachmentName'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'UPLOADUSRFULLNAME',
      title: $t('login-options.batchManager.uploadUserName'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'UPLOADDATE',
      title: $t('login-options.batchManager.uploadDate'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      align: 'center',
      field: 'COMMENTS',
      title: $t('login-options.batchManager.comments'),
      editRender: { name: 'input' },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'STARDOC_ID',
      title: $t('login-options.batchManager.starDocId'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('login-options.operation'),
      minWidth: 100,
    },
  ];
}
