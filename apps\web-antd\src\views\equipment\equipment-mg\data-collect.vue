<script lang="ts" setup>
import type { SelectProps } from 'ant-design-vue';

import { computed, ref, watch } from 'vue';

import { Select, SelectOption, Space, TabPane, Tabs } from 'ant-design-vue';

import { getDsDcuDetailsApi } from '#/api/equipment/equipment-mg';
import { useEquipmentStore } from '#/store';

import AnalysisTable from './data-collect-components/analysis-table.vue';
import CollectLog from './data-collect-components/collect-log.vue';
import FilePage from './data-collect-components/file-page.vue';
import RestPage from './data-collect-components/rest-page.vue';
import SerialPage from './data-collect-components/serial-page.vue';
import TcpPage from './data-collect-components/tcp-page.vue';
import WebsocketPage from './data-collect-components/websocket-page.vue';

const equipmentStore = useEquipmentStore();
const currentRow = computed(() => equipmentStore.getCurrentRow);
interface RowType {
  [key: string]: any;
}
const value = ref('');

watch(
  currentRow,
  async (newRow: RowType) => {
    if (currentRow.value) {
      const res = await getDsDcuDetailsApi([newRow.EQID]);
      value.value = res[0].CONNECTIONTYPE;
    }
  },
  { deep: true },
);

const options = ref<SelectProps['options']>([
  {
    value: 'File',
    label: 'File',
  },
  {
    value: 'Serial',
    label: 'Serial',
  },
  {
    value: 'REST',
    label: 'REST',
  },
  {
    value: 'WebSocket',
    label: 'WebSocket',
  },
]);
const components = [
  {
    value: 'File',
    page: FilePage,
  },
  {
    value: 'Serial',
    page: SerialPage,
  },
  {
    value: 'TCP',
    page: TcpPage,
  },
  {
    value: 'REST',
    page: RestPage,
  },
  {
    value: 'WebSocket',
    page: WebsocketPage,
  },
];
const activeKey = ref('采集日志');
const tabList = [
  {
    title: '分析表',
    page: AnalysisTable,
  },
  {
    title: '采集日志',
    page: CollectLog,
  },
];
</script>
<template>
  <div class="flex-container h-full">
    <div class="flex-item-grow-0">
      <Space :size="[4, 0]" wrap>
        <Select v-model:value="value" style="width: 120px">
          <SelectOption
            v-for="item in options"
            :value="item.value"
            :key="item.value"
          >
            {{ item.label }}
          </SelectOption>
        </Select>
      </Space>
      <div class="h-2/5 w-2/5">
        <component
          :is="components.find((item) => item.value === value)?.page"
        />
      </div>
    </div>
    <div class="flex-item-grow-1">
      <Tabs v-model:active-key="activeKey">
        <TabPane v-for="item in tabList" :key="item.title" :tab="item.title">
          <component
            :is="
              tabList.find((item) => item.title === activeKey)?.page ||
              CollectLog
            "
          />
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>
