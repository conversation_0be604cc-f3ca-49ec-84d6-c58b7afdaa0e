<script lang="ts" setup>
import type { TestManagerApi } from '#/api/business-static-tables/test-manager';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import {
  $delMethodDocApi,
  $getMethodDocListApi,
} from '#/api/business-static-tables/test-manager';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';
import { confirm } from '#/utils/utils';

import AddRequiredDocuments from './add-required-documents.vue';
import { useDocColumns } from './data';

interface FormArgs {
  method: string;
  testCode: number;
  mode: string;
}
const formArgs = ref<FormArgs>();

const [Modal, modalApi] = useVbenModal({
  showCancelButton: false,
  confirmText: $t('commons.close'),
  onConfirm: async () => {
    modalApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<FormArgs>();
      if (data) {
        formArgs.value = data;
        queryData();
      }
    }
  },
  title: $t('business-static-tables.testManager.relatedFile'),
});
const [AddModal, addModalApi] = useVbenModal({
  connectedComponent: AddRequiredDocuments,
  destroyOnClose: true,
});

const colums = useDocColumns();
const queryData = async () => {
  if (!formArgs.value) return [];
  return await $getMethodDocListApi({
    method: formArgs.value.method,
    testCode: formArgs.value.testCode,
  });
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};
const {
  Grid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
} = useLimsGridsConfig<TestManagerApi.TEST_METHOD_REQ_DOCS>(
  colums,
  [],
  queryData,
  girdOption,
);

function onCreate() {
  addModalApi.setData(formArgs.value).open();
}
async function onDelete() {
  const checkRecords: TestManagerApi.TEST_METHOD_REQ_DOCS[] =
    gridApi.grid?.getCheckboxRecords();
  if (checkRecords.length === 0) {
    message.warning($t('commons.selectOne'));
    return;
  }
  await confirm($t('commons.deleteConfirm'), $t('commons.deleteConfirmTitle'));
  const origrecs = checkRecords.map((item) => item.ORIGREC);
  const starDocs = checkRecords.map((item) => item.STARDOC_ID);
  // 删除逻辑
  await $delMethodDocApi({ origrecs, starDocs });
  message.success($t('commons.deleteSuccess'));
  onRefresh();
}

function onRefresh() {
  gridApi.query();
}
</script>

<template>
  <Modal>
    <AddModal @success="onRefresh" />
    <div class="modal-content-full mb-4">
      <Grid>
        <template #toolbar-actions>
          <Space :size="[4, 0]" wrap>
            <Button type="primary" @click="onCreate">
              {{ $t('ui.actionTitle.create') }}
            </Button>
            <Button type="primary" danger @click="onDelete">
              {{ $t('ui.actionTitle.delete') }}
            </Button>
          </Space>
        </template>
        <template #action="{ row }">
          <template v-if="hasEditStatus(row)">
            <Button type="link" @click="saveRowEvent(row)">
              {{ $t('commons.save') }}
            </Button>
            <Button type="link" @click="cancelRowEvent(row)">
              {{ $t('commons.cancel') }}
            </Button>
          </template>
          <template v-else>
            <Button type="link" @click="editRowEvent(row)">
              {{ $t('commons.edit') }}
            </Button>
          </template>
        </template>
      </Grid>
    </div>
  </Modal>
</template>

<style scoped>
.modal-content-full {
  display: flex;
  flex-direction: column;
  height: 100%;
}
</style>
