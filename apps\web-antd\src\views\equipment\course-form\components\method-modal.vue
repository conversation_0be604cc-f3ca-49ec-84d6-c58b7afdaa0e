<script lang="ts" setup>
import { ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import {
  Button,
  CheckboxGroup,
  message,
  Radio,
  RadioGroup,
  Select,
  SelectOption,
} from 'ant-design-vue';

import {
  getMethodChkListBoxApi,
  getMethodListBoxApi,
  getTestChoiceApi,
  getTestListApi,
  updateMethodApi,
} from '#/api/equipment/course-form';

const emit = defineEmits(['success']);

const value = ref('由测试');

watch(value, (val) => {
  if (val === '由方法') {
    getMethodList();
  }
});
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    modalApi.lock();
    try {
      emit('success');

      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      const res = await getTestChoiceApi();
      options.value = res.map((item: { TESTS: string }) => {
        return {
          value: item.TESTS,
          label: item.TESTS,
        };
      });

      // const data = modalApi.getData<SampleTypeApi.SampleType>();
    }
  },
});
const radioOptions = [
  { label: '由测试', value: '由测试' },
  { label: '由方法', value: '由方法' },
];
const options = ref<{ label: string; value: string }[]>([]);
const items = ref<{ label: string; value: string }[]>([]);
const selectedItem = ref({ value: '', label: '' });
const methodItems = ref<{ label: string; value: string }[]>([]);
const getMethodList = async () => {
  const clickRow = modalApi.getData().clickRow;

  const res = await getMethodListBoxApi([clickRow.COURSECODE]);
  methodItems.value = res.map((item: { METHOD: string }) => {
    return {
      value: item.METHOD,
      label: item.METHOD,
    };
  });
};
const clickItem = async (item: { label: string; value: string }) => {
  selectedItem.value = item;
  const clickRow = modalApi.getData().clickRow;
  const params = [type.value, item.value, clickRow.COURSECODE];
  const res = await getMethodChkListBoxApi(params);
  plainOptions.value = res.map((item: { METHOD: string }) => item.METHOD);
  console.warn(item);
};
const methodItem = ref({ value: '', label: '' });
const clickMethodItem = async (item: { label: string; value: string }) => {
  methodItem.value = item;
};
const plainOptions = ref([]);
const typeChange = async (value: any) => {
  const res = await getTestListApi([value]);
  items.value = res.map((item: { TESTCODE: string; TESTNO: string }) => {
    return {
      value: item.TESTCODE,
      label: item.TESTNO,
    };
  });
  plainOptions.value = [];
};

const checkedList = ref<string[]>([]);
const type = ref('');
const add = async (close: Boolean) => {
  const clickRow = modalApi.getData().clickRow;
  if (value.value === '由测试') {
    if (!selectedItem.value.value) {
      message.warn('请选择测试');
      return;
    }
    if (checkedList.value.length === 0) {
      message.warn('请选择方法');
      return;
    }
  }
  if (value.value === '由方法' && !methodItem.value.value) {
    message.warn('请选择方法');
    return;
  }
  const params =
    value.value === '由测试'
      ? [
          clickRow.COURSECODE,
          selectedItem.value.value,
          checkedList.value,
          'ByTest',
        ]
      : [clickRow.COURSECODE, 0, [methodItem.value.value], 'ByMethod'];
  await updateMethodApi(params);
  emit('success');
  selectedItem.value = { value: '', label: '' };
  checkedList.value = [];
  if (close) {
    modalApi.close();
  }
};
</script>
<template>
  <Modal title="选择课程方法" class="h-3/6 w-1/3">
    <RadioGroup v-model:value="value">
      <Radio v-for="item of radioOptions" :value="item.value" :key="item.value">
        {{ item.label }}
      </Radio>
    </RadioGroup>
    <div
      v-if="value === '由测试'"
      class="flex h-[90%] flex-col border-2 border-solid border-gray-200 p-4"
    >
      <div class="py-2">
        请从左边的列表中选择一个分析，并在右边选择一个相关的方法。
      </div>
      <div>
        <span>测试类别：</span>
        <Select v-model:value="type" style="width: 150px" @change="typeChange">
          <SelectOption
            v-for="item in options"
            :value="item.value"
            :key="item.value"
          >
            {{ item.label }}
          </SelectOption>
        </Select>
      </div>
      <div class="flex h-full flex-row">
        <div class="mr-4 w-1/2">
          <div class="pb-2">测试</div>

          <ul
            class="h-4/5 overflow-auto border-2 border-solid border-gray-200 p-2"
          >
            <li
              v-for="item in items"
              :key="item.value"
              class="list-item hover:bg-blue-200"
              :style="{
                backgroundColor:
                  selectedItem.value === item.value ? '#f0f0f0' : 'white',
              }"
              @click="clickItem(item)"
            >
              {{ item.label }}
            </li>
          </ul>
        </div>
        <div class="w-1/2 px-4">
          <div class="pb-2">方法</div>
          <div class="h-4/5 border-2 border-solid border-gray-200 p-2">
            <CheckboxGroup
              v-model:value="checkedList"
              name="checkboxgroup"
              :options="plainOptions"
              class="grid"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      v-else
      class="flex h-[90%] flex-col border-2 border-solid border-gray-200 p-4"
    >
      <div class="py-2">从列表中选择一个方法:</div>
      <div class="mr-4 h-full w-full">
        <div class="pb-2">测试</div>

        <ul
          class="h-5/6 overflow-auto border-2 border-solid border-gray-200 p-2"
        >
          <li
            v-for="item in methodItems"
            :key="item.value"
            class="list-item hover:bg-blue-200"
            :style="{
              backgroundColor:
                methodItem.value === item.value ? '#f0f0f0' : 'white',
            }"
            @click="clickMethodItem(item)"
          >
            {{ item.label }}
          </li>
        </ul>
      </div>
    </div>
    <template #footer>
      <Button type="primary" @click="add(false)">
        {{ $t('equipment.add') }}
      </Button>
      <Button type="primary" @click="add(true)">
        {{ $t('equipment.addAndClose') }}
      </Button>
      <Button @click="modalApi.close"> {{ $t('equipment.close') }}</Button>
    </template>
  </Modal>
</template>
<style>
.list {
  padding: 0;
  list-style-type: none;
}

.list-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #ccc;
  transition: background-color 0.3s;
}

.list-item:hover {
  background-color: #f0f0f0;
}

.list-item.bg-blue-100 {
  color: #000;
}
</style>
