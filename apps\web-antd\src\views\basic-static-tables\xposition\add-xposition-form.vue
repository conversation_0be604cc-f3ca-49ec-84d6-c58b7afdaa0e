<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { $addXPositionApi } from '#/api/basic-static-tables/xposition';
import { $t } from '#/locales';

import { useSchema } from './data';

const emit = defineEmits(['success']);
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: useSchema(),
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    data.eventCode = 'addXPosition';
    data.comment = '添加XPOSITION数据';
    modalApi.lock();
    try {
      const res = await $addXPositionApi(data);
      if (!res) {
        message.error($t('commons.exists'));
        return;
      }
      if (res) emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  title: $t('ui.actionTitle.create', [
    $t('basic-static-tables.xposition.position'),
  ]),
});
</script>

<template>
  <Modal>
    <Form class="mx-4" />
  </Modal>
</template>
