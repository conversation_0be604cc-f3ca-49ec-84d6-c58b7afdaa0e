<script lang="ts" setup>
import type { SpecSchemasApi } from '#/api/business-static-tables';

import { computed, ref } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';

import { Button } from 'ant-design-vue';

import {
  $getComboSpecSchemaFiledDispPropertyApi,
  $getSpecSchemaFieldApi,
} from '#/api/business-static-tables';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import {
  useSpecSchemasFieldColumns,
  useSpecSchemasFieldFilterSchema,
} from './data';

const specSchemaData = ref<SpecSchemasApi.SpecSchemas>();

const feildDispOptions = ref<any[]>([]);
async function loadFeildDispOptions() {
  feildDispOptions.value = await $getComboSpecSchemaFiledDispPropertyApi();
}
loadFeildDispOptions();

const colums = useSpecSchemasFieldColumns(feildDispOptions.value);
const filterSchema = useSpecSchemasFieldFilterSchema(feildDispOptions.value);
const queryData = async () => {
  return await $getSpecSchemaFieldApi({
    specSchemaCode: specSchemaData.value?.SPECSCHEMACODE ?? '',
  });
};

const {
  Grid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
} = useLimsGridsConfig<SpecSchemasApi.SpecSchemaFields>(
  colums,
  filterSchema,
  queryData,
);

const [Drawer, drawerApi] = useVbenDrawer({
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData();
      if (data) {
        specSchemaData.value = data.specSchema;
        gridApi?.query();
      } else {
        gridApi?.reload();
      }
    }
  },
  showCancelButton: false,
  showConfirmButton: false,
  class: '',
});

const getDrawerTitle = computed(() => {
  return `${$t('business-static-tables.specSchema.action')}: ${specSchemaData.value?.SPECSCHEMANAME ?? ''}`;
});
</script>

<template>
  <Drawer class="w-full max-w-[1500px]" :title="getDrawerTitle">
    <Page auto-content-height>
      <Grid>
        <template #action="{ row }">
          <template v-if="hasEditStatus(row)">
            <Button type="link" @click="saveRowEvent(row)">
              {{ $t('commons.save') }}
            </Button>
            <Button type="link" @click="cancelRowEvent(row)">
              {{ $t('commons.cancel') }}
            </Button>
          </template>
          <template v-else>
            <Button type="link" @click="editRowEvent(row)">
              {{ $t('commons.edit') }}
            </Button>
          </template>
        </template>
      </Grid>
    </Page>
  </Drawer>
</template>
