<script lang="ts" setup>
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { SolutionPreparationApi } from '#/api/dilution-management/solution-preparation';

import {
  Button,
  Space,
  TabPane,
  Tabs,
  message,
  Modal as AModal,
} from 'ant-design-vue';
import {
  testColumns,
  historyColumns,
  fileColumns,
} from '../solution preparation-data';
import {
  getTestSoultionApi,
  getTestsolutionRecordApi,
  updateProviderApi,
  getListAttachmentsApi,
  deleteSolutionApi,
} from '#/api/dilution-management/solution-preparation';
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import CommitModal from './commit-modal.vue';
import PreparationModal from './preparation-modal.vue';
const emit = defineEmits(['update:clickRow']);

const [CommitFormModal, commitFormModalApi] = useVbenModal({
  connectedComponent: CommitModal,
  destroyOnClose: true,
});
const [PreparationFormModal, preparationFormModalApi] = useVbenModal({
  connectedComponent: PreparationModal,
  destroyOnClose: true,
});
const formargs = [
  'SolutionConfiguration',
  'Draft',
  ' and STATUS = ?',
  "'Draft'",
];
interface RowType {
  [key: string]: any;
}

const gridOptions: VxeTableGridOptions<SolutionPreparationApi.Form> = {
  columns: testColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        const params = [formargs[1]];
        const data = await getTestSoultionApi(params);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
const historyGridOptions: VxeTableGridOptions<SolutionPreparationApi.Form> = {
  columns: historyColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!clickRow.value.SEQUENCY) {
          return [];
        }

        const data = await getTestsolutionRecordApi([clickRow.value.SEQUENCY]);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
const fileGridOptions: VxeTableGridOptions<SolutionPreparationApi.Form> = {
  columns: fileColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!clickRow.value.ORIGREC) {
          return [];
        }
        const params = ['DILUTION_STORE', clickRow.value.ORIGREC];
        const data = await getListAttachmentsApi(params);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
const gridEvents: VxeGridListeners<SolutionPreparationApi.Form> = {
  currentChange: async ({ row }) => {
    if (row) {
      clickRow.value = row;
      emit('update:clickRow', row);
      refreshChildren();
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents,
});
const [HistoryGrid, historyGridApi] = useVbenVxeGrid({
  gridOptions: historyGridOptions,
  gridEvents: {},
});
const [FileGrid, fileGridApi] = useVbenVxeGrid({
  gridOptions: fileGridOptions,
  gridEvents: {},
});
const clickRow = ref<RowType>({});
const solutionPreparation = () => {
  preparationFormModalApi.setData({ type: 'test' }).open();
};

const submit = () => {
  commitFormModalApi.setData({ type: '试液', clickRow: clickRow.value }).open();
};
const deleteSolution = () => {
  if (!clickRow.value.ORIGREC) {
    message.warning('未选择行，无法删除');
    return;
  }
  //TODO:电子签名校验
  const oESig = false;
  if (oESig) {
    message.warning('请先进行电子签名');
    return;
  }
  AModal.confirm({
    title: '提示',
    content: '是否要删除当前溶液？',
    cancelText: '否',
    okText: '是',
    async onOk() {
      const params = [clickRow.value?.ORIGREC,oESig];
      await deleteSolutionApi(params);
      gridApi.query();
      refreshChildren();
    },
    onCancel() {},
  });
};
const activeKey = ref('历史记录');
const tabList = [
  {
    title: '历史记录',
  },
  {
    title: '附件',
  },
];
const hasEditStatus = (row: RowType) => {
  return gridApi.grid?.isEditByRow(row);
};

const editRowEvent = (row: RowType) => {
  gridApi.grid?.setEditRow(row);
};

const saveRowEvent = async () => {
  await gridApi.grid?.clearEdit();
  if (!clickRow.value) {
    message.warning('未选择行，无法保存');
    return;
  }
  const row = clickRow.value;
  const rowList = Object.keys(row).map((key) => {
    const isNum = typeof row[key] === 'number' ? 'N' : 'S';
    return [key, row[key], isNum, ''];
  });
  const params = [
    'dgTestSoution',
    'DILUTION_STORE',
    rowList,
    row.ORIGREC,
    null,
  ];
  await updateProviderApi(params);
};

const cancelRowEvent = (row: RowType) => {
  gridApi.grid?.clearEdit().then(() => {
    // 还原行数据
    gridApi.grid.revertData(row);
  });
};
const tabsChange = (key: string) => {
  refreshChildren();
};
const refreshChildren = () => {
  if (activeKey.value === '历史记录') {
    historyGridApi.query();
  }
  if (activeKey.value === '附件') {
    fileGridApi.query();
  }
};
const onRefresh = () => {
  gridApi.query();
  refreshChildren();
};
const addFiles = () => {
  //TODO：上传文件
};
const deleteFiles = () => {
  //TODO：删除文件
};
const viewFiles = () => {
  //TODO：查看文件
};
const takePhoto = () => {
  //TODO：拍照
};
const batchAddFiles = () => {
  //TODO：批量上传文件
};
const downloadFiles = () => {
  //TODO:下载文件
};
</script>
<template>
  <PreparationFormModal @success="onRefresh" />
  <CommitFormModal @success="onRefresh" />
  <Grid class="h-2/5">
    <template #toolbar-actions>
      <Space :size="[8, 0]" wrap>
        <Button type="primary" @click="solutionPreparation">
          {{
            $t('dilution-management.solution-preparation.solutionPreparation')
          }}
        </Button>
        <Button type="primary" @click="submit">
          {{ $t('dilution-management.solution-preparation.submit') }}
        </Button>
        <Button type="primary" danger @click="deleteSolution">
          {{ $t('dilution-management.solution-preparation.delete') }}
        </Button>
      </Space>
    </template>
    <template #action="{ row }">
      <template v-if="hasEditStatus(row)">
        <Button type="link" @click="saveRowEvent()">
          {{ $t('business-static-tables.save') }}
        </Button>
        <Button type="link" @click="cancelRowEvent(row)">
          {{ $t('business-static-tables.cancel') }}
        </Button>
      </template>
      <template v-else>
        <Button type="link" @click="editRowEvent(row)">
          {{ $t('business-static-tables.edit') }}
        </Button>
      </template>
    </template>
  </Grid>
  <Tabs v-model:active-key="activeKey" class="w-full" @change="tabsChange">
    <TabPane v-for="item in tabList" :key="item.title" :tab="item.title" />
  </Tabs>
  <HistoryGrid class="h-2/5" v-show="activeKey === '历史记录'" />
  <FileGrid class="h-2/5" v-show="activeKey === '附件'">
    <template #toolbar-actions>
      <Space :size="[8, 0]" wrap>
        <Button type="primary" @click="addFiles">
          {{ $t('dilution-management.solution-preparation.add') }}
        </Button>
        <Button type="primary" @click="deleteFiles">
          {{ $t('dilution-management.solution-preparation.delete') }}
        </Button>
        <Button type="primary" danger @click="viewFiles">
          {{ $t('dilution-management.solution-preparation.view') }}
        </Button>
        <Button type="primary" @click="takePhoto">
          {{ $t('dilution-management.solution-preparation.takePhoto') }}
        </Button>
        <Button type="primary" @click="batchAddFiles">
          {{ $t('dilution-management.solution-preparation.batchAdd') }}
        </Button>
        <Button type="primary" @click="downloadFiles">
          {{ $t('dilution-management.solution-preparation.download') }}
        </Button>
      </Space>
    </template>
  </FileGrid>
</template>
