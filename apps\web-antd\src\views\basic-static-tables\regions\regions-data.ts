import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { RegionTableApi } from '#/api/basic-static-tables/regions';

import { z } from '#/adapter/form';
import { $t } from '#/locales';

export function useColumns(): VxeTableGridOptions<RegionTableApi.Region>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'REGIONCODE',
      title: $t('basicStatic.regions.regionCode'),
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'REGIONNAME',
      title: $t('basicStatic.regions.regionName'),
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('basicStatic.lookups.dataTable.columns.actions'),
      width: 140,
    },
  ];
}

export function useSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'REGIONCODE',
      label: $t('basicStatic.regions.regionCode'),
      rules: z
        .string()
        .min(
          1,
          $t('ui.formRules.minLength', [
            $t('basicStatic.regions.regionCode'),
            1,
          ]),
        )
        .max(
          20,
          $t('ui.formRules.maxLength', [
            $t('basicStatic.regions.regionCode'),
            20,
          ]),
        ),
    },
    {
      component: 'Input',
      fieldName: 'REGIONNAME',
      label: $t('basicStatic.regions.regionName'),
      rules: z
        .string()
        .min(
          1,
          $t('ui.formRules.minLength', [
            $t('basicStatic.regions.regionName'),
            1,
          ]),
        )
        .max(
          50,
          $t('ui.formRules.maxLength', [
            $t('basicStatic.regions.regionName'),
            50,
          ]),
        ),
    },
  ];
}

export function useFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        // placeholder: 'Please enter code',
      },
      fieldName: 'REGIONCODE',
      label: $t('basicStatic.regions.regionCode'),
    },
    {
      component: 'Input',
      fieldName: 'REGIONNAME',
      componentProps: {
        // placeholder: 'Please enter name',
      },
      label: $t('basicStatic.regions.regionName'),
    },
  ];
}
