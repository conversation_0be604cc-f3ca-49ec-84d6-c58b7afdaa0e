<script lang="ts" setup>
import type { VbenFormSchema } from '#/adapter/form';
import type { EsigResult } from '#/types/types';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import { $t } from '#/locales';

interface EsigParam {
  EventCode: string;
  ConfirmSuccessCallback: (params: EsigResult) => void;
}

const emit = defineEmits(['success']);

function useSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'UserId',
      label: $t('commons.userName'),
      rules: 'required',
    },
    {
      component: 'InputPassword',
      componentProps: {
        props: {
          type: 'password',
        },
      },
      fieldName: 'Password',
      label: $t('commons.password'),
      rules: 'required',
    },
    {
      component: 'DatePicker',
      componentProps: {
        attrs: {
          type: 'date',
        },
      },
      fieldName: 'StartDate',
      label: $t('commons.startDate'),
    },
    {
      component: 'DatePicker',
      label: $t('commons.endDate'),
      componentProps: {
        attrs: {
          type: 'date',
        },
      },
      fieldName: 'EndDate',
    },
  ];
}

const esigParam = ref<EsigParam>();
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: useSchema(),
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues<EsigResult>();
    modalApi.lock();
    try {
      if (emit) {
        emit('success');
      }
      const confirmSuccessCallback = esigParam.value?.ConfirmSuccessCallback;
      if (confirmSuccessCallback) {
        confirmSuccessCallback(data);
      }

      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<EsigParam>();
      if (data) {
        esigParam.value = data;
      }
    }
  },
});

const getTitle = computed(() => {
  return $t('commons.esig');
});
</script>

<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
  </Modal>
</template>
