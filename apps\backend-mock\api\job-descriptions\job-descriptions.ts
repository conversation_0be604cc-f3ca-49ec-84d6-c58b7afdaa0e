import { faker } from '@faker-js/faker';
import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, usePageResponseSuccess } from '~/utils/response';

function generateMockDataList(count: number) {
  const dataList = [];

  for (let i = 0; i < count; i++) {
    const dataItem = {
      ORIGREC: faker.number.int({ min: 1, max: 1000 }),
      JOBDESCRIPTION: faker.commerce.productDescription(),
    };

    dataList.push(dataItem);
  }

  return dataList;
}

const mockData = generateMockDataList(100);

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  await sleep(600);

  const { page, pageSize, sortBy, sortOrder } = getQuery(event);
  const listData = structuredClone(mockData);
  if (sortBy && Reflect.has(listData[0], sortBy as string)) {
    listData.sort((a, b) => {
      if (sortOrder === 'asc') {
        return a[sortBy as string] > b[sortBy as string] ? 1 : -1;
      } else {
        return a[sortBy as string] < b[sortBy as string] ? 1 : -1;
      }
    });
  }

  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
