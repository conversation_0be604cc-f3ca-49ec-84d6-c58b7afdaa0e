---
description:
globs:
alwaysApply: false
---
# 组件使用指南

## 基础组件

基础组件位于 `packages/components` 目录下，包括：

- 表单组件
- 表格组件
- 弹窗组件
- 导航组件
- 布局组件

## 组件使用规范

1. 导入方式
```typescript
import { ComponentName } from '@packages/components';
```

2. 类型定义
```typescript
import type { ComponentNameProps } from '@packages/components';
```

## 组件开发流程

1. 在 `packages/components` 下创建新组件目录
2. 创建以下文件：
   - `index.ts`: 组件导出
   - `ComponentName.tsx`: 组件实现
   - `ComponentName.less`: 样式文件
   - `__tests__/ComponentName.test.tsx`: 测试文件
   - `README.md`: 使用文档

## 组件文档要求

每个组件文档必须包含：

- 组件描述
- 属性说明
- 使用示例
- 注意事项
- 相关链接
