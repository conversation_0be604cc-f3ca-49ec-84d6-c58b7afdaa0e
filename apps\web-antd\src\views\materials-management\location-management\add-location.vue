<script lang="ts" setup>
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { LocationManageApi } from '#/api/materials-management/location-management';

import { computed, onMounted, reactive, ref, watch } from 'vue';

import { $t } from '@vben/locales';

import { Button, message, Space, Steps } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addLocation,
  addLocationInfo,
  getLocationType,
  getStorageConditionLayoutList,
  getStorageConditionList,
} from '#/api/materials-management/location-management';

import {
  useLayoutConfigColumns,
  useStorageConditionColumns,
} from './location-data';

const props = defineProps<{
  roomId: number;
}>();

// 定义组件事件
const emit = defineEmits(['success', 'close']);
const RoomId = ref<number>();
onMounted(() => {
  if (props.roomId) {
    // 可以用来设置表单默认值或其他逻辑
    // console.log('接收到父级代码:', props.roomId);
    RoomId.value = props.roomId;
  }
});

// 当前步骤
const currentStep = ref(1);
const loading = ref(false);

// 表单数据
const formData = reactive({
  basic: {
    LOCATIONCODE: '',
    LOCATION_NAME: '',
    DESCRIPTION: '',
    LOCATION_TYPE_ID: '',
    LOCATION_TYPE_LABEL: '',
    IS_STORABLE: '',
    IS_GXP: '',
  },
  storageCondition: null as LocationManageApi.StorageCondition | null,
  layoutConfigs: [] as LocationManageApi.LayoutConfig[],
});

// 步骤配置
const steps = [
  { title: '存样位置', description: '存样位置定义' },
  { title: '存储条件', description: '选择存储条件' },
  { title: '存储布局', description: '选择存储布局' },
];

// 在 setup 中定义 locationTypeList 并初始化
const locationTypeList = ref<{ LOCATION_TYPE_ID: string; TYPE_NAME: string }[]>(
  [],
);

getLocationType().then((res) => {
  locationTypeList.value = res.items;
});

// 第一步：基本信息表单
const [BasicForm, basicFormApi] = useVbenForm({
  layout: 'vertical',
  schema: [
    {
      component: 'Input',
      fieldName: 'LOCATIONCODE',
      label: $t('materials-management.location-manage.locationcode'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'LOCATION_NAME',
      label: $t('materials-management.location-manage.location_name'),
      rules: 'required',
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'ApiSelect',
      // 对应组件的参数
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        style: { width: '600px' }, // 设置固定宽度
        api: async () => {
          const res = await getLocationType();
          return res.items.map(
            (item: { LOCATION_TYPE_ID: number; TYPE_NAME: string }) => ({
              label: item.TYPE_NAME,
              value: item.LOCATION_TYPE_ID,
            }),
          );
        },
        // autoSelect: 'first',
        immediate: true,
      },
      // 字段名
      fieldName: 'LOCATION_TYPE_ID',
      // 界面显示的label
      label: $t('materials-management.location-manage.location_type_id'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'DESCRIPTION',
      label: $t('materials-management.location-manage.description'),
    },
    {
      component: 'Checkbox',
      fieldName: 'IS_STORABLE',
      label: '',
      renderComponentContent: () => {
        return {
          default: () => $t('materials-management.location-manage.is_storable'),
        };
      },
    },
    {
      component: 'Checkbox',
      fieldName: 'IS_GXP',
      label: '',
      renderComponentContent: () => {
        return {
          default: () => $t('materials-management.location-manage.is_gxp'),
        };
      },
    },
  ],
  wrapperClass: 'grid-cols-1 md:grid-cols-2 gap-x-6',
  showDefaultActions: false,
  // 监听表单值变化
  handleValuesChange: (_changedValues) => {
    // 实时更新formData.basic以便canNext计算属性能够响应
    Object.assign(formData.basic, _changedValues);
    /*     console.log('表单所有值:', allValues);
    console.log('变更:', _changedValues); */
    if ('LOCATION_TYPE_ID' in _changedValues) {
      const selectedId = _changedValues.LOCATION_TYPE_ID;
      const selectedItem = locationTypeList.value.find(
        (item) => item.LOCATION_TYPE_ID === selectedId,
      );

      // 更新 label
      formData.basic.LOCATION_TYPE_LABEL = selectedItem?.TYPE_NAME || '';
      // console.log('变更:', formData.basic.LOCATION_TYPE_LABEL);
    }
  },
});

// 第二步：存储条件表格
const storageConditionGridOptions: VxeTableGridOptions<LocationManageApi.StorageCondition> =
  {
    columns: useStorageConditionColumns(),
    height: '300px',
    stripe: true,
    border: true,
    showOverflow: true,
    rowConfig: {
      keyField: 'ORIGREC',
      isCurrent: true,
    },
    proxyConfig: {
      ajax: {
        query: async () => {
          return await getStorageConditionList();
        },
      },
    },
  };

const storageConditionGridEvents: VxeGridListeners<LocationManageApi.StorageCondition> =
  {
    currentChange: ({ row }) => {
      formData.storageCondition = row;
    },
  };

const [StorageConditionGrid, storageConditionGridApi] = useVbenVxeGrid({
  gridOptions: storageConditionGridOptions,
  gridEvents: storageConditionGridEvents,
});

// 第三步：布局配置表格
const layoutConfigGridOptions: VxeTableGridOptions<LocationManageApi.LayoutConfig> =
  {
    columns: useLayoutConfigColumns(),
    height: '300px',
    stripe: true,
    border: true,
    showOverflow: true,
    rowConfig: {
      keyField: 'id',
    },
    proxyConfig: {
      ajax: {
        query: async () => {
          // const locationTypeId = formData.basic.LOCATION_TYPE_ID;
          const locationTypeName = formData.basic.LOCATION_TYPE_LABEL;
          // message.success(`当前页面${locationTypeId}`);
          return await getStorageConditionLayoutList(locationTypeName);
        },
      },
    },
  };

const layoutConfigGridEvents: VxeGridListeners<LocationManageApi.LayoutConfig> =
  {
    checkboxChange: () => {
      const selectRecords = layoutConfigGridApi.grid?.getCheckboxRecords();
      formData.layoutConfigs = selectRecords;
      // console.log('layoutConfigs:', formData.layoutConfigs);
    },
  };

const [LayoutConfigGrid, layoutConfigGridApi] = useVbenVxeGrid({
  gridOptions: layoutConfigGridOptions,
  gridEvents: layoutConfigGridEvents,
});

// 计算属性
const isFirstStep = computed(() => currentStep.value === 1);
const isLastStep = computed(() => currentStep.value === 3);
const canNext = computed(() => {
  if (currentStep.value === 1) {
    return (
      formData.basic.LOCATIONCODE &&
      formData.basic.LOCATION_NAME &&
      formData.basic.LOCATION_TYPE_ID
    );
  }
  if (currentStep.value === 2) {
    return formData.storageCondition !== null;
  }
  return true;
});

// 步骤导航
async function nextStep() {
  if (currentStep.value === 1) {
    // 验证第一步表单
    try {
      const values = await basicFormApi.validate();
      Object.assign(formData.basic, values);
      currentStep.value = 2;
    } catch {
      message.error('请填写完整的基本信息');
    }
  } else if (currentStep.value === 2) {
    // 验证第二步选择
    if (!formData.storageCondition) {
      message.error('请选择一个存储条件');
      return;
    }
    currentStep.value = 3;
  }
}

function prevStep() {
  if (currentStep.value > 1) {
    currentStep.value--;
  }
}

// 提交表单
async function submitForm() {
  loading.value = true;
  try {
    const targetArray = formData.layoutConfigs.map((item) => [
      item.NAME || '',
      item.PREFIX || '',
      item.NUMBERING_METHOD || '',
      item.LOCTYPE_ORDER,
      item.LOCTYPE_SIZE,
      formData.basic.LOCATIONCODE,
      item.IS_STORABLE ? 'Y' : 'N', // 转换为字符串 '1'/'0'
    ]);

    const locationCode = formData.basic.LOCATIONCODE;
    const locationTypeId = formData.basic.LOCATION_TYPE_ID;
    const locationName = formData.basic.LOCATION_NAME;
    const description = formData.basic.DESCRIPTION;
    const isStorable = formData.basic.IS_STORABLE ? 'Y' : 'N';
    const isGxp = formData.basic.IS_GXP ? 'Y' : 'N';

    const locationArray: LocationManageApi.LocationConfig = {
      LOCATIONCODE: locationCode,
      LOCATIONTYPEID: locationTypeId,
      ROOMID: RoomId.value, // 如果允许 null，则接口字段应设为可选
      CONDITION: formData.storageCondition?.STCONDITION_CODE,
      LOCATION_NAME: locationName,
      LOCATIONID: locationCode,
      LOCTYPE_SIZE: 12,
      DESCRIPTION: description,
      IS_STORABLE: isStorable,
      IS_GXP: isGxp,
    };

    await addLocation(locationArray, 'Test');
    await addLocation(locationArray, 'Add');
    await addLocationInfo(targetArray, locationArray);

    // console.log('targetArray:', targetArray);

    message.success('位置添加成功');
    // emit('success', newLocation);
    emit('success');
    resetForm();
  } catch (error) {
    console.error('位置添加失败:', error);
    message.error('位置添加失败');
  } finally {
    loading.value = false;
  }
}

// 重置表单
function resetForm() {
  currentStep.value = 1;
  formData.basic = {
    LOCATIONCODE: '',
    LOCATION_NAME: '',
    DESCRIPTION: '',
    LOCATION_TYPE_ID: '',
    LOCATION_TYPE_LABEL: '',
    IS_STORABLE: '',
    IS_GXP: '',
  };
  formData.storageCondition = null;
  formData.layoutConfigs = [];

  basicFormApi.resetForm();
  storageConditionGridApi.grid?.clearRadioRow();
  layoutConfigGridApi.grid?.clearCheckboxRow();
}

// 监听步骤变化，设置表单初始值
watch(
  () => currentStep.value,
  (newStep) => {
    if (newStep === 1) {
      basicFormApi.setValues(formData.basic);
    }
  },
  { immediate: true },
);

// 取消操作
function handleCancel() {
  resetForm();
  emit('close');
}
</script>

<template>
  <div class="p-6">
    <!-- 步骤指示器 -->
    <div class="mb-6">
      <Steps :current="currentStep - 1" size="small">
        <Steps.Step
          v-for="(step, index) in steps"
          :key="index"
          :title="step.title"
          :description="step.description"
        />
      </Steps>
    </div>

    <!-- 步骤内容 -->
    <div class="min-h-[400px]">
      <!-- 第一步：基本信息 -->
      <div v-if="currentStep === 1">
        <h3 class="mb-4 text-lg font-medium">基本信息</h3>
        <BasicForm />
      </div>

      <!-- 第二步：存储条件 -->
      <div v-if="currentStep === 2">
        <h3 class="mb-4 text-lg font-medium">选择存储条件</h3>
        <p class="mb-4 text-gray-600">请选择一个适合的存储条件：</p>
        <StorageConditionGrid />
        <div
          v-if="formData.storageCondition"
          class="mt-4 rounded bg-blue-50 p-3"
        >
          <p class="text-sm text-blue-600">
            已选择：{{ formData.storageCondition.STCONDITION_CODE }} ({{
              formData.storageCondition.TEMPERATURE
            }}, {{ formData.storageCondition.HUMIDITY }})
          </p>
        </div>
      </div>

      <!-- 第三步：布局配置 -->
      <div v-if="currentStep === 3">
        <h3 class="mb-4 text-lg font-medium">布局配置</h3>
        <p class="mb-4 text-gray-600">请选择需要的布局配置项（可多选）：</p>
        <LayoutConfigGrid />
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="mt-6 flex justify-between border-t pt-4">
      <div></div>

      <Space>
        <Button v-if="!isFirstStep" @click="prevStep"> 上一步 </Button>
        <Button @click="handleCancel"> 取消 </Button>
        <Button
          v-if="!isLastStep"
          type="primary"
          @click="nextStep"
          :disabled="!canNext"
        >
          下一步
        </Button>
        <Button
          v-if="isLastStep"
          type="primary"
          :loading="loading"
          @click="submitForm"
        >
          提交
        </Button>
      </Space>
    </div>
  </div>
</template>
