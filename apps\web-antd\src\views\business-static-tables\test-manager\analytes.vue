<script setup lang="ts">
import type { TestManagerApi } from '#/api/business-static-tables/test-manager';

import { computed, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import {
  $delAnalyteApi,
  $getAnalyteListApi,
} from '#/api/business-static-tables/test-manager';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';
import { confirm } from '#/utils/utils';

import AddTestForm from './add-test-form.vue';
import { useAnalyteColumns, useAnlyteFilterSchema } from './data';
import PassibleResults from './passible-results.vue';

const props = defineProps<{
  currentTestRow: null | TestManagerApi.Test;
}>();

watch(
  () => props.currentTestRow,
  (_val) => {
    onRefresh();
  },
);
const colums = useAnalyteColumns();
const filterSchema = useAnlyteFilterSchema();
const queryData = async () => {
  if (!props.currentTestRow?.TESTCODE) return [];
  return $getAnalyteListApi(props.currentTestRow.TESTCODE);
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};

const {
  Grid: AnalyteGrid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
  CurrentRow,
} = useLimsGridsConfig<TestManagerApi.Analyte>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddTestForm,
  destroyOnClose: true,
});

const [PassibleResultsFormModal, passibleResultsModalApi] = useVbenModal({
  showConfirmButton: true,
  confirmText: $t('commons.finish'),
  showCancelButton: false,
  destroyOnClose: true,
  onConfirm() {
    passibleResultsModalApi.close();
  },
});

async function onDelete() {
  const checkRecords = gridApi.grid?.getCheckboxRecords();
  if (checkRecords.length === 0) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const analytes = checkRecords.map((item) => item.ANALYTE);
  const testCode = checkRecords[0].TESTCODE;
  await confirm($t('commons.deleteConfirm'), $t('commons.deleteConfirmTitle'));
  const notDeleted = await $delAnalyteApi({
    testCode,
    analytes,
  });
  if (notDeleted && notDeleted[0].length > 0) {
    message.error(
      $t('business-static-tables.testManager.delAnalyte', [
        notDeleted[0].join(','),
      ]),
    );
    return;
  }
  if (notDeleted && notDeleted[1].length > 0) {
    message.error(
      $t('business-static-tables.testManager.analUsedInSamplesWarn', [
        notDeleted[1].join(','),
      ]),
    );
    return;
  }
  if (notDeleted && notDeleted[2].length > 0) {
    message.error(
      $t('business-static-tables.testManager.analyteAndMaterials', [
        notDeleted[2].join(','),
      ]),
    );
    return;
  }
  message.success($t('commons.deleteSuccess'));
  onRefresh();
}

function onCreate() {
  if (!props.currentTestRow?.TESTCODE) {
    message.warning($t('business-static-tables.testManager.selectTestFirst'));
    return;
  }
  formModalApi
    .setData({
      currTestCatCode: props.currentTestRow?.TESTCATCODE,
      currTestCode: props.currentTestRow?.TESTCODE,
      currTestName: props.currentTestRow?.TESTNO,
      mode: 'analyteApp',
    })
    .open();
}

async function showPassibleResult() {
  passibleResultsModalApi
    .setData({
      analyte: CurrentRow.value,
      mode: 'analyteApp',
    })
    .open();
}

function onRefresh() {
  gridApi.query();
}

const showPassibleBtn = computed(() => {
  return !CurrentRow.value || CurrentRow.value?.ANALTYPE === 'N';
});
</script>

<template>
  <FormModal @success="onRefresh" class="!w-[800px]" />
  <PassibleResultsFormModal class="!w-[800px]">
    <PassibleResults :analyte="CurrentRow" mode="TestManager" />
  </PassibleResultsFormModal>
  <AnalyteGrid class="h-full">
    <template #toolbar-actions>
      <Space :size="[4, 0]" wrap>
        <Button type="primary" @click="onCreate">
          {{ $t('ui.actionTitle.create') }}
        </Button>
        <Button type="primary" danger @click="onDelete">
          {{ $t('ui.actionTitle.delete') }}
        </Button>
        <Button
          type="default"
          @click="showPassibleResult"
          :disabled="showPassibleBtn"
        >
          {{ $t('business-static-tables.testManager.possibleResult') }}
        </Button>
      </Space>
    </template>

    <template #action="{ row }">
      <template v-if="hasEditStatus(row)">
        <Button type="link" @click="saveRowEvent(row)">
          {{ $t('commons.save') }}
        </Button>
        <Button type="link" @click="cancelRowEvent(row)">
          {{ $t('commons.cancel') }}
        </Button>
      </template>
      <template v-else>
        <Button type="link" @click="editRowEvent(row)">
          {{ $t('commons.edit') }}
        </Button>
      </template>
    </template>
  </AnalyteGrid>
</template>
