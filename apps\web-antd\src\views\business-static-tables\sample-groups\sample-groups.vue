<script lang="ts" setup>
import type { DefaultOptionType, SelectValue } from 'ant-design-vue/es/select';

import type { Recordable } from '@vben/types';

import type { SampleGroupsApi } from '#/api/business-static-tables/sample-groups';

import { onMounted, ref } from 'vue'; // 添加了 onMounted 和 reactive 的导入

import { confirm, Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import {
  Button,
  Card,
  message,
  Select,
  SelectOption,
  Space,
  TabPane,
  Tabs,
} from 'ant-design-vue';

import {
  delSampGroup,
  getSampleGrpsList,
  getTestPlanGroups,
} from '#/api/business-static-tables/sample-groups';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import AddSampleGrpForm from './add-sample-group.vue';
import Recipe from './recipe.vue';
import {
  useSampleGrpColumns,
  useSampleGrpFilterSchema,
} from './sample-groups-data';
import IpSampleGroupDetail from './sample-groups-detail.vue';
import SampleRequirement from './sample-requirement.vue';

const activeKey = ref('1');

const colums = useSampleGrpColumns();
const filterSchema = useSampleGrpFilterSchema();
const queryData = async () => {
  if (!selectedTestGroup.value) return [];
  return getSampleGrpsList(selectedTestGroup.value, 'Create');
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
};

const {
  Grid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
  CurrentRow,
} = useLimsGridsConfig<SampleGroupsApi.IpSampleGroups>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

function onRefresh() {
  gridApi.query(); // 主动触发一次查询
}

// 添加样品模板组
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddSampleGrpForm,
});

async function onCreate() {
  formModalApi.setData(null).open();
}

async function onDelete() {
  // 获取选中行
  const samplegrp = gridApi.grid?.getCurrentRecord();
  if (!samplegrp) return;

  const sSamplegroupCode = samplegrp.SAMPLEGROUPCODE;

  if (sSamplegroupCode === 'GR000807') {
    message.warning($t('business-static-tables.sampleGroups.NoDelAdHocSGT'));
    return;
  }

  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的数据吗？`,
      icon: 'warning',
      centered: false,
    });

    const sRet = await delSampGroup(sSamplegroupCode);
    switch (sRet) {
      case -103: {
        message.warning(
          $t('business-static-tables.sampleGroups.sampleGroupInSamples'),
        );
        break;
      }
      case -102: {
        message.warning(
          $t(
            'business-static-tables.sampleGroups.sampleGroupHasTestPlansExistingInOtherSampGrp',
          ),
        );
        break;
      }
      case -101: {
        message.warning(
          $t(
            'business-static-tables.sampleGroups.sampleGroupHasReleasedTestPlans',
          ),
        );
        break;
      }
      case -100: {
        message.warning(
          $t('business-static-tables.sampleGroups.sampleGroupInBatch'),
        );
        break;
      }
      case 0: {
        message.success('删除成功');
        onRefresh();
        break;
      }
    }
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

// 组件挂载时展开所有节点
onMounted(async () => {
  await getTestGroupList();
});

const selectedTestGroup = ref<string>();

const onSelectChange = (
  _value: SelectValue,
  _option: DefaultOptionType | DefaultOptionType[],
) => {
  onRefresh();
};

const testGroupOptions = ref<Recordable<string>[]>([]);
const getTestGroupList = async () => {
  try {
    const res = await getTestPlanGroups('Create');
    if (res && res.items && res.items.length > 0) {
      testGroupOptions.value = res.items;
    } else {
      console.warn('样品模板组数据为空，请检查接口返回结果');
    }
  } catch (error) {
    console.error('获取样品模板组失败:', error);
  }
};
</script>
<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <Card>
      <label class="w-24 pr-2 text-left">样品模板组：</label>
      <Select
        placeholder="请选择样品模板组"
        style="width: 50%"
        v-model:value="selectedTestGroup"
        @change="onSelectChange"
      >
        <SelectOption
          v-for="item in testGroupOptions"
          :key="item.PRODGROUP"
          :value="item.PRODGROUP"
        >
          {{ item.PRODGROUP }}
        </SelectOption>
      </Select>
    </Card>
    <div class="my-2 h-[350px] w-full">
      <Grid>
        <template #toolbar-actions>
          <Space>
            <Button type="primary" @click="onCreate">
              {{ $t('ui.actionTitle.create') }}
            </Button>
            <Button type="primary" danger @click="onDelete">
              {{ $t('ui.actionTitle.delete') }}
            </Button>
            <Button type="default">
              {{ $t('business-static-tables.copy') }}
            </Button>
          </Space>
        </template>
        <template #action="{ row }">
          <template v-if="hasEditStatus(row)">
            <Button type="link" @click="saveRowEvent(row)">
              {{ $t('business-static-tables.save') }}
            </Button>
            <Button type="link" @click="cancelRowEvent(row)">
              {{ $t('business-static-tables.cancel') }}
            </Button>
          </template>
          <template v-else>
            <Button type="link" @click="editRowEvent(row)">
              {{ $t('business-static-tables.edit') }}
            </Button>
          </template>
        </template>
      </Grid>
    </div>
    <Tabs v-model:active-key="activeKey">
      <TabPane key="1" tab="测试计划组">
        <IpSampleGroupDetail :current-test-row="CurrentRow" />
      </TabPane>
      <TabPane key="2" tab="配方">
        <Recipe :current-test-row="CurrentRow" />
      </TabPane>
      <TabPane key="3" tab="取样要求">
        <SampleRequirement :current-test-row="CurrentRow" />
      </TabPane>
    </Tabs>
  </Page>
</template>
