<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { EquipmentMgApi } from '#/api/equipment/equipment-mg';

import { computed, watch } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { Button, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getEquipmentUseLogApi } from '#/api/equipment/equipment-mg';
import { $t } from '#/locales';
import { useEquipmentStore } from '#/store';

import AddModal from './components/add-modal.vue';
import { usedRocordColumns } from './equipment-mg-data';

const [AddFormModal, addFormModalApi] = useVbenModal({
  connectedComponent: AddModal,
  destroyOnClose: true,
});

const equipmentStore = useEquipmentStore();
const currentRow: EquipmentMgApi.RowType = computed(
  () => equipmentStore.getCurrentRow,
);
interface RowType {
  [key: string]: any;
}
watch(
  currentRow,
  async (newRow: RowType) => {
    if (newRow) {
      gridApi.query();
    }
  },
  { deep: true },
);
const gridOptions: VxeTableGridOptions<EquipmentMgApi.MetaDataEquipment> = {
  columns: usedRocordColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'cell',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},

  proxyConfig: {
    ajax: {
      query: async () => {
        if (!currentRow.value) {
          return [];
        }
        const params = [currentRow.value.EQID, ''];
        const data = await getEquipmentUseLogApi(params);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: {},
});
const hasEditStatus = (row: EquipmentMgApi.RowType) => {
  return gridApi.grid?.isEditByRow(row);
};
const editRowEvent = (row: EquipmentMgApi.RowType) => {
  gridApi.grid?.setEditRow(row);
};

const saveRowEvent = async (row: EquipmentMgApi.RowType) => {
  console.warn(row);
  await gridApi.grid?.clearEdit();
  gridApi.setLoading(true);
  setTimeout(() => {
    gridApi.setLoading(false);
  }, 600);
};
const cancelRowEvent = (_row: EquipmentMgApi.RowType) => {
  gridApi.grid?.clearEdit();
};
const onAdd = () => {
  addFormModalApi.setData(null).open();
};
const viewELN = () => {
  // TODO 查看ELN
};
const launchELN = () => {
  // TODO 运行ELN
};
const onRefresh = () => {
  gridApi.query();
};
</script>
<template>
  <Page auto-content-height>
    <AddFormModal @success="onRefresh" />
    <Grid class="h-5/6">
      <template #toolbar-actions>
        <Space :size="[8, 0]" wrap>
          <Button type="primary" @click="onAdd">
            {{ $t('equipment.equipment-mg.add') }}
          </Button>
          <Button type="default" @click="viewELN">
            {{ $t('equipment.equipment-mg.inspectELN') }}
          </Button>
          <Button type="default" @click="launchELN">
            {{ $t('equipment.equipment-mg.launchELN') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent(row)">
            {{ $t('basic-static-tables.save') }}
          </Button>
          <Button type="link" @click="cancelRowEvent(row)">
            {{ $t('equipment.cancel') }}
          </Button>
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">
            {{ $t('basic-static-tables.edit') }}
          </Button>
        </template>
      </template>
    </Grid>
  </Page>
</template>
