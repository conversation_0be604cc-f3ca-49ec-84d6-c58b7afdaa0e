import { Dayjs } from 'dayjs';

import { callServer, getDataSet, getDataSetNoPage } from '../core';

export namespace TestManagerApi {
  export interface Test {
    [key: string]: any;
    ORIGREC: number;
    TESTCATCODE: string;
    TESTCODE: number;
    TESTNO: string;
    TESTNO_ENG: string;
    TESTDESC: string;
    TESTNO_ALIAS: string;
    PREPTMNAME: string;
    TESTFLAG: string;
    TMNAME: string;
    WF_CODE: string;
    CREATERUNTYPE: string;
    APPRNEED: string;
    TESTTYPE: string;
    SCORETYPE: string;
  }

  export interface Analyte {
    [key: string]: any;
    ORIGREC: number;
    SORTER: number; // 排序
    ANALYTE: string; // 分析项目
    ANALYTE_ENG: string; // 分析项目英文
    SINONYM: string; // 别名
    SCHEMANAME: string; // 标准设置
    CALCNAME: string; // 计算公式
    PICTURE: string; // 修约
    LOW: string; // 低限
    HIGH: string; // 高限
    UNITS: string; // 计量单位
    NOREP: string; // 重复项
    ANALTYPE: string; // 结果类型
    TESTCODE: number; // 测试代码
  }

  export interface MethodRelatedToTests {
    [key: string]: any;
    ORIGREC: number;
    DEFAULTMETHOD: string;
    METHOD: string;
    DEPT: string;
    METHODSTATUS: string;
    SERVGRP: string;
    TEAM: string;
    EQTYPE: string;
    ALL_EQUIPMENT: string;
    INSTVERIFY: string;
    ASSIGN_TYPE: string;
    ELNREFERENCE: string;
    DESIREDTAT: string;
    STARTDATE: Dayjs;
    TODATE: Dayjs;
    TESTCODE: number;
    STARTDDATE: Dayjs;
    ELN_ID: string;
    MAX_ALLOCATION_SAMPLE_QUANTITY: number;
    ALLOCATION_SAMPLE_UNIT: string;
  }

  export interface TestPlan {
    [key: string]: any;
    ORIGREC: number;
    PROGNAME: string;
  }

  export interface DelTestParam {
    testCodes: number[];
    tmnames: string[];
    testcatcodes: string[];
    testnames: string[];
    aTestFlags: string[];
  }

  export interface PassibleResults {
    [key: string]: any;
    ORIGREC: number;
    RESULT: string;
    SNOMEDCODE: string;
    RESULT_STATUS: string;
    CONCLUSION_REPORT: string;
    TESTCODE: number;
    ANALYTE: string;
    SORTER: number; // 排序
  }

  export interface OutSourceLabs {
    [key: string]: any;
    ORIGREC: number;
    LOGGEDBYDEPT: string; // 登记实验室
    DEPT: string; // 外包实验室
    PREFERENCE: string; // 优先级
    CERTIFIEDTILL: Dayjs; // 认证到期日
    TESTCODE: number; // 测试代码
    METHOD: string; // 方法
  }

  export interface TESTMETHODOTHEREQUIPMENT {
    [key: string]: any;
    ORIGREC: number;
    EQTYPE: string; // 设备类型
    TESTINGEQUIPMENT: string; // 测试设备
    TIMEUSEDINMINUTES: number; // 设备使用(分钟)
    TESTCODE: number;
    METHOD: string;
  }

  export interface TEST_METHOD_REQ_DOCS {
    [key: string]: any;
    ORIGREC: number;
    NAME: string; // 文档名称
    URL: string; // 文档地址
    STARDOC_ID: string; // 文档ID
    TESTCODE: number;
    METHOD: string;
  }
}

const $getTestCatApi = async () => {
  return await getDataSetNoPage('TEST_MANAGER.dsTestLibrary', []);
};

// dsTestCatNoAll
const $getTestCatNoAllApi = async () => {
  return await getDataSetNoPage('TEST_MANAGER.dsTestCatNoAll', []);
};

// CB_ANALYTE
const $getAnalyteApi = async (notInTestCode: number) => {
  return await getDataSetNoPage('TEST_MANAGER.CB_ANALYTE', [notInTestCode]);
};

// DS_ANALYTES
const $getAnalyteListApi = async (testCode: number) => {
  return await getDataSet('TEST_MANAGER.DS_ANALYTES', [testCode]);
};

// CB_ANALYTE_TYPE
const $getAnalyteTypeApi = async () => {
  return await getDataSetNoPage('TEST_MANAGER.CB_ANALYTE_TYPE', []);
};

const $moveTestCatogoryApi = async (
  newTestCatCode: string,
  oldTestCatCode: string,
  moveOrigrec: number[],
) => {
  return await callServer('TEST_MANAGER.MOVE_TEST', [
    newTestCatCode,
    oldTestCatCode,
    moveOrigrec,
  ]);
};

const $getTestListApi = async (testCatCode: string) => {
  return await getDataSet('TEST_MANAGER.DS_TEST_LIST', [testCatCode]);
};

const $delTestApi = async (testlist: TestManagerApi.DelTestParam) => {
  return await callServer('TEST_MANAGER.DEL_TEST', [
    testlist.tmnames,
    testlist.testCodes,
    testlist.testcatcodes,
    testlist.testnames,
    testlist.aTestFlags,
  ]);
};

const $getCboMethodApi = async () => {
  return await getDataSetNoPage('TEST_MANAGER.CB_METHOD', []);
};

const $getCboServgrpApi = async () => {
  return await getDataSetNoPage('TEST_MANAGER.DS_SERVEGRPS3', []);
};

const $getCboEqTypeApi = async () => {
  return await getDataSetNoPage('TEST_MANAGER.DS_EQUIPTYPES', []);
};

const $addTestApi = async (test: Omit<TestManagerApi.Test, 'ORIGREC'>) => {
  return await callServer('TEST_MANAGER.ADD_TEST', [
    test.TESTNO,
    test.TESTCATCODE,
    test.METHOD,
    test.SERVGRP,
    test.EQTYPE,
    test.CHECKEQSTATUS,
    test.NEEDCERT,
    null, // ELN
    null, // AutoLoad
    test.ISALLEQ,
    test.TESTNO_ENG,
  ]);
};

const $getUnitApi = async () => {
  return await getDataSetNoPage('TEST_MANAGER.CB_UNITS', []);
};

// DS_POSSIBLE_RESULTS
const $getPossibleResultsApi = async (data: {
  analyte: string;
  mode: string;
  testCode: number;
}) => {
  return await getDataSet('TEST_MANAGER.DS_POSSIBLE_RESULTS', [
    data.analyte,
    data.testCode,
    data.mode,
  ]);
};

// DEL_POSSIBLE_RESULT
const $delPossibleResultApi = async (data: {
  analyte: string;
  origrec: number;
  result: string;
  testCode: number;
}) => {
  return await callServer('TEST_MANAGER.DEL_POSSIBLE_RESULT', [
    data.origrec,
    data.testCode,
    data.analyte,
    data.result,
  ]);
};

// ADD_POSSIBLE_RESULT
const $addPossibleResultApi = async (
  data: Omit<TestManagerApi.PassibleResults, 'ORIGREC'>,
) => {
  return await callServer('TEST_MANAGER.ADD_POSSIBLE_RESULT', [
    data.TESTCODE,
    data.ANALYTE,
    data.RESULT,
    data.SNOMEDCODE,
    data.CONCLUSION_REPORT,
    data.RESULT_STATUS,
    data.mode,
  ]);
};

// TEST_MANAGER.ADD_ANALYTE
const $addAnalyteApi = async (
  data: Omit<TestManagerApi.Analyte, 'ORIGREC'>,
) => {
  return await callServer('TEST_MANAGER.ADD_ANALYTE', [
    data.TESTCODE,
    data.ANALYTE,
    data.SINONYM,
    data.SCHEMANAME,
    data.UNITS,
    data.NOREP,
    data.ANALTYPE,
    '', // picture
    '', // attachtype
    data.ANALYTE_ENG,
  ]);
};

const $getResultStatusApi = async () => {
  return await getDataSetNoPage('TEST_MANAGER.CB_STATUS', []);
};

// ADD_ROUNDING
const $addRoundingApi = async (data: { origrec: number; rounding: string }) => {
  return await callServer('Wizard.ADD_ROUNDING', [data.origrec, data.rounding]);
};

// CB_LOGIN_FLAG
const $getLoginFlagApi = async () => {
  return await getDataSetNoPage('TEST_MANAGER.CB_LOGIN_FLAG', []);
};

// DELETE_ANALYTE
const $delAnalyteApi = async (data: {
  analytes: string[];
  testCode: number;
}) => {
  return await callServer('TEST_MANAGER.DELETE_ANALYTE', [
    data.testCode,
    data.analytes,
  ]);
};

// ADD_METHOD
const $addMethodRelatedTestApi = async (
  data: Omit<TestManagerApi.MethodRelatedToTests, 'ORIGREC'>,
) => {
  return await callServer('TEST_MANAGER.ADD_METHOD', [
    data.TESTCODE,
    data.METHOD,
    data.DEPT,
    data.ELN_ID,
    data.SERVGRP,
    new Date(),
  ]);
};

const $getMethodRelatedListApi = async (testCode: number) => {
  return await getDataSet('TEST_MANAGER.DGD_METHODS', [testCode]);
};

// cbShareDBSites
const $getShareDBSitesApi = async () => {
  return await getDataSetNoPage('TEST_MANAGER.cbShareDBSites', []);
};

// DS_SERVEGRPS2
const $getShareDBSitesApi2 = async (data: { dept: string }) => {
  return (await getDataSetNoPage('TEST_MANAGER.DS_SERVEGRPS2', [
    data.dept,
  ])) as { SERVGRP: string }[];
};

//
const $checkEquipOtherServGrpApi = async (data: {
  arrDelData: number[];
  aSelectedEq: null | string[];
  nTestCode: number;
  sCrtServGrp: string;
  sDept: string;
  sEqType: string;
  sMethod: string;
}) => {
  return await callServer('TEST_MANAGER.checkEquipOtherServGrp', [
    data.nTestCode,
    data.sMethod,
    data.aSelectedEq,
    data.sCrtServGrp,
    data.sDept,
    data.sEqType,
    data.arrDelData,
  ]);
};

const $delMethodRelatedApi = async (data: {
  aDept: string[];
  arrDelData: number[];
  aSelServGrp: string[];
  nTestCode: number;
}) => {
  return await callServer('TEST_MANAGER.DEL_METHOD', [
    data.arrDelData,
    data.nTestCode,
    data.aSelServGrp,
    data.aDept,
  ]);
};

// EquipmentsByType
const $getEquipmentsByTypeApi = async ({
  strEquipmentType,
  nTestCode,
  strMethod,
  strServiceGroup,
  strDepartment,
}: {
  nTestCode: number;
  strDepartment: string;
  strEquipmentType: string;
  strMethod: string;
  strServiceGroup: string;
}): Promise<{ EQID: string; SELECTED: string }[]> => {
  return await getDataSetNoPage('TEST_MANAGER.EquipmentsByType', [
    strEquipmentType,
    nTestCode,
    strMethod,
    strServiceGroup,
    strDepartment,
  ]);
};

const $editEquipmentListApi = async ({
  testCode,
  method,
  selectedEquipments,
  servgrp,
  dept,
}: {
  dept: string;
  method: string;
  selectedEquipments: string[];
  servgrp: string;
  testCode: number;
}) => {
  return await callServer('TEST_MANAGER.EditEquipmentList', [
    testCode,
    method,
    selectedEquipments,
    servgrp,
    dept,
  ]);
};

const $getOutSourceLabsApi = async ({
  testCode,
  method,
}: {
  method: string;
  testCode: string;
}) => {
  return await getDataSet('TEST_MANAGER.OutSourceLabs_dg', [method, testCode]);
};

// AddOutSourceLabs
const $addOutSourceLabsApi = async (
  data: Omit<TestManagerApi.OutSourceLabs, 'ORIGREC'>,
) => {
  return await callServer('TEST_MANAGER.AddOutSourceLabs', [
    data.CERTIFIEDTILL,
    data.DEPT,
    data.PREFERENCE,
    data.TESTCODE,
    data.LOGGEDBYDEPT,
    data.METHOD,
  ]);
};

// LoggedIn_cb {Text:string, Value:string}[]
const $getCbLoggedInApi = async (): Promise<
  { Text: string; Value: string }[]
> => {
  return await getDataSetNoPage('TEST_MANAGER.LoggedIn_cb', []);
};

const $getCbOutSourceApi = async ({
  loggedbydept,
}: {
  loggedbydept: string;
}): Promise<{ DEPT: string }[]> => {
  return await getDataSetNoPage('TEST_MANAGER.OutSource_cb', [loggedbydept]);
};

const $deleteOutSourceLabApi = async ({
  strOrigrec,
}: {
  strOrigrec: string;
}) => {
  return await callServer('TEST_MANAGER.DeleteOutSourceLab', [strOrigrec]);
};

const $editOtherEquipmentListApi = async ({
  testCode,
  method,
  selectEqTypes,
}: {
  method: string;
  selectEqTypes: string[];
  testCode: number | string;
}) => {
  return await callServer('TEST_MANAGER.EditOtherEquipmentList', [
    testCode,
    method,
    selectEqTypes,
  ]);
};

const $getOtherEquipmentListApi = async ({
  testCode,
  method,
}: {
  method: string;
  testCode: string;
}): Promise<TestManagerApi.TESTMETHODOTHEREQUIPMENT[]> => {
  return await getDataSet('TEST_MANAGER.dgdOtherEquipTypes', [
    testCode,
    method,
  ]);
};

const $mcEquipTypesApi = async ({
  testCode,
  method,
}: {
  method: string;
  testCode: string;
}): Promise<{ EQTYPE: string; selected: string }[]> => {
  return await getDataSetNoPage('TEST_MANAGER.mcEquipTypes', [
    testCode,
    method,
  ]);
};

// dsMethod_ELN
const $getSelctedElnApi = async ({
  elnId,
}: {
  elnId: string;
}): Promise<{ SELECTED: string; TEXT: string; VALUE: string }[]> => {
  return await getDataSetNoPage('TEST_MANAGER.dsMethod_ELN', [elnId]);
};

const $updateElnIdApi = async ({
  elnId,
  origrec,
}: {
  elnId: string;
  origrec: number;
}) => {
  return await callServer('Common.Update', [
    'METHODSRELATEDTOTESTS',
    'ELN_ID',
    elnId,
    origrec,
    'ORIGREC',
  ]);
};

// testplan
const $getTestPlanListApi = async ({ testCode }: { testCode: number }) => {
  return await getDataSet('TEST_MANAGER.DS_TEST_PLANS', [testCode]);
};

const $getMethodDocListApi = async ({
  testCode,
  method,
}: {
  method: string;
  testCode: number;
}): Promise<TestManagerApi.TEST_METHOD_REQ_DOCS[]> => {
  return await getDataSet('TEST_MANAGER.requiredDocuments', [testCode, method]);
};

const $delMethodDocApi = async ({
  origrecs,
  starDocs,
}: {
  origrecs: number[];
  starDocs: string[];
}) => {
  return await callServer('Enterprise_Utilities.DeleteAttachment', [
    'TEST_METHOD_REQ_DOCS',
    origrecs,
    starDocs,
    true,
    '',
    '',
    [],
  ]);
};

const $addRequiredDocumentsApi = async ({
  TESTCODE,
  METHOD,
  NAME,
}: Omit<TestManagerApi.TEST_METHOD_REQ_DOCS, 'ORIGREC'>) => {
  return await callServer('TEST_MANAGER.requiredDocuments_Add', [
    TESTCODE,
    METHOD,
    NAME,
  ]);
};

export {
  $addAnalyteApi,
  $addMethodRelatedTestApi,
  $addOutSourceLabsApi,
  $addPossibleResultApi,
  $addRequiredDocumentsApi,
  $addRoundingApi,
  $addTestApi,
  $checkEquipOtherServGrpApi,
  $delAnalyteApi,
  $deleteOutSourceLabApi,
  $delMethodDocApi,
  $delMethodRelatedApi,
  $delPossibleResultApi,
  $delTestApi,
  $editEquipmentListApi,
  $editOtherEquipmentListApi,
  $getAnalyteApi,
  $getAnalyteListApi,
  $getAnalyteTypeApi,
  $getCbLoggedInApi,
  $getCboEqTypeApi,
  $getCboMethodApi,
  $getCboServgrpApi,
  $getCbOutSourceApi,
  $getEquipmentsByTypeApi,
  $getLoginFlagApi,
  $getMethodDocListApi,
  $getMethodRelatedListApi,
  $getOtherEquipmentListApi,
  $getOutSourceLabsApi,
  $getPossibleResultsApi,
  $getResultStatusApi,
  $getSelctedElnApi,
  $getShareDBSitesApi,
  $getShareDBSitesApi2,
  $getTestCatApi,
  $getTestCatNoAllApi,
  $getTestListApi,
  $getTestPlanListApi,
  $getUnitApi,
  $mcEquipTypesApi,
  $moveTestCatogoryApi,
  $updateElnIdApi,
};
