import type { VxeColumnPropTypes } from 'vxe-table';

import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { SubLocationTypeApi } from '#/api/';

import { $t } from '#/locales';

export function useSubLocationTypeColumns(
  methodOptions: VxeColumnPropTypes.EditRender,
): VxeTableGridOptions<SubLocationTypeApi.SubLocationType>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'origrec',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'NAME',
      title: $t('basic-static-tables.sub-location-type.name'),
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'PREFIX',
      title: $t('basic-static-tables.sub-location-type.prefix'),
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'NUMBERING_METHOD',
      title: $t('basic-static-tables.sub-location-type.numbering_method'),
      editRender: methodOptions,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SUBLOCATIONS_TEMPLATE_ID',
      title: $t(
        'basic-static-tables.sub-location-type.sublocations_template_id',
      ),
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },

    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('basic-static-tables.operation'),
      width: 140,
    },
  ];
}
export function useSubLocationTypeFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        // placeholder: 'Please enter code',
      },
      fieldName: 'NAME',
      label: $t('basic-static-tables.sub-location-type.name'),
    },
    {
      component: 'Input',
      fieldName: 'PREFIX',
      componentProps: {
        // placeholder: 'Please enter name',
      },
      label: $t('basic-static-tables.sub-location-type.prefix'),
    },
  ];
}
