<script lang="ts" setup>
import type { VxeColumnPropTypes } from 'vxe-table';

import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { QcTypeApi } from '#/api/basic-static-tables/qc-types';

import { onMounted, reactive } from 'vue'; // 添加了 onMounted 和 reactive 的导入

import { confirm, Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Button, message, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addQcType,
  deleteQcType,
  getQcGroup,
  getQcTypeList,
} from '#/api/basic-static-tables/qc-types';

import AddQcTypeForm from './add-qc-type.vue';
import { useQcTypeColumns, useQcTypeFilterSchema } from './qc-types-data';

function createEditRender(): VxeColumnPropTypes.EditRender {
  return {
    name: 'select',
    options: [],
  };
}

const qcGroupEditRender = reactive(createEditRender());

onMounted(async () => {
  // 绑定QC组数据
  const qcGroupResult = await getQcGroup();
  qcGroupEditRender.options = qcGroupResult.items.map((item) => ({
    label: item.DISPLAY_TEXT,
    value: item.QC_GROUP,
  }));
});

const gridOptions: VxeTableGridOptions<QcTypeApi.QcType> = {
  columns: useQcTypeColumns(qcGroupEditRender),
  stripe: true,
  keepSource: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getQcTypeList();
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const formOptions: VbenFormProps = {
  schema: useQcTypeFilterSchema(),
  submitOnChange: false,
  collapsed: true,
  showCollapseButton: true,
  submitButtonOptions: {
    content: '查询',
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  formOptions,
});

function hasEditStatus(row: QcTypeApi.QcType) {
  return gridApi.grid?.isEditByRow(row);
}

function editRowEvent(row: QcTypeApi.QcType) {
  gridApi.grid?.setEditRow(row);
}

async function saveRowEvent(row: QcTypeApi.QcType) {
  await gridApi.grid?.clearEdit();
  addQcType(row);
  gridApi.setLoading(true);
  setTimeout(() => {
    gridApi.setLoading(false);
    message.success({
      content: `保存成功！`,
    });
  }, 600);
}

const cancelRowEvent = (row: QcTypeApi.QcType) => {
  gridApi.grid?.clearEdit().then(() => {
    // 还原行数据
    gridApi.grid.revertData(row);
  });
};

function onRefresh() {
  gridApi.query();
}

// 添加QC类型
const [AddQcTypeFormModal, qcTypeModalApi] = useVbenModal({
  connectedComponent: AddQcTypeForm,
});

async function addQcTypeFun() {
  qcTypeModalApi.setData(null).open();
}

// 删除QC类型
async function deleteQcTypeConfirm() {
  // 获取选中行
  const aQctype: string[] = gridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.QCTYPE);

  if (aQctype.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${aQctype.length} 条数据吗？`,
      icon: 'warning',
      centered: false,
    });

    await deleteQcType(aQctype);

    message.success('删除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}
</script>
<template>
  <Page auto-content-height>
    <FormDrawer @success="onRefresh" />
    <AddQcTypeFormModal @success="onRefresh" />
    <Grid>
      <template #toolbar-actions>
        <Space>
          <Button type="primary" @click="addQcTypeFun">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="deleteQcTypeConfirm">
            {{ $t('ui.actionTitle.delete') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent(row)">
            {{ $t('basic-static-tables.save') }}
          </Button>
          <Button type="link" @click="cancelRowEvent(row)">
            {{ $t('basic-static-tables.cancel') }}
          </Button>
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">
            {{ $t('basic-static-tables.edit') }}
          </Button>
        </template>
      </template>
    </Grid>
  </Page>
</template>
