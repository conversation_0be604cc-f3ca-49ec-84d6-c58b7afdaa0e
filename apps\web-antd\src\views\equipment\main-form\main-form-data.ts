import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { MainFormApi } from '#/api/equipment/main-form';

import dayjs from 'dayjs';

import { getCBWeightListApi, getEventApi } from '#/api/equipment/main-form';
import { $t } from '#/locales';

export function balanceColumns(): VxeTableGridOptions<MainFormApi.MainForm>['columns'] {
  return [
    {
      align: 'center',
      field: 'EQID',
      title: $t('equipment.equipment-mg.equipmentId'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },

    {
      align: 'center',
      field: 'EQNAME',
      title: $t('equipment.equipment-mg.equipmentName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MAINSTATUS',
      title: $t('equipment.equipment-mg.status'),
      filterRender: {
        name: 'TableFilterInput',
      },
      formatter: 'formatTranslate',
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'EQTYPE',
      title: $t('equipment.main-form.model'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MFG',
      title: $t('equipment.main-form.manufacturer'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RANGE',
      title: $t('equipment.main-form.balanceRange'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'PRECISIONS',
      title: $t('equipment.main-form.balancePrecision'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'DIVISIONVALUE',
      title: $t('equipment.main-form.verificationScale'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}
export function useSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'EQID',
      label: $t('equipment.equipment-mg.equipmentId'),
    },
    {
      component: 'Input',
      fieldName: 'EQNAME',
      label: $t('equipment.equipment-mg.equipmentName'),
    },
    {
      component: 'Select',
      fieldName: 'EQTYPE',
      label: $t('equipment.equipment-mg.equipmentType'),
      componentProps: {
        class: 'w-full',
        allowClear: true,
        filterOption: true,
        options: [
          {
            label: '选项1',
            value: '1',
          },
          {
            label: '选项2',
            value: '2',
          },
        ],
        placeholder: '请选择',
        showSearch: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'MFG',
      label: $t('equipment.equipment-mg.manufacturer'),
    },
    {
      component: 'Input',
      fieldName: 'SPEC',
      label: $t('equipment.equipment-mg.model'),
    },
    {
      component: 'Input',
      fieldName: 'MODELNO',
      label: $t('equipment.equipment-mg.modelNo'),
    },
    {
      component: 'Input',
      fieldName: 'EQUIPMANAGER',
      label: $t('equipment.equipment-mg.primaryResponsibleParty'),
    },
    {
      component: 'Input',
      fieldName: 'SERIALNO',
      label: $t('equipment.equipment-mg.serialNumber'),
    },
    {
      component: 'Input',
      fieldName: 'EQPATH',
      label: $t('equipment.equipment-mg.storageLocation'),
    },

    {
      component: 'Input',
      fieldName: 'EQUIPMANAGER_SECOND',
      label: $t('equipment.equipment-mg.secondaryResponsibleParty'),
    },
    {
      component: 'Select',
      fieldName: 'STATUS',
      label: $t('equipment.equipment-mg.equipmentStatus'),

      componentProps: {
        class: 'w-full',
        allowClear: true,
        filterOption: true,
        options: [
          {
            label: 'In Service',
            value: 'In Service',
          },
          {
            label: 'Not In Service',
            value: 'Not In Service',
          },
        ],
        placeholder: '请选择',
        showSearch: true,
      },
    },
    {
      component: 'DatePicker',
      fieldName: 'INSRVDATE',
      label: $t('equipment.equipment-mg.installationDate'),
    },
    {
      component: 'DatePicker',
      fieldName: 'PRODUCTIONDATE',
      label: $t('equipment.equipment-mg.activationDate'),
    },
    {
      component: 'Input',
      fieldName: 'OFFLINESTATUS',
      label: $t('equipment.equipment-mg.offlineStatus'),
      componentProps: {
        class: 'w-full',
        allowClear: true,
        filterOption: true,
        options: [
          {
            label: '完好',
            value: '完好',
          },
          {
            label: '停用',
            value: '停用',
          },
          {
            label: '维修',
            value: '维修',
          },
          {
            label: '报废',
            value: '报废',
          },
        ],
        placeholder: '请选择',
        showSearch: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'RELATIONEQID',
      label: $t('equipment.equipment-mg.associatedEquipment'),
    },
    {
      component: 'Input',
      fieldName: 'CALMECHANISM',
      label: $t('equipment.equipment-mg.calibrationInstitution'),
    },
    {
      component: 'Input',
      fieldName: 'CALPARAM',
      label: $t('equipment.equipment-mg.calibrationParameters'),
    },

    {
      component: 'Input',
      fieldName: 'SOFTNM',
      label: $t('equipment.equipment-mg.softwareName'),
    },
    {
      component: 'Input',
      fieldName: 'SOFTVERSION',
      label: $t('equipment.equipment-mg.softwareVersion'),
    },
    {
      component: 'Input',
      fieldName: 'RANGE',
      label: $t('equipment.equipment-mg.scaleCapacity'),
    },
    {
      component: 'Input',
      fieldName: 'PRECISIONS',
      label: $t('equipment.equipment-mg.scalePrecision'),
    },
    {
      component: 'Input',
      fieldName: 'MEASUREMENTCODE',
      label: $t('equipment.equipment-mg.equipmentCode'),
    },
    {
      component: 'Input',
      fieldName: 'DIVISIONVALUE',
      label: $t('equipment.equipment-mg.verificationSeparation'),
    },
    {
      component: 'Input',
      fieldName: 'ELN_ID',
      label: $t('equipment.equipment-mg.usageRecordELN'),
    },
  ];
}

export function recordColumns(): VxeTableGridOptions<MainFormApi.MainForm>['columns'] {
  return [
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('equipment.main-form.recordId'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },

    {
      align: 'center',
      field: 'MAINTENANCETYPE',
      title: $t('equipment.main-form.eventType'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      formatter: 'formatTranslate',

      sortable: true,
    },
    {
      align: 'center',
      field: 'MAINTENANCEEVENT',
      title: $t('equipment.main-form.event'),

      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'STATUS',
      title: $t('equipment.main-form.status'),
      formatter: 'formatTranslate',

      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'WEIGHTID',
      title: $t('equipment.main-form.weightId'),

      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'OPENEDBY',
      title: $t('equipment.main-form.initiator'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'OPENDATE',
      title: $t('equipment.main-form.initiationTime'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss') : '';
      },
      sortable: true,
    },
    {
      align: 'center',
      field: 'MAINTBY',
      title: $t('equipment.main-form.maintainer'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MAINTDATE',
      title: $t('equipment.main-form.maintenanceTime'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss') : '';
      },
      sortable: true,
    },
    {
      align: 'center',
      field: 'OPENREASON',
      title: $t('equipment.main-form.initiationReason'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'REMARKS',
      title: $t('equipment.main-form.remark'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: {
        name: 'Input',
      },
      sortable: true,
    },
  ];
}
export function checkColumns(): VxeTableGridOptions<MainFormApi.MainForm>['columns'] {
  return [
    {
      align: 'center',
      field: 'TYPE',
      title: $t('equipment.main-form.targetPoint'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'VALUE',
      title: $t('equipment.main-form.weightValue_g'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'LOWVALUE',
      title: $t('equipment.main-form.lowerLimit_g'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'HIGHVALUE',
      title: $t('equipment.main-form.upperLimit_g'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RESULT',
      title: $t('equipment.main-form.conclusion'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'COMMONT',
      title: $t('equipment.main-form.remark'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}
export function weightColumns(): VxeTableGridOptions<MainFormApi.MainForm>['columns'] {
  return [
    {
      align: 'center',
      field: 'FULLNAME',
      title: $t('equipment.main-form.operator'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'OPTIONTIME',
      title: $t('equipment.main-form.operationTime'),
      filterRender: {
        name: 'TableFilterDate',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'VALUE',
      title: $t('equipment.main-form.measuredValue_g'),
      filterRender: {
        name: 'TableFilterNumber',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => `${cellValue} g`,
    },
    {
      align: 'center',
      field: 'COMMONTS',
      title: $t('equipment.main-form.remark'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: false,
    },
  ];
}

export function fileColumns(): VxeTableGridOptions<MainFormApi.MainForm>['columns'] {
  return [
    {
      align: 'center',
      field: 'fileName',
      title: $t('equipment.main-form.fileName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'fileDescription',
      title: $t('equipment.main-form.fileDescription'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: false,
      showOverflow: true, // 允许内容溢出显示省略号
    },
  ];
}
export function checkModalSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      fieldName: 'event',
      label: $t('equipment.main-form.event'),
      rules: 'required',
      componentProps: {
        api: getEventApi,
        labelField: 'Text',
        valueField: 'Value',
        class: 'w-full',
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'weight',
      label: $t('equipment.main-form.weight'),
      componentProps: {
        api: getCBWeightListApi,
        labelField: 'Text',
        valueField: 'Value',
        class: 'w-full',
      },
    },
    {
      component: 'Textarea',
      fieldName: 'reason',
      label: $t('equipment.main-form.reason'),
    },
  ];
}
export function searchConditionColumns(): VxeTableGridOptions<MainFormApi.MainForm>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'event',
      title: $t('equipment.equipment-mg.event'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'Not',
      title: $t('equipment.equipment-mg.not'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: {
        name: 'select',
        options: [
          { label: 'Y', value: 'Y' },
          { label: 'N', value: 'N' },
        ],
      },

      sortable: true,
    },
    {
      align: 'center',
      field: 'operateSymbol',
      title: $t('equipment.equipment-mg.operateSymbol'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: {
        name: 'select',
        options: [
          { value: '=', label: '=' },
          { value: '<', label: '<' },
          { value: '>', label: '>' },
          { value: '<=', label: '<=' },
          { value: '>=', label: '>=' },
          { value: '<>', label: '<>' },
          { value: 'IN', label: 'IN' },
          { value: 'BETWEEN', label: 'BETWEEN' },
          { value: 'IS NULL', label: 'IS NULL' },
          { value: 'STARTS WITH', label: 'STARTS WITH' },
          { value: 'CONTAINS', label: 'CONTAINS' },
          { value: 'ENDS WITH', label: 'ENDS WITH' },
          { value: 'SOUNDEX', label: 'SOUNDEX' },
        ],
      },

      sortable: true,
    },
    {
      align: 'center',
      field: 'value',
      title: $t('equipment.equipment-mg.value'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: { name: 'input' },
      sortable: true,
    },
    {
      align: 'center',
      field: 'logic',
      title: $t('equipment.equipment-mg.logic'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: {
        name: 'select',
        options: [
          { label: 'Amd', value: 'And' },
          { label: 'Or', value: 'Or' },
        ],
      },

      sortable: true,
    },
  ];
}
export function addcheckModalSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'weight',
      label: $t('equipment.main-form.weight'),
      rules: 'required',
    },

    {
      component: 'Textarea',
      fieldName: 'comment',
      label: $t('equipment.main-form.remark'),
    },
  ];
}
