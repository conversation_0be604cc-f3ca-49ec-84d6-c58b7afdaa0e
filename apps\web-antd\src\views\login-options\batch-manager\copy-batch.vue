<script lang="ts" setup>
import type { BatcheManagerApi } from '#/api/login-options/batch-manager';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { copyBatch, getBatchInfo } from '#/api/login-options/batch-manager';
import { $t } from '#/locales';

const emit = defineEmits(['success']);

const sSourceBatchId = ref<number>(0);
const sSampleData = ref([]);

const formData = ref<BatcheManagerApi.Batches>();

const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 提交函数
  handleSubmit: onSubmit,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'vertical',
  schema: [
    {
      component: 'Input',
      fieldName: 'NEWBATCHNO',
      label: $t('login-options.batchManager.newBatchNo'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'BATCHNO',
      label: $t('login-options.batchManager.sourceBatchNo'),
      rules: 'required',
    },
  ],
  // wrapperClass: 'grid-cols-1',
  showDefaultActions: false,
});

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: [
      { field: 'checkbox', type: 'checkbox', width: 80 },
      { title: $t('commons.origrec'), field: 'ORIGREC', visible: false },
      {
        title: $t('login-options.batchManager.ordno'),
        field: 'ORDNO',
        width: 100,
      },
      {
        title: $t('login-options.batchManager.clSampleno'),
        field: 'CLSAMPNO',
      },
    ],
    data: sSampleData.value,
    editConfig: {
      mode: 'cell',
      trigger: 'click',
    },
    pagerConfig: {
      enabled: false,
    },
    sortConfig: {
      multiple: true,
    },
    height: '100%',
    stripe: true, // 斑马纹
    toolbarConfig: {
      custom: true,
      export: true,
      // import: true,
      refresh: true,
      zoom: true,
    },
  },
});

function onRefresh() {
  gridApi.query(); // 主动触发一次查询
}

const [Modal, modalApi] = useVbenModal({
  draggable: true,
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
    // modalApi.close();
  },
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData<BatcheManagerApi.Batches>();
      if (data) {
        sSourceBatchId.value = data.BATCHID;

        const aBatchData = await getBatchInfo(sSourceBatchId.value);
        if (!aBatchData) return;
        data.BATCHNO = aBatchData[0];
        const aSamples = aBatchData[1];

        sSampleData.value = aSamples.map((item: string[]) => ({
          ORDNO: item[0],
          CLSAMPNO: item[1],
          ORIGREC: item[2],
        }));
        gridApi.setGridOptions({
          data: sSampleData.value,
        });
        onRefresh();

        formData.value = data;
        formApi.setValues(formData.value);
      }
    }
  },
  title: '复制批',
});

async function onSubmit() {
  const aOrigrec: number[] = gridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.ORIGREC);

  if (aOrigrec.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  try {
    message.loading({
      content: '正在提交中...',
      duration: 0,
      key: 'is-form-submitting',
    });
    modalApi.lock();

    const data = (await formApi.getValues()) as BatcheManagerApi.Batches;
    const nNewBatchID = await copyBatch(
      sSourceBatchId.value,
      data.NEWBATCHNO,
      aOrigrec,
    );
    if (!nNewBatchID) return;
    if (nNewBatchID === -99) {
      message.warn($t('login-options.batchManager.batchExists'));
      return;
    }

    emit('success');
    modalApi.close();
    message.success({
      content: '复制成功',
      duration: 2,
      key: 'is-form-submitting',
    });
  } catch (error) {
    message.error({
      content: `复制失败：${(error as Error).message}`,
      key: 'is-form-submitting',
    });
    modalApi.close();
  } finally {
    modalApi.unlock();
  }
}
</script>

<template>
  <Modal class="w-[800px]">
    <Form />
    <Grid />
    <template #prepend-footer>
      <div class="flex-auto"></div>
    </template>
  </Modal>
</template>
