<!-- EditRecipeGrids.vue -->
<script lang="ts" setup>
import type { MaterialManagerApi } from '#/api/materials-management/material-manager';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addRecipeDetail,
  getMaterialsList,
  getRecipesMaterialByRecipeCode,
} from '#/api/materials-management/material-manager';
import { createGridOptions } from '#/utils/grid-option';

import { useRecipesMaterialColumns } from './material-manager-data';

// 定义组件事件
const emit = defineEmits(['success']);

const formData = ref<MaterialManagerApi.Recipes>();

// 当前 RecipeMaterialGrid 数据源
const recipeMaterialData = ref<any[]>([]);

// 物料列表
const materialGridOptions = {
  ...createGridOptions<MaterialManagerApi.MaterialManager>(),
  columns: useRecipesMaterialColumns(),
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getMaterialsList();
      },
    },
  },
};

// 第一个表格配置 - 物料选择
const [MaterialGrid, materialGridApi] = useVbenVxeGrid({
  tableTitle: '物料选择',
  gridOptions: materialGridOptions,
});

const recipeGridOptions = {
  ...createGridOptions<MaterialManagerApi.MaterialManager>(),
  columns: useRecipesMaterialColumns(),
  proxyConfig: {
    ajax: {
      query: async () => {
        const recipeCode = formData.value?.RECIPECODE;
        if (typeof recipeCode !== 'number') {
          message.warning('配方编码无效');
          return [];
        }

        const newRecipeData = await getRecipesMaterialByRecipeCode(recipeCode);

        // 先清空，再把接口的数据填充，避免添加的时候被覆盖
        recipeMaterialData.value = [];
        recipeMaterialData.value.push(...newRecipeData.items);

        return newRecipeData;
      },
    },
  },
};

// 第二个表格配置 - 配方的物料
const [RecipeMaterialGrid, recipeMaterialGridApi] = useVbenVxeGrid({
  tableTitle: '所选物料',
  gridOptions: recipeGridOptions,
});

// 新增
async function onCreate() {
  const selectedRows =
    materialGridApi.grid?.getCheckboxRecords() as MaterialManagerApi.MaterialManager[];

  if (selectedRows.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  // 过滤掉已存在于 recipeMaterialData 的 MATCODE
  const existingMatCodes = new Set(
    recipeMaterialData.value.map((item) => item.MATCODE),
  );

  const newRows = selectedRows.filter(
    (row) => !existingMatCodes.has(row.MATCODE),
  );

  if (newRows.length === 0) {
    message.info('所选数据已添加过,请选择不同的数据！');
    return;
  }

  // 添加新数据
  recipeMaterialData.value = [...recipeMaterialData.value, ...newRows];

  // 刷新表格
  recipeMaterialGridApi.setGridOptions({
    data: recipeMaterialData.value,
  });
}

// 删除
async function onDelete() {
  const selectedRows = recipeMaterialGridApi.grid?.getCheckboxRecords();

  if (!selectedRows || selectedRows.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  // 过滤掉被选中的行
  recipeMaterialData.value = recipeMaterialData.value.filter(
    (item) => !selectedRows.some((row) => row.MATCODE === item.MATCODE),
  );

  // 刷新表格
  recipeMaterialGridApi.setGridOptions({
    data: recipeMaterialData.value,
  });
}

// 保存修改
async function saveChanges() {
  try {
    // 1. 获取表格中所有行数据
    const allData = recipeMaterialGridApi.grid?.getData();

    if (!allData || allData.length === 0) {
      message.warning('没有可保存的物料数据');
      return;
    }

    // 2. 提取所有 MATCODE，转换为带单引号的字符串数组
    const matCodes = allData.map((item) => `'${item.MATCODE}'`);

    const recipeCode = formData.value?.RECIPECODE;
    if (typeof recipeCode !== 'number') {
      return;
    }

    await addRecipeDetail(matCodes, recipeCode);
    // 实现保存逻辑
    message.success('保存成功');
    emit('success');
    // 关闭Modal
    modalApi.close();
  } catch (error) {
    console.warn('保存失败:', error);
    message.error('保存失败');
  }
}

// 获取Modal实例
const [Modal, modalApi] = useVbenModal({
  title: '编辑配方详情',
  onCancel() {
    modalApi.close();
  },
  onConfirm: saveChanges,
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData<MaterialManagerApi.Recipes>();
      if (data) {
        formData.value = data;
      }
    }
  },
});
</script>

<template>
  <Modal class="w-[800px]">
    <div
      style="
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 700px;
        overflow: hidden;
      "
    >
      <div style="flex: 1; overflow: hidden">
        <!-- <h3>物料选择</h3> -->
        <MaterialGrid />
      </div>
      <!-- 按钮区域 -->
      <div style="margin: 16px 0; text-align: center">
        <Space>
          <Button type="primary" @click="onCreate">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="onDelete">
            {{ $t('ui.actionTitle.delete') }}
          </Button>
        </Space>
      </div>
      <div style="flex: 1; margin-top: 5px; overflow: hidden">
        <!-- <h3>所选物料</h3> -->
        <RecipeMaterialGrid />
      </div>
    </div>
  </Modal>
</template>
