<script setup lang="ts">
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { CourseFormApi } from '#/api/equipment/course-form';

import { ref, watch } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import {
  Button,
  message,
  Modal,
  Space,
  TabPane,
  Tabs,
  Textarea,
} from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  delCourseApi,
  deleteAttachmentApi,
  deleteMethodsApi,
  getCoursesApi,
  getDetailApi,
  getMethodApi,
} from '#/api/equipment/course-form';
import { $t } from '#/locales';

import CourseModal from './components/course-modal.vue';
import MethodModal from './components/method-modal.vue';
import { courseColumns, methodColumns } from './course-form-data';
import { UploadWitlabFile, DownloadWitlabFile } from '#/api/core/witlab';
import type { UploadChangeParam } from 'ant-design-vue';

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: CourseModal,
  destroyOnClose: true,
});
const [MethodFormModal, methodFormModalApi] = useVbenModal({
  connectedComponent: MethodModal,
  destroyOnClose: true,
});
const clickRow = ref<CourseFormApi.RowType | null>(null);
const clickMethodRow = ref<CourseFormApi.RowType | null>(null);

watch(
  clickRow,
  async (newRow: CourseFormApi.RowType) => {
    if (newRow) {
      const data = await getDetailApi([newRow.COURSECODE]);
      abstract.value = data[0].ABSTRACT;
      methodGridApi.query();
    }
  },
  { deep: true },
);

const gridOptions: VxeTableGridOptions<CourseFormApi.CourseForm> = {
  columns: courseColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        const data = await getCoursesApi();
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
const methodGridOptions: VxeTableGridOptions<CourseFormApi.CourseForm> = {
  columns: methodColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},

  proxyConfig: {
    ajax: {
      query: async () => {
        if (!clickRow.value) {
          return [];
        }
        const data = await getMethodApi([clickRow.value?.COURSECODE]);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: {},
    search: true,
    zoom: true,
  },
};
const gridEvents: VxeGridListeners<CourseFormApi.CourseForm> = {
  currentChange: async ({ row }) => {
    if (row) {
      clickRow.value = row;
    }
  },
};
const methodGridEvents: VxeGridListeners<CourseFormApi.CourseForm> = {
  currentChange: async ({ row }) => {
    if (row) {
      clickMethodRow.value = row;
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents,
  tableTitle: '提供课程',
});

const [MethodGrid, methodGridApi] = useVbenVxeGrid({
  gridOptions: methodGridOptions,
  gridEvents: methodGridEvents,
  tableTitle: '涵盖的方法',
});
const activeKey = ref('方法');
const tabList = [
  {
    title: '方法',
  },
];
const abstract = ref<string>('');
const onRefresh = () => {
  gridApi.query();
};
const onRefreshMethod = () => {
  methodGridApi.query();
};
const addCourse = () => {
  formModalApi.setData(null).open();
};
const deleteCourse = () => {
  Modal.confirm({
    title: '询问',
    content: '此操作将会删除当前课程，继续吗',
    cancelText: '否',
    okText: '是',
    async onOk() {
      const data = clickRow.value;
      if (!data) {
        message.warning('请至少选择一条数据！');
        return;
      }

      const params = [[data.COURSECODE], [data.COURSENAME], [data.STARDOC_ID]];
      const res = await delCourseApi(params);
      if (res[0]) {
        gridApi.query();
      } else {
        if (res[1].length > 0) {
          message.warning(
            `还有学员在参加课程：${data.COURSENAME}的培训，无法删除！`,
          );
        } else {
          message.warning(
            `由于课程：${data.COURSENAME}当前处于激活状态，方法不能删除！`,
          );
        }
      }
      console.warn('删除');
    },
    onCancel() {},
  }); // Delete logic here
  console.warn('Delete course');
};
const printReport = () => {
  // TODO：打印报表
  // Print report logic here
  console.warn('Print report');
};
const editList = () => {
  // Edit list logic here
  methodFormModalApi.setData({ clickRow: clickRow.value }).open();
  console.warn('Edit list');
};
const removeMethod = () => {
  const data = clickRow.value;
  // Remove method logic here
  if (!data || !clickMethodRow.value) {
    message.warning('请至少选择一条数据！');
    return;
  }
  Modal.confirm({
    title: '您确定吗？',
    content: '此操作将会删除选中的方法，继续吗？',
    cancelText: '否',
    okText: '是',
    async onOk() {
      const params = [data.COURSECODE, [clickMethodRow.value?.ORIGREC]];

      const res = await deleteMethodsApi(params);
      if (res) {
        methodGridApi.query();
      } else {
        message.warning(
          `由于课程：${data.COURSENAME}当前处于激活状态，方法不能删除！`,
        );
      }
      console.warn('删除');
    },
    onCancel() {},
  }); // Delete logic here
  console.warn('Remove method');
};

const deleteDocuments = () => {
  const data = clickRow.value;

  Modal.confirm({
    title: '询问',
    content: '是否确定要删除选定的记录?',
    cancelText: '否',
    okText: '是',
    onOk() {
      try {
        // TODO:电子签名
        const params = [
          'OFFERED_COURSES',
          data.ORIGREC,
          data.STARDOC_ID,
          null,
          null,
          null,
          [],
          null,
          null,
          null,
          null,
        ];
        deleteAttachmentApi(params);
      } catch {
        console.warn('删除失败');
      }
      console.warn('删除');
      // TODO：电子签名
    },
    onCancel() {},
  });
  console.warn('Delete documents');
};
const viewDocuments = async () => {
  if (clickRow.value&&!clickRow.value.ORIGREC) {
    message.warning('请选择文件');
    return;
  }
  await DownloadWitlabFile(
    clickRow.value.STARDOC_ID,
    clickRow.value.FILENAME,
  );
};
const fileList = ref([]);
const headers = {
  authorization: 'authorization-text',
};
const uploadDocuments = async (info: UploadChangeParam) => {
  if (info.file && info.file.originFileObj) {
    await UploadWitlabFile(info.file.originFileObj);
  }
};
</script>
<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <MethodFormModal @success="onRefreshMethod" />
    <div class="flex h-full flex-row">
      <Grid height="100%" class="h-full w-1/3">
        <template #toolbar-actions>
          <Space :size="[8, 0]" wrap>
            <Button type="primary" @click="addCourse">
              {{ $t('equipment.add') }}
            </Button>
            <Button type="primary" @click="deleteCourse">
              {{ $t('equipment.delete') }}
            </Button>
            <Button type="primary" @click="printReport">
              {{ $t('equipment.course-form.printReport') }}
            </Button>
          </Space>
        </template>
      </Grid>
      <div class="flex flex-1 flex-col ">
        <div class="h-1/6 p-5">
          <div class="text-base">{{ $t('equipment.course-form.summary') }}</div>
          <Textarea
            v-model:value="abstract"
            :auto-size="{ minRows: 5, maxRows: 6 }"
          />
        </div>
        <Space :size="[8, 0]" wrap class="h-16">
                  <Upload
          v-model:file-list="fileList"
          name="file"
          :showUploadList="false"
          :headers="headers"
          :max-count="1"
          @change="uploadDocuments"
        >
          <Button type="primary">
            {{ $t('equipment.course-form.additionalDocuments') }}
          </Button>
        </Upload>
          <Button type="primary" @click="viewDocuments">
            {{ $t('equipment.course-form.viewDocuments') }}
          </Button>
          <Button type="primary" @click="deleteDocuments">
            {{ $t('equipment.course-form.deleteDocuments') }}
          </Button>
        </Space>
        <Tabs v-model:active-key="activeKey" class="h-16 ">
          <TabPane
            v-for="item in tabList"
            :key="item.title"
            :tab="item.title"
          />
        </Tabs>
        <div class="flex-1 overflow-scroll">
          <MethodGrid height="auto">
            <template #toolbar-actions>
              <Space :size="[8, 4]" wrap>
                <Button type="primary" @click="editList">
                  {{ $t('equipment.course-form.editList') }}
                </Button>
                <Button type="primary" @click="removeMethod">
                  {{ $t('equipment.course-form.removeMethod') }}
                </Button>
              </Space>
            </template>
          </MethodGrid>
        </div>
      </div>
    </div>
  </Page>
</template>
