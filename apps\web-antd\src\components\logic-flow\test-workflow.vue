<template>
  <div class="test-workflow-container">
    <h2>工作流组件测试</h2>
    <p>测试图标库修复效果</p>
    
    <!-- 测试 WorkflowDesigner 组件 -->
    <div class="test-section">
      <h3>WorkflowDesigner 组件测试</h3>
      <div style="height: 600px; border: 1px solid #e8e8e8;">
        <WorkflowDesigner
          v-model:flow-data="flowData"
          :mode="mode"
          :approver-data-source="approverDataSource"
          @node-select="handleNodeSelect"
          @validate="handleValidate"
        />
      </div>
    </div>

    <!-- 测试结果显示 -->
    <div class="test-results">
      <h3>测试结果</h3>
      <a-alert
        :message="testStatus.message"
        :type="testStatus.type"
        show-icon
        style="margin-bottom: 16px"
      />
      
      <div v-if="selectedNode">
        <h4>选中的节点：</h4>
        <pre>{{ JSON.stringify(selectedNode, null, 2) }}</pre>
      </div>
      
      <div v-if="validationResult">
        <h4>验证结果：</h4>
        <pre>{{ JSON.stringify(validationResult, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import WorkflowDesigner from './WorkflowDesigner.vue';

// 测试数据
const mode = ref<'edit' | 'view'>('edit');
const selectedNode = ref(null);
const validationResult = ref(null);

const flowData = ref({
  nodes: [
    {
      id: 'start-1',
      type: 'start',
      x: 100,
      y: 100,
      properties: {
        name: '开始节点'
      }
    },
    {
      id: 'approval-1',
      type: 'approval',
      x: 300,
      y: 100,
      properties: {
        name: '审批节点',
        approver: 'admin'
      }
    },
    {
      id: 'end-1',
      type: 'end',
      x: 500,
      y: 100,
      properties: {
        name: '结束节点'
      }
    }
  ],
  edges: [
    {
      id: 'edge-1',
      type: 'polyline',
      sourceNodeId: 'start-1',
      targetNodeId: 'approval-1'
    },
    {
      id: 'edge-2',
      type: 'polyline',
      sourceNodeId: 'approval-1',
      targetNodeId: 'end-1'
    }
  ]
});

const approverDataSource = ref([
  { value: 'admin', label: '管理员' },
  { value: 'user1', label: '用户1' },
  { value: 'user2', label: '用户2' }
]);

// 测试状态
const testStatus = reactive({
  type: 'info' as 'success' | 'info' | 'warning' | 'error',
  message: '组件加载中，请检查图标是否正常显示...'
});

// 事件处理
const handleNodeSelect = (node: any) => {
  selectedNode.value = node;
  if (node) {
    testStatus.type = 'success';
    testStatus.message = '节点选择功能正常，图标显示正确！';
    message.success('节点选择成功');
  }
};

const handleValidate = (result: any) => {
  validationResult.value = result;
  testStatus.type = result.valid ? 'success' : 'warning';
  testStatus.message = result.valid ? '验证通过，所有功能正常！' : '发现验证问题，但图标显示正常';
};

// 组件挂载后检查
import { onMounted } from 'vue';

onMounted(() => {
  setTimeout(() => {
    testStatus.type = 'success';
    testStatus.message = '组件加载完成，请测试各个按钮的图标是否正常显示';
  }, 1000);
});
</script>

<style scoped>
.test-workflow-container {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
}

.test-results {
  margin-top: 20px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 4px;
}

h2, h3, h4 {
  color: #1890ff;
  margin-bottom: 16px;
}

pre {
  background: #f0f0f0;
  padding: 12px;
  border-radius: 4px;
  overflow: auto;
  max-height: 200px;
}
</style>
