<script lang="ts" setup>
import type { BatcheManagerApi } from '#/api/login-options/batch-manager';

import { ref } from 'vue';

import { Page } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addLotPlanSamples,
  cbMfgEquip,
  getProductStepArray,
  getRequestReason,
  getSampGroupsByProdGrpAndPlant,
  getTestPlanGroups,
} from '#/api/login-options/batch-manager';
import { $t } from '#/locales';

const emit = defineEmits(['success']);
const sSampleGroupCode = ref<string>('');
const requestId = ref<string>('');
const sTestPlanGroup = ref<string>('');

const spCodeData = ref([]);
async function onLotPlanSubmit() {
  try {
    // const data = values as BatcheManagerApi.Batches;
    const data =
      (await lotPlanSampleFormApi.getValues()) as BatcheManagerApi.Batches;
    data.SAMPLEGROUPCODE = sSampleGroupCode.value;
    data.REQUESTID = requestId.value;
    data.IFLAG = 'N';
    const dataList: string[] = [];
    if (gridApi.grid.getData().length === 0) {
      message.warn($t('login-options.batchManager.noSpCodeData'));
      return;
    }
    gridApi.grid.getData().forEach((item) => {
      dataList.push(
        item.Col1,
        item.Col2,
        item.Col3,
        item.Col4,
        item.Col5,
        item.Col6,
        item.Col7,
        item.Col8,
        item.Col9,
        item.Col10,
        item.Col11,
      );
    });
    const spCodeList = [];
    spCodeList.push(dataList);
    data.SPCODELIST = spCodeList;
    const arrResult = await addLotPlanSamples(data);
    if (!arrResult) return;
    const batchId = arrResult[2];
    switch (batchId) {
      case -801: {
        message.warn($t('login-options.batchManager.MATCODE_NULL'));
        return;
      }
      case -99: {
        message.warn($t('login-options.batchManager.batchExists'));
        return;
      }
      case -80: {
        message.warn($t('login-options.batchManager.NO_TEST_METHODS'));
        return;
      }
      case -1: {
        message.warn($t('login-options.batchManager.SP_CODE_NULL'));
        return;
      }
    }

    emit('success');
  } catch (error) {
    console.error('产品/中间产品表单提交失败:', error);
    throw error; // 重新抛出错误，让调用方处理
  }
}

const [LotPlanSamplesForm, lotPlanSampleFormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  handleSubmit: onLotPlanSubmit,
  layout: 'horizontal',
  resetButtonOptions: {
    show: false,
  },
  schema: [
    {
      component: 'ApiSelect',
      fieldName: 'PRODGROUP',
      label: $t('login-options.batchManager.prodGroup'),
      rules: 'required',
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: getTestPlanGroups.bind(null, '', 'LOTTEMPLATE'),
        labelField: 'PRODGROUP',
        valueField: 'PRODGROUP',
        immediate: true,
        autoSelect: true,
        onChange: async (value: string) => {
          sTestPlanGroup.value = value;
        },
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'PLANT',
      label: $t('login-options.batchManager.plant'),
      rules: 'required',
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: async () => {
          const res = await cbMfgEquip('LOTTEMPLATE');
          return res.items.map((item: { Text: string; Value: string }) => ({
            label: item.Text,
            value: item.Value,
          }));
        },
        immediate: true,
        onChange: async (value: string) => {
          // message.success(`当前页面${value}`);
          const res = await getSampGroupsByProdGrpAndPlant(
            sTestPlanGroup.value,
            'LOTTEMPLATE',
            value,
            '',
          );
          const options = res.items.map(
            (item: { Text: string; Value: string }) => ({
              label: item.Text,
              value: item.Value,
            }),
          );
          lotPlanSampleFormApi.updateSchema([
            {
              fieldName: 'SAMPLEGROUPNAME', // 精准更新字段
              componentProps: {
                options,
              },
            },
          ]);
        },
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'SAMPLEGROUPNAME',
      label: $t('login-options.batchManager.sampleGroupName'),
      rules: 'required',
      componentProps: {
        onChange: async (value: string) => {
          // message.success(`当前页面${value}`);
          const aParams = value.split('>');
          if (aParams) {
            sSampleGroupCode.value = aParams[0] as string;
            const data = await getProductStepArray(sSampleGroupCode.value);
            // console.log(data);
            spCodeData.value = data.map((item: string[]) => ({
              Col1: item[0],
              Col2: item[1],
              Col3: item[2],
              Col4: item[3],
              Col5: item[4],
              Col6: item[5],
              Col7: item[6],
              Col8: item[7],
              Col9: item[8],
              Col10: item[9],
              Col11: item[10],
            }));
            // console.log(materialData.value);
            gridApi.setGridOptions({
              data: spCodeData.value,
            });
            onRefresh();
          }
        },
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'PROCESS',
      label: $t('login-options.batchManager.process'),
    },
    {
      component: 'Input',
      fieldName: 'RETEST_BATCHID',
      label: $t('login-options.batchManager.retestBatchId'),
    },
    {
      component: 'Input',
      fieldName: 'RETESTORDNO',
      label: $t('login-options.batchManager.retestOrderNo'),
    },
    {
      component: 'ApiSelect',
      fieldName: 'REQUEST_REASON',
      label: $t('login-options.batchManager.requestReason'),
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: async () => {
          const res = await getRequestReason();
          return res.items.map((item: { TEXT: string; VALUE: string }) => ({
            label: item.TEXT,
            value: item.VALUE,
          }));
        },
        immediate: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'BATCHNO',
      label: $t('login-options.batchManager.batchno'),
      rules: 'required',
    },
    {
      component: 'DatePicker',
      fieldName: 'PRODUCTION_DATE',
      label: $t('login-options.batchManager.productionDate'),
    },
    {
      component: 'Input',
      fieldName: 'ORDERNO',
      label: $t('login-options.batchManager.orderNo'),
    },
    {
      component: 'Input',
      fieldName: 'ESTIMATEDVOL',
      label: $t('login-options.batchManager.estimatedVol'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'ACTUALVOL',
      label: $t('login-options.batchManager.actualVol'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'ESTIMATEDVOL_UNITS',
      label: $t('login-options.batchManager.estimatedVolUnits'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'PACKING_SPEC',
      label: $t('login-options.batchManager.packingSpec'),
    },
    {
      component: 'Input',
      fieldName: 'STORAGE_CONDITION',
      label: $t('login-options.batchManager.storageCondition'),
    },
    {
      component: 'Input',
      fieldName: 'REQUEST_DEPT',
      label: $t('login-options.batchManager.requestDept'),
    },
    {
      component: 'DatePicker',
      fieldName: 'REQUEST_DATE',
      label: $t('login-options.batchManager.requestDate'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'REQUESTER',
      label: $t('login-options.batchManager.requester'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'COMMENTS',
      label: $t('login-options.batchManager.comments'),
    },
  ],
  // wrapperClass: 'grid-cols-1 md:grid-cols-1 lg:grid-cols-1',
  wrapperClass: 'grid-cols-1 md:grid-cols-3 gap-x-8',
  showDefaultActions: false,
});

const [LotPlanGrid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: [
      { title: $t('login-options.batchManager.col1'), field: 'Col1' },
      { title: $t('login-options.batchManager.col2'), field: 'Col2' },
      {
        title: $t('login-options.batchManager.col3'),
        field: 'Col3',
        type: 'checkbox',
        editRender: {
          name: 'select',
          options: [
            { value: 'Y', label: 'Y' },
            { value: 'N', label: 'N' },
          ],
        },
      },
      {
        title: $t('login-options.batchManager.col4'),
        field: 'Col4',
        visible: false,
      },
      { title: $t('login-options.batchManager.col11'), field: 'Col11' },
      { title: $t('login-options.batchManager.col5'), field: 'Col5' },
      {
        title: $t('login-options.batchManager.col6'),
        field: 'Col6',
        visible: false,
      },
      {
        title: $t('login-options.batchManager.col7'),
        field: 'Col7',
        visible: false,
      },
      {
        title: $t('login-options.batchManager.col8'),
        field: 'Col8',
        visible: false,
      },
      {
        title: $t('login-options.batchManager.col10'),
        field: 'Col10',
        visible: false,
      },
      { title: $t('login-options.batchManager.col9'), field: 'Col9' },
    ],
    data: spCodeData.value,
    editConfig: {
      mode: 'cell',
      trigger: 'click',
    },
    pagerConfig: {
      enabled: false,
    },
    sortConfig: {
      multiple: true,
    },
    height: '100%',
    stripe: true, // 斑马纹
    toolbarConfig: {
      custom: true,
      export: true,
      // import: true,
      refresh: true,
      zoom: true,
    },
  },
});

function onRefresh() {
  gridApi.query(); // 主动触发一次查询
}

// 暴露表单API供父组件调用
defineExpose({
  lotPlanSampleFormApi,
  onLotPlanSubmit,
});
</script>

<template>
  <!--  <Modal title="产品/中间产品登录" class="h-[1000px] w-[1200px]"> -->
  <Page>
    <LotPlanSamplesForm />
    <LotPlanGrid />
  </Page>
  <!--  </Modal> -->
</template>
