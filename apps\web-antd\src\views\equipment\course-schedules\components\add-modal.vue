<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import {
  addTrainingApi,
  scCourseExistsApi,
} from '#/api/equipment/course-schedules';
import { addSchema } from '../course-schedules-data';
import { Modal as AModal } from 'ant-design-vue';

const emit = defineEmits(['success']);
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: addSchema(),
  showDefaultActions: false,
});
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    modalApi.lock();

    try {
      const params = [
        data.startTime.toISOString(),
        data.endTime.toISOString(),
        data.course,
      ];

      const res = await scCourseExistsApi(params);
      if (res) {
        addCourse(data);
      } else {
        AModal.confirm({
          title: '警告',
          content: '在同一时间里存在另一条目，你是否确认添加?',
          cancelText: '否',
          okText: '是',
          async onOk() {
            addCourse(data);
          },
        });
      }
    } finally {
      modalApi.lock(false);
    }
  },

});
const addCourse = async (data: any) => {
  const addParams = [
    data.startTime.toISOString(),
    data.endTime.toISOString(),
    data.course,
    data.location,
    data.location,
    data.remarks,
  ];
  await addTrainingApi(addParams);
  emit('success');
  modalApi.close();
};
</script>
<template>
  <Modal title="搜索课程计划">
    <Form class="mx-4">
    </Form>
  </Modal>
</template>
