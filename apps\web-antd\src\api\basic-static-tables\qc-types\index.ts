import { callServer, getDataSet } from '#/api/core/witlab';
import { requestClient } from '#/api/request';

export namespace QcTypeApi {
  export interface PageFetchParams {
    [key: string]: any;
    page: number;
    pageSize: number;
  }

  export interface QcType {
    ORIGREC: number;
    QCTYPE: string;
    QCGROUP: string;
    UPDPARENT: string;
    AUTOSELECT: string;
    INSTRUMENTQC: boolean;
    COLOR: string;
    EMPW_FUNCTION: string;
  }
}

/**
 * 获取QC类型列表数据
 */
async function getQcTypeList() {
  return getDataSet('QC_TYPES.GET_QCTYPES', []);
}

/**
 * 获取QC组
 */
async function getQcGroup() {
  return getDataSet('QC_TYPES.CB_QC_GROUP', []);
}

/**
 * 添加QC类型
 * @param data QC类型数据
 */
async function addQcType(data: QcTypeApi.QcType) {
  return await callServer('QC_TYPES.ADD_QC_TYPE', [
    data.QCTYPE,
    data.QCGROUP,
    data.UPDPARENT,
    data.AUTOSELECT,
    data.INSTRUMENTQC,
    data.COLOR,
    'AddQCType',
  ]);
}

/**
 * 更新QC类型
 *
 * @param data QC类型数据
 * @returns boolean
 */
async function updateQcType(data: QcTypeApi.QcType) {
  return requestClient.post<boolean>('/qc-types/updateQcType', data);
}

/**
 * 删除QC类型
 *
 * @param origrec QC类型数据
 * @returns boolean
 */
async function deleteQcType(qcType: string[]) {
  return await callServer('QC_TYPES.DELETE_QC_TYPE', [qcType]);
}

export { addQcType, deleteQcType, getQcGroup, getQcTypeList, updateQcType };
