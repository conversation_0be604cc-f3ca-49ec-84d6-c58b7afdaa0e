// import { requestClient } from '#/api/request';
import { callServer, getDataSet } from '#/api/core/witlab';

export namespace RegionTableApi {
  export interface PageFetchParams {
    [key: string]: any;
    page: number;
    pageSize: number;
  }

  export interface Region {
    [key: string]: any;
    ORIGREC: number;
    REGIONCODE: string;
    REGIONNAME: string;
  }
}

/**
 * 获取区域表格数据/api/v1/lims/Regions/Regions
 */
async function getRegionTableApi() {
  return getDataSet('Regions.GET_REGIONS', []);
}
/**
 * 添加区域
 * @param data - 区域数据
 * @returns boolean
 * @description 添加区域
 */
async function addRegionApi(data: Omit<RegionTableApi.Region, 'ORIGREC'>) {
  return callServer('Regions.ADD_REGION', [data.REGIONCODE, data.REGIONNAME]);
}

/**
 * 删除区域
 * @param regionCode - 区域编码
 * @returns boolean
 * @description 删除区域
 */
async function deleteRegionApi(regionCode: string) {
  return callServer('Regions.DELETE_REGION', [regionCode]);
}

export { addRegionApi, deleteRegionApi, getRegionTableApi };
