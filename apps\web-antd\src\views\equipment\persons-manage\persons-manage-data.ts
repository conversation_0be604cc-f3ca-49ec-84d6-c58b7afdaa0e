import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { FormApi } from '#/api/equipment/persons-manage';
import dayjs from 'dayjs';

import { $t } from '#/locales';
import { getCourseNameApi, getScoreApi } from '#/api/equipment/persons-manage';
export function personColumns(): VxeTableGridOptions<FormApi.Form>['columns'] {
  return [
    {
      align: 'center',
      field: 'USRNAM',
      title: $t('equipment.persons-manage.username'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'FULLNAME',
      title: $t('equipment.persons-manage.name'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SERVGRP',
      title: $t('equipment.persons-manage.position'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}
export function baseInfoVbenSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'USRNAM',
      label: $t('equipment.persons-manage.username'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'FULLNAME',
      label: $t('equipment.persons-manage.name'),
    },

    {
      component: 'Select',
      fieldName: 'GENDER',
      label: $t('equipment.persons-manage.gender'),
      componentProps: {
        class: 'w-full',
        allowClear: true,
        filterOption: true,
        options: [
          {
            label: '男',
            value: '男',
          },
          {
            label: '女',
            value: '女',
          },
        ],
        placeholder: '请选择',
        showSearch: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'SERVGRP',
      label: $t('equipment.persons-manage.position'),
      componentProps: {
        class: 'w-full',
        allowClear: true,
        filterOption: true,
        options: [
          {
            label: '男',
            value: '男',
          },
          {
            label: '女',
            value: '女',
          },
        ],
        placeholder: '请选择',
        showSearch: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'JOBDESCRIPTION',
      label: $t('equipment.persons-manage.jobTitle'),
      componentProps: {
        class: 'w-full',
        allowClear: true,
        filterOption: true,
        options: [
          {
            label: '男',
            value: '男',
          },
          {
            label: '女',
            value: '女',
          },
        ],
        placeholder: '请选择',
        showSearch: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'ADLEVEL',
      label: $t('equipment.persons-manage.administrativeLevel'),
      componentProps: {
        class: 'w-full',
        allowClear: true,
        filterOption: true,
        options: [
          {
            label: '男',
            value: '男',
          },
          {
            label: '女',
            value: '女',
          },
        ],
        placeholder: '请选择',
        showSearch: true,
      },
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'USERPHONE',
      label: $t('equipment.persons-manage.contact'),
    },
    {
      component: 'DatePicker',
      componentProps: {
        type: 'date',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: 'ENTRYDATE',
      label: $t('equipment.persons-manage.birthDate'),
    },
    {
      component: 'Select',
      fieldName: 'WORKSTATUS',
      label: $t('equipment.persons-manage.status'),
      componentProps: {
        class: 'w-full',
        allowClear: true,
        filterOption: true,
        options: [
          {
            label: '男',
            value: '男',
          },
          {
            label: '女',
            value: '女',
          },
        ],
        placeholder: '请选择',
        showSearch: true,
      },
    },
    {
      component: 'Select',
      componentProps: {
        class: 'w-full',
        allowClear: true,
        filterOption: true,
        options: [
          {
            label: '男',
            value: '男',
          },
          {
            label: '女',
            value: '女',
          },
        ],
        placeholder: '请选择',
        showSearch: true,
      },
      fieldName: 'EDUCATION',
      label: $t('equipment.persons-manage.highestDegree'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'GRADSCHOOL',
      label: $t('equipment.persons-manage.graduationSchool'),
    },
    {
      component: 'DatePicker',
      fieldName: 'GRADUATIONDATE',
      label: $t('equipment.persons-manage.graduationDate'),
      componentProps: {
        type: 'date',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'MAJOR',
      label: $t('equipment.persons-manage.major'),
    },
    {
      component: 'DatePicker',
      fieldName: 'BIRTHDATE',
      label: $t('equipment.persons-manage.birthDate'),
      componentProps: {
        type: 'date',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
    { fieldName: 'BIRTHDATE', component: '' },

    {
      component: 'Textarea',
      fieldName: 'SKILL',
      label: $t('equipment.persons-manage.skills'),
      componentProps: {
        class: 'w-full',
      },
    },
    { fieldName: 'BIRTHDATE', component: '' },
    { fieldName: 'BIRTHDATE', component: '' },
    {
      component: 'Textarea',
      fieldName: 'COMMENTS',
      label: $t('equipment.persons-manage.remarks'),
      componentProps: {
        class: 'w-full',
      },
    },
  ];
}
export function methodColumns(): VxeTableGridOptions<FormApi.Form>['columns'] {
  return [
    {
      align: 'center',
      field: 'TESTNO',
      title: $t('equipment.persons-manage.testName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'METHOD',
      title: $t('equipment.persons-manage.method'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'METHODVERSION',
      title: $t('equipment.persons-manage.methodVersion'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'CERTDATE',
      title: $t('equipment.persons-manage.authorizationDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      formatter: 'formatDate',
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'EXPDATE',
      title: $t('equipment.persons-manage.expirationDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      formatter: 'formatDate',

      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'CERTCOMMENT',
      title: $t('equipment.persons-manage.remarks'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}
export function fileColumns(): VxeTableGridOptions<FormApi.Form>['columns'] {
  return [
    {
      align: 'center',
      field: 'FILENAME',
      title: $t('equipment.persons-manage.fileName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'DESCRIPTION',
      title: $t('equipment.persons-manage.description'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}
export function deviceColumns(): VxeTableGridOptions<FormApi.Form>['columns'] {
  return [
    {
      align: 'center',
      field: 'EQID',
      title: $t('equipment.persons-manage.equipmentId'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'EQNAME',
      title: $t('equipment.persons-manage.equipmentName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'CERTDATE',
      title: $t('equipment.persons-manage.authorizationDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'EXPDATE',
      title: $t('equipment.persons-manage.expirationDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      formatter: 'formatDate',
      filters: [{ data: '' }],
      sortable: true,
    },

    {
      align: 'center',
      field: 'CERTCOMMENT',
      title: $t('equipment.persons-manage.remarks'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}
export function trainColumns(): VxeTableGridOptions<FormApi.Form>['columns'] {
  return [
    {
      align: 'center',
      field: 'COURSEID',
      title: $t('equipment.persons-manage.courseId'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'COURSENAME',
      title: $t('equipment.persons-manage.courseName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'STARTDATE',
      title: $t('equipment.persons-manage.startTime'),
      filterRender: {
        name: 'TableFilterInput',
      },
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss') : '';
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ENDDATE',
      title: $t('equipment.persons-manage.endTime'),
      filterRender: {
        name: 'TableFilterInput',
      },
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss') : '';
      },
      filters: [{ data: '' }],
      sortable: true,
    },

    {
      align: 'center',
      field: 'COURSELOCATION',
      title: $t('equipment.persons-manage.trainingLocation'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },

    {
      align: 'center',
      field: 'TRAINER',
      title: $t('equipment.persons-manage.instructor'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'EVALUATIONMODE',
      title: $t('equipment.persons-manage.assessmentMethod'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'TRAINWAY',
      title: $t('equipment.persons-manage.trainingMethod'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'STUDENTSCORE',
      title: $t('equipment.persons-manage.score'),
      filterRender: {
        name: 'TableFilterInput',
      },
      formatter: 'formatDate',
      filters: [{ data: '' }],
      sortable: true,
    },

    {
      align: 'center',
      field: 'COMMENTS',
      title: $t('equipment.persons-manage.remarks'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}
export function courseColumns(): VxeTableGridOptions<FormApi.Form>['columns'] {
  return [
    {
      align: 'center',
      field: 'TESTNO',
      title: $t('equipment.persons-manage.testName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'METHOD',
      title: $t('equipment.persons-manage.method'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SERVGRP',
      title: $t('equipment.persons-manage.position'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}
export function boardColumns(): VxeTableGridOptions<FormApi.Form>['columns'] {
  return [
    {
      align: 'center',
      field: 'CERTIFICATENAME',
      title: $t('equipment.persons-manage.certificateName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'GETTIME',
      title: $t('equipment.persons-manage.acquisitionDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: 'formatDate',
    },
    {
      align: 'center',
      field: 'EXPDATE',
      title: $t('equipment.persons-manage.expirationDates'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: 'formatDate',
    },
    {
      align: 'center',
      field: 'COMMENTS',
      title: $t('equipment.persons-manage.remarks'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: { name: 'input' },
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('business-static-tables.operation'),
      minWidth: 140,
    },
  ];
}

export function extendModalSchema(): VbenFormSchema[] {
  return [
    {
      component: 'DatePicker',
      fieldName: 'authorizationDate',
      label: $t('equipment.persons-manage.authorizationDate'),
      componentProps: {
        class: 'w-full',
        type: 'date',
      },
      rules: 'required',
    },
    {
      component: 'DatePicker',
      fieldName: 'lostDate',
      label: $t('equipment.persons-manage.lostDate'),
      componentProps: {
        class: 'w-full',
        type: 'date',
      },
      rules: 'required',
    },
  ];
}
export function authorizeModalSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Select',
      fieldName: 'position',
      label: $t('equipment.persons-manage.position'),
    },
    {
      component: 'Select',
      fieldName: 'testCategory',
      label: $t('equipment.persons-manage.testCategory'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'inspectionItem',
      label: $t('equipment.persons-manage.inspectionItem'),
    },
    {
      component: 'DatePicker',
      fieldName: 'authorizationDate',
      label: $t('equipment.persons-manage.authorizationDate'),
      componentProps: {
        class: 'w-full',
        type: 'date',
        format: 'YYYY-MM-DD',
      },
      rules: 'required',
    },
  ];
}
export function oneClickAuthorizeModalSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Select',
      fieldName: 'position',
      label: $t('equipment.persons-manage.position'),
    },
    {
      component: 'Select',
      fieldName: 'laboratory',
      label: $t('equipment.persons-manage.laboratory'),
    },
  ];
}
export function deviceModalSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Select',
      fieldName: 'equipmentType',
      label: $t('equipment.persons-manage.equipmentType'),
    },
    {
      component: 'DatePicker',
      fieldName: 'authorizationDate',
      label: $t('equipment.persons-manage.authorizationDate'),
      componentProps: {
        class: 'w-full',
        type: 'date',
        format: 'YYYY-MM-DD',
      },
      rules: 'required',
    },
  ];
}

export function trainModalSchema(): VbenFormSchema[] {
  return [
    {
      component: 'DatePicker',
      fieldName: 'startTime',
      label: $t('equipment.persons-manage.startTime'),
      componentProps: {
        type: 'date',
        format: 'YYYY-MM-DD HH:mm:ss',
        showTime: {
          defaultValue: dayjs('00:00:00', 'HH:mm:ss'),
        },
        // valueFormat: 'YYYY-MM-DD',
      },
      rules: 'required',
    },
    {
      component: 'DatePicker',
      fieldName: 'endTime',
      label: $t('equipment.persons-manage.endTime'),
      componentProps: {
        type: 'date',
        format: 'YYYY-MM-DD HH:mm:ss',
        showTime: {
          defaultValue: dayjs('00:00:00', 'HH:mm:ss'),
        },
        // valueFormat: 'YYYY-MM-DD',
      },
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      fieldName: 'courseName',
      label: $t('equipment.persons-manage.courseName'),
      rules: 'required',
      componentProps: {
        api: getCourseNameApi,
        labelField: 'COURSENAME',
        valueField: 'COURSECODE',
        class: 'w-full',
      },
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'trainingLocation',
      label: $t('equipment.persons-manage.trainingLocation'),
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'instructor',
      label: $t('equipment.persons-manage.instructor'),
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'trainingMethod',
      label: $t('equipment.persons-manage.trainingMethod'),
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'assessmentMethod',
      label: $t('equipment.persons-manage.assessmentMethod'),
      rules: 'required',
    },

    {
      component: 'ApiSelect',
      fieldName: 'score',
      label: $t('equipment.persons-manage.score'),
      rules: 'required',
      componentProps: {
        api: getScoreApi,
        labelField: 'TEXT',
        valueField: 'VALUE',
        class: 'w-full',
      },
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'courseId',
      label: $t('equipment.persons-manage.courseId'),
      rules: 'required',
    },
    {
      component: 'Textarea',
      componentProps: {},
      fieldName: 'remark',
      label: $t('equipment.persons-manage.remark'),
    },
  ];
}
export function certificateModalSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'certificateName',
      label: $t('equipment.persons-manage.certificateName'),
    },
    {
      component: 'DatePicker',
      fieldName: 'acquisitionTime',
      label: $t('equipment.persons-manage.acquisitionTime'),
      componentProps: {
        class: 'w-full',
        type: 'date',
        format: 'YYYY-MM-DD',
      },
      rules: 'required',
    },
    {
      component: 'DatePicker',
      fieldName: 'expirationTime',
      label: $t('equipment.persons-manage.expirationTime'),
      componentProps: {
        class: 'w-full',
        type: 'date',
        format: 'YYYY-MM-DD',
      },
      rules: 'required',
    },
  ];
}
