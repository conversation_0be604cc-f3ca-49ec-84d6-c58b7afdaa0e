import { callServer, getDataSet, getDataSetNoPage } from '#/api/core/witlab';

export namespace CourseFormApi {
  export interface CourseForm {
    [key: string]: any;
    ORIGREC: number;
    NAME: string;
    EFFECT: string;
    TYPE: string;
    SORTER: number;
  }
  export interface RowType {
    [key: string]: any;
  }
}
async function delPointValueApi(params: Array<string>) {
  return await callServer('BalanceDailyCalibration.DelPointValue', params);
}
async function getCBWeightListApi() {
  return getDataSetNoPage('BalanceDailyCalibration.CB_WeightList', []);
}
async function getCoursesApi() {
  return getDataSet('COURSES.DS_OFFERED_COURSES', []);
}
async function getDetailApi(params: Array<string>) {
  return getDataSetNoPage('COURSES.DS_DETAILS', params);
}
async function getMethodApi(params: Array<string>) {
  return getDataSet('COURSES.DS_METHODS_COVERED', params);
}
async function addCourseApi(params: Array<string>) {
  return await callServer('COURSES.ADD_COURSE', params);
}
async function delCourseApi(params: Array<any>) {
  return await callServer('COURSES.DEL_COURSE', params);
}
async function deleteAttachmentApi(params: Array<any>) {
  return await callServer('Enterprise_Utilities.DeleteAttachment', params);
}
async function getTestChoiceApi() {
  return getDataSetNoPage('COURSES.DS_TESTCHOICE', []);
}
async function getTestListApi(params: Array<any>) {
  return getDataSetNoPage('COURSES.TestListBox_DS', params);
}
async function getMethodChkListBoxApi(params: Array<any>) {
  return getDataSetNoPage('COURSES.MethodChkListBox_DS', params);
}
async function getMethodListBoxApi(params: Array<any>) {
  return getDataSetNoPage('COURSES.MethodListBox_DS', params);
}
async function updateMethodApi(params: Array<any>) {
  return await callServer('COURSES.UPDATE_METHODS', params);
}
async function deleteMethodsApi(params: Array<any>) {
  return await callServer('COURSES.DeleteMethods', params);
}

export {
  addCourseApi,
  delCourseApi,
  deleteAttachmentApi,
  deleteMethodsApi,
  delPointValueApi,
  getCBWeightListApi,
  getCoursesApi,
  getDetailApi,
  getMethodApi,
  getMethodChkListBoxApi,
  getMethodListBoxApi,
  getTestChoiceApi,
  getTestListApi,
  updateMethodApi,
};
