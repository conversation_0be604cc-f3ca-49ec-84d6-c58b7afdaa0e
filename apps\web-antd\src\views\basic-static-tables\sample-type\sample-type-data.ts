import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { SampleTypeApi } from '#/api/basic-static-tables/sample-type';

import { $t } from '#/locales';

export function useColumns(): VxeTableGridOptions<SampleTypeApi.SampleType>['columns'] {
  return [
    { field: 'radio', type: 'radio', width: 80 },
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SAMPLE_TYPE',
      title: $t('basic-static-tables.sampleType.sampleType'),
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SAMPLEMANAGEMENT',
      title: $t('basic-static-tables.sampleType.sampleManagement'),
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'DESCRIPTION',
      title: $t('commons.description'),
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('commons.action'),
      width: 140,
    },
  ];
}

export function useSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'SAMPLE_TYPE',
      label: $t('basic-static-tables.sampleType.sampleType'),
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        options: [
          { label: $t('commons.yes'), value: '是' },
          { label: $t('commons.no'), value: '否' },
        ],
      },
      fieldName: 'SAMPLEMANAGEMENT',
      label: $t('basic-static-tables.sampleType.sampleManagement'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'DESCRIPTION',
      label: $t('commons.description'),
    },
  ];
}

export function useFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'SAMPLE_TYPE',
      label: $t('basic-static-tables.sampleType.sampleType'),
    },
    {
      component: 'Input',
      fieldName: 'DESCRIPTION',
      componentProps: {},
      label: $t('commons.description'),
    },
  ];
}
