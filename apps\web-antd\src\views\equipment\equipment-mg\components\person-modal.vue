<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Input } from 'ant-design-vue';

import {
  getEqListApi,
  getEquipManagerListApi,
} from '#/api/equipment/equipment-mg';
import { useEquipmentStore } from '#/store';

const emit = defineEmits(['success']);

const equipmentStore = useEquipmentStore();
interface EquipmentRow {
  EQID: string;
  [key: string]: any;
}
const currentRow = computed<EquipmentRow | null>(
  () => equipmentStore.getCurrentRow as EquipmentRow | null,
);
onMounted(async () => {
  const res = await getEquipManagerListApi();
  console.warn(res);
});
const type = ref();
const searchQuery = ref('');
interface PersonItem {
  TEXT: string;
  VALUE: string;
}
const filteredItems = computed(() => {
  if (!searchQuery.value) {
    return items.value;
  }
  const query = searchQuery.value.toLowerCase();
  return items.value.filter((item) => item.VALUE.toLowerCase().includes(query));
});

const items = ref<PersonItem[]>([]);

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    modalApi.lock();
    try {
      emit('success', selectedItem.value);
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      type.value = modalApi.getData<Record<string, any>>();
      if (type.value?.modalType === 'person') {
        const res = await getEquipManagerListApi();
        items.value = res.items;
      } else {
        if (currentRow.value && currentRow.value.EQID) {
          const res = await getEqListApi([currentRow.value.EQID]);
          items.value = res.items.map((item: any) => ({
            TEXT: item.EQTEXT,
            VALUE: item.EQVAL,
          }));
        } else {
          items.value = [];
        }
      }
    }
  },
});

const selectedItem = ref<PersonItem>({} as PersonItem);
const clickItem = (item: PersonItem) => {
  selectedItem.value = item;
  console.warn(item);
};
</script>
<template>
  <Modal
    :title="type?.modalType === 'person' ? '按拼音查询' : ''"
    class="h-1/2"
  >
    <div class="static" v-if="type?.modalType === 'person'">
      <Input v-model:value="searchQuery" placeholder="按拼音查询" />
    </div>
    <ul class="overflow-auto">
      <li
        v-for="item in filteredItems"
        :key="item.VALUE"
        class="list-item hover:bg-blue-200"
        :style="{
          backgroundColor:
            selectedItem.VALUE === item.VALUE ? '#f0f0f0' : 'white',
        }"
        @click="clickItem(item)"
      >
        {{ item.TEXT }}
      </li>
    </ul>
  </Modal>
</template>
<style>
.list {
  padding: 0;
  list-style-type: none;
}

.list-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #ccc;
  transition: background-color 0.3s;
}

.list-item:hover {
  background-color: #f0f0f0;
}

.list-item.bg-blue-100 {
  color: #000;
}
</style>
