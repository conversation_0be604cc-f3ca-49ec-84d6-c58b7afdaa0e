<script lang="ts" setup>
import type { MaterialManagerApi } from '#/api/materials-management/material-manager';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  addMaterialUnitConv,
  getMatCodeAndNameByMatType,
  getMatTypeList,
} from '#/api/materials-management/material-manager';
import { $t } from '#/locales';

const emit = defineEmits(['success']);

const formData = ref<MaterialManagerApi.MaterialUnitConversion>();

const matCode = ref<string>('');

const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 提交函数
  handleSubmit: onSubmit,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'vertical',
  schema: [
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'ApiSelect',
      // 对应组件的参数
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: async () => {
          const res = await getMatTypeList();
          return res.items.map((item) => ({
            label: item.MATTYPE,
            value: item.MATTYPE,
          }));
        },
        // autoSelect: 'first',
        immediate: true,
        onChange: async (value: string) => {
          // message.success(`当前页面${value}`);
          formApi.setValues({ PARENT_CONTAINER_MATCODE: '' });
          const res = await getMatCodeAndNameByMatType(value);
          const options = res.items.map((item) => ({
            label: item.Text,
            value: item.Value,
          }));
          formApi.updateSchema([
            {
              fieldName: 'PARENT_CONTAINER_MATCODE', // 精准更新字段
              componentProps: {
                options,
              },
            },
          ]);
        },
      },
      // 字段名
      fieldName: 'PATENTMATTYPE',
      // 界面显示的label
      label: $t('materials-management.material-manager.parentmattype'),
      rules: 'required',
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Select',
      // 对应组件的参数
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        immediate: true,
      },
      // 字段名
      fieldName: 'PARENT_CONTAINER_MATCODE',
      // 界面显示的label
      label: $t(
        'materials-management.material-manager.parent_container_matcode',
      ),
      rules: 'required',
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'ApiSelect',
      // 对应组件的参数
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: async () => {
          const res = await getMatTypeList();
          return res.items.map((item) => ({
            label: item.MATTYPE,
            value: item.MATTYPE,
          }));
        },
        // autoSelect: 'first',
        immediate: true,
        onChange: async (value: string) => {
          // message.success(`当前页面${value}`);
          formApi.setValues({ CONTAINER_MATCODE: '' });
          const res = await getMatCodeAndNameByMatType(value);
          const options = res.items.map((item) => ({
            label: item.Text,
            value: item.Value,
          }));
          formApi.updateSchema([
            {
              fieldName: 'CONTAINER_MATCODE', // 精准更新字段
              componentProps: {
                options,
              },
            },
          ]);
        },
      },
      // 字段名
      fieldName: 'CHILDMATTYPE',
      // 界面显示的label
      label: $t('materials-management.material-manager.childmattype'),
      rules: 'required',
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Select',
      // 对应组件的参数
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        immediate: true,
      },
      // 字段名
      fieldName: 'CONTAINER_MATCODE',
      // 界面显示的label
      label: $t('materials-management.material-manager.container_matcode'),
      rules: 'required',
    },
    {
      component: 'Input',
      // 字段名
      fieldName: 'CONVERSION_FACTOR',
      // 界面显示的label
      label: $t('materials-management.material-manager.conversion_factor'),
      rules: 'required',
    },
  ],
  showDefaultActions: false,
});

/* function resetForm() {
  formApi.resetForm();
  formApi.setValues(formData.value || {});
} */

const [Modal, modalApi] = useVbenModal({
  draggable: true,
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
    // modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data =
        modalApi.getData<MaterialManagerApi.MaterialUnitConversion>();
      if (data) {
        matCode.value = data.MATCODE;
        formData.value = data;
        formApi.setValues(formData.value);
      }
    }
  },
  title: '新增容器单位转换',
});

async function onSubmit() {
  try {
    message.loading({
      content: '正在提交中...',
      duration: 0,
      key: 'is-form-submitting',
    });
    modalApi.lock();

    const data =
      (await formApi.getValues()) as MaterialManagerApi.MaterialUnitConversion;
    data.MATCODE = matCode.value;

    if (data.PARENT_CONTAINER_MATCODE === data.CONTAINER_MATCODE) {
      message.error({
        content: '子容器不能等于父容器，请提供不同的子容器!',
        key: 'is-form-submitting',
      });
      return;
    }
    // 调用添加分类 API
    await addMaterialUnitConv(data);
    emit('success');
    modalApi.close();
    message.success({
      content: '新增成功',
      duration: 2,
      key: 'is-form-submitting',
    });
  } catch (error) {
    message.error({
      content: `新增失败：${(error as Error).message}`,
      key: 'is-form-submitting',
    });
    modalApi.close();
  } finally {
    modalApi.unlock();
  }
}
</script>

<template>
  <Modal>
    <Form />
    <template #prepend-footer>
      <div class="flex-auto">
        <!--      <Button type="primary" danger @click="resetForm">
          {{ $t('common.reset') }}
        </Button> -->
      </div>
    </template>
  </Modal>
</template>
