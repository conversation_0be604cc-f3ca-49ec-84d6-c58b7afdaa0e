<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import { ref } from 'vue';
import { certificateModalSchema } from '../persons-manage-data';
import dayjs from 'dayjs';
import { addCredentialsApi } from '#/api/equipment/persons-manage';
import { message } from 'ant-design-vue';

const emit = defineEmits(['success']);
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: certificateModalSchema(),
  showDefaultActions: false,
});
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    console.warn(data);

    modalApi.lock();
    try {
      const clickRow = modalApi.getData().clickRow || [];
      const data = await formApi.getValues();

      //电子签名
      const params = [
        clickRow.USRNAM,
        data.certificateName,
        dayjs(data.acquisitionTime).format('YYYY-MM-DD'),
        dayjs(data.expirationTime).format('YYYY-MM-DD'),
      ];
      const res = await addCredentialsApi(params);
      if (res) {
        message.success('添加证书成功');
        emit('success');
        modalApi.close();
      } else {
        message.warning('证书已存在请重新添加');
        return;
      }
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      type.value = modalApi.getData().type;
      title.value =
        type.value === 'METHOD'
          ? '选择方法授权失效日期'
          : '选择设备授权失效日期';
      formApi.setFieldValue('authorizationDate', dayjs());
      formApi.setFieldValue('lostDate', dayjs());
    }
  },
});
const type = ref('METHOD');
const title = ref('选择方法授权失效日期');
</script>
<template>
  <Modal title="添加证书信息">
    <Form class="mx-4" />
  </Modal>
</template>
