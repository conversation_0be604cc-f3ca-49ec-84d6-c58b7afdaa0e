<script lang="ts" setup>
import { computed, ref } from 'vue';

import { useVbenModal, z } from '@vben/common-ui';

import { Button, message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { createRoom } from '#/api';
import { $t } from '#/locales';

const emit = defineEmits(['success']);
const formData = ref<
  Record<string, any> & { BUILDING_ID: string; ORIGREC?: string }
>({
  BUILDING_ID: '',
});
const getTitle = computed(() => {
  return formData.value?.ORIGREC
    ? $t('ui.actionTitle.edit', [$t('system.dept.building.roomName')])
    : $t('ui.actionTitle.create', [$t('system.dept.building.roomName')]);
});

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: [
    {
      component: 'Input',
      fieldName: 'ROOM_CODE',
      label: $t('system.dept.building.roomCode'),
      rules: z
        .string()
        .min(
          2,
          $t('ui.formRules.minLength', [
            $t('system.dept.building.roomCode'),
            2,
          ]),
        ),
    },
    {
      component: 'Input',
      fieldName: 'ROOM_NAME',
      label: $t('system.dept.building.roomName'),
      rules: z
        .string()
        .min(
          2,
          $t('ui.formRules.minLength', [
            $t('system.dept.building.roomName'),
            2,
          ]),
        )
        .max(
          20,
          $t('ui.formRules.maxLength', [
            $t('system.dept.building.roomName'),
            20,
          ]),
        ),
    },
    {
      component: 'Input',
      fieldName: 'CLASS',
      label: $t('system.dept.building.class'),
      rules: z
        .string()
        .min(
          2,
          $t('ui.formRules.minLength', [$t('system.dept.building.class'), 1]),
        )
        .max(
          20,
          $t('ui.formRules.maxLength', [$t('system.dept.building.class'), 20]),
        ),
    },
    {
      component: 'Textarea',
      componentProps: {
        maxLength: 200,
        showCount: true,
        rows: 3,
        class: 'w-full',
      },
      fieldName: 'DESCRIPTION',
      label: $t('system.dept.building.description'),
      rules: z
        .string()
        .max(
          500,
          $t('ui.formRules.maxLength', [
            $t('system.dept.building.description'),
            500,
          ]),
        )
        .optional(),
    },
  ],
  showDefaultActions: false,
});

function resetForm() {
  formApi.resetForm();
  formApi.setValues(formData.value || {});
}

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (valid) {
      modalApi.lock();
      const data = await formApi.getValues();
      try {
        const [success] = await createRoom({
          ...data,
          BUILDING_ID: formData.value.BUILDING_ID,
        });
        if (!success) {
          message.error(
            $t('ui.actionMessage.operationFailed', [data.ROOM_NAME]),
          );
          resetForm();
          return;
        }
        modalApi.close();
        emit('success');
      } finally {
        modalApi.lock(false);
      }
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<
        Record<string, any> & { BUILDING_ID: string }
      >();
      if (data) {
        formData.value = data;
        formApi.setValues(formData.value);
      }
    }
  },
});
</script>

<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
    <template #prepend-footer>
      <div class="flex-auto">
        <Button type="primary" danger @click="resetForm">
          {{ $t('common.reset') }}
        </Button>
      </div>
    </template>
  </Modal>
</template>
