import { callServer, getDataSet } from '#/api/core/witlab';
import { requestClient } from '#/api/request';

export namespace SubLocationTypeApi {
  export interface PageFetchParams {
    [key: string]: any;
    page: number;
    pageSize: number;
  }

  export interface SubLocationType {
    ORIGREC: number;
    NAME: string;
    PREFIX: string;
    NUMBERING_METHOD: string;
    SUBLOCATIONS_TEMPLATE_ID: number;
  }
}

/**
 * 获取子位置类型列表数据
 */
async function getSubLocationTypeList() {
  return getDataSet('SubLocationType.dsSubLocationTypeSearch', []);
}

/**
 * 获取编码方法
 */
async function getNumMethod() {
  return getDataSet('SubLocationType.cboNumMethod', []);
}

/**
 * 添加子位置类型
 * @param data 子位置类型数据
 */
async function addSubLocationType(data: SubLocationTypeApi.SubLocationType) {
  return await callServer('SubLocationType.scSubLocationTypeAdd', [
    data.NAME,
    data.PREFIX,
    data.NUMBERING_METHOD,
    'AddSubLocationType',
  ]);
}

/**
 * 更新子位置类型
 *
 * @param data 子位置类型数据
 * @returns boolean
 */
async function updateSubLocationType(data: SubLocationTypeApi.SubLocationType) {
  return requestClient.post<boolean>(
    '/spec-categories/updateSubLocationType',
    data,
  );
}

/**
 * 删除子位置类型
 *
 * @param origrec 子位置类型数据
 * @returns boolean
 */
async function deleteSubLocationType(origrec: number[]) {
  return await callServer('SubLocationType.scSubLocationTypeDel', [origrec]);
}

export {
  addSubLocationType,
  deleteSubLocationType,
  getNumMethod,
  getSubLocationTypeList,
  updateSubLocationType,
};
