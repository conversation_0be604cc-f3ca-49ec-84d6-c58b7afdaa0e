import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { JobDescriptionsTableApi } from '#/api/basic-static-tables/job-descriptions';

import { z } from '#/adapter/form';
import { $t } from '#/locales';

export function useColumns(): VxeTableGridOptions<JobDescriptionsTableApi.JobDescriptions>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'JOBDESCRIPTION',
      title: $t('basicStatic.jobDescriptions.jobDescriptions'),
      // width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('basicStatic.lookups.dataTable.columns.actions'),
      width: 140,
    },
  ];
}

export function useSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'JOBDESCRIPTION',
      label: $t('basicStatic.jobDescriptions.jobDescriptions'),
      rules: z
        .string()
        .min(
          1,
          $t('ui.formRules.minLength', [
            $t('basicStatic.jobDescriptions.jobDescriptions'),
            1,
          ]),
        )
        .max(
          20,
          $t('ui.formRules.maxLength', [
            $t('basicStatic.jobDescriptions.jobDescriptions'),
            20,
          ]),
        ),
    },
  ];
}

export function useFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        // placeholder: 'Please enter code',
      },
      fieldName: 'jobDescription',
      label: $t('basicStatic.jobDescriptions.jobDescriptions'),
    },
  ];
}
