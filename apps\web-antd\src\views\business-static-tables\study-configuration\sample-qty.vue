<script setup lang="ts">
import type { VxeColumnPropTypes } from 'vxe-table';

import type { XFolderApi } from '#/api/business-static-tables';

import { onMounted, reactive, ref, watch } from 'vue';

import { Button, Divider, Empty, Skeleton, Tree } from 'ant-design-vue';

import {
  $getChildContainersApi,
  $getQtyMaterialsApi,
  $qtyRequired_GD_ssl,
} from '#/api/business-static-tables';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import { useStabTestQtyColumns } from './data';

const props = defineProps({
  stabRow: {
    type: [Object, null],
    required: true,
  },
});

const showTreeSkeleton = ref<boolean>(true);
const matTreeArray = ref<XFolderApi.XFolderRelations[]>([]);

const selectMat = ref<string[]>([]);
const loadMatTree = async () => {
  if (props.stabRow) {
    showTreeSkeleton.value = true;
    matTreeArray.value = await $getQtyMaterialsApi({
      stabNo: props.stabRow.STABNO,
    });
  } else {
    matTreeArray.value = [];
  }

  showTreeSkeleton.value = false;
};

onMounted(() => {
  loadMatTree();
});

watch(
  () => props.stabRow,
  () => {
    loadMatTree();
    gridApi.query();
  },
);

const unitEditRander = reactive<VxeColumnPropTypes.EditRender>({
  name: 'select',
  options: [],
});

async function editRowEvent(row: XFolderApi.XStabTestQty) {
  const storedContainer = row.CONTAINER_MATCODE;
  const unitOptions = await $getChildContainersApi({
    storedContainer,
    matCode: selectMat.value[0] as string,
  });
  unitEditRander.options = unitOptions.map(
    (item: { CONTAINER_MATCODE: string }) => ({
      label: item.CONTAINER_MATCODE,
      value: item.CONTAINER_MATCODE,
    }),
  );
  gridApi.grid?.setEditRow(row);
}
const {
  Grid,
  gridApi,
  CurrentRow,
  saveRowEvent,
  hasEditStatus,
  cancelRowEvent,
} = useLimsGridsConfig<XFolderApi.XStabTestQty>(
  useStabTestQtyColumns(unitEditRander),
  [],
  async () => {
    if (selectMat.value.length !== 1 || !props.stabRow) {
      return [];
    }
    return await $qtyRequired_GD_ssl({
      stabNo: props.stabRow.STABNO,
      matCode: selectMat.value[0] as string,
      spCode: props.stabRow.SP_CODE,
    });
  },
  {
    pagerConfig: {
      enabled: false,
    },
  },
);
async function selectMatChange() {
  if (!CurrentRow.value || selectMat.value.length !== 1 || !props.stabRow) {
    unitEditRander.options = [];
  } else {
    const storedContainer = CurrentRow.value.CONTAINER_MATCODE;
    const unitOptions = await $getChildContainersApi({
      storedContainer,
      matCode: selectMat.value[0] as string,
    });
    unitEditRander.options = unitOptions.map(
      (item: { CONTAINER_MATCODE: string }) => ({
        label: item.CONTAINER_MATCODE,
        value: item.CONTAINER_MATCODE,
      }),
    );
  }
  gridApi.query();
}
</script>
<template>
  <div
    class="h-full w-full"
    style="display: flex; padding: 8px; overflow-y: auto"
  >
    <Skeleton
      :loading="showTreeSkeleton"
      :paragraph="{ rows: 8 }"
      active
      class="p-[8px]"
    >
      <div
        class="bg-background flex h-full w-1/5 flex-col overflow-y-auto rounded-lg"
      >
        <Tree
          v-bind="$attrs"
          v-if="matTreeArray.length > 0"
          v-model:selected-keys="selectMat"
          :class="$attrs.class"
          :field-names="{ title: 'MATCODE', key: 'MATCODE' }"
          :show-line="{ showLeafIcon: false }"
          :tree-data="matTreeArray"
          :virtual="false"
          :checkable="false"
          :selectable="true"
          @select="selectMatChange"
        />
        <div v-else class="mt-5">
          <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE" description="无数据" />
        </div>
      </div>
    </Skeleton>
    <Divider type="vertical" class="h-full" />
    <div class="h-full w-4/5">
      <Grid>
        <template #action="{ row }">
          <template v-if="hasEditStatus(row)">
            <Button type="link" @click="saveRowEvent(row)">
              {{ $t('commons.save') }}
            </Button>
            <Button type="link" @click="cancelRowEvent(row)">
              {{ $t('commons.cancel') }}
            </Button>
          </template>
          <template v-else>
            <Button type="link" @click="editRowEvent(row)">
              {{ $t('commons.edit') }}
            </Button>
          </template>
        </template>
      </Grid>
    </div>
  </div>
</template>
