<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { VirusTypeApi } from '#/api/basic-static-tables/prompt-virus-type';

import { computed, ref } from 'vue';

import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  $delCommonApi,
  $getVirusInfoApi,
} from '#/api/basic-static-tables/prompt-virus-type';
import { confirm } from '#/utils/utils';

import AddvirusInfoForm from './add-virus-info-form.vue';
import { useInfoColumns, useInfoFilterSchema } from './prompt-virus-type-data';

const virusTypeData = ref<VirusTypeApi.VirusType>();

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddvirusInfoForm,
  destroyOnClose: true,
});
const gridOptions: VxeTableGridOptions<VirusTypeApi.VirusInfo> = {
  columns: useInfoColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!virusTypeData.value) return [];
        return await $getVirusInfoApi({
          typeName: virusTypeData.value.TYPENAME,
        });
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const formOptions: VbenFormProps = {
  schema: useInfoFilterSchema(),
  submitOnChange: false,
  collapsed: true,
  showCollapseButton: true,
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: {},
  formOptions,
});

function onCreate() {
  formModalApi.setData({ virusType: virusTypeData.value?.TYPENAME }).open();
}

async function onDelete() {
  const checkOrig: number[] =
    (gridApi.grid
      ?.getCheckboxRecords()
      .map((item) => item.ORIGREC) as number[]) || [];

  if (checkOrig.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }
  await confirm('是否确认删除选中的数据？', '删除确认');
  await $delCommonApi({
    origrecs: checkOrig,
    tableName: 'VIRUSINFO',
  });
  message.success('删除成功！');
  onRefresh();
}

function onRefresh() {
  gridApi.query();
}

const [Drawer, drawerApi] = useVbenDrawer({
  onConfirm: onSubmit,
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<VirusTypeApi.VirusType>();
      if (data) {
        virusTypeData.value = data;
        gridApi?.query();
      }
    }
  },
  showCancelButton: false,
  showConfirmButton: false,
});

async function onSubmit() {
  drawerApi.close();
}

const getDrawerTitle = computed(() => {
  if (virusTypeData.value) {
    return `${virusTypeData.value.TYPENAME}`;
  }
  return '';
});
</script>

<template>
  <Drawer class="w-full max-w-[800px]" :title="getDrawerTitle">
    <Page auto-content-height>
      <FormModal @success="onRefresh" />
      <Grid>
        <template #toolbar-actions>
          <Space :size="[4, 0]" wrap>
            <Button type="primary" @click="onCreate">
              {{ $t('ui.actionTitle.create') }}
            </Button>
            <Button type="primary" danger @click="onDelete">
              {{ $t('ui.actionTitle.delete') }}
            </Button>
          </Space>
        </template>
      </Grid>
    </Page>
  </Drawer>
</template>
