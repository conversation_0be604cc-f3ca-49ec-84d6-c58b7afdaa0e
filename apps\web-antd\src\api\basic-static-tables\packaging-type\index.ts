import { callServer, getDataSet } from '#/api/core';

export namespace XPackagingTypeApi {
  export interface XPackagingType {
    [key: string]: any;
    ORIGREC: number;
    PACKAGINGTYPECODE: string;
  }
}

const $delXPackagingTypeApi = async (types: string[]) => {
  return await callServer('PackagingTypes.deletePackType', [types]);
};
const $getXPackagingTypeApi = async () => {
  return await getDataSet('PackagingTypes.getPackType', []);
};
const $addXPackagingTypeApi = async (
  data: Omit<XPackagingTypeApi.XPackagingType, 'ORIGREC'>,
) => {
  return await callServer('PackagingTypes.addPackType', [
    data.PACKAGINGTYPECODE,
    data.eventCode,
    data.comment,
  ]);
};

export { $addXPackagingTypeApi, $delXPackagingTypeApi, $getXPackagingTypeApi };
