<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { UnitsManagementApi } from '#/api/basic-static-tables/units-management';

import { confirm, Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteMeasureType,
  getMeasureTypeList,
} from '#/api/basic-static-tables/units-management';

import AddMeasureTypeForm from './add-measure-type.vue';
import {
  useMeasureTypesColumns,
  useMeasureTypesFilterSchema,
} from './units-management-data';
import UnitsOfMeasure from './units-of-measure.vue';

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: UnitsOfMeasure,
  destroyOnClose: true,
  class: 'w-[900px]',
});

const gridOptions: VxeTableGridOptions<UnitsManagementApi.MeasureTypes> = {
  columns: useMeasureTypesColumns(),
  stripe: true,
  border: true,
  checkboxConfig: {
    highlight: true,
    range: true,
    labelField: 'select',
  },
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getMeasureTypeList();
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const formOptions: VbenFormProps = {
  schema: useMeasureTypesFilterSchema(),
  submitOnChange: false,
  collapsed: true,
  showCollapseButton: true,
  submitButtonOptions: {
    content: '查询',
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: {},
  formOptions,
});

function viewDetails(row: UnitsManagementApi.MeasureTypes) {
  formDrawerApi.setData(row).open();
}

function onRefresh() {
  gridApi.query();
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddMeasureTypeForm,
  destroyOnClose: true,
});

function onCreate() {
  formModalApi.setData(null).open();
}

async function onDelete() {
  // 获取选中行
  const aOrigrec: number[] = gridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.ORIGREC);

  if (aOrigrec.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${aOrigrec.length} 条数据吗？`,
      icon: 'warning',
      centered: false,
    });

    await deleteMeasureType(aOrigrec);

    message.success('删除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}
</script>

<template>
  <Page auto-content-height>
    <FormDrawer @success="onRefresh" />
    <FormModal @success="onRefresh" class="w-[600px]" />
    <Grid>
      <template #toolbar-actions>
        <Space>
          <Button type="primary" @click="onCreate">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="onDelete">
            {{ $t('ui.actionTitle.delete') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <Button type="link" @click="viewDetails(row)">
          {{ $t('basic-static-tables.detail') }}
        </Button>
      </template>
    </Grid>
  </Page>
</template>
