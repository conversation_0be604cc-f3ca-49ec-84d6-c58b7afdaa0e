<script lang="ts" setup>
import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { message, Step, Steps } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  $addEditStudyTemplateApi,
  $checkUniqueNameApi,
} from '#/api/business-static-tables';
import { $t } from '#/locales';

import PromptForConditionsIntervals from './prompt-for-conditions-intervals.vue';

interface FormArgs {
  openingMode: string;
  spCode: number;
  stabNo: number;
  sSite: string;
  bShowName: boolean;
  bDisableConditions: boolean;
  saveMode?: string;
  status?: string;
}
const emit = defineEmits(['success']);

const formArgs = ref<FormArgs>();

const currentTab = ref<number>(0); // 当前步骤
const currentIndex = ref(0); // 当前进度条步骤索引

const tabTitleMap = ref<string[]>([
  $t('business-static-tables.studyConfiguration.folderName'),
  $t('business-static-tables.studyConfiguration.conditionIntervals'),
]);
const promptArgs = ref<Record<string, any>>();
const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  showConfirmButton: true,
  confirmDisabled: false,
  confirmText: $t('commons.next'),
  onConfirm,
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<FormArgs>();
      if (data) {
        formArgs.value = data;
        if (!data.bShowName) {
          tabTitleMap.value = [
            $t('business-static-tables.studyConfiguration.conditionIntervals'),
          ];
          currentTab.value = 1;
          promptArgs.value = {
            bDisableConditions: data.bDisableConditions,
            nMaxIntervals: 25,
            stabNo: data.stabNo,
            openingMode: data.openingMode,
            spCode: data.spCode,
            sSite: data.sSite,
            bShowName: data.bShowName,
          };
          modalApi.setState({
            confirmText: $t('commons.confirm'),
          });
        }
      }
    }
  },

  onCancel: () => {
    modalApi.close();
  },
});

async function onConfirm() {
  switch (currentTab.value) {
    case 0: {
      const { valid } = await templateNameFormApi.validate();
      if (!valid) {
        return;
      }
      const values = await templateNameFormApi.getValues();
      await onFirstSubmit(values);
      break;
    }
    case 1: {
      await onSecondSubmit();
      break;
    }
    default: {
      break;
    }
  }
}

const [TemplateNameForm, templateNameFormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  resetButtonOptions: {
    show: false,
  },
  schema: [
    {
      component: 'Input',
      fieldName: 'FOLDERNAM',
      label: $t('business-static-tables.studyConfiguration.folderName'),
      rules: 'required',
    },
  ],
  submitButtonOptions: {
    show: false,
  },
  wrapperClass: 'grid-cols-1 md:grid-cols-1 lg:grid-cols-1',
});

const firstValues = ref<Record<string, any>>({});
const promptForConditionsIntervalsRef =
  ref<InstanceType<typeof PromptForConditionsIntervals>>();

async function onFirstSubmit(values: Record<string, any>) {
  const templateName = await $checkUniqueNameApi({
    name: values.FOLDERNAM,
    isProtocol: 'B',
  });
  if (!templateName) {
    message.error(
      $t('business-static-tables.studyConfiguration.existTemplateName'),
    );
    return;
  }
  firstValues.value = values;
  currentTab.value = 1;
  currentIndex.value = currentIndex.value + 1;
  modalApi.setState({
    confirmText: $t('commons.confirm'),
  });
}

async function onSecondSubmit() {
  const selectConditions =
    promptForConditionsIntervalsRef.value?.getSelectConditions();
  const checkIntervals = promptForConditionsIntervalsRef.value?.checkIntervals;
  if (
    !selectConditions ||
    selectConditions.length === 0 ||
    !checkIntervals ||
    checkIntervals.length === 0
  ) {
    message.error(
      $t(
        'business-static-tables.studyConfiguration.selectOneConditionsAndIntervals',
      ),
    );
    return;
  }
  if (!formArgs.value) {
    return;
  }
  await $addEditStudyTemplateApi({
    spCode: formArgs.value.spCode,
    conditions: selectConditions,
    intervals: checkIntervals,
    openingMode: formArgs.value.saveMode ?? formArgs.value.openingMode,
    testList: [-100],
    name: firstValues.value.FOLDERNAM,
    stabNo: formArgs.value.stabNo.toString(),
    status: formArgs.value.status,
  });
  emit('success');
  modalApi.close();
}
</script>
<template>
  <Modal :title="tabTitleMap[currentTab]">
    <Page>
      <div class="mx-auto">
        <Steps :current="currentIndex" class="steps">
          <Steps :current="currentIndex" class="steps">
            <template v-for="(title, _index) in tabTitleMap" :key="_index">
              <Step :title="title" />
            </template>
          </Steps>
        </Steps>
        <div class="p-0">
          <TemplateNameForm v-show="currentTab === 0" class="p-10" />
          <PromptForConditionsIntervals
            ref="promptForConditionsIntervalsRef"
            :form-args="promptArgs"
            v-if="currentTab === 1"
          />
        </div>
      </div>
    </Page>
  </Modal>
</template>
