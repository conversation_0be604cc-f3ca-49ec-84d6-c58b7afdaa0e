<script lang="ts" setup>
import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import { $moveTestCatogoryApi } from '#/api/business-static-tables';
import { $t } from '#/locales';

import { useMoveCatSchema } from './data';

const emit = defineEmits(['success']);
const moveTestOrigrecs = ref<number[]>([]);
const oldTestCatCode = ref<string>('');
const getTitle = computed(() => {
  return $t('business-static-tables.testManager.moveCategory');
});
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: useMoveCatSchema(),
  showDefaultActions: false,
});
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    modalApi.lock();
    try {
      await $moveTestCatogoryApi(
        data.TESTCATCODE,
        oldTestCatCode.value,
        moveTestOrigrecs.value,
      );
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },

  async onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData();
      if (data) {
        moveTestOrigrecs.value = data.moveTestOrigrecs;
        oldTestCatCode.value = data.oldTestCatCode;
      }
    }
  },
});
</script>
<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
  </Modal>
</template>
