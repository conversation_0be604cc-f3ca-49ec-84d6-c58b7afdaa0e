<script lang="ts" setup>
import type { VxeGridListeners } from '#/adapter/vxe-table';
import type { XIntervalsApi } from '#/api/basic-static-tables/intervals';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { Button, message, Space, TabPane, Tabs } from 'ant-design-vue';

import {
  $delSampleDueDateCalcApi,
  $delXIntervalsApi,
  $getIntervalTatApi,
  $getXIntervalsApi,
  $updateSampleCalcIntervalApi,
} from '#/api/basic-static-tables/intervals';
import DeptTree from '#/components/dept-tree.vue';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';
import { confirm } from '#/utils/utils';

import AddXIntervalsForm from './add-intervals-form.vue';
import AddIntervalsTatForm from './add-intervals-tat-form.vue';
import CopyFromToSiteForm from './copy-from-to-site-form.vue';
import {
  useColumns,
  useFilterSchema,
  useTatColumns,
  useTatFilterSchema,
} from './data';
import SelectIntervalTatForm from './select-interval-form.vue';

const columns = useColumns();
const filterSchema = useFilterSchema();
const queryData = async () => {
  if (selectDeptId.value.length === 1) {
    return await $getXIntervalsApi({ deptId: selectDeptId.value[0] as string });
  }
};
const event: VxeGridListeners = {};
const gridOptions = {};
const {
  Grid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  cancelRowEvent,
  saveRowEvent,
} = useLimsGridsConfig<XIntervalsApi.XInterval>(
  columns,
  filterSchema,
  queryData,
  gridOptions,
  event,
);
async function onDelete() {
  const checkRec = gridApi.grid?.getCheckboxRecords();

  if (checkRec.length === 0) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const origrecs = checkRec.map((item) => item.ORIGREC);
  await confirm($t('commons.deleteConfirm'), $t('commons.deleteConfirmTitle'));
  const res = await $delXIntervalsApi({ origrecs });
  if (res) {
    message.success($t('commons.deleteSuccess'));
    onRefresh();
  } else {
    message.error($t('basic-static-tables.intervals.nodel'));
  }
}
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddXIntervalsForm,
  destroyOnClose: true,
});
function onCreate() {
  formModalApi.setData({ deptId: selectDeptId.value[0] as string }).open();
}
function onRefresh() {
  gridApi.query();
}

const selectDeptId = ref<string[]>([]);
const activeKey = ref('interval');

const columnsTat = useTatColumns();
const filterTatSchema = useTatFilterSchema();
const queryDataTat = async () => {
  if (selectDeptId.value.length === 1) {
    return await $getIntervalTatApi({
      deptId: selectDeptId.value[0] as string,
    });
  }
};
const {
  Grid: GridTat,
  gridApi: gridApiTat,
  hasEditStatus: hasEditStatusTat,
  editRowEvent: editRowEventTat,
  cancelRowEvent: cancelRowEventTat,
  saveRowEvent: saveRowEventTat,
  CurrentRow: tatCurrentRow,
} = useLimsGridsConfig<XIntervalsApi.XIntervalTat>(
  columnsTat,
  filterTatSchema,
  queryDataTat,
);
async function onTatDelete() {
  const checkRec = gridApiTat.grid?.getCheckboxRecords();

  if (checkRec.length === 0) {
    message.warning($t('commons.selectOne'));
    return;
  }
  const origrecs = checkRec.map((item) => item.ORIGREC);
  await confirm($t('commons.deleteConfirm'), $t('commons.deleteConfirmTitle'));
  const res = await $delSampleDueDateCalcApi({ origrecs });
  if (res) {
    message.success($t('commons.deleteSuccess'));
    onTatRefresh();
  } else {
    message.error($t('basic-static-tables.intervals.nodel'));
  }
}
function onTatCreate() {
  if (selectDeptId.value.length === 1) {
    formModalTatApi.setData({ deptId: selectDeptId.value[0] as string }).open();
  } else {
    message.warning($t('basic-static-tables.intervals.selectDept'));
  }
}
function onTatRefresh() {
  gridApiTat.query();
}

function select() {
  if (activeKey.value === 'interval') {
    gridApi.reload();
  } else {
    gridApiTat.reload();
  }
}
function onTabChange() {
  if (activeKey.value === 'interval') {
    gridApi.reload();
  } else {
    gridApiTat.reload();
  }
}
const [TatFormModal, formModalTatApi] = useVbenModal({
  connectedComponent: AddIntervalsTatForm,
  destroyOnClose: true,
});
const [UpdateTatFormModal, updateTatFormModalApi] = useVbenModal({
  connectedComponent: SelectIntervalTatForm,
  destroyOnClose: true,
});

function openUpdateIntervalTat() {
  if (tatCurrentRow.value) {
    updateTatFormModalApi.setData({ deptId: tatCurrentRow.value.DEPT }).open();
  } else {
    message.warning($t('commons.selectOne'));
  }
}

async function handleUpdateIntervalTat(newIntervalStr: string) {
  if (tatCurrentRow.value) {
    const nPos = newIntervalStr.indexOf(' ');
    const newInterval = newIntervalStr.slice(0, Math.max(0, nPos));
    const newUnit = newIntervalStr.slice(Math.max(0, nPos + 1));
    if (
      newInterval === tatCurrentRow.value.INTERVAL &&
      newUnit === tatCurrentRow.value.UNIT
    ) {
      return;
    }
    const bUpdateOk = await $updateSampleCalcIntervalApi({
      nOrigrec: tatCurrentRow.value.ORIGREC,
      sInterval: newInterval,
      sUnit: newUnit,
      sSite: tatCurrentRow.value.DEPT,
      sOrigInterval: tatCurrentRow.value.INTERVAL,
      sOrigUnit: tatCurrentRow.value.UNIT,
    });
    if (bUpdateOk) {
      onTatRefresh();
    } else {
      message.error($t('commons.updateFail'));
    }
  }
}
const [CopyFromModal, copyFromModalApi] = useVbenModal({
  connectedComponent: CopyFromToSiteForm,
  destroyOnClose: true,
});

const onCopyFrom = () => {
  let deptId = '';
  if (selectDeptId.value.length === 1) {
    deptId = selectDeptId.value[0] as string;
  }
  copyFromModalApi.setData({ fromDept: deptId }).open();
};
</script>

<template>
  <FormModal @success="onRefresh" />
  <TatFormModal @success="onTatRefresh" />
  <UpdateTatFormModal @success="handleUpdateIntervalTat" />
  <CopyFromModal @success="onRefresh" />
  <Page auto-content-height>
    <div class="flex h-full gap-[8px]">
      <DeptTree
        v-model:select-dept-id="selectDeptId"
        class="w-[260px]"
        @reload="select"
        @select="select"
      />
      <Tabs
        v-model:active-key="activeKey"
        class="flex-1 overflow-hidden"
        @change="onTabChange"
      >
        <TabPane
          key="interval"
          :tab="$t('basic-static-tables.intervals.interval')"
        >
          <Grid>
            <template #toolbar-actions>
              <Space :size="[4, 0]" wrap>
                <Button type="primary" @click="onCreate">
                  {{ $t('ui.actionTitle.create') }}
                </Button>
                <Button type="primary" danger @click="onDelete">
                  {{ $t('ui.actionTitle.delete') }}
                </Button>
                <Button type="default" @click="onCopyFrom">
                  {{ $t('commons.copy') }}
                </Button>
              </Space>
            </template>
            <template #action="{ row }">
              <template v-if="hasEditStatus(row)">
                <Button type="link" @click="saveRowEvent(row)">保存</Button>
                <Button type="link" @click="cancelRowEvent(row)">取消</Button>
              </template>
              <template v-else>
                <Button type="link" @click="editRowEvent(row)">编辑</Button>
              </template>
            </template>
          </Grid>
        </TabPane>
        <TabPane
          key="tat"
          :tab="$t('basic-static-tables.intervals.intervalTat')"
        >
          <GridTat class="flex-1 overflow-hidden">
            <template #toolbar-actions>
              <Space :size="[4, 0]" wrap>
                <Button type="primary" @click="onTatCreate">
                  {{ $t('ui.actionTitle.create') }}
                </Button>
                <Button type="primary" danger @click="onTatDelete">
                  {{ $t('ui.actionTitle.delete') }}
                </Button>
                <Button type="default" @click="openUpdateIntervalTat">
                  {{ $t('basic-static-tables.intervals.updateInterval') }}
                </Button>
              </Space>
            </template>
            <template #action="{ row }">
              <template v-if="hasEditStatusTat(row)">
                <Button type="link" @click="saveRowEventTat(row)">保存</Button>
                <Button type="link" @click="cancelRowEventTat(row)">
                  取消
                </Button>
              </template>
              <template v-else>
                <Button type="link" @click="editRowEventTat(row)">编辑</Button>
              </template>
            </template>
          </GridTat>
        </TabPane>
      </Tabs>
    </div>
  </Page>
</template>

<style>
.ant-tabs-content {
  height: 100%;
}
</style>
