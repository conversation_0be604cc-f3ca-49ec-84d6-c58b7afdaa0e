<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { CheckListApi } from '#/api/basic-static-tables/check-list';

import { Page, useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  $deleteCheckListApi,
  $getCheckListApi,
} from '#/api/basic-static-tables/check-list';
import { confirm } from '#/utils/utils';

import AddSolutionTypeForm from './add-check-list-form.vue';
import { useColumns, useFilterSchema } from './check-list-data';

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddSolutionTypeForm,
  destroyOnClose: true,
});
const gridOptions: VxeTableGridOptions<CheckListApi.CheckList> = {
  columns: useColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        return await $getCheckListApi();
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const formOptions: VbenFormProps = {
  schema: useFilterSchema(),
  submitOnChange: false,
  collapsed: true,
  showCollapseButton: true,
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: {},
  formOptions,
});

function hasEditStatus(row: CheckListApi.CheckList) {
  return gridApi.grid?.isEditByRow(row);
}

function editRowEvent(row: CheckListApi.CheckList) {
  gridApi.grid?.setEditRow(row);
}

async function saveRowEvent(_row: CheckListApi.CheckList) {
  await gridApi.grid?.clearEdit();
  gridApi.setLoading(true);
  setTimeout(() => {
    gridApi.setLoading(false);
    message.success({
      content: `保存成功！`,
    });
  }, 600);
}

const cancelRowEvent = (_row: CheckListApi.CheckList) => {
  gridApi.grid?.clearEdit();
};

function onRefresh() {
  gridApi.query();
}

function onCreate() {
  formModalApi.setData(null).open();
}

async function onDelete() {
  const checkOrig: number[] =
    (gridApi.grid
      ?.getCheckboxRecords()
      .map((item) => item.ORIGREC) as number[]) || [];

  if (checkOrig.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }
  if (checkOrig.length > 1) {
    message.warning('只能选择一条数据！');
    return;
  }
  await confirm('是否确认删除选中的数据？', '删除确认');
  await $deleteCheckListApi(checkOrig[0] ?? 0);
  message.success('删除成功！');
  onRefresh();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <Grid>
      <template #toolbar-actions>
        <Space :size="[4, 0]" wrap>
          <Button type="primary" @click="onCreate">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="onDelete">
            {{ $t('ui.actionTitle.delete') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent(row)">保存</Button>
          <Button type="link" @click="cancelRowEvent(row)">取消</Button>
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">编辑</Button>
        </template>
      </template>
    </Grid>
  </Page>
</template>
