<script lang="ts" setup>
import type { SelectProps } from 'ant-design-vue';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import {
  Modal as AModal,
  Button,
  Checkbox,
  message,
  Select,
  SelectOption,
} from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  checkIfELNMethodEQLISTApi,
  getAllEquipmentsApi,
  getComponentEventsApi,
  getMaintEventsApi,
  getMaintTypeApi,
  openMaintRecApi,
} from '#/api/equipment/equipment-mg';
import { useEquipmentStore } from '#/store';

import { maintainModalSchema } from '../equipment-mg-data';
import DeviceModal from './device-modal.vue';

const emit = defineEmits(['success']);
const [FormModal] = useVbenModal({
  connectedComponent: DeviceModal,
  destroyOnClose: true,
});
interface EquipmentRow {
  [key: string]: any;
}
const equipmentStore = useEquipmentStore();
const currentRow = computed<EquipmentRow | null>(
  () => equipmentStore.getCurrentRow as EquipmentRow | null,
);
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: maintainModalSchema(),
  showDefaultActions: false,
});
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    const type = typeOptions.value.find(
      (item) => item.value === data.sType,
    )?.type;
    let aHasELNMethod = [];
    if (type === 'EQUIPMENT EVENT') {
      aHasELNMethod = await checkIfELNMethodEQLISTApi([
        data.aEqIdList,
        data.sEvent,
      ]);
    }
    const aEqIdList = data.aEqIdList.split(',');

    console.warn(data);
    modalApi.lock();
    try {
      emit('success');
      for (const element of aEqIdList.entries()) {
        const params = [
          data.reason,
          data.sEvent,
          data.sType,
          aHasELNMethod,
          element ?? '',
        ];
        await openMaintRecApi(params);
      }
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      const data = await getMaintTypeApi();
      typeOptions.value = data.map((item: any) => ({
        value: item.TEXT,
        label: item.TEXT,
        type: item.VALUE,
      }));
      // const data = modalApi.getData<SampleTypeApi.SampleType>();
    }
  },
});
interface PlainOption {
  value: string;
  label: string;
  checked: boolean;
}
const plainOptions = ref<PlainOption[]>([]);

const addDevice = async () => {
  const formValue = await formApi.getValues();
  if (!formValue.sType || !formValue.sEvent) {
    message.warning('请先选择一个维护类型');
    return;
  }
  const formData = await formApi.getValues();
  const params = [
    currentRow.value?.EQTYPE ?? '',
    [currentRow.value?.EQID ?? ''],
    // formValue.sEvent,
    formData.sEvent,
    'NotFinish', // TODO: 这里需要传入一个状态从url获取
  ];

  const data = await getAllEquipmentsApi(params);
  plainOptions.value = data.map((item: any) => ({
    value: item.EQID,
    label: item.EQ_DISPLAY,
    checked: false,
  }));
  open.value = true;
};
const open = ref(false);
const onRefresh = () => {
  modalApi.open();
};
const options = ref<SelectProps['options']>([]);
const handleOk = () => {
  const selectedDevices = plainOptions.value
    .filter((item) => item.checked)
    .map((item) => item.label);
  formApi.setFieldValue('aEqIdList', selectedDevices.join(','));
  open.value = false;
};

const typeOptions = ref<{ label: string; type: string; value: string }[]>([]);
const typeChange = async (value) => {
  const type = typeOptions.value.find((item) => item.value === value)?.type;
  if (type === 'EQUIPMENT EVENT') {
    const StepCode = ''; // TODO : 这里需要传入一个步骤代码从url获取
    const params = [currentRow.value?.EQID ?? '', StepCode];
    const data = await getMaintEventsApi(params);
    options.value = data.map((item: any) => ({
      value: item.MAINTENANCEEVENT,
      label: item.MAINTENANCEEVENT,
    }));
  } else if (type === 'COMPONENT EVENT') {
    const params = [currentRow.value?.EQID ?? ''];
    const data = await getComponentEventsApi(params);
    options.value = data.map((item: any) => ({
      value: item.MAINTENANCEEVENT,
      label: item.MAINTENANCEEVENT,
    }));
  }
};
</script>
<template>
  <FormModal @success="onRefresh" />

  <Modal title="添加发送信息">
    <AModal
      v-model:open="open"
      title="选择设备ID"
      @ok="handleOk"
      class="h-[100px]"
    >
      <div>请选择设备ID</div>
      <!-- <div
        class="h-1/6 overflow-hidden border-2 border-solid border-gray-200 p-2"
      >
        <CheckboxGroup
          v-model:value="checkedList"
          :options="plainOptions"
          style="display: grid"
          class="h-1/6 overflow-auto"
        />
      </div> -->

      <ul
        class="grid h-[400px] overflow-scroll border-2 border-solid border-gray-200 p-2"
      >
        <Checkbox
          v-model:checked="item.checked"
          v-for="item in plainOptions"
          :key="item.value"
        >
          {{ item.label }}
        </Checkbox>
      </ul>
    </AModal>
    <Form class="mx-4">
      <template #addBtn>
        <Button @click="addDevice" type="primary" ghost>添加设备</Button>
      </template>
      <template #sType="slotProps">
        <Select v-bind="slotProps" class="w-full" @change="typeChange">
          <SelectOption
            v-for="item in typeOptions"
            :value="item.value"
            :key="item.value"
          >
            {{ item.label }}
          </SelectOption>
        </Select>
      </template>
      <template #sEvent="slotProps">
        <Select v-bind="slotProps" class="w-full">
          <SelectOption
            v-for="item in options"
            :value="item.value"
            :key="item.value"
          >
            {{ item.label }}
          </SelectOption>
        </Select>
      </template>
      <!--   <template #reason="slotProps">
        <Textarea
          v-bind="slotProps"
          placeholder="请输入打开事件的原因。"
          :auto-size="{ minRows: 2, maxRows: 5 }"
        />
      </template> -->
    </Form>
  </Modal>
</template>
