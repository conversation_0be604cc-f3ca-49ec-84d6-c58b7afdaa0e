<script setup lang="ts">
import type { SampleGroupsApi } from '#/api/business-static-tables/sample-groups';

import { ref, watch } from 'vue';

import { Button, Space } from 'ant-design-vue';

import {
  editAnalytes,
  getAnalytes,
} from '#/api/business-static-tables/sample-groups';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import AnalyteTransferModal from './edit-analytes.vue';
import {
  useSpAnalytesColumns,
  useSpAnalytesFilterSchema,
} from './sample-groups-data';
// import SampleReqDetail from './sample-requirement-detail.vue';

const props = defineProps<{
  currentTestRow: null | SampleGroupsApi.SpTests;
}>();

const isRowEditDisabled = ref(false);
const analyteTransferModalRef = ref();
const aAnalytes = ref<string[]>([]);
const sTestCode = ref<number>(0); // 修改定义处
const analyteTransferModalVisible = ref(false);
const profile = ref<string>('');
const drawNo = ref<number>(0);
const spCode = ref<number>(0);

watch(
  () => props.currentTestRow,
  (_val) => {
    onRefresh();
  },
);
const colums = useSpAnalytesColumns();
const filterSchema = useSpAnalytesFilterSchema();
const queryData = async () => {
  if (!props.currentTestRow) return [];
  return getAnalytes(
    props.currentTestRow.SP_CODE,
    props.currentTestRow.DRAWNO,
    props.currentTestRow.PROFILE,
    props.currentTestRow.TESTCODE,
  );
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};

const {
  Grid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
} = useLimsGridsConfig<SampleGroupsApi.SpAnalytes>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

function onRefresh() {
  gridApi.query();
}

function openAnalyteTransferModal() {
  const allData = gridApi.grid?.getData(); // 获取表格所有行数据
  if (allData.length === 0) return;

  if (props.currentTestRow === null) return;
  profile.value = props.currentTestRow.PROFILE;
  sTestCode.value = props.currentTestRow.TESTCODE;
  drawNo.value = props.currentTestRow.DRAWNO;
  spCode.value = props.currentTestRow.SP_CODE;
  aAnalytes.value = allData.map((item) => item.ANALYTE) as string[];

  analyteTransferModalRef.value?.open();
}

async function handleAnalyteTransferSubmit(data: {
  selectedItems: any[];
  selectedKeys: string[];
}) {
  await editAnalytes(
    spCode.value,
    drawNo.value,
    sTestCode.value,
    profile.value,
    data.selectedKeys,
  );
  onRefresh();
}
</script>

<template>
  <FormModal @success="onRefresh" />
  <AnalyteTransferModal
    ref="analyteTransferModalRef"
    :test-code="sTestCode"
    :sp-code="spCode"
    :draw-no="drawNo"
    :profile="profile"
    :a-analytes="aAnalytes"
    @submit="handleAnalyteTransferSubmit"
    @cancel="analyteTransferModalVisible = false"
  />
  <Grid>
    <template #toolbar-actions>
      <Space :size="[4, 0]" wrap>
        <Button type="primary" @click="openAnalyteTransferModal">
          {{ $t('business-static-tables.edit') }}
        </Button>
        <Button type="default">
          {{ $t('business-static-tables.sampleGroups.possibleResults') }}
        </Button>
      </Space>
    </template>
    <template #action="{ row }">
      <template v-if="hasEditStatus(row)">
        <Button type="link" @click="saveRowEvent(row)">
          {{ $t('business-static-tables.save') }}
        </Button>
        <Button type="link" @click="cancelRowEvent(row)">
          {{ $t('business-static-tables.cancel') }}
        </Button>
      </template>
      <template v-else>
        <Button
          type="link"
          @click="editRowEvent(row)"
          :disabled="isRowEditDisabled"
        >
          {{ $t('business-static-tables.edit') }}
        </Button>
      </template>
    </template>
  </Grid>
</template>
