<script setup lang="ts">
import type { BatcheManagerApi } from '#/api/login-options/batch-manager';

import { watch } from 'vue';

import { confirm } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import {
  deleteFormulationItem,
  getBatchRecipesList,
} from '#/api/login-options/batch-manager';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

// import AddRecipeForm from './add-recipe.vue';
import {
  useBatchRecipeColumns,
  useBatchRecipeFilterSchema,
} from './batch-manager-data';

const props = defineProps<{
  currentTestRow: BatcheManagerApi.Batches | null;
}>();

watch(
  () => props.currentTestRow,
  (_val) => {
    onRefresh();
  },
);
const colums = useBatchRecipeColumns();
const filterSchema = useBatchRecipeFilterSchema();
const queryData = async () => {
  if (!props.currentTestRow) return [];
  const data = await getBatchRecipesList(props.currentTestRow.BATCHID);
  return data.items;
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};

const {
  Grid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
} = useLimsGridsConfig<BatcheManagerApi.BatchRecipe>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

function onRefresh() {
  gridApi.query();
}

// 添加配方
// const [FormModal, formModalApi] = useVbenModal({
//   connectedComponent: AddRecipeForm,
// });

// async function onCreate() {
//   if (props.currentTestRow === null) return;
//   const matcode = props.currentTestRow.MATCODE;
//   const sampleGroupCode = props.currentTestRow.SAMPLEGROUPCODE;
//   formModalApi
//     .setData({ MATCODE: matcode, SAMPLEGROUPCODE: sampleGroupCode })
//     .open();
// }

// 移除配方
async function onRemove() {
  // 获取选中行
  const recipe = gridApi.grid?.getCurrentRecord();
  if (!recipe) return;

  const sOrigrec = recipe.ORIGREC;
  try {
    await confirm({
      title: '确认移除',
      content: `确定要移除选中的配方数据吗？`,
      icon: 'warning',
      centered: false,
    });

    await deleteFormulationItem(sOrigrec);

    message.success('移除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}
</script>

<template>
  <FormModal @success="onRefresh" />
  <div class="h-[350px] w-full">
    <Grid>
      <template #toolbar-actions>
        <Space>
          <Button type="primary">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="onRemove">
            {{ $t('login-options.remove') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent(row)">
            {{ $t('login-options.save') }}
          </Button>
          <Button type="link" @click="cancelRowEvent(row)">
            {{ $t('login-options.cancel') }}
          </Button>
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">
            {{ $t('login-options.edit') }}
          </Button>
        </template>
      </template>
    </Grid>
  </div>
</template>
