<script setup lang="ts">
import type { FormApi } from '#/api/equipment/persons-manage';
import { trainColumns, courseColumns } from '../persons-manage-data';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  getTrainApi,
  getCourseMethodApi,
  getTrainPlanUsernamApi,
} from '#/api/equipment/persons-manage';
import { ref, watch } from 'vue';
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import { Button, Space, message } from 'ant-design-vue';
import { useVbenModal } from '@vben/common-ui';
import TrainModal from './train-modal.vue';
import OneClickAuthorizeModal from './oneclick-authorize-modal.vue';
const [OneClickFormModal, oneClickAuthorizeModalApi] = useVbenModal({
  connectedComponent: OneClickAuthorizeModal,
  destroyOnClose: true,
});
interface RowType {
  [key: string]: any;
}
const props = defineProps<{
  clickRow: RowType;
}>();
const clickMethodRow = ref<RowType | null>(null);
const [TrainFormModal, trainFormModalApi] = useVbenModal({
  connectedComponent: TrainModal,
  destroyOnClose: true,
});
watch(
  () => props.clickRow,
  (row) => {
    if (row) {
      gridApi.query();
    }
  },
);
watch(clickMethodRow, (row) => {
  if (row) {
    fileGridApi.query();
  }
});
const gridOptions: VxeTableGridOptions<FormApi.Form> = {
  columns: trainColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        const data = props.clickRow
          ? await getTrainApi([props.clickRow.USRNAM])
          : [];
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  keepSource: true,
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
const gridEvents: VxeGridListeners<FormApi.Form> = {
  currentChange: async ({ row }) => {
    if (row) {
      clickMethodRow.value = row;
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: gridEvents,
  tableTitle: '培训经历',
});
const fileGridOptions: VxeTableGridOptions<FormApi.Form> = {
  columns: courseColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!props.clickRow || !clickMethodRow.value) {
          return [];
        }
        const params = [clickMethodRow.value.COURSECODE];
        const data = await getCourseMethodApi(params);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  keepSource: true,
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const [FileGrid, fileGridApi] = useVbenVxeGrid({
  gridOptions: fileGridOptions,
  gridEvents: {},
  tableTitle: '课程方法',
});
const onRefresh = () => {
  gridApi.query();
  fileGridApi.query();
};
const addTraining = () => {
  trainFormModalApi.setData({ clickRow: props.clickRow }).open();
};
const batchAppend = async () => {
  if (!clickMethodRow.value) {
    message.warning('请先选择培训记录');
    return;
  }
  const params = [[clickMethodRow.value.ORIGREC], [props.clickRow.USRNAM]];
  const res = await getTrainPlanUsernamApi(params);
  let usrnam = [];
  if (res && res.length > 0) {
    usrnam = res;
  }
  oneClickAuthorizeModalApi
    .setData({
      clickRow: clickMethodRow.value,
      usrnam: usrnam,
      type: 'train',
      personRow: props.clickRow,
    })
    .open();
};
</script>
<template>
  <OneClickFormModal @success="onRefresh" />

  <TrainFormModal @success="onRefresh" />
  <Grid class="h-[47%]">
    <template #toolbar-actions>
      <Space :size="[8, 0]" wrap>
        <Button type="primary" @click="addTraining">
          {{ $t('equipment.persons-manage.addTraining') }}
        </Button>
        <Button type="primary" @click="batchAppend">
          {{ $t('equipment.persons-manage.batchAppend') }}
        </Button>
      </Space>
    </template>
  </Grid>
  <FileGrid class="h-[47.5%]"> </FileGrid>
</template>
