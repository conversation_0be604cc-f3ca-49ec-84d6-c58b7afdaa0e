<script lang="ts" setup>
import { reactive, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { CheckboxGroup } from 'ant-design-vue';

const emit = defineEmits(['success']);

const type = ref();

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    modalApi.lock();
    try {
      emit('success');

      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      type.value = modalApi.getData<Record<string, any>>();
    }
  },
});
const options = reactive([
  1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
]);
const checkedList = ref<number[]>([]);
</script>
<template>
  <Modal title="选择设备ID" class="h-1/2">
    <CheckboxGroup
      v-model:value="checkedList"
      name="checkboxgroup"
      :options="options"
      style="display: grid"
    />
    <!-- <ul class="overflow-auto">
      <li
        v-for="item in items"
        :key="item.name"
        class="list-item hover:bg-blue-200"
        :style="{
          backgroundColor:
            selectedItem.name === item.name ? '#f0f0f0' : 'white',
        }"
        @click="clickItem(item)"
      >
        {{ item.name }}
      </li>
    </ul> -->
  </Modal>
</template>
<style>
.list {
  padding: 0;
  list-style-type: none;
}

.list-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #ccc;
  transition: background-color 0.3s;
}

.list-item:hover {
  background-color: #f0f0f0;
}

.list-item.bg-blue-100 {
  color: #000;
}
</style>
