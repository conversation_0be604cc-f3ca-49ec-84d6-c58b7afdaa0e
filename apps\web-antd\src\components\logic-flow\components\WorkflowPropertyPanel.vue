<script setup lang="ts">
import type {
  Approver,
  ConditionRule,
  WorkflowEdge,
  WorkflowNode,
} from '../types/workflow';

import { computed, ref, watch } from 'vue';

import { message } from 'ant-design-vue';
import { Delete, Plus } from 'lucide-vue-next';

import { CONDITION_OPERATORS } from '../config/nodes';

interface Props {
  element: WorkflowEdge | WorkflowNode;
  elementType: 'edge' | 'node';
  approverDataSource?: Approver[];
}

const props = defineProps<Props>();

const emit = defineEmits<{
  update: [element: WorkflowEdge | WorkflowNode];
}>();

// 响应式数据
const nodeForm = ref({
  name: '',
  description: '',
  approvers: [] as string[],
  approvalType: 'single',
  timeLimit: 24,
  autoApprove: false,
  conditions: [] as ConditionRule[],
  branches: [] as string[],
});

const edgeForm = ref({
  label: '',
  condition: '',
});

// 计算属性
const nodeElement = computed(() =>
  props.elementType === 'node' ? (props.element as WorkflowNode) : null,
);

const edgeElement = computed(() =>
  props.elementType === 'edge' ? (props.element as WorkflowEdge) : null,
);

const approverOptions = computed(
  () =>
    props.approverDataSource?.map((approver) => ({
      label: approver.name,
      value: approver.id,
    })) || [],
);

const operatorOptions = computed(() =>
  CONDITION_OPERATORS.map((op) => ({
    label: op.label,
    value: op.value,
  })),
);

// 监听元素变化
watch(
  () => props.element,
  (newElement) => {
    if (props.elementType === 'node') {
      const node = newElement as WorkflowNode;
      nodeForm.value = {
        name: node.properties.name || '',
        description: node.properties.description || '',
        approvers: node.properties.approvers?.map((a) => a.id) || [],
        approvalType: node.properties.approvalType || 'single',
        timeLimit: node.properties.timeLimit || 24,
        autoApprove: node.properties.autoApprove || false,
        conditions: node.properties.conditions || [],
        branches: node.properties.branches || ['分支1', '分支2'],
      };
    } else {
      const edge = newElement as WorkflowEdge;
      edgeForm.value = {
        label: edge.properties?.label || '',
        condition: edge.properties?.condition || '',
      };
    }
  },
  { immediate: true },
);

// 方法
const handleUpdate = () => {
  if (props.elementType === 'node') {
    const node = { ...nodeElement.value! };
    node.properties = {
      ...node.properties,
      name: nodeForm.value.name,
      description: nodeForm.value.description,
    };

    if (node.type === 'approval') {
      node.properties.approvers = nodeForm.value.approvers.map((id) => {
        const approver = props.approverDataSource?.find((a) => a.id === id);
        return approver || { id, name: id };
      });
      node.properties.approvalType = nodeForm.value.approvalType;
      node.properties.timeLimit = nodeForm.value.timeLimit;
      node.properties.autoApprove = nodeForm.value.autoApprove;
    }

    if (node.type === 'condition') {
      node.properties.conditions = nodeForm.value.conditions;
    }

    if (node.type === 'parallel') {
      node.properties.branches = nodeForm.value.branches;
    }

    emit('update', node);
  } else {
    const edge = { ...edgeElement.value! };
    edge.properties = {
      ...edge.properties,
      label: edgeForm.value.label,
      condition: edgeForm.value.condition,
    };
    emit('update', edge);
  }
};

const addCondition = () => {
  nodeForm.value.conditions.push({
    field: '',
    operator: 'eq',
    value: '',
  });
  handleUpdate();
};

const removeCondition = (index: number) => {
  nodeForm.value.conditions.splice(index, 1);
  handleUpdate();
};

const addBranch = () => {
  const branchCount = nodeForm.value.branches.length;
  nodeForm.value.branches.push(`分支${branchCount + 1}`);
  handleUpdate();
};

const removeBranch = (index: number) => {
  nodeForm.value.branches.splice(index, 1);
  handleUpdate();
};

const handleReset = () => {
  // 重置表单到初始状态
  watch(
    () => props.element,
    () => {},
    { immediate: true },
  );
  message.info('已重置');
};

const handleSave = () => {
  handleUpdate();
  message.success('保存成功');
};
</script>

<template>
  <div class="workflow-property-panel">
    <!-- 节点属性 -->
    <div v-if="elementType === 'node'" class="property-content">
      <a-form :model="nodeForm" layout="vertical">
        <!-- 基础属性 -->
        <a-form-item label="节点名称">
          <a-input
            v-model:value="nodeForm.name"
            placeholder="请输入节点名称"
            @change="handleUpdate"
          />
        </a-form-item>

        <a-form-item label="节点描述">
          <a-textarea
            v-model:value="nodeForm.description"
            placeholder="请输入节点描述"
            :rows="3"
            @change="handleUpdate"
          />
        </a-form-item>

        <!-- 审批节点特有属性 -->
        <template v-if="nodeElement?.type === 'approval'">
          <a-divider>审批设置</a-divider>

          <a-form-item label="审批人">
            <a-select
              v-model:value="nodeForm.approvers"
              mode="multiple"
              placeholder="请选择审批人"
              :options="approverOptions"
              @change="handleUpdate"
            />
          </a-form-item>

          <a-form-item label="审批类型">
            <a-radio-group
              v-model:value="nodeForm.approvalType"
              @change="handleUpdate"
            >
              <a-radio value="single">单人审批</a-radio>
              <a-radio value="all">全部审批</a-radio>
              <a-radio value="majority">多数审批</a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item label="审批时限（小时）">
            <a-input-number
              v-model:value="nodeForm.timeLimit"
              :min="1"
              :max="720"
              placeholder="24"
              style="width: 100%"
              @change="handleUpdate"
            />
          </a-form-item>

          <a-form-item>
            <a-checkbox
              v-model:checked="nodeForm.autoApprove"
              @change="handleUpdate"
            >
              超时自动审批
            </a-checkbox>
          </a-form-item>
        </template>

        <!-- 条件节点特有属性 -->
        <template v-if="nodeElement?.type === 'condition'">
          <a-divider>条件设置</a-divider>

          <div class="condition-rules">
            <div
              v-for="(condition, index) in nodeForm.conditions"
              :key="index"
              class="condition-rule"
            >
              <a-row :gutter="8">
                <a-col :span="8">
                  <a-input
                    v-model:value="condition.field"
                    placeholder="字段名"
                    @change="handleUpdate"
                  />
                </a-col>
                <a-col :span="6">
                  <a-select
                    v-model:value="condition.operator"
                    placeholder="操作符"
                    :options="operatorOptions"
                    @change="handleUpdate"
                  />
                </a-col>
                <a-col :span="8">
                  <a-input
                    v-model:value="condition.value"
                    placeholder="值"
                    @change="handleUpdate"
                  />
                </a-col>
                <a-col :span="2">
                  <a-button
                    type="text"
                    danger
                    size="small"
                    @click="removeCondition(index)"
                  >
                    <template #icon><Delete class="h-4 w-4" /></template>
                  </a-button>
                </a-col>
              </a-row>
            </div>

            <a-button type="dashed" block @click="addCondition">
              <template #icon><Plus class="h-4 w-4" /></template>
              添加条件
            </a-button>
          </div>
        </template>

        <!-- 并行节点特有属性 -->
        <template v-if="nodeElement?.type === 'parallel'">
          <a-divider>并行设置</a-divider>

          <div class="branch-settings">
            <div
              v-for="(branch, index) in nodeForm.branches"
              :key="index"
              class="branch-item"
            >
              <a-input
                v-model:value="nodeForm.branches[index]"
                :placeholder="`分支 ${index + 1}`"
                @change="handleUpdate"
              />
              <a-button
                v-if="nodeForm.branches.length > 2"
                type="text"
                danger
                size="small"
                @click="removeBranch(index)"
              >
                <template #icon><Delete /></template>
              </a-button>
            </div>

            <a-button type="dashed" block @click="addBranch">
              <template #icon><Plus /></template>
              添加分支
            </a-button>
          </div>
        </template>
      </a-form>
    </div>

    <!-- 连线属性 -->
    <div v-else-if="elementType === 'edge'" class="property-content">
      <a-form :model="edgeForm" layout="vertical">
        <a-form-item label="连线标签">
          <a-input
            v-model:value="edgeForm.label"
            placeholder="请输入连线标签"
            @change="handleUpdate"
          />
        </a-form-item>

        <a-form-item label="条件表达式">
          <a-textarea
            v-model:value="edgeForm.condition"
            placeholder="请输入条件表达式"
            :rows="3"
            @change="handleUpdate"
          />
        </a-form-item>
      </a-form>
    </div>

    <!-- 操作按钮 -->
    <div class="property-actions">
      <a-space>
        <a-button @click="handleReset">重置</a-button>
        <a-button type="primary" @click="handleSave">保存</a-button>
      </a-space>
    </div>
  </div>
</template>

<style scoped>
.workflow-property-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.property-content {
  flex: 1;
  padding: 16px 0;
  overflow-y: auto;
}

.condition-rules {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.condition-rule {
  padding: 8px;
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}

.branch-settings {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.branch-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.property-actions {
  padding: 16px;
  background: #fafafa;
  border-top: 1px solid #e8e8e8;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  font-size: 13px;
  font-weight: 500;
}

:deep(.ant-divider) {
  margin: 16px 0 12px;
  font-size: 12px;
  color: #8c8c8c;
}
</style>
