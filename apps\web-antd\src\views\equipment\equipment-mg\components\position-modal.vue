<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button } from 'ant-design-vue';
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from 'lucide-vue-next';

import {
  editEqServGroupsApi,
  getDsServGroupsApi,
  getEquipManagerListApi,
} from '#/api/equipment/equipment-mg';
import { useEquipmentStore } from '#/store';

const emit = defineEmits(['success']);

const equipmentStore = useEquipmentStore();
interface EquipmentRow {
  EQID: string;
  [key: string]: any;
}
const currentRow = computed<EquipmentRow | null>(
  () => equipmentStore.getCurrentRow as EquipmentRow | null,
);
onMounted(async () => {
  const res = await getEquipManagerListApi();
  console.warn(res);
});

interface PersonItem {
  TEXT: string;
  VALUE: string;
}
const originEvents: any[] = [];
const rightItems = ref<PersonItem[]>([]);
const leftItems = ref<PersonItem[]>([]);
const tableData = ref([]);
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    modalApi.lock();
    try {
      const arrNewEvents: string[] = rightItems.value.map(
        (item: PersonItem) => item.VALUE,
      );
      const params = [currentRow.value?.EQID ?? '', arrNewEvents];
      await editEqServGroupsApi(params);
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  async onOpenChange(isOpen) {
    if (isOpen && currentRow.value && currentRow.value.EQID) {
      let leftRes = await getDsServGroupsApi([currentRow.value.EQID]);
      tableData.value = modalApi.getData().tableData.items || [];
      rightItems.value = tableData.value.map((item: any) => ({
        TEXT: item.SERVGRP,
        VALUE: item.SERVGRP,
      }));
      originEvents.push(...leftRes.items);
      leftRes = leftRes.items.filter((item: any) => {
        return !tableData.value.some(
          (tableItem: any) => tableItem.SERVGRP === item.SERVGRP,
        );
      });
      leftItems.value = leftRes.items.map((item: any) => ({
        TEXT: item.SERVGRP,
        VALUE: item.SERVGRP,
      }));
    }
    //   rightItems.value = tableData.value.map((item: any) => ({
    //     TEXT: item.MAINTENANCEEVENT,
    //     VALUE: item.MAINTENANCEEVENT,
    //   }));

    //   let leftRes = await getMaintenanceEventsApi([currentRow.value.EQTYPE]);
    //   originEvents.push(...leftRes);
    //   leftRes = leftRes.filter((item: any) => {
    //     return !tableData.value.some(
    //       (tableItem: any) =>
    //         tableItem.MAINTENANCEEVENT === item.MAINTENANCEEVENT,
    //     );
    //   });
    //   leftItems.value = leftRes.items.map((item: any) => ({
    //     TEXT: item.MAINTENANCEEVENT,
    //     VALUE: item.MAINTENANCEEVENT,
    //   }));
    // } else {
    //   rightItems.value = [];
    //   leftItems.value = [];
    // }
  },
});

const leftSelectedItem = ref<PersonItem>({
  TEXT: '',
  VALUE: '',
});
const rightSelectedItem = ref<PersonItem>({
  TEXT: '',
  VALUE: '',
});
const clickLeftItem = (item: PersonItem) => {
  leftSelectedItem.value = item;

  console.warn(item);
};
const clickRightItem = (item: PersonItem) => {
  rightSelectedItem.value = item;

  console.warn(item);
};
const removeToRight = () => {
  if (leftSelectedItem.value.VALUE) {
    leftItems.value = leftItems.value.filter(
      (item) => item.VALUE !== leftSelectedItem.value.VALUE,
    );

    rightItems.value.push(leftSelectedItem.value);
  }
};
const removeToLeft = () => {
  if (rightSelectedItem.value.VALUE) {
    rightItems.value = rightItems.value.filter(
      (item) => item.VALUE !== rightSelectedItem.value.VALUE,
    );
    leftItems.value.push(rightSelectedItem.value);
  }
};
const removeAllToRight = () => {
  rightItems.value.push(...leftItems.value);
  leftItems.value = [];
};
const removeAllToLeft = () => {
  leftItems.value.push(...rightItems.value);
  rightItems.value = [];
};
</script>
<template>
  <Modal title="选择计划事件" class="h-1/2 w-2/5">
    <div class="flex h-full justify-around overflow-hidden">
      <div class="h-full w-2/5 overflow-hidden">
        <div>计划事件</div>
        <ul class="h-full w-full overflow-auto">
          <li
            v-for="item in leftItems"
            :key="item.VALUE"
            class="list-item hover:bg-blue-200"
            :style="{
              backgroundColor:
                leftSelectedItem.VALUE === item.VALUE ? '#f0f0f0' : 'white',
            }"
            @click="clickLeftItem(item)"
          >
            {{ item.TEXT }}
          </li>
        </ul>
      </div>

      <div class="flex flex-col justify-evenly">
        <div>
          <Button type="primary" @click="removeToRight">
            <ChevronRight class="ml-auto h-6 w-6" />
          </Button>
        </div>
        <div>
          <Button type="primary" @click="removeToLeft">
            <ChevronLeft class="ml-auto h-6 w-6" />
          </Button>
        </div>

        <div>
          <Button type="primary" @click="removeAllToRight">
            <ChevronsRight class="ml-auto h-6 w-6" />
          </Button>
        </div>
        <div>
          <Button type="primary" @click="removeAllToLeft">
            <ChevronsLeft class="ml-auto h-6 w-6" />
          </Button>
        </div>
      </div>
      <ul class="mt-5 h-full w-2/5 overflow-auto">
        <li
          v-for="item in rightItems"
          :key="item.VALUE"
          class="list-item hover:bg-blue-200"
          :style="{
            backgroundColor:
              rightSelectedItem.VALUE === item.VALUE ? '#f0f0f0' : 'white',
          }"
          @click="clickRightItem(item)"
        >
          {{ item.TEXT }}
        </li>
      </ul>
    </div>
  </Modal>
</template>
<style>
.list {
  padding: 0;
  list-style-type: none;
}

.list-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #ccc;
  transition: background-color 0.3s;
}

.list-item:hover {
  background-color: #f0f0f0;
}

.list-item.bg-blue-100 {
  color: #000;
}
</style>
