<script lang="ts" setup>
import type { JobDescriptionsTableApi } from '#/api/basic-static-tables/job-descriptions';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { addJobDescriptionsApi } from '#/api/basic-static-tables/job-descriptions';
import { $t } from '#/locales';

import { useSchema } from './job-description-data';

const emit = defineEmits(['success']);

const formData = ref<JobDescriptionsTableApi.JobDescriptions>();
const getTitle = computed(() => {
  return $t('ui.actionTitle.create', [
    $t('basicStatic.jobDescriptions.jobDescriptions'),
  ]);
});

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: useSchema(),
  showDefaultActions: false,
});

function resetForm() {
  formApi.resetForm();
  formApi.setValues(formData.value || {});
}

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    modalApi.lock();
    try {
      await addJobDescriptionsApi(data);
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<JobDescriptionsTableApi.JobDescriptions>();
      if (data) {
        formData.value = data;
        formApi.setValues(formData.value);
      }
    }
  },
});
</script>

<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
    <template #prepend-footer>
      <div class="flex-auto">
        <Button type="primary" danger @click="resetForm">
          {{ $t('common.reset') }}
        </Button>
      </div>
    </template>
  </Modal>
</template>
