import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { SupplierApi } from '#/api/';

import { $t } from '#/locales';

export function useSupplierColumns(): VxeTableGridOptions<SupplierApi.Supplier>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'origrec',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'SUPTYPE',
      title: $t('basic-static-tables.supplier.suptype'),
      editRender: {
        name: 'select',
        options: [
          { value: 'GY', label: '供应商' },
          { value: 'SC', label: '生产商' },
        ],
      },
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue === 'GY' ? '供应商' : '生产商';
      },
    },
    {
      align: 'center',
      field: 'SUPPCODE',
      width: 200,
      title: $t('basic-static-tables.supplier.suppcode'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SUPPNAM',
      title: $t('basic-static-tables.supplier.suppnam'),
      editRender: { name: 'input' },
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'SUPPADD',
      title: $t('basic-static-tables.supplier.suppadd'),
      editRender: { name: 'input' },
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'SUPPADD_A',
      title: $t('basic-static-tables.supplier.suppadd_a'),
      editRender: { name: 'input' },
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'SUPPCITY',
      title: $t('basic-static-tables.supplier.suppcity'),
      editRender: { name: 'input' },
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'SUPPSTNAM',
      title: $t('basic-static-tables.supplier.suppstnam'),
      editRender: { name: 'input' },
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'SUPPST',
      title: $t('basic-static-tables.supplier.suppst'),
      editRender: { name: 'input' },
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'SUPZIP',
      title: $t('basic-static-tables.supplier.suppzip'),
      editRender: { name: 'input' },
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'COUNTRYNAME',
      title: $t('basic-static-tables.supplier.countryname'),
      editRender: { name: 'input' },
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'PRIMARYPHONE',
      title: $t('basic-static-tables.supplier.primaryphone'),
      editRender: { name: 'input' },
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'PRIMARYFAX',
      title: $t('basic-static-tables.supplier.primaryfax'),
      editRender: { name: 'input' },
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'DEFAULTCONTACT',
      title: $t('basic-static-tables.supplier.defaultcontact'),
      editRender: { name: 'input' },
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'EMAIL',
      title: $t('basic-static-tables.supplier.email'),
      editRender: { name: 'input' },
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'URL',
      title: $t('basic-static-tables.supplier.url'),
      editRender: { name: 'input' },
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'COMMENTS',
      title: $t('basic-static-tables.supplier.comments'),
      editRender: { name: 'input' },
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'CITY_CODE',
      title: $t('basic-static-tables.supplier.city-code'),
      editRender: { name: 'input' },
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('basic-static-tables.operation'),
      width: 140,
    },
  ];
}
export function useSupplierFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        // placeholder: 'Please enter code',
      },
      fieldName: 'SUPPCODE',
      label: $t('basic-static-tables.supplier.suppcode'),
    },
    {
      component: 'Input',
      fieldName: 'SUPPNAM',
      componentProps: {
        // placeholder: 'Please enter name',
      },
      label: $t('basic-static-tables.supplier.suppnam'),
    },
  ];
}
