import type { WorkflowData, ValidationResult, WorkflowNode, WorkflowEdge } from '../types/workflow';

/**
 * 工作流验证器
 */
export class WorkflowValidator {
  private data: WorkflowData;
  private errors: ValidationResult['errors'] = [];

  constructor(data: WorkflowData) {
    this.data = data;
  }

  /**
   * 验证工作流
   */
  validate(): ValidationResult {
    this.errors = [];

    // 基础验证
    this.validateBasicStructure();
    
    // 节点验证
    this.validateNodes();
    
    // 连线验证
    this.validateEdges();
    
    // 流程完整性验证
    this.validateFlowIntegrity();

    return {
      valid: this.errors.filter(e => e.type === 'error').length === 0,
      errors: this.errors,
    };
  }

  /**
   * 验证基础结构
   */
  private validateBasicStructure(): void {
    if (!this.data.nodes || this.data.nodes.length === 0) {
      this.addError('工作流至少需要包含一个节点');
      return;
    }

    // 检查是否有开始节点
    const startNodes = this.data.nodes.filter(node => node.type === 'start');
    if (startNodes.length === 0) {
      this.addError('工作流必须包含一个开始节点');
    } else if (startNodes.length > 1) {
      this.addError('工作流只能包含一个开始节点');
    }

    // 检查是否有结束节点
    const endNodes = this.data.nodes.filter(node => node.type === 'end');
    if (endNodes.length === 0) {
      this.addWarning('建议添加结束节点');
    }
  }

  /**
   * 验证节点
   */
  private validateNodes(): void {
    this.data.nodes.forEach(node => {
      this.validateNode(node);
    });
  }

  /**
   * 验证单个节点
   */
  private validateNode(node: WorkflowNode): void {
    // 验证节点ID
    if (!node.id) {
      this.addError('节点ID不能为空', node.id);
      return;
    }

    // 验证节点类型
    if (!node.type) {
      this.addError('节点类型不能为空', node.id);
      return;
    }

    // 验证节点属性
    this.validateNodeProperties(node);
  }

  /**
   * 验证节点属性
   */
  private validateNodeProperties(node: WorkflowNode): void {
    const { type, properties } = node;

    switch (type) {
      case 'approval':
        this.validateApprovalNode(node);
        break;
      case 'condition':
        this.validateConditionNode(node);
        break;
      case 'parallel':
        this.validateParallelNode(node);
        break;
    }

    // 验证节点名称
    if (!properties.name || properties.name.trim() === '') {
      this.addWarning('建议为节点设置名称', node.id);
    }
  }

  /**
   * 验证审批节点
   */
  private validateApprovalNode(node: WorkflowNode): void {
    const { properties } = node;

    if (!properties.approvers || properties.approvers.length === 0) {
      this.addError('审批节点必须设置审批人', node.id);
    }

    if (properties.timeLimit && properties.timeLimit <= 0) {
      this.addError('审批时限必须大于0', node.id);
    }

    // 验证审批人信息
    if (properties.approvers) {
      properties.approvers.forEach((approver, index) => {
        if (!approver.id || !approver.name) {
          this.addError(`审批人${index + 1}信息不完整`, node.id);
        }
      });
    }
  }

  /**
   * 验证条件节点
   */
  private validateConditionNode(node: WorkflowNode): void {
    const { properties } = node;

    if (!properties.conditions || properties.conditions.length === 0) {
      this.addError('条件节点必须设置判断条件', node.id);
      return;
    }

    // 验证条件规则
    properties.conditions.forEach((condition, index) => {
      if (!condition.field || !condition.operator) {
        this.addError(`条件${index + 1}配置不完整`, node.id);
      }
    });
  }

  /**
   * 验证并行节点
   */
  private validateParallelNode(node: WorkflowNode): void {
    const { properties } = node;

    if (!properties.branches || properties.branches.length < 2) {
      this.addError('并行节点至少需要2个分支', node.id);
    }
  }

  /**
   * 验证连线
   */
  private validateEdges(): void {
    this.data.edges.forEach(edge => {
      this.validateEdge(edge);
    });
  }

  /**
   * 验证单条连线
   */
  private validateEdge(edge: WorkflowEdge): void {
    // 验证连线ID
    if (!edge.id) {
      this.addError('连线ID不能为空');
      return;
    }

    // 验证源节点和目标节点
    if (!edge.sourceNodeId || !edge.targetNodeId) {
      this.addError('连线必须指定源节点和目标节点', undefined, edge.id);
      return;
    }

    // 检查节点是否存在
    const sourceNode = this.data.nodes.find(n => n.id === edge.sourceNodeId);
    const targetNode = this.data.nodes.find(n => n.id === edge.targetNodeId);

    if (!sourceNode) {
      this.addError(`连线的源节点不存在: ${edge.sourceNodeId}`, undefined, edge.id);
    }

    if (!targetNode) {
      this.addError(`连线的目标节点不存在: ${edge.targetNodeId}`, undefined, edge.id);
    }

    // 验证连线规则
    if (sourceNode && targetNode) {
      this.validateConnectionRules(sourceNode, targetNode, edge);
    }
  }

  /**
   * 验证连线规则
   */
  private validateConnectionRules(
    sourceNode: WorkflowNode,
    targetNode: WorkflowNode,
    edge: WorkflowEdge
  ): void {
    // 开始节点不能作为目标节点
    if (targetNode.type === 'start') {
      this.addError('开始节点不能作为连线的目标节点', undefined, edge.id);
    }

    // 结束节点不能作为源节点
    if (sourceNode.type === 'end') {
      this.addError('结束节点不能作为连线的源节点', undefined, edge.id);
    }

    // 不能自连接
    if (sourceNode.id === targetNode.id) {
      this.addError('节点不能连接到自身', undefined, edge.id);
    }
  }

  /**
   * 验证流程完整性
   */
  private validateFlowIntegrity(): void {
    // 检查孤立节点
    this.checkIsolatedNodes();
    
    // 检查循环依赖
    this.checkCircularDependency();
    
    // 检查可达性
    this.checkReachability();
  }

  /**
   * 检查孤立节点
   */
  private checkIsolatedNodes(): void {
    this.data.nodes.forEach(node => {
      const hasIncoming = this.data.edges.some(edge => edge.targetNodeId === node.id);
      const hasOutgoing = this.data.edges.some(edge => edge.sourceNodeId === node.id);

      if (!hasIncoming && !hasOutgoing && node.type !== 'start') {
        this.addWarning('发现孤立节点', node.id);
      } else if (!hasIncoming && node.type !== 'start') {
        this.addWarning('节点没有输入连线', node.id);
      } else if (!hasOutgoing && node.type !== 'end') {
        this.addWarning('节点没有输出连线', node.id);
      }
    });
  }

  /**
   * 检查循环依赖
   */
  private checkCircularDependency(): void {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const dfs = (nodeId: string): boolean => {
      if (recursionStack.has(nodeId)) {
        return true; // 发现循环
      }

      if (visited.has(nodeId)) {
        return false;
      }

      visited.add(nodeId);
      recursionStack.add(nodeId);

      // 遍历所有出边
      const outgoingEdges = this.data.edges.filter(edge => edge.sourceNodeId === nodeId);
      for (const edge of outgoingEdges) {
        if (dfs(edge.targetNodeId)) {
          return true;
        }
      }

      recursionStack.delete(nodeId);
      return false;
    };

    for (const node of this.data.nodes) {
      if (!visited.has(node.id)) {
        if (dfs(node.id)) {
          this.addError('工作流中存在循环依赖');
          break;
        }
      }
    }
  }

  /**
   * 检查可达性
   */
  private checkReachability(): void {
    const startNodes = this.data.nodes.filter(node => node.type === 'start');
    if (startNodes.length === 0) return;

    const reachable = new Set<string>();
    const queue = [startNodes[0].id];

    while (queue.length > 0) {
      const nodeId = queue.shift()!;
      if (reachable.has(nodeId)) continue;

      reachable.add(nodeId);

      // 添加所有可达的节点
      const outgoingEdges = this.data.edges.filter(edge => edge.sourceNodeId === nodeId);
      outgoingEdges.forEach(edge => {
        if (!reachable.has(edge.targetNodeId)) {
          queue.push(edge.targetNodeId);
        }
      });
    }

    // 检查不可达的节点
    this.data.nodes.forEach(node => {
      if (!reachable.has(node.id) && node.type !== 'start') {
        this.addWarning('节点从开始节点不可达', node.id);
      }
    });
  }

  /**
   * 添加错误
   */
  private addError(message: string, nodeId?: string, edgeId?: string): void {
    this.errors.push({
      type: 'error',
      message,
      nodeId,
      edgeId,
    });
  }

  /**
   * 添加警告
   */
  private addWarning(message: string, nodeId?: string, edgeId?: string): void {
    this.errors.push({
      type: 'warning',
      message,
      nodeId,
      edgeId,
    });
  }
}

/**
 * 验证工作流数据
 */
export function validateWorkflow(data: WorkflowData): ValidationResult {
  const validator = new WorkflowValidator(data);
  return validator.validate();
}
