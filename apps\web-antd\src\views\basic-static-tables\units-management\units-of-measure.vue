<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { UnitsManagementApi } from '#/api/basic-static-tables/units-management';

import { computed, ref } from 'vue';

import { confirm, Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteUnitsOfMeasure,
  getUnitsOfMeasureList,
} from '#/api/basic-static-tables/units-management';

import AddUnitsOfMeasureForm from './add-units-of-measure.vue';
import { useUnitsOfMeasureColumns } from './units-management-data';

const measureTypesData = ref<UnitsManagementApi.MeasureTypes>();

const gridOptions: VxeTableGridOptions<UnitsManagementApi.UnitsOfMeasure> = {
  columns: useUnitsOfMeasureColumns(),
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        const data = drawerApi.getData<UnitsManagementApi.MeasureTypes>();
        if (!data) {
          return;
        }
        const measureType = data.MEASURE_TYPE;
        return await getUnitsOfMeasureList(measureType);
      },
    },
  },
  showOverflow: true,
  exportConfig: {},
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddUnitsOfMeasureForm,
  destroyOnClose: true,
});

function hasEditStatus(row: UnitsManagementApi.UnitsOfMeasure) {
  return gridApi.grid?.isEditByRow(row);
}

function editRowEvent(row: UnitsManagementApi.UnitsOfMeasure) {
  gridApi.grid?.setEditRow(row);
}

async function saveRowEvent() {
  await gridApi.grid?.clearEdit();
  gridApi.setLoading(true);
  // updateLocationTypeSubLoc(row);
  setTimeout(() => {
    gridApi.setLoading(false);
    message.success({
      content: `保存成功！`,
    });
  }, 600);
}

const cancelRowEvent = (_row: UnitsManagementApi.UnitsOfMeasure) => {
  gridApi.grid?.clearEdit();
};

const [Drawer, drawerApi] = useVbenDrawer({
  onConfirm: onSubmit,
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<UnitsManagementApi.MeasureTypes>();
      if (data) {
        measureTypesData.value = data;
        // gridApi?.query();
      } else {
        gridApi?.reload();
      }
    }
  },
  showCancelButton: false,
  showConfirmButton: false,
});

async function onSubmit() {
  drawerApi.close();
}

const getDrawerTitle = computed(() => {
  if (measureTypesData.value) {
    return `查看 ${measureTypesData.value?.MEASURE_TYPE}`;
  }
  return '计量单位';
});

function onCreate() {
  formModalApi
    .setData({ MEASURE_TYPE: measureTypesData.value?.MEASURE_TYPE })
    .open();
}

async function onDelete() {
  // 获取选中行
  const aOrigrec: number[] = gridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.ORIGREC);

  if (aOrigrec.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${aOrigrec.length} 条数据吗？`,
      icon: 'warning',
      centered: false,
    });

    await deleteUnitsOfMeasure(aOrigrec);

    message.success('删除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

function onRefresh() {
  gridApi.query();
}
</script>

<template>
  <Drawer class="w-full max-w-[1000px]" :title="getDrawerTitle">
    <FormModal @success="onRefresh" />
    <Page auto-content-height>
      <Grid>
        <template #toolbar-actions>
          <Space>
            <Button type="primary" @click="onCreate">
              {{ $t('ui.actionTitle.create') }}
            </Button>
            <Button type="primary" danger @click="onDelete">
              {{ $t('ui.actionTitle.delete') }}
            </Button>
          </Space>
        </template>
        <template #action="{ row }">
          <template v-if="hasEditStatus(row)">
            <Button type="link" @click="saveRowEvent()">
              {{ $t('basic-static-tables.save') }}
            </Button>
            <Button type="link" @click="cancelRowEvent(row)">
              {{ $t('basic-static-tables.cancel') }}
            </Button>
          </template>
          <template v-else>
            <Button type="link" @click="editRowEvent(row)">
              {{ $t('basic-static-tables.edit') }}
            </Button>
          </template>
        </template>
      </Grid>
    </Page>
  </Drawer>
</template>
