<script setup lang="ts">
import type { SampleGroupsApi } from '#/api/business-static-tables/sample-groups';

import { ref, watch } from 'vue';

import { confirm } from '@vben/common-ui';

import {
  But<PERSON>,
  Col,
  message,
  Row,
  Space,
  TabPane,
  Tabs,
} from 'ant-design-vue';

import {
  getProfiles,
  insertTests,
  updateExpirationDate,
} from '#/api/business-static-tables/sample-groups';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import TestTransferModal from './edit-test-list.vue';
import MethodEqTypeGrid from './method-eqtype.vue';
import MethodMetGrid from './method-met.vue';
import MethodTestGrid from './method-test.vue';
import {
  useSpTestsColumns,
  useSpTestsFilterSchema,
} from './sample-groups-data';
import SpAnalyteGrid from './sp-analytes.vue';

const props = defineProps<{
  drawNo: number;
  spCode: number;
}>();

const emit = defineEmits(['success']);

const testTransferModalRef = ref();
const testTransferModalVisible = ref(false);
const methodTestGridRef = ref();
const methodEqtypeRef = ref();
const methodMetRef = ref();
const profile = ref<string>('');
const aTestCodes = ref<string[]>([]);

watch(
  () => props.spCode,
  (_val) => {
    onRefresh();
  },
);
const spTestColums = useSpTestsColumns();
const spTestFilterSchema = useSpTestsFilterSchema();
const spTestQueryData = async () => {
  if (!props.spCode) return [];
  return getProfiles(props.spCode, props.drawNo);
};
const spTestGridOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};

const {
  Grid: SpTestGrid,
  gridApi: spTestGridApi,
  CurrentRow,
} = useLimsGridsConfig<SampleGroupsApi.SpTests>(
  spTestColums,
  spTestFilterSchema,
  spTestQueryData,
  spTestGridOption,
);

function onRefresh() {
  spTestGridApi.query();
}

function openTestTransferModal() {
  const record = spTestGridApi.grid?.getCurrentRecord();
  if (!record) return;
  aTestCodes.value = getTestCodes();
  profile.value = record.PROFILE;
  testTransferModalRef.value?.open();
}

function getTestCodes() {
  const allData = spTestGridApi.grid?.getData(); // 获取表格所有行数据

  const testCodes = allData.map((item) => item.TESTCODE) as string[]; // 提取 TESTCODE 字段
  // console.log('所有 TESTCODE:', testCodes);
  return testCodes;
}

async function handleTestTransferSubmit(data: {
  selectedItems: any[];
  selectedKeys: string[];
}) {
  // message.success(`已提交选中项: ${data.selectedKeys.join(', ')}`);
  // 这里可以调用保存接口
  // 刷新检项列表
  const addTests = await insertTests(
    props.spCode,
    props.drawNo,
    profile.value,
    data.selectedKeys,
  );
  if (addTests === 'EXISTS') {
    message.warn($t('business-static-tables.sampleGroups.profileExists'));
  } else {
    if (addTests === 'SAMPLES') {
      message.warn($t('business-static-tables.sampleGroups.SAMPLES'));
    }

    if (addTests === 'OTHER_PROFILE') {
      message.warn($t('business-static-tables.sampleGroups.OTHER_PROFILE'));
    }
  }
  onRefresh();
}

async function onSynchronization() {
  try {
    await confirm({
      title: '确认同步',
      content: `确定要同步方法失效日期吗？`,
      icon: 'warning',
      centered: false,
    });

    await updateExpirationDate(props.spCode);
    message.success('同步成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

function onNext() {
  emit('success');
}
</script>

<template>
  <TestTransferModal
    ref="testTransferModalRef"
    :sp-code="props.spCode"
    :draw-no="props.drawNo"
    :profile="profile"
    :a-test-code="aTestCodes"
    @submit="handleTestTransferSubmit"
    @cancel="testTransferModalVisible = false"
  />
  <div class="h-[550px] w-full">
    <!--   <div class="h-full w-full"> -->
    <!-- 使用 a-row 实现行布局 -->
    <Row :gutter="16" class="h-full flex-nowrap">
      <!-- 左侧新 Grid 区域，高度自适应 -->
      <Col :span="10">
        <div class="h-[570px] overflow-auto">
          <SpTestGrid>
            <template #toolbar-actions>
              <Space>
                <Button type="primary" @click="openTestTransferModal">
                  {{ $t('business-static-tables.sampleGroups.editTestList') }}
                </Button>
                <Button type="default" @click="onSynchronization">
                  {{
                    $t('business-static-tables.sampleGroups.Synchronization')
                  }}
                </Button>
              </Space>
            </template>
          </SpTestGrid>
        </div>
      </Col>

      <!-- 右侧原有的 RecipeGrid -->
      <Col :span="14">
        <Tabs default-active-key="1">
          <TabPane key="1" tab="分析项">
            <div class="h-[500px] overflow-auto">
              <SpAnalyteGrid :current-test-row="CurrentRow" />
            </div>
          </TabPane>

          <!-- 第二个 TabPane -->
          <TabPane key="2" tab="方法">
            <div class="grid h-[500px] grid-rows-[1fr_2fr] gap-2">
              <!-- 上面一个 Grid -->
              <div class="h-[290px] overflow-auto">
                <MethodTestGrid
                  ref="methodTestGridRef"
                  :current-test-row="CurrentRow"
                />
              </div>

              <!-- 下面两个 Grid，使用 flex 布局 -->
              <div class="grid grid-cols-2 gap-2">
                <div class="h-[210px] overflow-hidden">
                  <MethodEqTypeGrid
                    ref="methodEqtypeRef"
                    :current-test-row="methodTestGridRef?.CurrentRow"
                  />
                </div>
                <div class="h-[210px] overflow-hidden">
                  <MethodMetGrid
                    ref="methodMetRef"
                    :current-test-row="methodTestGridRef?.CurrentRow"
                  />
                </div>
              </div>
            </div>
          </TabPane>
        </Tabs>
      </Col>
    </Row>
    <div class="mt-4 flex justify-end">
      <Button type="primary" @click="onNext"> 下一步 </Button>
    </div>
  </div>
</template>
