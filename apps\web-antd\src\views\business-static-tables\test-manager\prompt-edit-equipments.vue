<script lang="ts" setup>
import type { Recordable } from '@vben/types';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Transfer } from 'ant-design-vue';

import {
  $checkEquipOtherServGrpApi,
  $editEquipmentListApi,
  $getEquipmentsByTypeApi,
} from '#/api/business-static-tables/test-manager';
import { $t } from '#/locales';
import { confirm } from '#/utils/utils';

interface FormArgs {
  nTestCode: number;
  strDepartment: string;
  strEquipmentType: string;
  strMethod: string;
  strServiceGroup: string;
}

const modalArgs = ref<FormArgs>();
const eqData = ref<Recordable<string>[]>([]);
const targetKeys = ref<string[]>([]);
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    // 保存逻辑
    const selectedKeys = targetKeys.value; // 获取选中的仪器ID
    const aEquipOtherSGrp = await $checkEquipOtherServGrpApi({
      aSelectedEq: selectedKeys,
      nTestCode: modalArgs.value?.nTestCode || 0,
      sCrtServGrp: modalArgs.value?.strServiceGroup || '',
      sDept: modalArgs.value?.strDepartment || '',
      sMethod: modalArgs.value?.strMethod || '',
      sEqType: modalArgs.value?.strEquipmentType || '',
      arrDelData: [],
    });
    if (aEquipOtherSGrp && aEquipOtherSGrp.length > 1) {
      const sEq = aEquipOtherSGrp[0].join('\n');
      const sMethods = aEquipOtherSGrp[1].join('\n');
      const sContinue = await confirm(
        $t('business-static-tables.testManager.confirmDelEqOtherSG', [
          sEq,
          sMethods,
        ]),
        $t('commons.question'),
      );
      if (!sContinue) return;
    }
    await $editEquipmentListApi({
      selectedEquipments: selectedKeys,
      testCode: modalArgs.value?.nTestCode || 0,
      servgrp: modalArgs.value?.strServiceGroup || '',
      dept: modalArgs.value?.strDepartment || '',
      method: modalArgs.value?.strMethod || '',
    });
    modalApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      // 当模态框打开时，重新获取数据
      const data = modalApi.getData<FormArgs>();
      if (data) {
        modalArgs.value = data;
        getEqList();
      }
    }
  },
  title: '选择仪器',
});

const getEqList = async () => {
  const eData = modalArgs.value
    ? await $getEquipmentsByTypeApi(modalArgs.value)
    : [];
  const selectKeys = eData
    .filter((item) => item.SELECTED === 'Y')
    .map((item) => item.EQID);
  eqData.value = eData;
  targetKeys.value = selectKeys;
};
// const handleChange = (
//   keys: string[],
//   direction: string,
//   moveKeys: string[],
// ) => {
//   console.log(keys, direction, moveKeys, targetKeys.value);
// };
</script>

<template>
  <Modal>
    <div
      class="flex h-full w-full items-center justify-center"
      style="width: 100%; min-height: 500px; padding: 20px"
    >
      <Transfer
        v-model:target-keys="targetKeys"
        :data-source="eqData"
        show-search
        :list-style="{
          width: '100%',
          height: '500px',
        }"
        :render="(item) => `${item.EQID}`"
        style="width: 100%"
        :row-key="(record) => record.EQID"
      >
        <template #notFoundContent>
          <span>没数据</span>
        </template>
      </Transfer>
    </div>
  </Modal>
</template>
