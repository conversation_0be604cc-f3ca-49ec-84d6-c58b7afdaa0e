<script lang="ts" setup>
import type LogicFlow from '@logicflow/core';

import { onMounted, ref } from 'vue';

import { Button } from 'ant-design-vue';

interface Props {
  lf: LogicFlow;
  catTurboData?: boolean;
}

defineOptions({
  name: 'Control',
});

const props = withDefaults(defineProps<Props>(), {
  catTurboData: false,
});

const emit = defineEmits<{
  catData: [];
  catTurboData: [];
}>();

// 响应式数据
const undoDisable = ref(true);
const redoDisable = ref(true);

// 方法
const zoomIn = () => {
  props.lf.zoom(true);
};

const zoomOut = () => {
  props.lf.zoom(false);
};

const zoomReset = () => {
  props.lf.resetZoom();
};

const translateRest = () => {
  props.lf.resetTranslate();
};

const reset = () => {
  props.lf.resetZoom();
  props.lf.resetTranslate();
};

const undo = () => {
  props.lf.undo();
};

const redo = () => {
  props.lf.redo();
};

const download = () => {
  props.lf.getSnapshot();
};

const handleCatData = () => {
  emit('catData');
};

const handleCatTurboData = () => {
  emit('catTurboData');
};

const showMiniMap = () => {
  const { lf } = props;
  lf.extension.miniMap.show(lf.graphModel.width - 150, 40);
};

onMounted(() => {
  props.lf.on('history:change', ({ data: { undoAble, redoAble } }: any) => {
    undoDisable.value = !undoAble;
    redoDisable.value = !redoAble;
  });
});
</script>
<template>
  <div>
    <Button.Group>
      <Button size="small" @click="zoomIn">放大</Button>
      <Button size="small" @click="zoomOut">缩小</Button>
      <Button size="small" @click="zoomReset">大小适应</Button>
      <Button size="small" @click="translateRest">定位还原</Button>
      <Button size="small" @click="reset">还原(大小&定位)</Button>
      <Button size="small" @click="undo" :disabled="undoDisable">
        上一步(ctrl+z)
      </Button>
      <Button size="small" @click="redo" :disabled="redoDisable">
        下一步(ctrl+y)
      </Button>
      <Button size="small" @click="download">下载图片</Button>
      <Button size="small" @click="handleCatData">查看数据</Button>
      <Button v-if="catTurboData" size="small" @click="handleCatTurboData">
        查看turbo数据
      </Button>
      <Button size="small" @click="showMiniMap">查看缩略图</Button>
    </Button.Group>
  </div>
</template>
<style scoped></style>
