<script lang="ts" setup>
import { computed, onMounted, watch } from 'vue';

import { Card } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { getRestPnldApi } from '#/api/equipment/equipment-mg';
import { useEquipmentStore } from '#/store';

import { restSchema } from '../equipment-mg-data';

const equipmentStore = useEquipmentStore();
interface RowType {
  [key: string]: any;
}
const currentRow = computed<null | RowType>(
  () => equipmentStore.getCurrentRow as null | RowType,
);
onMounted(() => {
  getData();
});
watch(
  currentRow,
  () => {
    getData();
  },
  { deep: true },
);
const getData = async () => {
  if (currentRow.value && currentRow.value.EQID) {
    const data = await getRestPnldApi([currentRow.value.EQID]);
    formApi.setFieldValue('REST_BASE_URL', data[0].REST_BASE_URL);
    formApi.setFieldValue('resultRequestScript', data[0].REST_SC_RUN);
    formApi.setFieldValue('calibrationRequestScript', data[0].REST_SC_CAL);
    formApi.setFieldValue('measurementTypeFilter', data[0].REST_MEASURETYPE);
  }
};
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: restSchema(),
  showDefaultActions: false,
});
</script>
<template>
  <Card title="REST Web服务设置" class="h-2/3">
    <Form class="mx-4" />
  </Card>
</template>
