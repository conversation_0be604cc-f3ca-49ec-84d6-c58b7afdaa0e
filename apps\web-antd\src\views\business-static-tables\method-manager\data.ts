import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { MethodManagerApi } from '#/api/business-static-tables';

import { $t } from '#/locales';
import { createColumn } from '#/utils/lims-grids-config';

const methodTypeOptions = [
  {
    label: $t('business-static-tables.methodManager.Test Method'),
    value: 'Test Method',
  },
  {
    label: $t('business-static-tables.methodManager.Equipment Method'),
    value: 'Equipment Method',
  },
];

export function useColumns(): VxeTableGridOptions<MethodManagerApi.TestMethod>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 60 },
    createColumn({
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 80,
    }),
    createColumn({
      field: 'METHOD',
      title: $t('business-static-tables.methodManager.method'),
      width: 200,
    }),
    createColumn({
      field: 'METHODCATEGORY',
      title: $t('business-static-tables.methodManager.methodCategory'),
      width: 200,
      editRender: {
        name: 'input',
      },
    }),
    createColumn({
      field: 'DESCRIPTION',
      title: $t('business-static-tables.methodManager.description'),
      width: 500,
      editRender: {
        name: 'input',
      },
    }),
    createColumn({
      field: 'SOP_ENG',
      title: $t('business-static-tables.methodManager.sopEng'),
      width: 500,
      editRender: {
        name: 'input',
      },
    }),
    createColumn({
      field: 'METHODTYPE',
      title: $t('business-static-tables.methodManager.methodType'),
      width: 200,
      formatter: 'formatTranslate',
      editRender: {
        name: 'select',
        options: methodTypeOptions,
      },
    }),
    createColumn({
      field: 'METHODNO',
      title: $t('business-static-tables.methodManager.methodNo'),
      editRender: {
        name: 'input',
      },
      // width: 200,
    }),
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('commons.action'),
      minWidth: 160,
      width: 160,
    },
  ];
}

export function useSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Select',
      componentProps: {
        options: methodTypeOptions,
        class: 'w-full',
      },
      fieldName: 'METHODTYPE',
      label: $t('business-static-tables.methodManager.methodType'),
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'METHOD',
      label: $t('business-static-tables.methodManager.method'),
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'METHODCATEGORY',
      label: $t('business-static-tables.methodManager.methodCategory'),
    },
    {
      component: 'Input',
      componentProps: {
        type: 'textarea',
        autoSize: { minRows: 2 },
      },
      fieldName: 'DESCRIPTION',
      label: $t('business-static-tables.methodManager.description'),
    },
    {
      component: 'Input',
      componentProps: {
        type: 'textarea',
        autoSize: { minRows: 2 },
      },
      fieldName: 'SOP_ENG',
      label: $t('business-static-tables.methodManager.sopEng'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'METHODNO',
      label: $t('business-static-tables.methodManager.methodNo'),
    },
  ];
}

export function useFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Select',
      componentProps: {
        options: methodTypeOptions,
        class: 'w-full',
      },
      fieldName: 'METHODTYPE',
      label: $t('business-static-tables.methodManager.methodType'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'METHOD',
      label: $t('business-static-tables.methodManager.method'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'METHODCATEGORY',
      label: $t('business-static-tables.methodManager.methodCategory'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'METHODNO',
      label: $t('business-static-tables.methodManager.methodNo'),
    },
  ];
}

export function useMethodVersionColumns(): VxeTableGridOptions<MethodManagerApi.TestMethodVersion>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 60 },
    createColumn({
      field: 'STATUS',
      title: $t('commons.status'),
      width: 100,
    }),
    createColumn({
      field: 'VERSION',
      title: $t('commons.version'),
      width: 100,
    }),
    createColumn({
      field: 'FILE_VERSION',
      title: $t('business-static-tables.methodVersionManager.fileVersion'),
      width: 150,
      editRender: {
        name: 'input',
      },
    }),
    createColumn({
      field: 'STARTDDATE',
      title: $t('business-static-tables.methodVersionManager.startDdate'),
      width: 200,
      editRender: {
        name: 'input',
        attrs: { type: 'date' },
      },
      formatter: 'formatDateTime',
    }),
    createColumn({
      field: 'EXPDATE',
      title: $t('business-static-tables.methodVersionManager.expDate'),
      width: 200,
      editRender: {
        name: 'input',
        attrs: { type: 'date' },
      },
      formatter: 'formatDateTime',
    }),
    createColumn({
      field: 'REFNO',
      title: $t('business-static-tables.methodVersionManager.refNo'),
      width: 100,
    }),
    createColumn({
      field: 'LINK',
      title: $t('business-static-tables.methodVersionManager.link'),
      width: 200,
      cellRender: {
        name: 'CellLink',
        props: {},
      },
    }),
    createColumn({
      field: 'STARDOC_ID',
      title: $t('business-static-tables.methodVersionManager.starDocId'),
      width: 100,
    }),
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('commons.action'),
      minWidth: 160,
      width: 160,
    },
  ];
}

export function useMethodVersionSchema(): VbenFormSchema[] {
  return [
    {
      component: 'InputNumber',
      componentProps: { class: 'w-full' },
      fieldName: 'VERSION',
      label: $t('commons.version'),
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'FILE_VERSION',
      label: $t('business-static-tables.methodVersionManager.fileVersion'),
    },
  ];
}

export function useMethodVersionFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'METHOD',
      label: $t('business-static-tables.methodManager.method'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'METHODCATEGORY',
      label: $t('business-static-tables.methodManager.methodCategory'),
    },
  ];
}
