{"title": "basic static data", "operation": "Operation", "add": "Add", "delete": "Delete", "edit": "Edit", "save": "Save", "cancel": "Cancel", "detail": "Detail", "removed": "Removed", "origrec": "Original Record", "origsts": " Original Status", "select": "Select", "material-types": {"list": "Department List", "mattype": "Material Type", "isproducematrial": "Is Produce Material", "description": "Description", "title": "Material Types", "appearanceinspection": "Appearance Inspection", "updatedept": "Update Department", "showall": "Show All"}, "material-types-detail": {"title": "Material Type Detail", "mattype": "Material Type", "dept": "Department", "batchcrysreportid": "Batch Crystal Report ID", "requestform_cryid": "Request Form Cry ID", "cert_sampling": "Certificate Sampling", "label_sample_test": "Label Sample Test", "label_sample_retain": "Label Sample Retain", "sample_elnid": "Sample ELN ID", "samplecycle": "Sample Cycle", "needappearanceinspection": "Need Appearance Inspection", "matinventory_sigmode": "Material Inventory Signature Mode", "addinventory_sigmode": "Add Inventory Signature Mode", "stocktaking_sigmode": "Stocktaking Signature Mode", "destruction_sigmode": "Destruction Signature Mode", "reserve_receive_sign_model": "Reserve Receive Sign Model", "reserved_destroy_sign_model": "Reserved Destroy Sign Model", "reserved_observe_sign_model": "Reserved Observe Sign Model"}, "material-child-types": {"title": "Child Type Detail", "typename": "Child Type Name", "typedesc": "Child Type Description"}, "material-appearance-item": {"title": "Material Appearance Item", "sorter": "Sorter", "mattype": "Material Type", "itemname": "Item Name", "addappearanceitem": "Add Appearance Item", "deleteappearanceitem": "Delete Appearance Item"}, "material-appearance-item-result": {"title": "Material Appearance Item Result", "sorter": "Sorter", "itemid": "Item ID", "filltype": "Fill Type", "resultsvalue": "Results Value", "addappearanceitemresult": "Add Appearance <PERSON><PERSON>t", "deleteappearanceitemresult": "Delete Appearance Item Result"}, "client-categories": {"title": "Client Categories", "category": "Category"}, "equip-types": {"title": "Equipment Type", "eqtype": "Eqtype", "description": "Description", "is_samecall": "Is SameCall"}, "equip-events": {"title": "Equipment Envents", "maintenanceevent": "Maintenance Event", "description": "Description", "binding_status": "Binding Status", "reminderWindow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eln_id": "<PERSON>n <PERSON>"}, "qc-types": {"title": "QC Type", "qctype": "QC Type", "qcgroup": "QC Group", "updparent": "Update Parent", "autoselect": "Auto Select", "instrumentqc": "Instrument QC", "color": "Color", "empw_function": "Empw Function"}, "test-categories": {"title": " Test Categories", "testcatcode": " Test Cat Code", "testcatdesc": " Test Cat Desc"}, "test-plan-groups": {"title": "Test Plan Groups", "prodgroup": "Prod Group", "prodgrpdesc": "Prod Grp Desc", "prodtype": "Prod Type"}, "spec-categories": {"title": "Spec Categories", "speccategory": "Spec Category", "regioncode": " Region Code"}, "supplier": {"title": "Supplier", "suptype": "Supplier Type", "suppcode": "Supplier Code", "suppnam": "Supplier Name", "suppadd": "Supplier Address", "suppadd_a": "Supplier Address A", "suppcity": "Supplier City", "suppstnam": "Supplier State Name", "suppst": "Supplier State", "suppzip": "Supplier Zip", "countryname": "Country Name", "primaryphone": "Primary Phone", "primaryfax": "Primary Fax", "defaultcontact": "Default Contact", "email": "Email", "url": "URL", "comments": "Comments", "city_code": "City Code"}, "sub-location-type": {"title": "Sub Type Type", "name": "Name", "prefix": "Prefix", "numbering_method": "Numbering Method", "sublocations_template": "Sublocations Template"}, "location-type": {"title": "Location Type", "type_name": "Type Name", "description": "Description", "imageref": "Image Reference", "location_type_id": "Location Type ID"}, "location-type_subloc": {"title": " Location Type Subloc", "location_type_id": "Location Type ID", "name": "Name", "prefix": "Prefix", "loctype_size": "Loctype <PERSON>", "loctype_order": "Loctype Order", "numbering_method": "Numbering Method", "is_storable": "Is Storable", "imageref": "Image Reference"}, "measure-types": {"title": " Measure Types", "measure_type": "Measure Type"}, "units-of-measure": {"title": " Units Of Measure", "unit_code": "Unit Code", "unit_name": "Unit Name", "uom_long_name": "Uom Log Name", "uom_short_name": "<PERSON>om Short Name", "is_si": "Is Sl", "measure_type": "Measure Type"}, "units-conversion": {"title": " Unit Conversion", "measure_type": "Measure Type", "unti_code": "Unit Code", "factor": "Factor", "to_unit_code": "To Unit Code", "is_na": "Is Na"}, "conditions": {"title": "Conditions", "condition": "Condition", "temperature": "Temperature", "temperature_max": "Temperature Max", "temperature_std": "Temperature Std", "humidity": "<PERSON><PERSON><PERSON><PERSON>", "luminosity": "Luminosity", "other": "Other", "position": "Position", "elapse_time_return_sample": "Elapse Time Return Sample"}, "conditions-tat": {"title": " Conditions TAT", "dept": "Department", "condition": "Condition", "tat": "TAT", "condition_tat_units": "Condition TAT Units"}}