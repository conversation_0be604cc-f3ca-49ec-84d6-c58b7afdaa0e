<script lang="ts" setup>
import type { SampleGroupsApi } from '#/api/business-static-tables/sample-groups';

import { computed, ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message, Transfer } from 'ant-design-vue';

import { getChooseSampleReqTests } from '#/api/business-static-tables/sample-groups';

// 定义Transfer数据项类型
interface TransferItem {
  key: string;
  title: string;
  description?: string;
}

const props = defineProps<{
  aTestCodes: string[];
  spCode: number;
}>();

const emit = defineEmits<{
  (
    e: 'submit',
    data: { selectedItems: TransferItem[]; selectedKeys: string[] },
  ): void;
  (e: 'cancel'): void;
}>();

// 数据源与状态
const transferTestData = ref<TransferItem[]>([]);
const targetTestKeys = ref<string[]>([]);
const loading = ref(false);
const error = ref<string>('');

// 计算属性
const selectedItems = computed(() => {
  return transferTestData.value.filter((item) =>
    targetTestKeys.value.includes(item.key),
  );
});

// 获取数据
async function fetchData(sSpCode: number, aTestCodes: string[]) {
  loading.value = true;
  error.value = '';

  try {
    const result = await getChooseSampleReqTests(sSpCode, aTestCodes);
    const data =
      result.items?.map((item: SampleGroupsApi.SamplingRequirementsTests) => ({
        key: item.TESTCODE,
        title: item.TESTNO,
        description: item.TESTNO || '',
      })) || [];

    transferTestData.value = data;

    // 设置初始选中项
    targetTestKeys.value =
      props.aTestCodes.length > 0 ? [...props.aTestCodes] : [];
  } catch (error_) {
    error.value = `数据加载失败: ${(error_ as Error).message}`;
    message.error(error.value);
  } finally {
    loading.value = false;
  }
}

watch(
  () => props.aTestCodes,
  () => {
    // 处理 testCode 变化后的逻辑
    fetchData(props.spCode, props.aTestCodes);
  },
);

// 使用VbenModal
const [TransferTestModal, modalApi] = useVbenModal({
  onOpenChange: async (isOpen: boolean) => {
    // console.log(props.testCode);
    // console.log(props.aAnalytes);
    if (isOpen && props.spCode !== null && props.aTestCodes.length > 0) {
      await fetchData(props.spCode, props.aTestCodes);
    }
  },
  onConfirm: () => {
    handleSubmit();
  },
  onCancel: () => {
    handleCancel();
  },
});

// 处理提交
function handleSubmit() {
  const result = {
    selectedKeys: targetTestKeys.value,
    selectedItems: selectedItems.value,
  };
  // console.log(result);
  emit('submit', result);
  // message.success(`已选择 ${targetKeys.value.length} 项`);
  modalApi.close();
}

// 处理取消
function handleCancel() {
  emit('cancel');
  modalApi.close();
}

// 暴露方法给父组件
defineExpose({
  open: () => modalApi.open(),
  close: () => modalApi.close(),
  setData: (data: any) => modalApi.setData(data),
});
</script>

<template>
  <TransferTestModal title="编辑测试" class="w-[800px]" :loading="loading">
    <Transfer
      v-model:target-keys="targetTestKeys"
      :data-source="transferTestData"
      show-search
      :render="(item) => item.title"
      :list-style="{
        width: '400px',
        height: '400px',
      }"
      :operations="['选择', '移除']"
    />
  </TransferTestModal>
</template>
