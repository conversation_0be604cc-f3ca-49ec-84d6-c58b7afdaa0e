<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { MaterialManagerApi } from '#/api/materials-management/material-manager';

import { computed, ref } from 'vue';

import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { Button, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getRecipesDetail } from '#/api/materials-management/material-manager';

import EditRecipeDetail from './edit-recipe-detail.vue';
import { useRecipesDetailColumns } from './material-manager-data';

const recipeData = ref<MaterialManagerApi.Recipes>();

const status = ref<string>('');

const isDisabled = ref(false);

const gridOptions: VxeTableGridOptions<MaterialManagerApi.Recipes> = {
  columns: useRecipesDetailColumns(),
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        const data = drawerApi.getData<MaterialManagerApi.Recipes>();
        if (!data) {
          return;
        }
        const recipeCode = data.RECIPECODE;
        return await getRecipesDetail(recipeCode);
      },
    },
  },
  showOverflow: true,
  exportConfig: {},
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });

// const [FormModal, formModalApi] = useVbenModal({
//   connectedComponent: AddLocationTypeSubLocForm,
//   destroyOnClose: true,
// });

const [Drawer, drawerApi] = useVbenDrawer({
  onConfirm: onSubmit,
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<MaterialManagerApi.Recipes>();
      if (data) {
        recipeData.value = data;
        status.value = getStatusOfRecipe(data);
        if (
          status.value === 'Active' ||
          status.value === 'Expired' ||
          status.value === 'Retired'
        ) {
          isDisabled.value = true;
        }
        // gridApi?.query();
      } else {
        // gridApi?.reload();
      }
    }
  },
  showCancelButton: false,
  showConfirmButton: false,
});

function getStatusOfRecipe(row: MaterialManagerApi.Recipes) {
  const expDate = new Date(row.EXPDATE);

  if (row.RETIREDDAT) {
    return 'Retired';
  }
  if (row.STARTDDATE && expDate && expDate > new Date()) {
    return 'Active';
  }
  if (row.STARTDDATE && expDate && expDate <= new Date()) {
    return 'Expired';
  }
  return 'Unknown';
}

async function onSubmit() {
  drawerApi.close();
}

const getDrawerTitle = computed(() => {
  if (recipeData.value) {
    return `查看配方 ${recipeData.value.RECIPENAME}`;
  }
  return '配方详情';
});

/* function onCreate() {
  formModalApi.setData({ RECIPECODE: recipeData.value?.RECIPECODE }).open();
} */

function onRefresh() {
  gridApi.query();
}

const [EditRecipeGridsModal, editRecipeGridsModalApi] = useVbenModal({
  connectedComponent: EditRecipeDetail,
  destroyOnClose: true,
});

function onEditRecipeDetail() {
  const currentRecipe = drawerApi.getData<MaterialManagerApi.Recipes>();
  if (currentRecipe) {
    // 确保传递正确的数据格式
    // editRecipeGridsModalApi.setData({ data: currentRecipe }).open();
    editRecipeGridsModalApi
      .setData({ RECIPECODE: currentRecipe.RECIPECODE })
      .open();
    // 使用允许的console方法
    console.warn('打开编辑配方弹窗');
  } else {
    console.warn('无法获取当前配方数据');
  }
}
</script>

<template>
  <Drawer class="w-full max-w-[1000px]" :title="getDrawerTitle">
    <Page auto-content-height>
      <Grid>
        <template #toolbar-actions>
          <Space>
            <!--             <Button type="primary" @click="onCreate">
              {{ $t('ui.actionTitle.create') }}
            </Button> -->
            <Button
              type="primary"
              @click="onEditRecipeDetail"
              :disabled="isDisabled"
            >
              编辑配方
            </Button>
          </Space>
        </template>
      </Grid>
    </Page>
  </Drawer>
  <EditRecipeGridsModal @success="onRefresh" />
</template>
