<script lang="ts" setup>
import { Page } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { WorkflowExample } from '#/components/logic-flow';

defineOptions({
  name: 'FlowContainer',
});
</script>

<template>
  <Page
    :title="$t('workflow.title')"
    :description="$t('workflow.description')"
    auto-content-height
  >
    <WorkflowExample class="h-full w-full" />
  </Page>
</template>

<style scoped></style>
