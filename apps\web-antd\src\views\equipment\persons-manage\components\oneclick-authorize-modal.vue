<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Select, SelectOption } from 'ant-design-vue';
import {
  addOtherMethodCertApi,
  addOtherEQCertApi,
  addTraningOthersApi,
  getDataByIdApi,
  getServgrpByDeptApi,
  getCommonMcUsersApi,
} from '#/api/equipment/persons-manage';
import DoubleList from './double-list.vue';
import { oneClickAuthorizeModalSchema } from '../persons-manage-data';
import { useVbenForm } from '#/adapter/form';

interface RowType {
  [key: string]: any;
}
const emit = defineEmits(['success']);
interface PersonItem {
  TEXT: string;
  VALUE: string;
}
import { message } from 'ant-design-vue';
const rightItems = ref<PersonItem[]>([]);
const leftItems = ref<PersonItem[]>([]);
const positionOptions = ref<{ value: string; label: string }[]>([]);

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    if (rightItems.value.length === 0) {
      modalApi.close();
      return;
    }
    modalApi.lock();
    try {
      let result = true;
      const clickRow = modalApi.getData().clickRow;
      const querys = {
        device: addOtherEQCertApi,
        method: addOtherMethodCertApi,
        train: addTraningOthersApi,
      };
      type QueryType = keyof typeof querys;
      const type = modalApi.getData().type as QueryType;
      const query = querys[type];
      for (const item of rightItems.value) {
        const res = await query([[clickRow.ORIGREC], item.VALUE]);
        if (res === false) {
          result = false;
          break;
        }
      }
      if (result) {
        message.success('授权完成！');
        emit('success');
        modalApi.close();
      } else {
        modalApi.close();
        return;
      }
    } finally {
      modalApi.lock(false);
    }
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      const res = await getDataByIdApi([]);
      positionOptions.value = res.map((item: any) => ({
        value: item.Value,
        label: item.Text,
      }));
      formApi.setFieldValue('position', positionOptions.value[0]?.value);
      await getDeptData();
      getUSerList();
    }
  },
});

const getUSerList = async () => {
  const data = await formApi.getValues();
  const usrnam = modalApi.getData().usrnam;

  const usrnamParams = usrnam ? usrnam.flat().join(',') : '';
  const params = [usrnamParams, data.position, data.laboratory];
  const res = await getCommonMcUsersApi(params);
  leftItems.value = res
    .filter((item: any) => item.Selected === 'false')
    .map((item: any) => ({
      TEXT: item.Text,
      VALUE: item.Value,
    }));
  const type = modalApi.getData().type;

  if (type === 'train') {
    const personRow = modalApi.getData().personRow;
    rightItems.value = res
      .filter((item: any) => item.Value === personRow.USRNAM)
      .map((item: any) => ({
        TEXT: item.Text,
        VALUE: item.Value,
      }));
  } else {
    rightItems.value = res
      .filter((item: any) => item.Value === 'true')
      .map((item: any) => ({
        TEXT: item.Text,
        VALUE: item.Value,
      }));
  }
};
const getDeptData = async () => {
  formApi.setFieldValue('laboratory', '');
  const data = await formApi.getValues();
  const res = await getServgrpByDeptApi([data.position]);
  categoryOptions.value = res.map((item: any) => ({
    value: item.Value,
    label: item.Text,
  }));
};
const leftSelectedItem = ref<PersonItem>({
  TEXT: '',
  VALUE: '',
});
const rightSelectedItem = ref<PersonItem>({
  TEXT: '',
  VALUE: '',
});
const removeToRight = () => {
  if (leftSelectedItem.value.VALUE) {
    leftItems.value = leftItems.value.filter(
      (item) => item.VALUE !== leftSelectedItem.value.VALUE,
    );

    rightItems.value.push(leftSelectedItem.value);
  }
};
const removeToLeft = () => {
  if (rightSelectedItem.value.VALUE) {
    rightItems.value = rightItems.value.filter(
      (item) => item.VALUE !== rightSelectedItem.value.VALUE,
    );
    leftItems.value.push(rightSelectedItem.value);
  }
};
const removeAllToRight = () => {
  rightItems.value.push(...leftItems.value);
  leftItems.value = [];
};
const removeAllToLeft = () => {
  leftItems.value.push(...rightItems.value);
  rightItems.value = [];
};
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: oneClickAuthorizeModalSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});
const positionChange = () => {
  getDeptData();
  getUSerList();
};

const categoryChange = () => {
  getUSerList();
};

const categoryOptions = ref<{ value: string; label: string }[]>([]);
</script>
<template>
  <Modal title="选择用户" class="h-1/2 w-2/5">
    <Form class="mx-4">
      <template #position="slotProps">
        <Select v-bind="slotProps" class="w-full" @change="positionChange">
          <SelectOption
            v-for="item in positionOptions"
            :value="item.value"
            :key="item.value"
          >
            {{ item.label }}
          </SelectOption>
        </Select>
      </template>
      <template #laboratory="slotProps">
        <Select v-bind="slotProps" class="w-full" @change="categoryChange">
          <SelectOption
            v-for="item in categoryOptions"
            :value="item.value"
            :key="item.value"
          >
            {{ item.label }}
          </SelectOption>
        </Select>
      </template>
    </Form>
    <DoubleList
      :leftItems="leftItems"
      :rightItems="rightItems"
      :leftSelectedItem="leftSelectedItem"
      :rightSelectedItem="rightSelectedItem"
      @update:leftSelectedItem="leftSelectedItem = $event"
      @update:rightSelectedItem="rightSelectedItem = $event"
      @removeToRight="removeToRight"
      @removeToLeft="removeToLeft"
      @removeAllToRight="removeAllToRight"
      @removeAllToLeft="removeAllToLeft"
    />
  </Modal>
</template>
