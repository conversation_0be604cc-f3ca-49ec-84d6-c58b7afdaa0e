import { callServer, getDataSet } from '#/api/core/witlab';

export namespace VirusTypeApi {
  export interface VirusType {
    [key: string]: any;
    ORIGREC: number;
    TYPENAME: string;
    AGARVALIDITY: string;
  }

  export interface VirusInfo {
    [key: string]: any;
    ORIGREC: number;
    VIRUSNAME: string;
    TYPENAME: string;
  }
}

const $addVirusTypeApi = async (
  data: Omit<VirusTypeApi.VirusType, 'ORIGREC'>,
  eventCode: string = 'AddVirusType',
  comment: string = '',
) => {
  return callServer('FungusManage.AddVirusType', [
    data.TYPENAME,
    data.AGARVALIDITY,
    data.CONFIGURE_METHOD,
    eventCode,
    comment,
  ]);
};

const $addVirusInfoApi = async (
  data: Omit<VirusTypeApi.VirusInfo, 'ORIGREC'>,
  eventCode: string = 'AddVirusInfo',
  comment: string = '',
) => {
  return callServer('FungusManage.AddVirusInfo', [
    data.TYPENAME,
    data.VIRUSNAME,
    eventCode,
    comment,
  ]);
};

const $getVirusTypeApi = async () => {
  const data = await getDataSet('FungusManage.Ds_VirusType', []);
  return data;
};

const $getVirusInfoApi = async (data: { typeName: string }) => {
  const result = await getDataSet('FungusManage.Ds_VirusInfo', [data.typeName]);
  return result;
};

const $delCommonApi = async (data: {
  origrecs: number[];
  tableName: string;
}) => {
  const result = await callServer('Common.DeleteRows', [
    data.tableName,
    data.origrecs,
  ]);
  return result;
};

export {
  $addVirusInfoApi,
  $addVirusTypeApi,
  $delCommonApi,
  $getVirusInfoApi,
  $getVirusTypeApi,
};
