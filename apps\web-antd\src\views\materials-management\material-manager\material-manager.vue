<script lang="ts" setup>
import type { VxeColumnPropTypes } from 'vxe-table';

import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { MaterialManagerApi } from '#/api/materials-management/material-manager';

import { onMounted, reactive, ref } from 'vue'; // 添加了 onMounted 和 reactive 的导入

import { confirm, Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Button, message, Space, TabPane, Tabs } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  approveRecipe,
  deleteMaterial,
  deleteMaterialUnitConv,
  deleteProdSupplier,
  deleteRecipes,
  deleteSynonyms,
  getInspectionLevels,
  getMaterialsList,
  getMaterialUnitConvList,
  getMatTypeList,
  getProdSuppliersList,
  getRecipesList,
  getSynonymsList,
  retireRecipe,
} from '#/api/materials-management/material-manager';
import { createGridOptions } from '#/utils/grid-option';

import AddMaterialSinonymForm from './add-material-sinonym.vue';
import AddMaterialUnitForm from './add-material-unit-conv.vue';
import AddMaterialForm from './add-material.vue';
import AddProdSupplierForm from './add-prod-supplier.vue';
import AddRecipeForm from './add-recipe.vue';
import CopyRecipeForm from './copy-recipe.vue';
import {
  useMaterialManagerColumns,
  useMaterialUnitConvColumns,
  useProdSuppliersColumns,
  useRecipesColumns,
  useSynonimsColumns,
} from './material-manager-data';
import RecipeDetail from './recipe-details.vue';

const activeKey = ref('1');

function createEditRender(): VxeColumnPropTypes.EditRender {
  return {
    name: 'select',
    options: [],
  };
}

const matTypeEditRender = reactive(createEditRender());

const inspectionLevelEditRender = reactive(createEditRender());

const isApprovedRecipeDisabled = ref(false);
const isDeleteRecipeDisabled = ref(false);
const isRetireRecipeDisabled = ref(false);
const isUploadFileDisabled = ref(false);
const isViewFileDisabled = ref(false);

const showContainerUnitTab = ref(true); // 默认显示容器单位转换 tab

onMounted(async () => {
  // 绑定语言版本数据
  const matTypeResult = await getMatTypeList();
  matTypeEditRender.options = matTypeResult.items.map(
    (item: { MATTYPE: string }) => ({
      label: item.MATTYPE,
      value: item.MATTYPE,
    }),
  );

  // 绑定执行脚本数据
  const inspectionLevelResult = await getInspectionLevels();
  inspectionLevelEditRender.options = inspectionLevelResult.items.map(
    (item: { INSPECTION_LEVEL: string }) => ({
      label: item.INSPECTION_LEVEL,
      value: item.INSPECTION_LEVEL,
    }),
  );
});

const materialGridOptions: VxeTableGridOptions<MaterialManagerApi.MaterialManager> =
  {
    columns: useMaterialManagerColumns(
      matTypeEditRender,
      inspectionLevelEditRender,
    ),
    stripe: true,
    border: true,
    keepSource: true,
    editConfig: {
      mode: 'row',
      trigger: 'manual',
    },
    height: 'auto',
    pagerConfig: {},
    proxyConfig: {
      ajax: {
        query: async () => {
          return await getMaterialsList();
        },
      },
    },
    exportConfig: {},
    showOverflow: true,
    rowConfig: {
      keyField: 'ORIGREC',
      isCurrent: true,
      isHover: true,
    },
    toolbarConfig: {
      custom: true,
      export: true,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  };

const matterialGridEvents: VxeGridListeners<MaterialManagerApi.MaterialManager> =
  {
    currentChange: async ({ row }) => {
      if (row) {
        if (row.MATTYPE === 'SampleContainer') {
          showContainerUnitTab.value = false;
          activeKey.value = '1';
        } else {
          showContainerUnitTab.value = true;
        }

        // 行切换时绑定tab下的Grid
        handleTabChange();
      }
    },
  };

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: materialGridOptions,
  // formOptions,
  gridEvents: matterialGridEvents, // 确保事件被绑定
});

function hasEditStatus(row: MaterialManagerApi.MaterialManager) {
  return gridApi.grid?.isEditByRow(row);
}

function editRowEvent(row: MaterialManagerApi.MaterialManager) {
  gridApi.grid?.setEditRow(row);
}

async function saveRowEvent(row: MaterialManagerApi.MaterialManager) {
  await gridApi.grid?.clearEdit();
  // addQcType(row);
  gridApi.grid.reloadRow(row);
  gridApi.setLoading(true);
  setTimeout(() => {
    gridApi.setLoading(false);
    message.success({
      content: `保存成功！`,
    });
  }, 600);
}

const cancelRowEvent = (row: MaterialManagerApi.MaterialManager) => {
  gridApi.grid?.clearEdit().then(() => {
    // 还原行数据
    gridApi.grid.revertData(row);
  });
};

function onRefresh() {
  gridApi.query(); // 主动触发一次查询
}

// 添加物料
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddMaterialForm,
});

async function onMaterialCreate() {
  formModalApi.setData(null).open();
}

// 删除物料
async function onMaterialDelete() {
  // 获取选中行
  const aMatCode: string[] = gridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.MATCODE);

  if (aMatCode.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${aMatCode.length} 条数据吗？`,
      icon: 'warning',
      centered: false,
    });

    await deleteMaterial(aMatCode);

    message.success('删除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

// 同义词列表
const materialsSynonimsGridOptions =
  createGridOptions<MaterialManagerApi.MaterialsSynonims>();
materialsSynonimsGridOptions.columns = useSynonimsColumns();
// materialsSynonimsGridOptions.height = '400px'; // 或者设置一个固定高度，如 '400px'

const [MatSinonymGrid, matSinonymGridApi] = useVbenVxeGrid({
  gridOptions: materialsSynonimsGridOptions,
});

// 添加同义词
const [MaterialSinonymFormModal, materialSinonymFormModalApi] = useVbenModal({
  connectedComponent: AddMaterialSinonymForm,
});

async function onSinonymCreate() {
  const material = gridApi.grid?.getCurrentRecord();
  if (!material) return;
  const matcode = material.MATCODE;
  materialSinonymFormModalApi.setData({ MATCODE: matcode }).open();
}

// 删除同义词
async function onSinonymDelete() {
  const material = gridApi.grid?.getCurrentRecord();
  if (!material) return;
  const matcode = material.MATCODE;

  // 获取选中行
  const aSinonym: string[] = matSinonymGridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.MATSYNONIM);

  if (aSinonym.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${aSinonym.length} 条数据吗？`,
      icon: 'warning',
      centered: false,
    });

    await deleteSynonyms(aSinonym, matcode);

    message.success('删除成功');
    handleTabChange();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

function onTabRefresh() {
  handleTabChange();
}

// 厂商列表
const prodSupplierGridOptions = {
  ...createGridOptions<MaterialManagerApi.ProdSuppliers>(),
  columns: useProdSuppliersColumns(),
  rowConfig: {
    keyField: 'ORIGREC',
    isCurrent: true,
    isHover: true,
  },
};
// materialsSynonimsGridOptions.height = '400px'; // 或者设置一个固定高度，如 '400px'

const [ProdSupplierGrid, prodSupplierGridApi] = useVbenVxeGrid({
  gridOptions: prodSupplierGridOptions,
});

// 添加厂商
const [ProdSupplierFormModal, prodSupplierFormModalApi] = useVbenModal({
  connectedComponent: AddProdSupplierForm,
});

async function onSupplierCreate() {
  const material = gridApi.grid?.getCurrentRecord();
  if (!material) return;
  const matcode = material.MATCODE;
  prodSupplierFormModalApi.setData({ MATCODE: matcode }).open();
}

// 删除厂商
async function onSupplierDelete() {
  // 获取选中行
  const proSupplier = prodSupplierGridApi.grid?.getCurrentRecord();
  if (!proSupplier) return;
  const origrec = proSupplier.ORIGREC;

  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的厂商吗？`,
      icon: 'warning',
      centered: false,
    });

    await deleteProdSupplier(origrec);

    message.success('删除成功');
    handleTabChange();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

// 配方列表
const recipesGridOptions = {
  ...createGridOptions<MaterialManagerApi.Recipes>(),
  columns: useRecipesColumns(),
  rowConfig: {
    keyField: 'RECIPECODE',
    isCurrent: true,
  },
};

const recipeGridEvents: VxeGridListeners<MaterialManagerApi.Recipes> = {
  currentChange: async ({ row }) => {
    const stardocId = row.STARDOC_ID;
    isViewFileDisabled.value = stardocId === null;

    const status = getStatusOfRecipe(row);

    switch (status) {
      case 'Active': {
        isRetireRecipeDisabled.value = false;
        isDeleteRecipeDisabled.value = true;
        isApprovedRecipeDisabled.value = true;
        isUploadFileDisabled.value = true;
        break;
      }

      case 'Expired': {
        isRetireRecipeDisabled.value = false;
        isDeleteRecipeDisabled.value = true;
        isApprovedRecipeDisabled.value = true;
        isUploadFileDisabled.value = true;
        break;
      }

      case 'Retired': {
        isRetireRecipeDisabled.value = true;
        isDeleteRecipeDisabled.value = true;
        isApprovedRecipeDisabled.value = true;
        isUploadFileDisabled.value = true;
        break;
      }
      default: {
        isRetireRecipeDisabled.value = true;
        isDeleteRecipeDisabled.value = false;
        isApprovedRecipeDisabled.value = false;
        isUploadFileDisabled.value = false;
      }
    }
  },
};

function getStatusOfRecipe(row: MaterialManagerApi.Recipes) {
  const expDate = new Date(row.EXPDATE);

  if (row.RETIREDDAT) {
    return 'Retired';
  }
  if (row.STARTDDATE && expDate && expDate > new Date()) {
    return 'Active';
  }
  if (row.STARTDDATE && expDate && expDate <= new Date()) {
    return 'Expired';
  }
  return 'Unknown';
}

const [RecipesGrid, recipesGridApi] = useVbenVxeGrid({
  gridOptions: recipesGridOptions,
  gridEvents: recipeGridEvents,
});

// 添加配方
const [RecipeFormModal, recipeFormModalApi] = useVbenModal({
  connectedComponent: AddRecipeForm,
});

async function onRecipeCreate() {
  const material = gridApi.grid?.getCurrentRecord();
  if (!material) return;
  recipeFormModalApi.setData({ MATCODE: material.MATCODE }).open();
}

// 复制配方
const [CopyRecipeFormModal, copyRecipeFormModalApi] = useVbenModal({
  connectedComponent: CopyRecipeForm,
});
async function onRecipeCopy() {
  const recipe = recipesGridApi.grid?.getCurrentRecord();
  if (!recipe) return;
  copyRecipeFormModalApi.setData(recipe).open();
}

// 删除配方
async function onRecipeDelete() {
  // 获取选中行
  const recipe = recipesGridApi.grid?.getCurrentRecord();
  if (!recipe) return;

  const recipeCode = recipe.RECIPECODE;
  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的配方 ${recipe.RECIPECODE} 条数据吗？`,
      icon: 'warning',
      centered: false,
    });

    await deleteRecipes(recipeCode);

    message.success('删除成功');
    handleTabChange();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

// 批准配方
async function onRecipeApprove() {
  // 获取选中行
  const recipe = recipesGridApi.grid?.getCurrentRecord();
  if (!recipe) return;
  const recipeCode = recipe.RECIPECODE;

  try {
    await confirm({
      title: '确认批准',
      content: `确定要批准选中的配方吗？`,
      icon: 'warning',
      centered: false,
    });
    const startDate = new Date();
    const expireDate = new Date(startDate);
    expireDate.setFullYear(startDate.getFullYear() + 1);
    await approveRecipe(startDate, expireDate, recipeCode);

    message.success('批准成功');
    handleTabChange();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

// 停用配方
async function onRecipeRtired() {
  // 获取选中行
  const recipe = recipesGridApi.grid?.getCurrentRecord();
  if (!recipe) return;
  const recipeCode = recipe.RECIPECODE;

  try {
    await confirm({
      title: '确认停用',
      content: `确定要停用选中的配方吗？`,
      icon: 'warning',
      centered: false,
    });
    await retireRecipe(recipeCode);

    message.success('停用成功');
    handleTabChange();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: RecipeDetail,
  destroyOnClose: true,
  class: 'w-[900px]',
});

function viewRecipeDetails(row: MaterialManagerApi.Recipes) {
  formDrawerApi.setData(row).open();
}

// 容器单位列表
const MaterialUnitConvGridOptions = {
  ...createGridOptions<MaterialManagerApi.MaterialUnitConversion>(),
  columns: useMaterialUnitConvColumns(),
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  treeConfig: {
    transform: true, // 指定表格为树形表格
    parentField: 'PARENT', // 父节点字段名
    rowField: 'TEXT', // 行数据字段名
    expandAll: true, // 展开全部
  },
};

const [MaterialUnitGrid, materialUnitGridApi] = useVbenVxeGrid({
  gridOptions: MaterialUnitConvGridOptions,
  // gridEvents: recipeGridEvents,
});

// 添加容器单位
const [AddMaterialUnitFormModal, addMaterialUnitFormModalApi] = useVbenModal({
  connectedComponent: AddMaterialUnitForm,
});
async function onMaterialUnitCreate() {
  const material = gridApi.grid?.getCurrentRecord();
  if (!material) return;
  const matcode = material.MATCODE;
  addMaterialUnitFormModalApi.setData({ MATCODE: matcode }).open();
}
// 删除容器单位
async function onMaterialUnitDelete() {
  // 获取选中行
  const materialUnit = materialUnitGridApi.grid?.getCurrentRecord();
  if (!materialUnit) return;

  const origrec = materialUnit.ORIGREC;
  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的容器单位吗？`,
      icon: 'warning',
      centered: false,
    });

    await deleteMaterialUnitConv(origrec);

    message.success('删除成功');
    handleTabChange();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

// Tab行切换时绑定数据
async function handleTabChange() {
  // message.success(`当前页面${activeKey.value}`);
  const material = gridApi.grid?.getCurrentRecord();
  if (!material) return;
  const matcode = material.MATCODE;
  switch (activeKey.value) {
    case '1': {
      const materialSyonym = await getSynonymsList(matcode);
      const data = materialSyonym.items || [];
      matSinonymGridApi.setGridOptions({
        data,
      });
      break;
    }
    case '2': {
      const prodSupplier = await getProdSuppliersList(matcode);
      const data = prodSupplier.items || [];
      prodSupplierGridApi.setGridOptions({
        data,
      });
      break;
    }
    case '3': {
      const recipe = await getRecipesList(matcode);
      const data = recipe.items || [];
      recipesGridApi.setGridOptions({
        data,
      });

      break;
    }
    case '4': {
      const materialUnit = await getMaterialUnitConvList(matcode);
      const data = materialUnit.items || [];
      materialUnitGridApi.setGridOptions({
        data,
      });
      break;
    }
  }
}
</script>
<template>
  <Page auto-content-height>
    <FormDrawer @success="onRefresh" />
    <FormModal @success="onTabRefresh" />
    <MaterialSinonymFormModal @success="onTabRefresh" />
    <ProdSupplierFormModal @success="onTabRefresh" />
    <RecipeFormModal @success="onTabRefresh" />
    <CopyRecipeFormModal @success="onTabRefresh" />
    <AddMaterialUnitFormModal @success="onTabRefresh" />
    <a-layout style="display: flex; flex-direction: column; height: 100%">
      <a-layout-content style="flex: 1; overflow-y: auto">
        <Grid>
          <template #toolbar-actions>
            <Space>
              <Button type="primary" @click="onMaterialCreate">
                {{ $t('ui.actionTitle.create') }}
              </Button>
              <Button type="primary" danger @click="onMaterialDelete">
                {{ $t('ui.actionTitle.delete') }}
              </Button>
            </Space>
          </template>
          <template #action="{ row }">
            <template v-if="hasEditStatus(row)">
              <Button type="link" @click="saveRowEvent(row)">
                {{ $t('materials-management.save') }}
              </Button>
              <Button type="link" @click="cancelRowEvent(row)">
                {{ $t('materials-management.cancel') }}
              </Button>
            </template>
            <template v-else>
              <Button type="link" @click="editRowEvent(row)">
                {{ $t('materials-management.edit') }}
              </Button>
            </template>
          </template>
        </Grid>
      </a-layout-content>
      <a-layout-content style="flex: 1; padding-top: 16px; overflow-y: auto">
        <Tabs v-model:active-key="activeKey" @change="handleTabChange">
          <TabPane key="1" tab="同义词">
            <div class="h-[350px] overflow-auto">
              <MatSinonymGrid v-if="activeKey === '1'">
                <template #toolbar-actions>
                  <Space>
                    <Button type="primary" @click="onSinonymCreate">
                      {{ $t('ui.actionTitle.create') }}
                    </Button>
                    <Button type="primary" danger @click="onSinonymDelete">
                      {{ $t('ui.actionTitle.delete') }}
                    </Button>
                  </Space>
                </template>
              </MatSinonymGrid>
            </div>
          </TabPane>
          <TabPane key="2" tab="厂商">
            <div class="h-[350px] overflow-auto">
              <ProdSupplierGrid>
                <template #toolbar-actions>
                  <Space>
                    <Button type="primary" @click="onSupplierCreate">
                      {{
                        $t('materials-management.material-manager.addsupplier')
                      }}
                    </Button>
                    <Button type="primary" danger @click="onSupplierDelete">
                      {{
                        $t(
                          'materials-management.material-manager.deletesupplier',
                        )
                      }}
                    </Button>
                  </Space>
                </template>
              </ProdSupplierGrid>
            </div>
          </TabPane>
          <TabPane key="3" tab="配方">
            <div class="h-[350px] overflow-auto">
              <RecipesGrid v-if="activeKey === '3'">
                <template #action="{ row }">
                  <Button type="link" @click="viewRecipeDetails(row)">
                    {{ $t('materials-management.detail') }}
                  </Button>
                </template>
                <template #toolbar-actions>
                  <Space>
                    <Button type="primary" @click="onRecipeCreate">
                      {{
                        $t('materials-management.material-manager.addrecipe')
                      }}
                    </Button>
                    <Button
                      type="primary"
                      danger
                      @click="onRecipeDelete"
                      :disabled="isDeleteRecipeDisabled"
                    >
                      {{
                        $t('materials-management.material-manager.deleterecipe')
                      }}
                    </Button>
                    <Button type="default" @click="onRecipeCopy">
                      {{
                        $t('materials-management.material-manager.copyrecipe')
                      }}
                    </Button>
                    <Button
                      type="default"
                      :disabled="isApprovedRecipeDisabled"
                      @click="onRecipeApprove"
                    >
                      {{ $t('materials-management.material-manager.approve') }}
                    </Button>
                    <Button
                      type="default"
                      :disabled="isRetireRecipeDisabled"
                      @click="onRecipeRtired"
                    >
                      {{ $t('materials-management.material-manager.retire') }}
                    </Button>
                    <Button type="default" :disabled="isUploadFileDisabled">
                      {{
                        $t('materials-management.material-manager.uploadfile')
                      }}
                    </Button>
                    <Button type="default" :disabled="isViewFileDisabled">
                      {{ $t('materials-management.material-manager.viewfile') }}
                    </Button>
                  </Space>
                </template>
              </RecipesGrid>
            </div>
          </TabPane>
          <TabPane key="4" tab="容器单位转换" v-if="showContainerUnitTab">
            <div class="h-[350px] overflow-auto">
              <MaterialUnitGrid v-if="activeKey === '4'">
                <template #toolbar-actions>
                  <Space>
                    <Button type="primary" @click="onMaterialUnitCreate">
                      {{ $t('ui.actionTitle.create') }}
                    </Button>
                    <Button type="primary" danger @click="onMaterialUnitDelete">
                      {{ $t('ui.actionTitle.delete') }}
                    </Button>
                  </Space>
                </template>
              </MaterialUnitGrid>
            </div>
          </TabPane>
        </Tabs>
      </a-layout-content>
    </a-layout>
  </Page>
</template>
