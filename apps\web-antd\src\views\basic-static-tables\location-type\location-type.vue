<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { LocationTypeApi } from '#/api/basic-static-tables/location-type';

import { confirm, Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteLocationType,
  getLocationTypeList,
  updateLocationType,
} from '#/api/basic-static-tables/location-type';

import AddLocationTypeForm from './add-location-type.vue';
import {
  useLocationTypeColumns,
  useLocationTypeFilterSchema,
} from './location-type-data';
import LocationTypeSubLoc from './location-type-subloc.vue';

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: LocationTypeSubLoc,
  destroyOnClose: true,
  class: 'w-[900px]',
});

const gridOptions: VxeTableGridOptions<LocationTypeApi.LocationType> = {
  columns: useLocationTypeColumns(),
  stripe: true,
  border: true,
  checkboxConfig: {
    highlight: true,
    range: true,
    labelField: 'select',
  },
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getLocationTypeList();
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const formOptions: VbenFormProps = {
  schema: useLocationTypeFilterSchema(),
  submitOnChange: false,
  collapsed: true,
  showCollapseButton: true,
  submitButtonOptions: {
    content: '查询',
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: {},
  formOptions,
});

function hasEditStatus(row: LocationTypeApi.LocationType) {
  return gridApi.grid?.isEditByRow(row);
}

function editRowEvent(row: LocationTypeApi.LocationType) {
  gridApi.grid?.setEditRow(row);
}

function viewDetails(row: LocationTypeApi.LocationType) {
  formDrawerApi.setData(row).open();
}

async function saveRowEvent(row: LocationTypeApi.LocationType) {
  await gridApi.grid?.clearEdit();
  updateLocationType(row);
  gridApi.setLoading(true);
  setTimeout(() => {
    gridApi.setLoading(false);
    message.success({
      content: `保存成功！`,
    });
  }, 600);
}

const cancelRowEvent = (_row: LocationTypeApi.LocationType) => {
  gridApi.grid?.clearEdit();
};

function onRefresh() {
  gridApi.query();
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddLocationTypeForm,
  destroyOnClose: true,
});

function onCreate() {
  formModalApi.setData(null).open();
}

async function onDelete() {
  // 获取选中行
  const aTypeId: number[] = gridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.LOCATION_TYPE_ID);

  if (aTypeId.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  if (aTypeId.length > 1) {
    message.warning('只能选择一条数据！');
    return;
  }

  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${aTypeId.length} 条数据吗？`,
      icon: 'warning',
      centered: false,
    });

    const sTypeId = aTypeId[0] as number;
    await deleteLocationType(sTypeId);

    message.success('删除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}
</script>

<template>
  <Page auto-content-height>
    <FormDrawer @success="onRefresh" />
    <FormModal @success="onRefresh" class="w-[600px]" />
    <Grid>
      <template #toolbar-actions>
        <Space>
          <Button type="primary" @click="onCreate">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="onDelete">
            {{ $t('ui.actionTitle.delete') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent(row)">
            {{ $t('basic-static-tables.save') }}
          </Button>
          <Button type="link" @click="cancelRowEvent(row)">
            {{ $t('basic-static-tables.cancel') }}
          </Button>
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">
            {{ $t('basic-static-tables.edit') }}
          </Button>
          <Button type="link" @click="viewDetails(row)">
            {{ $t('basic-static-tables.detail') }}
          </Button>
        </template>
      </template>
    </Grid>
  </Page>
</template>
