import { callServer, getDataSet } from '#/api/core/witlab';

export namespace MaterialTypesApi {
  export interface MaterialTypes {
    ORIGREC: number;
    MATTYPE: string;
    ISPRODUCEMETRIAL: string;
    DESCRIPTION: string;
    DEPT: string;
  }

  export interface MaterialTypesDetail {
    ORIGREC: number;
    MATTYPE: string;
    DEPT: string;
    BATCHCRYSREPORTID: string;
    REQUESTFORM_CRYID: string;
    CERT_SAMPLING: string;
    LABEL_SAMPLE_TEST: string;
    LABEL_SAMPLE_RETAIN: string;
    SAMPLE_ELNID: string;
    RELEASE_ELNID: string;
    SAMPLECYCLE: string;
    NEEDAPPEARANCEINSPECTION: string;
    MATINVENTORY_SIGMODE: string;
    ADDINVENTORY_SIGMODE: string;
    STOCKTAKING_SIGMODE: string;
    INVALID_SIGMODE: string;
    DESTRUCTION_SIGMODE: string;
    RESERVED_RECEIVE_SIGN_MODEL: string;
    RESERVED_DESTROY_SIGN_MODEL: string;
    RESERVED_OBSERVE_SIGN_MODEL: string;
  }

  export interface ChildType {
    ORIGREC: number;
    MATTYPE: string;
    TYPENAME: string;
    TYPEDESC: string;
  }

  export interface AppearanceItem {
    ORIGREC: number;
    MATTYPE: string;
    SORTER: number;
    ITEMNAME: string;
    DEPT: string;
  }

  export interface AppearanceItemResult {
    ORIGREC: number;
    ITEMID: number;
    SORTER: number;
    FILLTYPE: string;
    RESULTSVALUE: string;
  }
}

/**
 * 获取材料类型列表数据
 */
async function getMatTypeList(isShowAll: string) {
  return getDataSet('MATERIAL_TYPES.ds_GetMaterialTypes', [isShowAll]);
}

/**
 * 获取ELN模板数据
 */
async function GetELNTemplate(params: string) {
  return getDataSet('MATERIAL_TYPES.ds_GetELNTemplate', [params]);
}

/**
 * 创建材料类型
 * @param data 材料类型数据
 */
async function addMatType(data: MaterialTypesApi.MaterialTypes) {
  return await callServer('MATERIAL_TYPES.ADD_MATTYPE', [
    data.MATTYPE,
    data.DESCRIPTION,
    data.ISPRODUCEMETRIAL,
  ]);
}

/**
 * 删除材料类型
 * @param origrec 材料类型数据
 */
async function deleteMatType(mattype: string[]) {
  return await callServer('MATERIAL_TYPES.DELETE_MATTYPE', [mattype]);
}

/**
 * 根据材料类型获取获取材料类型详情列表数据
 * @param mattype 材料类型 mattype
 */
async function getMatTypeDetailList(mattype: string) {
  return getDataSet('MATERIAL_TYPES.ds_GetMattypeDetail', [mattype]);
}

/**
 * 更新实验室
 */
async function updateDept() {
  return await callServer('MATERIAL_TYPES.UpdateDept', []);
}

/**
 * 获取子类型详情列表数据
 * @param mattype 材料类型 mattype
 */
async function getChildTypeList(mattype: string) {
  return getDataSet('MATERIAL_TYPES.dsGetChildType', [mattype]);
}

/**
 * 创建子类型
 * @param data 材料类型数据
 */
async function addChildType(data: MaterialTypesApi.ChildType) {
  return await callServer('MATERIAL_TYPES.AddChildMattype', [
    data.MATTYPE,
    data.TYPENAME,
    data.TYPEDESC,
  ]);
}

/**
 * 删除子类型
 * @param origrec origrec
 */
async function deleteChildType(origrec: number[]) {
  return await callServer('MATERIAL_TYPES.DeleteChildMattype', [origrec]);
}

/**
 * 根据材料类型获取检查项目列表数据
 * @param mattype 材料类型 mattype
 */
async function getAppearanceItemList(mattype: string, dept: string) {
  return getDataSet('MATERIAL_TYPES.dsGetInspectionItems', [mattype, dept]);
}

/**
 * 创建检查项目
 * @param data 检查项目数据
 */
async function addAppearanceItem(data: MaterialTypesApi.AppearanceItem) {
  return await callServer('MATERIAL_TYPES.AddAppearanceItem', [
    data.MATTYPE,
    data.ITEMNAME,
    data.SORTER,
    data.DEPT,
  ]);
}

/**
 * 删除检查项目
 * @param origrec origrec
 */
async function deleteAppearanceItem(origrec: number) {
  return await callServer('MATERIAL_TYPES.DeleteAppearanceItem', [origrec]);
}

/**
 * 根据项目ID获取检查项目结果列表数据
 * @param itemid 项目ID
 */
async function getAppearanceItemResultList(itemid: number) {
  return getDataSet('MATERIAL_TYPES.dsGetInspectionItemResults', [itemid]);
}

/**
 * 创建检查项目
 * @param data 检查项目数据
 */
async function addAppearanceItemResult(origrec: number) {
  return await callServer('MATERIAL_TYPES.AddItemResult', [origrec]);
}

/**
 * 删除检查项目结果
 * @param origrec origrec
 */
async function deleteAppearanceItemResult(origrec: number) {
  return await callServer('MATERIAL_TYPES.DeleteItemResult', [origrec]);
}

export {
  addAppearanceItem,
  addAppearanceItemResult,
  addChildType,
  addMatType,
  deleteAppearanceItem,
  deleteAppearanceItemResult,
  deleteChildType,
  deleteMatType,
  getAppearanceItemList,
  getAppearanceItemResultList,
  getChildTypeList,
  GetELNTemplate,
  getMatTypeDetailList,
  getMatTypeList,
  updateDept,
};
