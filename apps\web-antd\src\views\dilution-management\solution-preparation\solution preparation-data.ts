import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { SolutionPreparationApi } from '#/api/dilution-management/solution-preparation';

import dayjs from 'dayjs';

import { $t } from '#/locales';
import {
  getJob<PERSON>pi,
  getSolutionConditionApi,
  getAfterManApi,
} from '#/api/dilution-management/solution-preparation';

export function testColumns(): VxeTableGridOptions<SolutionPreparationApi.Form>['columns'] {
  return [
    {
      align: 'center',
      field: 'STATUS',
      title: $t('dilution-management.solution-preparation.status'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'BATCHNO',
      title: $t('dilution-management.solution-preparation.batchNumber'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SEQUENCY',
      title: $t('dilution-management.solution-preparation.solutionId'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'DILUTION_NAME',
      title: $t('dilution-management.solution-preparation.solutionName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'CONSISTENCY',
      title: $t('dilution-management.solution-preparation.concentration'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'UNIT',
      title: $t('dilution-management.solution-preparation.concentrationUnit'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'VALIDITYDATE',
      title: $t('dilution-management.solution-preparation.expiryDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
      editRender: { name: 'input', attrs: { type: 'date' } },

      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'CONDITION',
      title: $t('dilution-management.solution-preparation.storageCondition'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'LOCATION',
      title: $t('dilution-management.solution-preparation.storageLocation'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'return',
      title: $t('dilution-management.solution-preparation.isReturned'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'USERNAME',
      title: $t('dilution-management.solution-preparation.preparedBy'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'CREATEDATE',
      title: $t('dilution-management.solution-preparation.preparationDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SERVGRP',
      title: $t('dilution-management.solution-preparation.position'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MEDIUM',
      title: $t('dilution-management.solution-preparation.solvent'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'REMARKS',
      title: $t('dilution-management.solution-preparation.remark'),
      filterRender: {
        name: 'TableFilterInput',
      },
      editRender: { name: 'input' },

      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ELN_ID',
      title: $t('dilution-management.solution-preparation.elnTemplateId'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ELN_ORIGREC',
      title: $t('dilution-management.solution-preparation.elnRunId'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('business-static-tables.operation'),
      minWidth: 140,
    },
  ];
}
export function standardColumns(): VxeTableGridOptions<SolutionPreparationApi.Form>['columns'] {
  return [
    {
      align: 'center',
      field: 'STATUS',
      title: $t('dilution-management.solution-preparation.status'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'BATCHNO',
      title: $t('dilution-management.solution-preparation.batchNumber'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SEQUENCY',
      title: $t('dilution-management.solution-preparation.solutionId'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'DILUTION_NAME',
      title: $t('dilution-management.solution-preparation.solutionName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'CONSISTENCY',
      title: $t('dilution-management.solution-preparation.concentration'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'UNIT',
      title: $t('dilution-management.solution-preparation.concentrationUnit'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'VALIDITYDATE',
      title: $t('dilution-management.solution-preparation.expiryDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
      editRender: { name: 'input', attrs: { type: 'date' } },

      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'CONDITION',
      title: $t('dilution-management.solution-preparation.storageCondition'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'LOCATION',
      title: $t('dilution-management.solution-preparation.storageLocation'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ISRETURN',
      title: $t('dilution-management.solution-preparation.isReturned'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'USERNAME',
      title: $t('dilution-management.solution-preparation.preparedBy'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'CREATEDATE',
      title: $t('dilution-management.solution-preparation.preparationDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ISMIX',
      title: $t('dilution-management.solution-preparation.isMixedLabel'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SERVGRP',
      title: $t('dilution-management.solution-preparation.position'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MEDIUM',
      title: $t('dilution-management.solution-preparation.solvent'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },

    {
      align: 'center',
      field: 'ELN_ID',
      title: $t('dilution-management.solution-preparation.elnTemplateId'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ELN_ORIGREC',
      title: $t('dilution-management.solution-preparation.elnRunId'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MATCODE',
      title: $t('dilution-management.solution-preparation.materialCode'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'PH',
      title: $t('dilution-management.solution-preparation.phValue'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: { name: 'input' },
      sortable: true,
    },

    {
      align: 'center',
      field: 'BOTTLENUM',
      title: $t('dilution-management.solution-preparation.bottlesCount'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      editRender: { name: 'input' },
    },
    {
      align: 'center',
      field: 'CONFIGURATIONAMOUNT',
      title: $t('dilution-management.solution-preparation.preparationAmount'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SPEC',
      title: $t('dilution-management.solution-preparation.packingSpec'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      editRender: { name: 'input' },
    },
    {
      align: 'center',
      field: 'AMOUTUNIT',
      title: $t('dilution-management.solution-preparation.quantityUnit'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      editRender: { name: 'input' },
    },
    {
      align: 'center',
      field: 'STERILIZEUSER',
      title: $t('dilution-management.solution-preparation.sterilizer'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'STERILIZEDATE',
      title: $t('dilution-management.solution-preparation.sterilizationDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'REMARKS',
      title: $t('dilution-management.solution-preparation.remark'),
      filterRender: {
        name: 'TableFilterInput',
      },
      editRender: { name: 'input' },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('business-static-tables.operation'),
      minWidth: 140,
    },
  ];
}
export function titrationColumns(): VxeTableGridOptions<SolutionPreparationApi.Form>['columns'] {
  return [
    {
      align: 'center',
      field: 'STATUS',
      title: $t('dilution-management.solution-preparation.status'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'DILUTION_NAME',
      title: $t('dilution-management.solution-preparation.solutionName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SEQUENCY',
      title: $t('dilution-management.solution-preparation.solutionId'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'BATCHNO',
      title: $t('dilution-management.solution-preparation.batchNumber'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },

    {
      align: 'center',
      field: 'CONSISTENCY',
      title: $t('dilution-management.solution-preparation.concentration'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'UNIT',
      title: $t('dilution-management.solution-preparation.concentrationUnit'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'FVALUE',
      title: $t('dilution-management.solution-preparation.F_value'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'VALIDITYDATE',
      title: $t('dilution-management.solution-preparation.expiryDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
      editRender: { name: 'input', attrs: { type: 'date' } },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'CONDITION',
      title: $t('dilution-management.solution-preparation.storageCondition'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'LOCATION',
      title: $t('dilution-management.solution-preparation.storageLocation'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },

    {
      align: 'center',
      field: 'USERNAME',
      title: $t('dilution-management.solution-preparation.preparedBy'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'CREATEDATE',
      title: $t('dilution-management.solution-preparation.preparationDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'USERNAME',
      title: $t('dilution-management.solution-preparation.calibrationDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'CREATEDATE',
      title: $t('dilution-management.solution-preparation.recalibrator'),
      filterRender: {
        name: 'TableFilterInput',
      },
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'CALIBRATIONDATE',
      title: $t('dilution-management.solution-preparation.recalibrationDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ISRETURN',
      title: $t('dilution-management.solution-preparation.isReturned'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SERVGRP',
      title: $t('dilution-management.solution-preparation.position'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'REMARKS',
      title: $t('dilution-management.solution-preparation.remark'),
      filterRender: {
        name: 'TableFilterInput',
      },
      editRender: { name: 'input' },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ELN_ID',
      title: $t('dilution-management.solution-preparation.elnTemplateId'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ELN_ORIGREC',
      title: $t('dilution-management.solution-preparation.elnRunId'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'CHECKPERSON',
      title: $t('dilution-management.solution-preparation.calibrator'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: { name: 'input' },
      sortable: true,
    },
    {
      align: 'center',
      field: 'MAKETEMPERATURE',
      title: $t(
        'dilution-management.solution-preparation.preparationTemperature',
      ),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: { name: 'input' },
      sortable: true,
    },
    {
      align: 'center',
      field: 'CHECKCOUNT',
      title: $t('dilution-management.solution-preparation.calibrationCount'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: { name: 'input' },
      sortable: true,
    },

    {
      align: 'center',
      field: 'CHECKTEMPERATURE',
      title: $t(
        'dilution-management.solution-preparation.calibrationTemperature',
      ),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: { name: 'input' },
      sortable: true,
    },
    {
      align: 'center',
      field: 'CONFIGURATIONAMOUNT',
      title: $t('dilution-management.solution-preparation.preparationAmount'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: { name: 'input' },
      sortable: true,
    },
    {
      align: 'center',
      field: 'PH',
      title: $t('dilution-management.solution-preparation.phValue'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: { name: 'input' },
      sortable: true,
    },

    {
      align: 'center',
      field: 'BOTTLENUM',
      title: $t(
        'dilution-management.solution-preparation.packagingBottleCount',
      ),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: { name: 'input' },
      sortable: true,
    },
    {
      align: 'center',
      field: 'MATCODE',
      title: $t('dilution-management.solution-preparation.materialCode'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SPEC',
      title: $t(
        'dilution-management.solution-preparation.packagingSpecification',
      ),
      filterRender: {
        name: 'TableFilterInput',
      },
      editRender: { name: 'input' },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'AMOUTUNIT',
      title: $t('dilution-management.solution-preparation.quantityUnit'),
      filterRender: {
        name: 'TableFilterInput',
      },
      editRender: { name: 'input' },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'STERILIZEUSER',
      title: $t('dilution-management.solution-preparation.sterilizer'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'STERILIZEDATE',
      title: $t('dilution-management.solution-preparation.sterilizationDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('business-static-tables.operation'),
      minWidth: 140,
    },
  ];
}
export function historyColumns(): VxeTableGridOptions<SolutionPreparationApi.Form>['columns'] {
  return [
    {
      align: 'center',
      field: 'OPERATIONTYPE',
      title: $t('dilution-management.solution-preparation.operation'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'STEPNAME',
      title: $t('dilution-management.solution-preparation.stepName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'OPERATIONMAN',
      title: $t('dilution-management.solution-preparation.operator'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'OPERATIONDATE',
      title: $t('dilution-management.solution-preparation.operationTime'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss') : '';
      },
      sortable: true,
    },
    {
      align: 'center',
      field: 'OPERATIONRESON',
      title: $t('dilution-management.solution-preparation.remark'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}
export function fileColumns(): VxeTableGridOptions<SolutionPreparationApi.Form>['columns'] {
  return [
    {
      align: 'center',
      field: 'STARDOC_ID',
      title: $t('dilution-management.solution-preparation.attachmentId'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ATTACHMENTNAME',
      title: $t('dilution-management.solution-preparation.attachmentName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'UPLOADDATE',
      title: $t('dilution-management.solution-preparation.uploadDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'UPLOADUSRFULLNAME',
      title: $t('dilution-management.solution-preparation.uploader'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'COMMENTS',
      title: $t('dilution-management.solution-preparation.remark'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}
export function soluteColumns(): VxeTableGridOptions<SolutionPreparationApi.Form>['columns'] {
  return [
    {
      align: 'center',
      field: 'MATNAME',
      title: $t('dilution-management.solution-preparation.materialName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'BATCHNO',
      title: $t('dilution-management.solution-preparation.batchNumber'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'CONCENTRATION',
      title: $t('dilution-management.solution-preparation.concentration'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'UNIT_NAME',
      title: $t('dilution-management.solution-preparation.concentrationUnit'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}
export function commitModalSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Select',
      fieldName: 'approver',
      label: '请选择复核人',
      componentProps: {
        class: 'w-full',
        allowClear: true,
        filterOption: true,
        options: [],
        placeholder: '请选择',
        showSearch: true,
      },
      rules: 'required',
    },
  ];
}

export function preparationModalSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'solutionName',
      label: $t('dilution-management.solution-preparation.solutionName'),
    },
    {
      component: 'ApiSelect',
      fieldName: 'position',
      label: $t('dilution-management.solution-preparation.position'),
      componentProps: {
        api: getJobApi,
        labelField: 'Text',
        valueField: 'Value',
        class: 'w-full',
      },
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: getSolutionConditionApi,
        labelField: 'Text',
        valueField: 'Value',
        class: 'w-full',
      },
      rules: 'required',
      fieldName: 'storageCondition',
      label: $t('dilution-management.solution-preparation.storageCondition'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'storageLocation',
      label: $t('dilution-management.solution-preparation.storageLocation'),
    },
    {
      component: 'Select',
      // componentProps: {
      //   api: getSoultionNameApi,
      //   params: { type: 'tbTestSolution' },
      //   labelField: 'Text',
      //   valueField: 'Value',
      //   class: 'w-full',
      // },
      rules: 'required',
      fieldName: 'solutionType',
      label: $t('dilution-management.solution-preparation.solutionType'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'solvent',
      label: $t('dilution-management.solution-preparation.solvent'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'concentration',
      label: $t('dilution-management.solution-preparation.concentration'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'concentrationUnit',
      label: $t('dilution-management.solution-preparation.concentrationUnit'),
    },
    {
      component: 'Select',
      fieldName: 'isMixedLabel',
      label: $t('dilution-management.solution-preparation.isMixedLabel'),
      componentProps: {
        class: 'w-full',
        allowClear: true,
        filterOption: true,
        options: [
          {
            value: 'Y',
            label: '是',
          },
          {
            value: 'N',
            label: '否',
          },
        ],
        placeholder: '请选择',
        showSearch: true,
      },
      formItemClass: 'hidden',
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'F_value',
      label: $t('dilution-management.solution-preparation.F_value'),
      formItemClass: 'hidden',
    },
    {
      component: 'DatePicker',
      componentProps: {
        type: 'date',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        class: 'w-full',
      },
      fieldName: 'calibrationDate',
      label: $t('dilution-management.solution-preparation.calibrationDate'),
      formItemClass: 'hidden',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: getAfterManApi,
        labelField: 'Text',
        valueField: 'Value',
        class: 'w-full',
      },
      fieldName: 'recalibrator',
      label: $t('dilution-management.solution-preparation.recalibrator'),
      formItemClass: 'hidden',
    },
    {
      component: 'DatePicker',
      componentProps: {
        type: 'date',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        class: 'w-full',
      },
      fieldName: 'recalibrationDate',
      label: $t('dilution-management.solution-preparation.recalibrationDate'),
      formItemClass: 'hidden',
    },
  ];
}
export function addSoluteColumns(): VxeTableGridOptions<SolutionPreparationApi.Form>['columns'] {
  return [
    {
      align: 'center',
      field: 'MATNAME',
      title: $t('dilution-management.solution-preparation.materialName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'BATCHNO',
      title: $t('dilution-management.solution-preparation.batchNumber'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'PURITY',
      title: $t('dilution-management.solution-preparation.purity'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'UNIT_NAME',
      title: $t('dilution-management.solution-preparation.unit'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}
export function addReserveColumns(): VxeTableGridOptions<SolutionPreparationApi.Form>['columns'] {
  return [
    {
      align: 'center',
      field: 'MATNAME',
      title: $t('dilution-management.solution-preparation.materialName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'BATCHNO',
      title: $t('dilution-management.solution-preparation.batchNumber'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },

    {
      align: 'center',
      field: 'CONSISTENCY',
      title: $t(
        'dilution-management.solution-preparation.stockSolutionConcentration',
      ),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'UNIT_NAME',
      title: $t('dilution-management.solution-preparation.concentrationUnit'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}
export function deviceColumns(): VxeTableGridOptions<SolutionPreparationApi.Form>['columns'] {
  return [
    {
      align: 'center',
      field: 'EQID',
      title: $t('dilution-management.solution-preparation.equipmentId'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'EQNAME',
      title: $t('dilution-management.solution-preparation.equipmentName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}
export function deviceDetailColumns(): VxeTableGridOptions<SolutionPreparationApi.Form>['columns'] {
  return [
    {
      align: 'center',
      field: 'EQSTARTDATE',
      title: $t('dilution-management.solution-preparation.startTime'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'EQENDDATE',
      title: $t('dilution-management.solution-preparation.endTime'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'EXPIRY_DATE',
      title: $t('dilution-management.solution-preparation.validityPeriod'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}
export function materialColumns(): VxeTableGridOptions<SolutionPreparationApi.Form>['columns'] {
  return [
    {
      align: 'center',
      field: 'ID',
      title: '编号',
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MATNAME',
      title: '材料名称',
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MATTYPE',
      title: '材料类型',
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'TYPE',
      title: '类别',
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}

export function addMaterialColumns(): VxeTableGridOptions<SolutionPreparationApi.Form>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'INVENTORYID',
      title: $t('dilution-management.solution-preparation.stockNumber'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'BATCHNO',
      title: $t('dilution-management.solution-preparation.batchNumber'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MATNAME',
      title: $t('dilution-management.solution-preparation.materialName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'EXPIRE_DATE',
      title: $t('dilution-management.solution-preparation.expiryDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'PURITY',
      title: $t('dilution-management.solution-preparation.concentration'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MATCODE',
      title: $t('dilution-management.solution-preparation.materialCode'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MATTYPE',
      title: $t('dilution-management.solution-preparation.type'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'BEGINDATE',
      title: $t('dilution-management.solution-preparation.openingDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ENDDATE',
      title: $t(
        'dilution-management.solution-preparation.shelfLifeAfterOpening',
      ),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'DISPSTATUS',
      title: $t('dilution-management.solution-preparation.status'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
  ];
}
export function addSolutionColumns(): VxeTableGridOptions<SolutionPreparationApi.Form>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'BATCHNO',
      title: $t('dilution-management.solution-preparation.batchNumber'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'DILUTION_NAME',
      title: $t('dilution-management.solution-preparation.solutionName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'CREATEDATE',
      title: $t('dilution-management.solution-preparation.preparationDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'VALIDITYDATE',
      title: $t('dilution-management.solution-preparation.expiryDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'CONSISTENCY',
      title: $t('dilution-management.solution-preparation.concentration'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'UNIT',
      title: $t('dilution-management.solution-preparation.concentrationUnit'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'USERNAME',
      title: $t('dilution-management.solution-preparation.preparedBy'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'TYPE',
      title: $t('dilution-management.solution-preparation.solutionType'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'OPENEDDTAE',
      title: $t('dilution-management.solution-preparation.openingDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'DISPSTATUS',
      title: $t('dilution-management.solution-preparation.chineseStatus'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'CONDITION',
      title: $t('dilution-management.solution-preparation.storageCondition'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
  ];
}
export function addBacteriaColumns(): VxeTableGridOptions<SolutionPreparationApi.Form>['columns'] { 
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'BATCHNO',
      title: $t('dilution-management.solution-preparation.strainNumber'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'TYPENAME',
      title: $t('dilution-management.solution-preparation.strainType'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'NAME',
      title: $t('dilution-management.solution-preparation.strainName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'GENERATION',
      title: $t('dilution-management.solution-preparation.passageGeneration'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'GENERATIONDATE',
      title: $t('dilution-management.solution-preparation.passageDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'TYPE',
      title: $t('dilution-management.solution-preparation.passageType'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'NUM',
      title: $t('dilution-management.solution-preparation.passageSerialNumber'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'EXPTO',
      title: $t('dilution-management.solution-preparation.expiryDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
  ];
}
