import { createVNode } from 'vue';

import { Modal } from 'ant-design-vue';

import AuditViewer from '#/components/audit-viewer/audit-viewer.vue';

let modalInstance: any = null;

export interface AduitViewerOptions {
  tableName: string;
  origrec: number | string;
  title?: string;
}

export const showAduitViewer = (options: AduitViewerOptions) => {
  const { tableName, origrec, title = '审计跟踪' } = options;

  // 如果已经存在实例，先销毁
  if (modalInstance) {
    modalInstance.destroy();
  }

  // 创建新的 Modal 实例
  modalInstance = Modal.info({
    title,
    width: '60vw',
    icon: null,
    content: () => {
      return createVNode(AuditViewer, {
        tableName,
        origrec,
      });
    },
    okText: '关闭',
    cancelText: null,
    maskClosable: false,
    keyboard: true,
  });
};

export const closeAduitViewer = () => {
  if (modalInstance) {
    modalInstance.destroy();
    modalInstance = null;
  }
};
