<script setup lang="ts">
import type { Dayjs } from 'dayjs';

import { computed, onMounted, reactive, ref } from 'vue';

import {
  Button,
  Card,
  Col,
  DatePicker,
  Descriptions,
  Form,
  Input,
  message,
  Radio,
  Result,
  Row,
  Space,
  Steps,
} from 'ant-design-vue';
import dayjs from 'dayjs';

import { validateUserPassword } from '#/api';
import { $t } from '#/locales';

export interface ElectronicSignatureOptions {
  eventCode: string;
  needPassword?: boolean;
  needWitness?: boolean;
  showComment?: boolean;
  commentRequired?: boolean;
  commentEditable?: boolean;
  defaultComment?: string;
  showEffectiveDate?: boolean;
  showExpiryDate?: boolean;
  defaultEffectiveDate?: Dayjs;
  defaultExpiryDate?: Dayjs;
  showAgreement?: boolean;
  agreementText?: string;
}

export interface ElectronicSignatureData {
  hasEsig: boolean;
  silent: boolean;
  comment?: string;
  needPassword?: boolean;
  needWitness?: boolean;
  username: string;
  witnessUsername?: string;
  allowAgreement?: boolean;
  agreementText?: string;
  effectiveDate?: Dayjs;
  expiryDate?: Dayjs;
}

const props = defineProps<{
  options: ElectronicSignatureOptions;
}>();

const emit = defineEmits<{
  cancel: [];
  confirm: [data: ElectronicSignatureData];
}>();

const currentStep = ref(0);
const currentTime = ref('');

const formData = reactive({
  username: '',
  password: '',
  comment: props.options.defaultComment,
  effectiveDate: props.options.defaultEffectiveDate,
  expiryDate: props.options.defaultExpiryDate,
  allowAgreement: false,
  agreementText: props.options.agreementText,
  witnessUsername: '',
  witnessPassword: '',
});

const stepItems = computed(() => {
  const items = [
    {
      title: $t('components.esig.step1_title'),
      description: $t('components.esig.step1_description'),
      disabled: true,
    },
  ];

  if (props.options.needWitness) {
    items.push({
      title: $t('components.esig.step2_title'),
      description: $t('components.esig.step2_description'),
      disabled: true,
    });
  }

  items.push({
    title: $t('components.esig.step3_title'),
    description: $t('components.esig.step3_description'),
    disabled: true,
  });

  return items;
});

const canProceedToWitness = computed(() => {
  let valid = Boolean(formData.username && formData.password);

  if (props.options.showAgreement) {
    valid = valid && Boolean(formData.allowAgreement);
  }

  if (props.options.showComment && props.options.commentRequired) {
    valid = valid && !!formData.comment;
  }

  return valid;
});

const canConfirm = computed(() => {
  if (currentStep.value === 0 && !props.options.needWitness) {
    return canProceedToWitness.value;
  }

  if (currentStep.value === 1 && props.options.needWitness) {
    return (
      canProceedToWitness.value &&
      formData.witnessUsername &&
      formData.witnessPassword
    );
  }

  return false;
});

const finishStepIndex = computed(() => (props.options.needWitness ? 2 : 1));

const updateCurrentTime = () => {
  currentTime.value = dayjs().format('MM/DD/YYYY hh:mm:ss A');
};

const vaildateUser = async () => {
  if (!formData.username || !formData.password) {
    message.error($t('components.esig.username_password_required'));
    return false;
  }

  try {
    const validateResult = await validateUserPassword(
      formData.username,
      formData.password,
    );

    if (!validateResult) {
      message.error($t('components.esig.password_error'));
      return false;
    }
    return true;
  } catch {
    message.error($t('components.esig.password_validation_failed'));
    return false;
  }
};

const vaildateWitness = async () => {
  if (!formData.witnessUsername || !formData.witnessPassword) {
    message.error($t('components.esig.username_password_required'));
    return false;
  }

  if (formData.username === formData.witnessUsername) {
    message.error($t('components.esig.witness_cannot_be_same_as_user'));
    return false;
  }

  try {
    const validateResult = await validateUserPassword(
      formData.witnessUsername,
      formData.witnessPassword,
    );

    if (!validateResult) {
      message.error($t('components.esig.password_error'));
      return false;
    }
    return true;
  } catch {
    message.error($t('components.esig.password_validation_failed'));
    return false;
  }
};

const handleNext = async () => {
  if (props.options.needPassword) {
    const valid = await vaildateUser();
    if (!valid) {
      return;
    }
  }

  if (canProceedToWitness.value) {
    currentStep.value = 1;
  }
};

const handlePrevious = () => {
  currentStep.value = 0;
};

const handleConfirm = async () => {
  const data: ElectronicSignatureData = {
    username: formData.username,
    hasEsig: true,
    silent: false,
    needPassword: props.options.needPassword,
    needWitness: props.options.needWitness,
  };

  if (props.options.needPassword) {
    const valid = await vaildateUser();
    if (!valid) {
      return;
    }
  }

  if (props.options.showComment) {
    data.comment = formData.comment;
  }

  if (props.options.showEffectiveDate) {
    data.effectiveDate = formData.effectiveDate;
  }

  if (props.options.showExpiryDate) {
    data.expiryDate = formData.expiryDate;
  }

  if (props.options.showAgreement) {
    data.allowAgreement = formData.allowAgreement;
    data.agreementText = formData.agreementText;
  }

  if (props.options.needWitness) {
    data.witnessUsername = formData.witnessUsername;
    const valid = await vaildateWitness();
    if (!valid) {
      return;
    }
  }

  currentStep.value = finishStepIndex.value;

  setTimeout(() => {
    emit('confirm', data);
  }, 1000);
};

const handleCancel = () => {
  emit('cancel');
};

onMounted(() => {
  updateCurrentTime();
  // 每秒更新时间
  setInterval(updateCurrentTime, 1000);
});
</script>

<template>
  <div class="electronic-signature">
    <!-- 步骤条 -->
    <Steps
      v-model:current="currentStep"
      :items="stepItems"
      class="signature-container mb-4 mt-4"
    />
    <div class="signature-scroll-content">
      <div class="signature-container">
        <!-- Step 1: 用户签名 -->
        <div v-show="currentStep === 0" class="step-content">
          <!-- 用户签名区域 -->
          <Card
            :title="$t('components.esig.title')"
            class="mb-4"
            :head-style="{ fontSize: '16px', minHeight: '48px' }"
          >
            <Form
              layout="horizontal"
              label-align="right"
              :label-col="{ span: 4 }"
            >
              <Form.Item :label="$t('components.esig.username')">
                <Input
                  v-model:value="formData.username"
                  :placeholder="$t('components.esig.username_placeholder')"
                  style="width: 280px"
                />
              </Form.Item>
              <Form.Item :label="$t('components.esig.password')">
                <Input.Password
                  v-model:value="formData.password"
                  :placeholder="$t('components.esig.password_placeholder')"
                  style="width: 280px"
                />
              </Form.Item>
            </Form>
          </Card>

          <!-- 其他可选控件 -->
          <div v-if="options.showComment">
            <Card
              :title="$t('components.esig.reason')"
              class="mb-4"
              :head-style="{ fontSize: '16px', minHeight: '48px' }"
            >
              <Input.TextArea
                v-model:value="formData.comment"
                :required="options.commentRequired"
                :disabled="!options.commentEditable"
                :rows="3"
                :placeholder="$t('components.esig.reason_placeholder')"
              />
            </Card>
          </div>

          <Row
            :gutter="16"
            v-if="options.showEffectiveDate || options.showExpiryDate"
          >
            <Col :span="12" v-if="options.showEffectiveDate">
              <Card
                :title="$t('components.esig.effective_date')"
                class="mb-4"
                :head-style="{ fontSize: '16px', minHeight: '48px' }"
              >
                <DatePicker
                  v-model:value="formData.effectiveDate"
                  format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </Card>
            </Col>
            <Col :span="12" v-if="options.showExpiryDate">
              <Card
                :title="$t('components.esig.expiry_date')"
                class="mb-4"
                :head-style="{ fontSize: '16px', minHeight: '48px' }"
              >
                <DatePicker
                  v-model:value="formData.expiryDate"
                  format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </Card>
            </Col>
          </Row>

          <div v-if="options.showAgreement">
            <Card
              :title="$t('components.esig.agreement')"
              class="mb-4"
              :head-style="{ fontSize: '16px', minHeight: '48px' }"
            >
              <div class="agreement-content">
                {{
                  options.agreementText || $t('components.esig.agreement_text')
                }}
              </div>
              <Radio.Group
                v-model:value="formData.allowAgreement"
                class="mt-4 flex justify-center"
              >
                <Radio :value="false">
                  {{ $t('components.esig.disagree') }}
                </Radio>
                <Radio :value="true">{{ $t('components.esig.agree') }}</Radio>
              </Radio.Group>
            </Card>
          </div>
        </div>

        <!-- Step 2: 见证 (如果需要) -->
        <div
          v-if="options.needWitness"
          v-show="currentStep === 1"
          class="step-content"
        >
          <!-- 显示第一步信息（只读） -->
          <Card
            :title="$t('components.esig.signature_info')"
            class="mb-4"
            :head-style="{ fontSize: '16px', minHeight: '48px' }"
          >
            <Descriptions :column="1" bordered>
              <Descriptions.Item :label="$t('components.esig.username')">
                {{ formData.username }}
              </Descriptions.Item>
              <Descriptions.Item
                :label="$t('components.esig.reason')"
                v-if="options.showComment"
              >
                {{ formData.comment }}
              </Descriptions.Item>
              <Descriptions.Item
                :label="$t('components.esig.effective_date')"
                v-if="options.showEffectiveDate"
              >
                {{
                  formData.effectiveDate
                    ? dayjs(formData.effectiveDate).format('YYYY-MM-DD')
                    : ''
                }}
              </Descriptions.Item>
              <Descriptions.Item
                :label="$t('components.esig.expiry_date')"
                v-if="options.showExpiryDate"
              >
                {{
                  formData.expiryDate
                    ? dayjs(formData.expiryDate).format('YYYY-MM-DD')
                    : ''
                }}
              </Descriptions.Item>
              <Descriptions.Item
                :label="$t('components.esig.agreement')"
                v-if="options.showAgreement"
              >
                {{
                  formData.allowAgreement
                    ? $t('components.esig.agree')
                    : $t('components.esig.disagree')
                }}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          <!-- 见证签名区域 -->
          <Card
            :title="$t('components.esig.witness')"
            class="mb-4"
            :head-style="{ fontSize: '16px', minHeight: '48px' }"
          >
            <Form
              layout="horizontal"
              label-align="right"
              :label-col="{ span: 4 }"
            >
              <Form.Item :label="$t('components.esig.username')">
                <Input
                  v-model:value="formData.witnessUsername"
                  :placeholder="
                    $t('components.esig.witness_username_placeholder')
                  "
                  style="width: 280px"
                />
              </Form.Item>
              <Form.Item :label="$t('components.esig.password')">
                <Input.Password
                  v-model:value="formData.witnessPassword"
                  :placeholder="
                    $t('components.esig.witness_password_placeholder')
                  "
                  style="width: 280px"
                />
              </Form.Item>
            </Form>
          </Card>
        </div>

        <!-- "完成" 步骤 -->
        <div v-show="currentStep === finishStepIndex" class="step-content">
          <Result
            status="success"
            :title="$t('components.esig.completion_title')"
            :sub-title="$t('components.esig.completion_subtitle')"
          />
        </div>
      </div>
    </div>
    <!-- 操作按钮 -->
    <div class="footer-buttons" v-if="currentStep !== finishStepIndex">
      <div class="signature-container">
        <Space>
          <Button @click="handleCancel">
            {{ $t('components.esig.cancel') }}
          </Button>
          <Button v-if="currentStep > 0" @click="handlePrevious">
            {{ $t('components.esig.previous_step') }}
          </Button>
          <Button
            v-if="currentStep === 0 && options.needWitness"
            type="primary"
            @click="handleNext"
            :disabled="!canProceedToWitness"
          >
            {{ $t('components.esig.next_step') }}
          </Button>
          <Button
            v-if="
              (currentStep === 0 && !options.needWitness) ||
              (currentStep === 1 && options.needWitness)
            "
            type="primary"
            @click="handleConfirm"
            :disabled="!canConfirm"
          >
            {{ $t('components.esig.confirm') }}
          </Button>
        </Space>
      </div>
    </div>
  </div>
</template>

<style scoped>
.electronic-signature {
  display: flex;
  flex-direction: column;
  height: 65vh;
  overflow: hidden;
}

.signature-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.signature-scroll-content {
  flex: 1;
  padding: 12px;
  overflow-y: auto;
}

.step-content {
  margin: 12px 0;
}

.agreement-content {
  max-height: 200px;
  padding: 16px;
  overflow-y: auto;
  line-height: 1.6;
  background-color: #f9f9f9;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.footer-buttons {
  flex-shrink: 0;
  padding: 12px 24px;
  text-align: right;
  border-top: 1px solid #f0f0f0;
}

.mb-4 {
  margin-bottom: 16px;
}

.mb-6 {
  margin-bottom: 24px;
}

.mt-4 {
  margin-top: 16px;
}
</style>
