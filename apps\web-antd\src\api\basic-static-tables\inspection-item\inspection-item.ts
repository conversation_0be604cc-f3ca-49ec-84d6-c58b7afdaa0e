import { callServer, getDataSet } from '#/api/core/witlab';

export namespace InspectionItemApi {
  export interface InspectionItem {
    [key: string]: any;
    ORIGREC: number;
    ITEM_TYPE: string;
    ITEM_CONTENT: string;
    EDITFLAG: string;
    ITEM_SORT: string;
    FILENAME: string;
  }
}

const $getInspectionItemListApi = async () => {
  const res = await getDataSet('INSPECTION_ITEM.dgInspectionItemData', []);
  return res;
};

const $addInspectionItemApi = async (
  data: Omit<InspectionItemApi.InspectionItem, 'ORIGREC'>,
) => {
  const res = callServer('INSPECTION_ITEM.AddInspectionItem', [
    data.ITEM_TYPE,
    data.ITEM_CONTENT,
    data.EDITFLAG,
    data.ITEM_SORT,
    data.FILENAME,
  ]);
  return res;
};

const $delInspectionItemApi = async (
  origrecs: number[],
  comment: string = '',
) => {
  const res = callServer('INSPECTION_ITEM.DeleteInspectionItem', [
    origrecs,
    comment,
  ]);
  return res;
};

export {
  $addInspectionItemApi,
  $delInspectionItemApi,
  $getInspectionItemListApi,
};
