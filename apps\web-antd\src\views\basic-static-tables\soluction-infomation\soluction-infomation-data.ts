import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { SolutionInfoApi } from '#/api/basic-static-tables/soluction-infomation';

import { $t } from '#/locales';

export function useColumns(): VxeTableGridOptions<SolutionInfoApi.SolutionInfo>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SOLUCTIONNAME',
      title: $t('basic-static-tables.solutionInfo.solutionName'),
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'TYPE',
      width: 200,
      title: $t('commons.type'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      editRender: {
        name: 'input',
      },
    },
    {
      align: 'center',
      field: 'CONFIGURE_METHOD',
      title: $t('basic-static-tables.solutionInfo.configureMethod'),

      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      editRender: {
        name: 'textarea',
      },
    },

    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('commons.action'),
      width: 140,
    },
  ];
}

export function useSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'SOLUCTIONNAME',
      label: $t('basic-static-tables.solutionInfo.solutionName'),
      rules: 'required',
    },

    {
      component: 'Input',
      fieldName: 'TYPE',
      label: $t('commons.type'),
      rules: 'required',
    },
    {
      component: 'Textarea',
      fieldName: 'CONFIGURE_METHOD',
      label: $t('basic-static-tables.solutionInfo.configureMethod'),
      rules: 'required',
    },
  ];
}

export function useFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'SOLUCTIONNAME',
      label: $t('basic-static-tables.solutionInfo.solutionName'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'TYPE',
      label: $t('commons.type'),
    },
  ];
}
