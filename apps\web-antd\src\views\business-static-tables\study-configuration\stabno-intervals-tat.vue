<script lang="ts" setup>
import type { XFolderApi } from '#/api/business-static-tables';

import { ref } from 'vue';

import { alert, prompt, useVbenModal } from '@vben/common-ui';

import { Button, Select } from 'ant-design-vue';

import {
  $getIntervalTat_ssl,
  $getStabnoIntervalsTat,
  $updateStabnoIntervalTat,
} from '#/api/business-static-tables';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import { useXFolderIntervalTatColumns } from './data';

interface FormArgs {
  openingMode: string;
  stabNo: number;
}
const formArgs = ref<FormArgs>();

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<FormArgs>();
      if (data) {
        formArgs.value = data;
      }
    }
  },
  onCancel: () => {
    modalApi.close();
  },
  showConfirmButton: false,
  showCancelButton: false,
});
const { Grid, gridApi } = useLimsGridsConfig<XFolderApi.XFolderIntervalTat>(
  useXFolderIntervalTatColumns(),
  [],
  async () => {
    if (!formArgs.value) {
      return [];
    }
    return await $getStabnoIntervalsTat({ stabNo: formArgs.value?.stabNo });
  },
  {
    pagerConfig: {
      enabled: false,
    },
  },
);

const editingRow = ref<XFolderApi.XFolderIntervalTat>();
async function updateTat(row: XFolderApi.XFolderIntervalTat) {
  editingRow.value = row;
  showSelectPrompt();
}

async function showSelectPrompt() {
  if (!editingRow.value || !formArgs.value) return;
  const options = await $getIntervalTat_ssl({
    dept: editingRow.value.DEPT,
    interval: editingRow.value.INTERVAL,
  });
  prompt({
    async beforeClose(scope) {
      if (scope.isConfirm && !scope.value) {
        alert('请选择一个选项');
        return false;
      }
      return true;
    },
    component: Select,
    componentProps: {
      options,
      placeholder: $t('commons.selectPlaceholder'),
      popupClassName: 'pointer-events-auto',
      fieldNames: { label: 'Text', value: 'Value' },
    },
    content: '',
    icon: 'question',
    modelPropName: 'value',
  })
    .then((val) => {
      if (val) {
        if (!editingRow.value || !formArgs.value) return;
        return $updateStabnoIntervalTat({
          stabNo: formArgs.value.stabNo,
          interval: editingRow.value.INTERVAL,
          intervalTatOrigrec: val,
          dept: editingRow.value.DEPT,
        });
      }
    })
    .then((res) => {
      if (res) {
        gridApi.query();
      }
    });
}
</script>

<template>
  <Modal :title="$t('business-static-tables.studyConfiguration.intervalTat')">
    <!-- <SelectTatModal
      :title="$t('business-static-tables.studyConfiguration.selectintervalTat')"
    >
      <Form class="mx-4" />
    </SelectModal> -->
    <div class="h-[500px]">
      <Grid>
        <template #action="{ row }">
          <Button type="link" @click="updateTat(row)">
            {{ $t('commons.edit') }}
          </Button>
        </template>
      </Grid>
    </div>
  </Modal>
</template>
