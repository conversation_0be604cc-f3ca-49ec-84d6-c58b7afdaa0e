import { callServer, getDataSet } from '#/api/core/witlab';

export namespace SampleTypeApi {
  export interface SampleType {
    [key: string]: any;
    ORIGREC: number;
    SAMPLE_TYPE: string;
    DESCRIPTION: string;
    SAMPLEMANAGEMENT: string;
  }
}

const $getSampleTypeApi = async () => {
  const data = await getDataSet('SAMPLE_TYPE.getSampleType', []);
  return data;
};
const $addSampleTypeApi = async (
  data: Omit<SampleTypeApi.SampleType, 'ORIGREC'>,
  eventCode: string = 'AddSampleType',
  comment: string = '',
) => {
  const result = await callServer('SAMPLE_TYPE.AddSampleType', [
    data.SAMPLE_TYPE,
    data.DESCRIPTION,
    data.SAMPLEMANAGEMENT,
    eventCode,
    comment,
  ]);
  return result;
};
const $deleteSampleTypeApi = async (ORIGREC: number) => {
  const result = await callServer('SAMPLE_TYPE.DeleteSampleType', [ORIGREC]);
  return result;
};

export { $addSampleTypeApi, $deleteSampleTypeApi, $getSampleTypeApi };
