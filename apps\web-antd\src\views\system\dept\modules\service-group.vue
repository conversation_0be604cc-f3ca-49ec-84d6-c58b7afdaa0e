<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@vben-core/shadcn-ui';

import { Button } from 'ant-design-vue';
import { EditIcon } from 'lucide-vue-next';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  callServer,
  getAnalystListByServiceGroup,
  getServiceGroupListByDept,
  updateProvider,
} from '#/api';
import { showAduitViewer } from '#/components/audit-viewer';
import { $t } from '#/locales';
import { saveEditingRowOriginalData } from '#/utils/lims-grids-config';

import ServiceGroupAnalystForm from './service-group-analyst-form.vue';
import ServiceGroupForm from './service-group-form.vue';

const props = defineProps<{
  deptCode?: string;
}>();

const currentServGrpRow = ref<any>(null);

const [ServGrpForm, servGrpFormApi] = useVbenModal({
  connectedComponent: ServiceGroupForm,
  destroyOnClose: true,
});

const [ServGrpAnalystForm, servGrpAnalystFormApi] = useVbenModal({
  connectedComponent: ServiceGroupAnalystForm,
  destroyOnClose: true,
});

const [ServGrpGrid, servGrpGridApi] = useVbenVxeGrid({
  gridEvents: {
    currentRowChange: (params: any) => {
      currentServGrpRow.value = params.row;
      servGrpAnalystGridApi.grid.clearData();
      servGrpAnalystGridApi.query();
    },
    editActivated: saveEditingRowOriginalData,
    editClosed: updateProvider,
    cellMenu: ({ row, $grid }) => $grid.setCurrentRow(row),
    menuClick: ({ menu }) => {
      if (menu.code === 'viewAudit') {
        const currentRow = servGrpGridApi.grid?.getCurrentRecord();
        if (currentRow) {
          showAduitViewer({
            tableName: servGrpGridApi.grid.params.tableName,
            origrec: currentRow.ORIGREC,
          });
        }
      }
    },
  },
  gridOptions: {
    columns: [
      {
        align: 'left',
        field: 'ORIGREC',
        title: $t('commons.origrec'),
        visible: false,
        minWidth: 150,
      },
      {
        field: 'SERVGRP',
        title: $t('system.dept.serviceGroup.serviceGroup'),
        minWidth: 100,
      },
      {
        field: 'SERVGRPCODE',
        title: $t('system.dept.serviceGroup.serviceGroupCode'),
        minWidth: 100,
      },
      {
        field: 'DESCS',
        title: $t('system.dept.serviceGroup.serviceGroupDescription'),
        minWidth: 200,
        editRender: { name: 'VxeInput' },
      },
      {
        cellRender: {
          attrs: {
            beforeChange: onAutoReleaseChange,
          },
          props: {
            checkedChildren: $t('commons.yes'),
            checkedValue: 'Y',
            unCheckedChildren: $t('commons.no'),
            unCheckedValue: 'N',
          },
          name: 'CellSwitch',
        },
        // formatter: ({ cellValue }) => {
        //   return cellValue === 'Y' ? $t('commons.yes') : $t('commons.no');
        // },
        field: 'AUTORELEASE',
        title: $t('system.dept.serviceGroup.autoRelease'),
        visible: false,
        width: 100,
      },
      {
        cellRender: {
          attrs: { beforeChange: onRequireReceiveInLabChange },
          props: {
            checkedChildren: $t('commons.yes'),
            checkedValue: 'Y',
            unCheckedChildren: $t('commons.no'),
            unCheckedValue: 'N',
          },
          name: 'CellSwitch',
        },
        // formatter: ({ cellValue }) => {
        //   return cellValue === 'Y' ? $t('commons.yes') : $t('commons.no');
        // },
        field: 'REQUIRERECEIVEINLAB',
        title: $t('system.dept.serviceGroup.requireReceiveInLab'),
        visible: false,
        width: 100,
      },
      {
        field: 'DEPT',
        title: $t('system.dept.serviceGroup.dept'),
        visible: false,
        minWidth: 150,
      },
    ],
    editConfig: {
      trigger: 'click',
      mode: 'cell',
    },
    height: 'auto',
    pagerConfig: {
      enabled: false,
    },
    proxyConfig: {
      ajax: {
        query: async (_params) => {
          if (props.deptCode) {
            return await getServiceGroupListByDept(props.deptCode);
          }
          return [];
        },
      },
    },
    toolbarConfig: {
      slots: {
        buttons: 'toolbarButtons',
      },
      custom: true,
      export: false,
      refresh: { code: 'query' },
      zoom: true,
    },
    columnConfig: {
      resizable: true,
    },
    rowConfig: {
      isCurrent: true,
      keyField: 'ORIGREC',
    },
    menuConfig: {
      body: {
        options: [
          [
            {
              code: 'viewAudit',
              name: '查看历史',
              prefixConfig: { icon: 'vxe-icon-table' },
              visible: true,
              disabled: false,
            },
          ],
        ],
      },
    },
    keepSource: true,
    params: {
      limsControlId: 'dgdServgrp',
      tableName: 'SERVGRP',
    },
  } as VxeTableGridOptions,
});

const [ServGrpAnalystGrid, servGrpAnalystGridApi] = useVbenVxeGrid({
  gridEvents: {
    // currentRowChange: (params: any) => {},
  },
  gridOptions: {
    columns: [
      {
        align: 'left',
        field: 'ORIGREC',
        title: $t('commons.origrec'),
        visible: false,
        minWidth: 150,
      },
      {
        field: 'FULLNAME',
        title: $t('system.dept.serviceGroup.analystFullName'),
        minWidth: 100,
      },
    ],
    height: 'auto',
    keepSource: true,
    pagerConfig: {
      enabled: false,
    },
    proxyConfig: {
      ajax: {
        query: async (_params) => {
          if (currentServGrpRow.value) {
            return await getAnalystListByServiceGroup(
              currentServGrpRow.value.DEPT,
              currentServGrpRow.value.SERVGRP,
            );
          }
          return [];
        },
      },
    },
    toolbarConfig: {
      slots: {
        buttons: 'toolbarButtons',
      },
      custom: true,
      export: false,
      refresh: { code: 'query' },
      zoom: true,
    },
    columnConfig: {
      resizable: true,
    },
    rowConfig: {
      isCurrent: true,
      keyField: 'ORIGREC',
    },
  } as VxeTableGridOptions,
});

async function onAutoReleaseChange(newState: number, row: any) {
  try {
    return await callServer('Common.Update', [
      'SERVGRP',
      'AUTORELEASE',
      newState,
      row.ORIGREC,
    ]);
  } catch {
    return false;
  }
}

async function onRequireReceiveInLabChange(newState: number, row: any) {
  try {
    return await callServer('Common.Update', [
      'SERVGRP',
      'REQUIRERECEIVEINLAB',
      newState,
      row.ORIGREC,
    ]);
  } catch {
    return false;
  }
}

async function onServGrpEdit() {
  servGrpFormApi.setData({ deptCode: props.deptCode }).open();
}

async function onServGrpAnalystEdit() {
  servGrpAnalystFormApi.setData(currentServGrpRow.value).open();
}

watch(
  () => props.deptCode,
  (currentCode, oldCode) => {
    if (currentCode !== oldCode) {
      servGrpGridApi.grid.clearData();
      servGrpGridApi.query();
    }
  },
  { immediate: true },
);
</script>

<template>
  <ResizablePanelGroup direction="horizontal">
    <ResizablePanel :default-size="50">
      <ServGrpForm @success="servGrpGridApi.query()" />
      <ServGrpGrid class="mx-2">
        <template #toolbarButtons>
          <Button
            type="primary"
            @click="onServGrpEdit"
            :disabled="!props.deptCode"
          >
            <EditIcon class="size-5" />
            {{
              $t('ui.actionTitle.edit2', [
                $t('system.dept.serviceGroup.serviceGroup'),
              ])
            }}
          </Button>
        </template>
      </ServGrpGrid>
    </ResizablePanel>
    <ResizableHandle />
    <ResizablePanel :default-size="50">
      <ServGrpAnalystForm @success="servGrpAnalystGridApi.query()" />
      <ServGrpAnalystGrid class="mx-2">
        <template #toolbarButtons>
          <Button
            type="primary"
            @click="onServGrpAnalystEdit"
            :disabled="!currentServGrpRow"
          >
            <EditIcon class="size-5" />
            {{
              $t('ui.actionTitle.edit2', [
                $t('system.dept.serviceGroup.analystFullName'),
              ])
            }}
          </Button>
        </template>
      </ServGrpAnalystGrid>
    </ResizablePanel>
  </ResizablePanelGroup>
</template>
