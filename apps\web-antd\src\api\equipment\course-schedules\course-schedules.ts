import { callServer, getDataSet, getDataSetNoPage } from '#/api/core/witlab';

export namespace CourseSchedulesFormApi {
  export interface CourseSchedulesForm {
    [key: string]: any;
    ORIGREC: number;
    NAME: string;
    EFFECT: string;
    TYPE: string;
    SORTER: number;
  }
  export interface RowType {
    [key: string]: any;
  }
}


async function getScheduleApi(params: Array<any>) {
  return getDataSet('COURSE_SCHEDULES.dsSearchSchedule', params);
}
async function getParticpantsApi(params: Array<any>) {
  return getDataSet('COURSE_SCHEDULES.DS_PARTICIPANTS2', params);
}

async function getMethodApi(params: Array<any>) {
  return getDataSet('COURSE_SCHEDULES.DS_GetMethod', params);
}
async function getHistoryApi(params: Array<any>) {
  return getDataSet('COURSE_SCHEDULES.getHistory', params);
}
async function getCboScheduledCoursesApi(params: Array<any>) {
  return getDataSetNoPage('COURSE_SCHEDULES.cboScheduledCourses', []);
}
async function getDsUsersInACourseApi(params: Array<any>) {
  return getDataSetNoPage('COURSE_SCHEDULES.dsUsersInACourse', []);
}
async function getCboStatusApi(params: Array<any>) {
  return getDataSetNoPage('COURSE_SCHEDULES.COURSE_SCHEDULES_SEARCHER_cboStatus_DataSource', []);
}
async function getServiceGroupApi(params: Array<any>) {
  return getDataSetNoPage('COURSE_SCHEDULES.DS_SERVICE_GROUP', []);
}
async function getDsStatusApi(params: Array<any>) {
  return getDataSetNoPage('COURSE_SCHEDULES.DS_GETSTATUS', []);
}
async function addTrainingApi(params: Array<string>) {
  return await callServer('COURSE_SCHEDULES.scAddTraining', params);
}
async function scCourseExistsApi(params: Array<string>) {
  return await callServer('COURSE_SCHEDULES.scCourseExists', params);
}
async function scDeleteTrainingApi(params: Array<string>) {
  return await callServer('COURSE_SCHEDULES.scDeleteTraining', params);
}
async function getDeptListApi() {
  return getDataSetNoPage('COURSE_SCHEDULES.COURSE_SCHEDULES_ADD_PARTICIPANTS_PROMPT_cboSite_DataSource', []);
}
async function getParticPantsListApi(params: Array<any>) {
  return getDataSetNoPage('COURSE_SCHEDULES.DS_ADD_PARTICIPANTS', params);
}
async function addParticiPantApi(params: Array<string>) {
  return await callServer('COURSE_SCHEDULES.ADD_PARTICIPANTS', params);
}

async function inviteParticiPantApi(params: Array<any>) {
  return await callServer('COURSE_SCHEDULES.INVITE_PARTICIPANTS', params);
}
async function certifyApi(params: Array<any>) {
  return await callServer('COURSE_SCHEDULES.CERTIFY', params);
}
async function updateProviderApi(params: Array<any>) {
  return await callServer('Runtime_Support.WS_UPDATEPROVIDER', params);
}

export {
  getParticpantsApi,
  getScheduleApi,
  getMethodApi,
  getHistoryApi,
  getCboScheduledCoursesApi,
  getDsUsersInACourseApi,
  getCboStatusApi,
  getServiceGroupApi,
  getDsStatusApi,
  addTrainingApi,
  scCourseExistsApi,
  scDeleteTrainingApi,
  getDeptListApi,
  getParticPantsListApi,
  addParticiPantApi,
  inviteParticiPantApi,
  certifyApi,
  updateProviderApi
};
