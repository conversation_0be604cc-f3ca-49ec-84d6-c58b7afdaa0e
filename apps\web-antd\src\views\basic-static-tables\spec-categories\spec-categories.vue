<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { SpecCategoriesApi } from '#/api/basic-static-tables/spec-categories';

import { confirm, Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Button, message, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteSpecCategory,
  getSpecCategoryList,
} from '#/api/basic-static-tables/spec-categories';

import AddSpecCategoryForm from './add-spec-category.vue';
import {
  useSpecCategoryColumns,
  useSpecCategoryFilterSchema,
} from './spec-categories-data';

const gridOptions: VxeTableGridOptions<SpecCategoriesApi.SpecCategorries> = {
  columns: useSpecCategoryColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getSpecCategoryList();
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const formOptions: VbenFormProps = {
  schema: useSpecCategoryFilterSchema(),
  submitOnChange: false,
  collapsed: true,
  showCollapseButton: true,
  submitButtonOptions: {
    content: '查询',
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  formOptions,
});

function onRefresh() {
  gridApi.query();
}

// 添加质量标准分类
const [AddSpecCategoryFormModal, specCategoryModalApi] = useVbenModal({
  connectedComponent: AddSpecCategoryForm,
});

async function addSpecCategory() {
  specCategoryModalApi.setData(null).open();
}

// 删除质量标准分类
async function deleteSpecCategoryConfirm() {
  // 获取选中行
  const aSpecCategory: string[] = gridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.SPECCATEGORY);

  if (aSpecCategory.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${aSpecCategory.length} 条数据吗？`,
      icon: 'warning',
      centered: false,
    });

    await deleteSpecCategory(aSpecCategory);

    message.success('删除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}
</script>
<template>
  <Page auto-content-height>
    <FormDrawer @success="onRefresh" />
    <AddSpecCategoryFormModal @success="onRefresh" />
    <Grid>
      <template #toolbar-actions>
        <Space>
          <Button type="primary" @click="addSpecCategory">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="deleteSpecCategoryConfirm">
            {{ $t('ui.actionTitle.delete') }}
          </Button>
        </Space>
      </template>
    </Grid>
  </Page>
</template>
