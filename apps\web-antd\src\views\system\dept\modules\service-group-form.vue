<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message, Transfer } from 'ant-design-vue';

import { callServer, getSelectedServiceGroupListByDept } from '#/api';
import { $t } from '#/locales';

interface TransferItem {
  key: string;
  title: string;
  selected: boolean;
}
const emit = defineEmits(['success']);
const deptCode = ref<string>('');

const ds = ref<TransferItem[]>([]);

const targetKeys = ref<string[]>([]);

const pendingSubmitData = ref<{
  direction: string;
  moveKeys: string[];
  targetKeys: string[];
}>({
  direction: '',
  moveKeys: [],
  targetKeys: [],
});

const getData = async (deptCode: string) => {
  try {
    modalApi.lock();
    const keys: string[] = [];
    const tData = await getSelectedServiceGroupListByDept<{
      selected: string;
      SERVGRP: string;
    }>(deptCode);
    ds.value = tData.map(({ selected, SERVGRP }) => {
      return {
        key: SERVGRP,
        title: SERVGRP,
        selected: selected === 'true',
      };
    });
    for (const item of ds.value) {
      if (item.selected) {
        keys.push(item.key);
      }
    }
    targetKeys.value = keys;
  } finally {
    modalApi.lock(false);
  }
};
const filterOption = (inputValue: string, item: TransferItem) => {
  return item.title.includes(inputValue);
};
const handleChange = async (
  targetKeys: string[],
  direction: string,
  moveKeys: string[],
) => {
  pendingSubmitData.value = {
    targetKeys,
    direction,
    moveKeys,
  };
};

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { targetKeys } = pendingSubmitData.value;
    try {
      modalApi.lock();
      await (targetKeys.length === 0
        ? callServer('LABS.DEL_TEAMS', [deptCode.value])
        : callServer('LABS.UPDATE_TEAMS', [targetKeys, deptCode.value]));
      modalApi.close();
      emit('success');
    } catch {
      message.error($t('system.dept.serviceGroup.updateFailed'));
      await getData(deptCode.value);
    } finally {
      modalApi.lock(false);
    }
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<{ deptCode: string }>();
      if (data) {
        deptCode.value = data.deptCode;
        await getData(deptCode.value);
      }
    }
  },
});
</script>

<template>
  <Modal
    :title="
      $t('ui.actionTitle.edit2', [$t('system.dept.serviceGroup.serviceGroup')])
    "
    class="flex h-[720px] w-fit justify-center gap-4"
  >
    <Transfer
      class="mx-4 h-full"
      :row-key="(record) => record?.key || ''"
      :list-style="{ width: '300px', height: '100%' }"
      v-model:target-keys="targetKeys"
      :data-source="ds"
      show-search
      :filter-option="filterOption"
      :render="(item) => item.title"
      @change="handleChange"
    />
  </Modal>
</template>
