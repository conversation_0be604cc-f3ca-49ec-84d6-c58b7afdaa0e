<script lang="ts" setup>
import type { Recordable } from '@vben/types';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Select, Skeleton, Transfer } from 'ant-design-vue';

import {
  $getMaterialsMultichoiceApi,
  $getMaterialTypesApi,
} from '#/api/business-static-tables';

const emit = defineEmits(['updateMaterials']);

interface FormArgs {
  stabNo: number;
  materials: string[];
}
const formArgs = ref<FormArgs>(); // 表单数据

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  onConfirm: async () => {
    emit('updateMaterials', targetKeys.value);
    modalApi.close();
  },
  onCancel: () => {
    modalApi.close();
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      // 当模态框打开时，重新获取数据
      const data = modalApi.getData<FormArgs>();
      if (data) {
        formArgs.value = data;
        matTypeList.value = await $getMaterialTypesApi();
        await getMatList();
        if (data.materials?.length > 0) {
          data.materials.forEach((item) => {
            targetKeys.value.push(item);
          });
        }
        materialList.value.forEach((item) => {
          if (item.Selected === 'true') {
            targetKeys.value.push(item.Value);
          }
        });
        showSkeleton.value = false;
      }
    }
  },
});

const targetKeys = ref<string[]>([]); // 目标列表
const materialList = ref<Recordable<any>[]>([]); // 物料列表
const matTypeList = ref<{ label: string; value: string }[]>([]);
const selectType = ref<string>('');
const showSkeleton = ref<boolean>(true);
const getMatList = async () => {
  if (!formArgs.value) return;
  const param = {
    stabNo: formArgs.value.stabNo.toString(),
    materials: formArgs.value.materials?.toString() ?? '',
    matType: selectType.value,
    addMaterials: '',
  };
  showSkeleton.value = true;
  materialList.value = await $getMaterialsMultichoiceApi(param);
  showSkeleton.value = false;
};
</script>

<template>
  <Modal :title="$t('business-static-tables.studyConfiguration.editMaterials')">
    <div
      class="flex h-full w-full items-center justify-center"
      style="width: 100%; min-height: 500px; padding: 20px"
    >
      <div class="flex h-full w-full flex-col gap-4">
        <Select
          :options="matTypeList"
          v-model:value="selectType"
          class="mb-4 w-[200px]"
          :field-names="{ label: 'MATTYPE', value: 'MATTYPE' }"
          @select="getMatList"
        />
        <Skeleton
          :loading="showSkeleton"
          :paragraph="{ rows: 8 }"
          active
          class="flex-1 p-[8px]"
        >
          <Transfer
            v-model:target-keys="targetKeys"
            :data-source="materialList"
            show-search
            :list-style="{
              width: '100%',
              height: '500px',
            }"
            :render="(item) => `${item.Text}`"
            :row-key="(record) => record.Value"
            class="w-full"
          >
            <template #notFoundContent>
              <span>没数据</span>
            </template>
          </Transfer>
        </Skeleton>
      </div>
    </div>
  </Modal>
</template>
