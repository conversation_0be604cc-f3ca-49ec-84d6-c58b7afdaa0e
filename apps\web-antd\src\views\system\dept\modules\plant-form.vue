<script lang="ts" setup>
import { computed, ref } from 'vue';

import { useVbenModal, z } from '@vben/common-ui';

import { Button, message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { createPlant, getSiteAreas } from '#/api';
import { $t } from '#/locales';

const emit = defineEmits(['success']);
const formData = ref<Record<string, any> & { DEPT: string; ORIGREC?: string }>(
  {},
);
const getTitle = computed(() => {
  return formData.value?.ORIGREC
    ? $t('ui.actionTitle.edit', [$t('system.dept.plant.plant')])
    : $t('ui.actionTitle.create', [$t('system.dept.plant.plant')]);
});

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: [
    {
      component: 'ApiSelect',
      componentProps: {
        api: () => getSiteAreas(formData.value.DEPT),
        labelField: 'AREA_NAME',
        valueField: 'AREA_NAME',
        class: 'w-full',
      },
      fieldName: 'AREA_NAME',
      label: $t('system.dept.plant.areaName'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'PLANT',
      label: $t('system.dept.plant.plantName'),
      rules: z
        .string()
        .min(
          2,
          $t('ui.formRules.minLength', [$t('system.dept.plant.plantName'), 2]),
        )
        .max(
          20,
          $t('ui.formRules.maxLength', [$t('system.dept.plant.plantName'), 20]),
        ),
    },
    {
      component: 'Textarea',
      componentProps: {
        maxLength: 200,
        showCount: true,
        rows: 3,
        class: 'w-full',
      },
      fieldName: 'DESCRIPTION',
      label: $t('system.dept.plant.plantDescription'),
      rules: z
        .string()
        .max(
          500,
          $t('ui.formRules.maxLength', [
            $t('system.dept.plant.plantDescription'),
            500,
          ]),
        )
        .optional(),
    },
  ],
  showDefaultActions: false,
});

function resetForm() {
  formApi.resetForm();
  formApi.setValues(formData.value || {});
}

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (valid) {
      modalApi.lock();
      const data = await formApi.getValues();
      try {
        const newOrignalRecord = await createPlant({
          ...data,
          DEPT: formData.value.DEPT,
        });
        if (newOrignalRecord === -1) {
          message.error(
            $t('system.dept.plant.plantAlreadyExists', [data.PLANT]),
          );
          resetForm();
          return;
        }
        modalApi.close();
        emit('success');
      } finally {
        modalApi.lock(false);
      }
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<Record<string, any> & { DEPT: string }>();
      if (data) {
        formData.value = data;
        formApi.setValues(formData.value);
      }
    }
  },
});
</script>

<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
    <template #prepend-footer>
      <div class="flex-auto">
        <Button type="primary" danger @click="resetForm">
          {{ $t('common.reset') }}
        </Button>
      </div>
    </template>
  </Modal>
</template>
