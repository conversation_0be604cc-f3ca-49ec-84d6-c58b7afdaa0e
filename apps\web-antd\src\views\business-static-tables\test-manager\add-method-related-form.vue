<script lang="ts" setup>
import type { TestManagerApi } from '#/api/business-static-tables';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import { $addMethodRelatedTestApi } from '#/api/business-static-tables';
import { $t } from '#/locales';

import { useMethodRelatedTestSchema } from './data';

const emit = defineEmits(['success']);

const testData = ref<null | TestManagerApi.Test>(null);

const getTitle = computed(() => {
  return $t('ui.actionTitle.create', [
    $t('business-static-tables.testManager.possibleResult'),
  ]);
});

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: useMethodRelatedTestSchema(),
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    modalApi.lock();
    try {
      data.TESTCODE = testData.value?.TESTCODE;
      await $addMethodRelatedTestApi(data);
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<{
        testData: TestManagerApi.Test;
      }>();
      if (data) {
        testData.value = data.testData;
      }
    }
  },
});
</script>

<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
  </Modal>
</template>
