<script lang="ts" setup>
import { onBeforeUnmount, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, Input, message, Space, Textarea } from 'ant-design-vue';

const emit = defineEmits(['success']);

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    modalApi.lock();
    try {
      emit('success');

      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      // const data = modalApi.getData<SampleTypeApi.SampleType>();
    }
  },
});
let ws: null | WebSocket = null;

// 组件卸载时关闭连接
onBeforeUnmount(() => {
  ws?.close();
});
const initWebSocket = () => {
  ws = new WebSocket(baseurl.value);

  // eslint-disable-next-line unicorn/prefer-add-event-listener
  ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    result.value = JSON.stringify(data);
  };
};
const sendMessage = () => {
  if (ws?.readyState === WebSocket.OPEN) {
    ws.send(JSON.stringify(data.value));
  }
};
const baseurl = ref('');
const data = ref('');
const result = ref('');
const connect = () => {
  if (typeof WebSocket === 'undefined') {
    message.error('您的浏览器不支持WebSocket');
  } else {
    initWebSocket();
  }
};
const disconnect = () => {
  ws?.close();
};
</script>
<template>
  <Modal title="websocket相关信息">
    <Space :size="[16, 0]">
      <Input v-model:value="baseurl" />
      <Button type="primary" primary @click="connect">
        {{ $t('equipment.main-form.connect') }}
      </Button>
      <Button type="primary" primary @click="disconnect">
        {{ $t('equipment.main-form.disconnect') }}
      </Button>
    </Space>
    <div>
      <div class="my-5">
        {{ $t('equipment.main-form.sendData') }}
      </div>
      <Textarea v-model:value="data" :auto-size="{ minRows: 2, maxRows: 5 }" />
      <div class="my-5">
        <Button type="primary" primary @click="sendMessage">
          {{ $t('equipment.main-form.send') }}
        </Button>
      </div>
    </div>
    <div class="my-5">
      <div class="my-5">{{ $t('equipment.main-form.receiveData') }}</div>

      <Textarea
        v-model:value="result"
        :auto-size="{ minRows: 2, maxRows: 5 }"
      />
    </div>
  </Modal>
</template>
