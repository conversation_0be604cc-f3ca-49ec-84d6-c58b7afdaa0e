<script lang="ts" setup>
import type { SampleGroupsApi } from '#/api/business-static-tables/sample-groups';

import { computed, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { message, Step, Steps } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { addTestPlan } from '#/api/business-static-tables/sample-groups';
import { $t } from '#/locales';

// import TestTransferModal from './edit-test-list.vue';
import ProfileGrid from './profiles.vue';
import SampleRequirementGrid from './requirements.vue';
import SpecsGrid from './setup-specs.vue';

const emit = defineEmits(['success']);
const currentTab = ref(0);
const sampleGroupCode = ref<string>('');
const sTestPlanGroup = ref<string>('');
const sMatCode = ref<string>('');

const profile = ref<string>('');
const spCode = ref<number>(0);
const sDrawNo = ref<number>(1);
async function onFirstSubmit(values: Record<string, any>) {
  const data = values as SampleGroupsApi.IpSampleGroupDetails;
  data.SAMPLEGROUPCODE = sampleGroupCode.value;
  data.PRODGROUP = sTestPlanGroup.value;
  data.MATCODE = sMatCode.value;
  const nAddTestPlan = await addTestPlan(data);
  if (nAddTestPlan === -99) {
    message.warn($t('business-static-tables.sampleGroups.testPlanExists'));
    return;
  }
  spCode.value = nAddTestPlan;
  currentTab.value = 1;
}

const [TestPlanForm] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  handleSubmit: onFirstSubmit,
  layout: 'vertical',
  resetButtonOptions: {
    show: false,
  },
  schema: [
    // {
    //   component: 'ApiSelect',
    //   componentProps: {
    //     allowClear: true,
    //     filterOption: true,
    //     showSearch: true,
    //     api: async () => {
    //       const res = await getSpecCategory();
    //       return res.items.map(
    //         (item: { SPECCATEGORY: string; Text: string }) => ({
    //           label: item.Text,
    //           value: item.SPECCATEGORY,
    //         }),
    //       );
    //     },
    //     // autoSelect: 'first',
    //     immediate: true,
    //   },
    //   fieldName: 'MATCODE',
    //   label: $t('business-static-tables.sampleGroups.matcode'),
    // },
    // {
    //   component: 'Input',
    //   fieldName: 'MATCODE',
    //   label: $t('business-static-tables.sampleGroups.matcode'),
    //   // rules: 'required',
    // },
    // {
    //   component: 'Input',
    //   fieldName: 'MATNAME',
    //   label: $t('business-static-tables.sampleGroups.matname'),
    // },
    {
      component: 'Input',
      fieldName: 'PROGNAME',
      label: $t('business-static-tables.sampleGroups.progname'),
      rules: 'required',
    },
  ],
  submitButtonOptions: {
    content: '下一步',
  },
  wrapperClass: 'grid-cols-1 md:grid-cols-1 lg:grid-cols-1',
});

const getTitle = computed(() => {
  return $t('ui.actionTitle.create', [tabTitleMap[currentTab.value]]);
});
const tabTitleMap = ['添加样品', '编辑质量标准', '添加方案', '取样要求'];

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  showConfirmButton: true,
  confirmDisabled: true,
  onConfirm: async () => {
    emit('success');
    modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData<SampleGroupsApi.IpSampleGroups>();
      if (data) {
        sampleGroupCode.value = data.SAMPLEGROUPCODE;
        sTestPlanGroup.value = data.PRODGROUP;
        sMatCode.value = data.MATCODE;
      }
    }
  },
});

async function onSpecSuccess() {
  currentTab.value = 2;
}
async function onProfileSuccess() {
  currentTab.value = 3;
  modalApi.setState({ confirmDisabled: false });
}
</script>

<template>
  <Modal :title="getTitle" class="h-[1000px] w-[1200px]">
    <Page>
      <div class="mx-auto">
        <Steps :current="currentTab" class="steps">
          <Step :title="tabTitleMap[0]" />
          <Step :title="tabTitleMap[1]" />
          <Step :title="tabTitleMap[2]" />
          <Step :title="tabTitleMap[3]" />
        </Steps>
        <div class="p-0">
          <TestPlanForm v-show="currentTab === 0" class="p-10" />
          <SpecsGrid
            v-if="currentTab === 1"
            :sp-code="spCode"
            @success="onSpecSuccess"
          />

          <ProfileGrid
            v-if="currentTab === 2"
            :sp-code="spCode"
            :draw-no="sDrawNo"
            @success="onProfileSuccess"
          />
          <div class="h-[550px] w-full">
            <SampleRequirementGrid
              v-if="currentTab === 3"
              :profile="profile"
              :sample-group-code="sampleGroupCode"
              :sp-code="spCode"
            />
          </div>
        </div>
      </div>
    </Page>
  </Modal>
</template>
