/**
 * 审批工作流组件类型定义
 */

// 工作流模式
export type WorkflowMode = 'edit' | 'view';

// 节点类型
export type NodeType = 
  | 'start'           // 开始节点
  | 'approval'        // 审批节点
  | 'condition'       // 条件节点
  | 'parallel'        // 并行节点
  | 'merge'           // 合并节点
  | 'end';            // 结束节点

// 审批人类型
export interface Approver {
  id: string;
  name: string;
  email?: string;
  department?: string;
  role?: string;
}

// 条件规则
export interface ConditionRule {
  field: string;        // 字段名
  operator: string;     // 操作符：eq, ne, gt, lt, gte, lte, in, not_in
  value: any;          // 比较值
  label?: string;      // 显示标签
}

// 节点属性
export interface NodeProperties {
  // 通用属性
  name?: string;
  description?: string;
  
  // 审批节点属性
  approvers?: Approver[];
  approvalType?: 'single' | 'all' | 'majority';  // 审批类型：单人、全部、多数
  timeLimit?: number;                             // 审批时限（小时）
  autoApprove?: boolean;                          // 超时自动审批
  
  // 条件节点属性
  conditions?: ConditionRule[];
  
  // 并行节点属性
  branches?: string[];                            // 分支名称
  
  // 自定义属性
  [key: string]: any;
}

// 节点数据
export interface WorkflowNode {
  id: string;
  type: NodeType;
  x: number;
  y: number;
  properties: NodeProperties;
  text?: {
    x: number;
    y: number;
    value: string;
  };
}

// 连线数据
export interface WorkflowEdge {
  id: string;
  type: string;
  sourceNodeId: string;
  targetNodeId: string;
  startPoint: { x: number; y: number };
  endPoint: { x: number; y: number };
  properties?: {
    label?: string;
    condition?: string;
    [key: string]: any;
  };
  text?: {
    x: number;
    y: number;
    value: string;
  };
  pointsList?: Array<{ x: number; y: number }>;
}

// 工作流数据
export interface WorkflowData {
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
}

// 组件Props
export interface WorkflowComponentProps {
  // 工作流数据
  flowData?: WorkflowData;
  
  // 工作模式
  mode?: WorkflowMode;
  
  // 是否只读
  readonly?: boolean;
  
  // 画布配置
  config?: {
    width?: number;
    height?: number;
    background?: string;
    grid?: boolean;
    miniMap?: boolean;
    keyboard?: boolean;
  };
  
  // 节点模板
  nodeTemplates?: Array<{
    type: NodeType;
    name: string;
    icon?: string;
    properties?: Partial<NodeProperties>;
  }>;
  
  // 审批人数据源
  approverDataSource?: Approver[];
}

// 组件Events
export interface WorkflowComponentEvents {
  // 数据变更
  'update:flowData': (data: WorkflowData) => void;
  
  // 节点选择
  'node-select': (node: WorkflowNode | null) => void;
  
  // 连线选择
  'edge-select': (edge: WorkflowEdge | null) => void;
  
  // 节点添加
  'node-add': (node: WorkflowNode) => void;
  
  // 节点删除
  'node-delete': (nodeId: string) => void;
  
  // 节点更新
  'node-update': (node: WorkflowNode) => void;
  
  // 连线添加
  'edge-add': (edge: WorkflowEdge) => void;
  
  // 连线删除
  'edge-delete': (edgeId: string) => void;
  
  // 验证结果
  'validate': (result: ValidationResult) => void;
}

// 验证结果
export interface ValidationResult {
  valid: boolean;
  errors: Array<{
    type: 'error' | 'warning';
    message: string;
    nodeId?: string;
    edgeId?: string;
  }>;
}

// 组件方法
export interface WorkflowComponentMethods {
  // 获取工作流数据
  getFlowData(): WorkflowData;
  
  // 设置工作流数据
  setFlowData(data: WorkflowData): void;
  
  // 验证工作流
  validate(): ValidationResult;
  
  // 导出数据
  exportData(format?: 'json' | 'xml'): string;
  
  // 导入数据
  importData(data: string, format?: 'json' | 'xml'): boolean;
  
  // 清空画布
  clear(): void;
  
  // 适应画布
  fitView(): void;
  
  // 缩放
  zoom(ratio: number): void;
  
  // 撤销
  undo(): void;
  
  // 重做
  redo(): void;
  
  // 获取选中元素
  getSelectedElements(): { nodes: WorkflowNode[]; edges: WorkflowEdge[] };
  
  // 删除选中元素
  deleteSelectedElements(): void;
}

// 节点配置
export interface NodeConfig {
  type: NodeType;
  name: string;
  icon: string;
  color: string;
  description: string;
  defaultProperties: Partial<NodeProperties>;
  allowedConnections?: {
    source?: NodeType[];
    target?: NodeType[];
  };
}

// 工作流模板
export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  data: WorkflowData;
  preview?: string;
}
