<script lang="ts" setup>
import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Input, message, Select, SelectOption, Space } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { addPointApi } from '#/api/equipment/main-form';
import { useMainFormStore } from '#/store';

import { addcheckModalSchema } from '../main-form-data';

interface RowType {
  [key: string]: any;
}
const emit = defineEmits(['success']);

const mainFormStore = useMainFormStore();

const eventRow = computed<RowType>(
  () => mainFormStore.getEventRow as unknown as RowType,
);
const currentRow = computed<RowType>(
  () => mainFormStore.getCurrentRow as unknown as RowType,
);
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: addcheckModalSchema(),
  showDefaultActions: false,
});
const title = ref('添加目标点');

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    console.warn(data);

    modalApi.lock();
    try {
      const type = modalApi.getData().type === 'check' ? 'N' : 'Y';
      const params = [
        eventRow.value.ORIGREC,
        data.weight,
        type,
        currentRow.value.DIVISIONVALUE,
        data.comment,
        eventRow.value.DIVISIONUNIT,
        value.value,
      ];
      const res = await addPointApi(params);
      if (res === '-1') {
        message.warning('已设置目标点，不可重复设置');
      }
      if (res === false) {
        message.warning('添加目标点失败');
      }
      emit('success');

      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      title.value =
        modalApi.getData().type === 'check' ? '添加校验点' : '添加目标点';
      // const data = modalApi.getData<SampleTypeApi.SampleType>();
    }
  },
});
const options = [
  {
    value: 'g',
    label: 'g',
  },
  {
    value: 'mg',
    label: 'mg',
  },
];
const value = ref();
</script>
<template>
  <Modal :title="title">
    <Form class="mx-4">
      <template #weight="slotProps">
        <Space :size="[4, 0]" wrap>
          <Input v-bind="slotProps" />
          <Select v-model:value="value" style="width: 120px">
            <SelectOption
              v-for="item in options"
              :value="item.value"
              :key="item.value"
            >
              {{ item.label }}
            </SelectOption>
          </Select>
        </Space>
      </template>
    </Form>
  </Modal>
</template>
