<script lang="ts" setup>
import { ref } from 'vue';

import { Page, useVbenForm, useVbenModal } from '@vben/common-ui';

import {
  Button,
  Checkbox,
  CheckboxGroup,
  Empty,
  List,
  ListItem,
  message,
  Space,
  Step,
  Steps,
} from 'ant-design-vue';
// import { Ellipsis } from 'lucide-vue-next';

import {
  $addEditStudyTemplateApi,
  $addProtocolApi,
  $checkUniqueNameApi,
  $deleteSampleProgramApi,
  $getStablityTestListApi,
  $getTemplatesApi,
} from '#/api/business-static-tables';
import { $t } from '#/locales';

import EditMaterials from './edit-materials.vue';
import EditTestList from './edit-test-list.vue';
import PromptForConditionsIntervals from './prompt-for-conditions-intervals.vue';

const currentTab = ref<number>(0); // 当前步骤
const currentIndex = ref(0); // 当前进度条步骤索引
const spCode = ref<number>(-1);
const tabTitleMap = ref<string[]>([
  $t('business-static-tables.studyConfiguration.folderName'),
  $t('business-static-tables.studyConfiguration.conditionIntervals'),
  $t('business-static-tables.studyConfiguration.chooseTest'),
]);

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  showConfirmButton: true,
  confirmDisabled: false,
  confirmText: $t('commons.next'),
  onConfirm: next,
  // onOpenChange: async (isOpen) => {},
  onCancel: async () => {
    if (spCode.value && spCode.value > 0) {
      await $deleteSampleProgramApi({ spCode: spCode.value });
    }
    modalApi.close();
  },
});

async function next() {
  switch (currentTab.value) {
    case 0: {
      const validation = await protocolNameFormApi.validate();
      if (!validation) {
        return;
      }
      const values = await protocolNameFormApi.getValues();
      await onFirstSubmit(values);
      break;
    }
    case 1: {
      await onSecondSubmit();
      modalApi.setState({
        confirmText: $t('commons.confirm'),
      });
      break;
    }
    case 2: {
      await onThirdSubmit();
      modalApi.close();
      break;
    }
    default: {
      break;
    }
  }
}
const protocalFormValues = ref<Record<string, any>>();
const intervalFormValues = ref<Record<string, any>>();
const [ProtocolNameForm, protocolNameFormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  resetButtonOptions: {
    show: false,
  },
  schema: [
    {
      component: 'Input',
      fieldName: 'protocolName',
      label: $t('business-static-tables.studyConfiguration.protocolName'),
      rules: 'required',
    },
    {
      component: 'List',
      fieldName: 'materials',
      label: $t('business-static-tables.studyConfiguration.materials'),
      rules: 'required',
      componentProps: {},
    },
    {
      component: 'RadioGroup',
      fieldName: 'createWay',
      label: $t('business-static-tables.studyConfiguration.createWay'),
      rules: 'required',
      componentProps: {
        options: [
          {
            label: $t('business-static-tables.studyConfiguration.isAddNew'),
            value: 'new',
          },
          {
            label: $t('business-static-tables.studyConfiguration.isNotAdd'),
            value: 'template',
          },
        ],
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'folderName',
      label: $t('business-static-tables.studyConfiguration.folderName'),
      rules: 'required',
      componentProps: {
        api: $getTemplatesApi,
        labelField: 'Text',
        valueField: 'Value',
        params: {
          dept: 'SITE1',
        },
      },
      dependencies: {
        triggerFields: ['createWay'],
        show(values) {
          return values.createWay === 'template';
        },
      },
    },
  ],
  submitButtonOptions: {
    show: false,
  },
  wrapperClass: 'grid-cols-1 md:grid-cols-1 lg:grid-cols-1',
});

const promptForConditionsIntervalsRef =
  ref<InstanceType<typeof PromptForConditionsIntervals>>();
const promptArgs = ref<Record<string, any>>();
async function onFirstSubmit(values: any) {
  const { valid } = await protocolNameFormApi.validate();
  if (!valid) {
    return;
  }
  const templateName = await $checkUniqueNameApi({
    name: values.protocolName,
    isProtocol: 'Y',
  });
  if (!templateName) {
    message.error(
      $t('business-static-tables.studyConfiguration.existTemplateName'),
    );
    return;
  }
  const nSpCode = await $addProtocolApi({
    sProtocolName: values.protocolName,
    sProdGroup: $t('business-static-tables.studyConfiguration.Stability'),
    bNewVersion: false,
  });
  if (nSpCode === -100) {
    message.error(
      $t('business-static-tables.studyConfiguration.protocolExist'),
    );
    return;
  }
  spCode.value = nSpCode;
  protocalFormValues.value = values;
  promptArgs.value = {
    bDisableConditions: false,
    nMaxIntervals: 25,
    stabNo: values.folderName ?? -1,
    openingMode: 'PROTOCOLCONSOLE',
  };
  currentTab.value++;
  currentIndex.value++;
}
async function onSecondSubmit() {
  const selectConditions =
    promptForConditionsIntervalsRef.value?.getSelectConditions();
  const checkIntervals = promptForConditionsIntervalsRef.value?.checkIntervals;
  if (
    !selectConditions ||
    selectConditions.length === 0 ||
    !checkIntervals ||
    checkIntervals.length === 0
  ) {
    message.error(
      $t(
        'business-static-tables.studyConfiguration.selectOneConditionsAndIntervals',
      ),
    );
    return;
  }
  intervalFormValues.value = {
    conditions: selectConditions,
    intervals: checkIntervals,
  };
  await loadTest();
  currentTab.value++;
  currentIndex.value++;
}
const mode = ref('PROTOCOLCONSOLE');
async function onThirdSubmit() {
  await $addEditStudyTemplateApi({
    spCode: spCode.value,
    conditions: intervalFormValues.value?.conditions,
    intervals: intervalFormValues.value?.intervals,
    testList: selectTestList.value,
    openingMode: mode.value,
    name: protocalFormValues.value?.protocolName,
    stabNo: -1,
    status: null,
    nFomStabNo: protocalFormValues.value?.folderName ?? -1,
    aMaterials: protocalFormValues.value?.materials,
    sCycle: 'Cycle',
    sWorkflowCode: 'N/A',
  });
}

async function loadTest() {
  testList.value = await $getStablityTestListApi({
    spCode: spCode.value,
    stabNo: -1,
  });
  selectTestList.value = testList.value
    .filter((item) => item.Checked === 'true')
    .map((item) => item.Value);
}

const [EditMaterialsModal, editMaterialsModalApi] = useVbenModal({
  destroyOnClose: true,
  connectedComponent: EditMaterials,
});
const [EditTestListModal, editTestListModalApi] = useVbenModal({
  destroyOnClose: true,
  connectedComponent: EditTestList,
});

async function onAddMat() {
  const values = await protocolNameFormApi.getValues();
  const selectedMat = values.materials;
  editMaterialsModalApi
    .setData({
      stabNo: -1,
      materials: selectedMat,
    })
    .open();
}

const testList = ref<{ Checked: string; Text: string; Value: string }[]>([]);
const selectTestList = ref<string[]>([]);
const onEditTest = async () => {
  editTestListModalApi
    .setData({
      spCode: spCode.value,
      drawNo: 1,
      callingApp: 'Edit',
      profile: 'Default',
    })
    .open();
};
</script>

<template>
  <Modal :title="tabTitleMap[currentTab]">
    <EditMaterialsModal
      class="w-[800px]"
      @update-materials="protocolNameFormApi.setFieldValue('materials', $event)"
    />
    <EditTestListModal class="w-[800px]" @success="loadTest" />
    <Page>
      <div class="mx-auto">
        <Steps :current="currentIndex" class="steps">
          <Steps :current="currentIndex" class="steps">
            <template v-for="(title, _index) in tabTitleMap" :key="_index">
              <Step :title="title" />
            </template>
          </Steps>
        </Steps>
        <div class="p-0">
          <ProtocolNameForm v-show="currentTab === 0" class="p-10">
            <template #materials="slotProps">
              <div class="flex w-full flex-col gap-2">
                <Space>
                  <Button @click="onAddMat">编辑</Button>
                </Space>
                <List
                  size="small"
                  class="w-full"
                  v-bind="slotProps"
                  bordered
                  style="max-height: 200px; overflow-y: auto"
                >
                  <template #renderItem="{ item }">
                    <ListItem>{{ item }}</ListItem>
                  </template>
                </List>
              </div>
            </template>
          </ProtocolNameForm>
          <PromptForConditionsIntervals
            ref="promptForConditionsIntervalsRef"
            :form-args="promptArgs"
            v-if="currentTab === 1"
          />
          <div class="flex w-full flex-col gap-2 p-2" v-if="currentTab === 2">
            <Space>
              <Button @click="onEditTest">
                {{ $t('ui.actionTitle.edit') }}
              </Button>
            </Space>
            <div class="p-5" style="height: 500px; overflow-y: auto">
              <template v-if="testList.length > 0">
                <CheckboxGroup
                  v-model:value="selectTestList"
                  style="display: flex; flex-direction: column; width: 100%"
                >
                  <Checkbox
                    v-for="item in testList"
                    class="w-full"
                    :key="item.Value"
                    :value="item.Value"
                    :label="item.Text"
                  >
                    {{ item.Text }}
                  </Checkbox>
                </CheckboxGroup>
              </template>
              <template v-else>
                <Empty />
              </template>
            </div>
          </div>
        </div>
      </div>
    </Page>
  </Modal>
</template>
