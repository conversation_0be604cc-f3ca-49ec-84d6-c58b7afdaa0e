<script lang="ts" setup>
import { <PERSON><PERSON>, Card, Space } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';

import { tcpSchema } from '../../equipment-mg-data';

const [Form] = useVbenForm({
  layout: 'vertical',
  schema: tcpSchema(),
  showDefaultActions: false,
});
const testMethod = () => {};
</script>
<template>
  <Card title="TCP 服务器参数" class="h-full">
    <Form class="mx-4" />
    <Space :size="[4, 0]">
      <Button type="primary" @click="testMethod"> 测试 </Button>
    </Space>
  </Card>
</template>
