import type { Dayjs } from 'dayjs';

import type {
  ElectronicSignatureData,
  ElectronicSignatureOptions,
} from './electronic-signature.vue';

import { createVNode } from 'vue';

import { useUserStore } from '@vben/stores';

import { Modal } from 'ant-design-vue';
import dayjs from 'dayjs';

import { callServer } from '#/api';
import { limsStringToBoolean } from '#/utils/boolean';

import ElectronicSignature from './electronic-signature.vue';

let modalInstance: any = null;

export interface ShowElectronicSignatureOptions
  extends ElectronicSignatureOptions {
  title?: string;
  width?: number | string;
}

export const esignBegin = async (
  options: ShowElectronicSignatureOptions,
): Promise<ElectronicSignatureData> => {
  const { title = '电子签名', width = '50vw', ...restOptions } = options;

  // 如果已经存在实例，先销毁
  if (modalInstance) {
    modalInstance.destroy();
  }

  if (!options.eventCode) {
    throw new Error('eventCode is required');
  }

  const eventCode = options.eventCode;
  const esigParams = await callServer('ESignatures.GetESigParams', [eventCode]);

  const [
    esigComment,
    esigPassword,
    esigWitness,
    esigAgreement,
    esigAgreementText,
    esigCommentRequired,
    _hasEsig,
    hasStartDate,
    hasEndDate,
    defaultStartDtN,
    defaultStartDtU,
    defaultEndDtN,
    defaultEndDtU,
    esigCommentEditable,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    esigCommentArray,
    esigCommentDefault,
  ] = esigParams;

  const userStore = useUserStore();

  // 组装 esigOptions
  const now = dayjs();
  const defaultEffectiveDate =
    defaultStartDtN && defaultStartDtU
      ? calcDateByUnit(now, defaultStartDtN, defaultStartDtU)
      : now;
  const defaultExpiryDate =
    defaultEndDtN && defaultEndDtU
      ? calcDateByUnit(now, defaultEndDtN, defaultEndDtU)
      : calcDateByUnit(now, 1, 'Years');

  const esigOptions = {
    username: userStore.userInfo?.username,
    commentEditable: limsStringToBoolean(esigCommentEditable),
    commentRequired: limsStringToBoolean(esigCommentRequired),
    defaultComment: esigCommentDefault,
    defaultEffectiveDate,
    defaultExpiryDate,
    needPassword: limsStringToBoolean(esigPassword),
    needWitness: limsStringToBoolean(esigWitness),
    showComment: limsStringToBoolean(esigComment),
    showEffectiveDate: limsStringToBoolean(hasStartDate),
    showExpiryDate: limsStringToBoolean(hasEndDate),
    showAgreement: limsStringToBoolean(esigAgreement),
    agreementText: esigAgreementText,
    ...restOptions,
  };
  const hasEsig =
    esigOptions.showComment ||
    esigOptions.needPassword ||
    esigOptions.needWitness ||
    esigOptions.showAgreement ||
    esigOptions.showEffectiveDate ||
    esigOptions.showExpiryDate;

  const silent = !hasEsig;

  if (hasEsig) {
    return new Promise((resolve, reject) => {
      // 创建新的 Modal 实例
      modalInstance = Modal.info({
        title,
        width,
        icon: null,
        content: () => {
          return createVNode(ElectronicSignature, {
            options: esigOptions,
            onConfirm: (data: ElectronicSignatureData) => {
              modalInstance.destroy();
              modalInstance = null;
              resolve(data);
            },
            onCancel: () => {
              modalInstance.destroy();
              modalInstance = null;
              reject(new Error('用户取消了电子签名'));
            },
          });
        },
        okText: null,
        cancelText: null,
        maskClosable: false,
        keyboard: false,
        footer: null,
      });
    });
  }

  return new Promise((resolve) => {
    resolve({
      hasEsig,
      silent,
      comment: '',
      needPassword: false,
      needWitness: false,
      username: '',
      witnessUsername: '',
      allowAgreement: false,
      agreementText: '',
      effectiveDate: undefined,
      expiryDate: undefined,
    } satisfies ElectronicSignatureData);
  });
};

export const esignEnd = () => {
  if (modalInstance) {
    modalInstance.destroy();
    modalInstance = null;
  }
};

function calcDateByUnit(base: Dayjs, n?: number, u?: string): Dayjs {
  if (!n || !u) return base;
  switch (u) {
    case 'Days': {
      return base.add(n, 'day');
    }
    case 'Months': {
      return base.add(n, 'month');
    }
    case 'Years': {
      return base.add(n, 'year');
    }
    default: {
      return base;
    }
  }
}

// 导出类型
export type { ElectronicSignatureData, ElectronicSignatureOptions };
