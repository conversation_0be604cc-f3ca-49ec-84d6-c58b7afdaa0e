<script lang="ts" setup>
import { computed, onMounted, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, Card, Input, Space } from 'ant-design-vue';
import { Ellipsis } from 'lucide-vue-next';

import { useVbenForm } from '#/adapter/form';
import { getWebSocketApi } from '#/api/equipment/equipment-mg';
import { useEquipmentStore } from '#/store';

import WebsocketModal from '../components/websocket-modal.vue';
import { websocketSchema } from '../equipment-mg-data';

const equipmentStore = useEquipmentStore();
interface RowType {
  [key: string]: any;
}
const currentRow = computed<null | RowType>(
  () => equipmentStore.getCurrentRow as null | RowType,
);
onMounted(() => {
  getData();
});
watch(
  currentRow,
  () => {
    getData();
  },
  { deep: true },
);
const getData = async () => {
  if (currentRow.value && currentRow.value.EQID) {
    const data = await getWebSocketApi([currentRow.value.EQID]);
    formApi.setFieldValue('ipAddress', data[0].WS_ADDRESS);
    formApi.setFieldValue('port', data[0].WS_PORT);
    formApi.setFieldValue('parseMethod', data[0].WSPARSER);
  }
};
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: WebsocketModal,
  destroyOnClose: true,
});

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: websocketSchema(),
  showDefaultActions: false,
});
const testMethod = () => {
  formModalApi.setData(null).open();
};
const onRefresh = () => {};
const viewScript = () => {
  // TODO:脚本查看修改
};
</script>
<template>
  <FormModal @success="onRefresh" />

  <Card title="websocket相关信息" class="h-2/3">
    <Form class="mx-4">
      <template #parseMethod="slotProps">
        <Input v-bind="slotProps" placeholder="">
          <template #suffix>
            <Ellipsis class="ml-auto h-6 w-6" @click="viewScript()" />
          </template>
        </Input>
      </template>
    </Form>
    <Space :size="[4, 0]">
      <Button type="primary" @click="testMethod"> 测试 </Button>
    </Space>
  </Card>
</template>
