import { callServer, getDataSet, getDataSetNoPage } from '#/api/core/witlab';

export namespace LookupTableApi {
  export interface PageFetchParams {
    [key: string]: any;
    page: number;
    pageSize: number;
  }

  export interface MetaDataLookups {
    [key: string]: any;
    ORIGREC: number;
    NAME: string;
    EFFECT: string;
    TYPE: string;
  }

  export interface MetaDataLookupValue {
    [key: string]: any;
    ORIGREC: number;
    LOOKUP_NAME: string;
    VALUE: string;
    TEXT: string;
    SORTER: number;
  }
}

/**
 * 获取选择内容表格数据
 */
async function getLookupTableApi() {
  return getDataSet('GenericMetadata.Lookups', ['ALL']);
}

/**
 * 添加选择内容
 * @param data - 选择内容数据
 * @returns boolean
 * @description 添加选择内容
 */
async function addLookupApi(
  data: Omit<LookupTableApi.MetaDataLookups, 'ORIGREC'>,
) {
  return await callServer('GenericMetadata.AddLookup', [
    data.NAME,
    '',
    'AddedLookup',
    '',
    data.EFFECT,
    data.TYPE,
  ]);
}

async function deleteLookupApi(lookupName: string) {
  return await callServer('GenericMetadata.DeleteLookup', [lookupName]);
}

/**
 * 获取选择内容详情表格数据
 */
async function getLookupValuesTableApi(params: LookupTableApi.PageFetchParams) {
  return getDataSet('GenericMetadata.LookupValues', [params.key]);
}

async function deleteLookupValueApi(delParams: string[]) {
  return await callServer('GenericMetadata.DeleteLookupValue', delParams);
}

async function addLookupValueApi(
  data: Omit<LookupTableApi.MetaDataLookupValue, 'ORIGREC'>,
) {
  return await callServer('GenericMetadata.AddLookupValue', [
    data.LOOKUP_NAME,
    data.VALUE,
    data.SORTER,
    data.TEXT,
  ]);
}

async function $getLookupValuesSimple(
  lookupName: string | { key: string },
): Promise<{ TEXT: string; VALUE: string }[]> {
  let lookupNameStr = '';
  lookupNameStr = typeof lookupName === 'string' ? lookupName : lookupName.key;
  return await getDataSetNoPage('GenericMetadata.LookupValuesSimple', [
    lookupNameStr,
  ]);
}

export {
  $getLookupValuesSimple,
  addLookupApi,
  addLookupValueApi,
  deleteLookupApi,
  deleteLookupValueApi,
  getLookupTableApi,
  getLookupValuesTableApi,
};
