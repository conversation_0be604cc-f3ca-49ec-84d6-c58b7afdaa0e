<script setup lang="ts">
import type { BatcheManagerApi } from '#/api/login-options/batch-manager';
import type { NotifySamplersApi } from '#/api/notify-samplers/notify-samplers';

import { watch } from 'vue';

import { confirm, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import {
  getSampleRequirementList,
  removedAdHocSamplingRequirement,
} from '#/api/login-options/batch-manager';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import AddBatchRequirementForm from '../login-options/batch-manager/add-batch-requirement.vue';
import Detail from '../login-options/batch-manager/batch-req-tests.vue';
import {
  useSampleRequirementColumns,
  useSampleRequirementFilterSchema,
} from './notify-samplers-data';

const props = defineProps<{
  currentTestRow: NotifySamplersApi.NotifySamplers | null;
}>();

watch(
  () => props.currentTestRow,
  (_val) => {
    onRefresh();
  },
);
const colums = useSampleRequirementColumns();
const filterSchema = useSampleRequirementFilterSchema();
const queryData = async () => {
  if (!props.currentTestRow) return [];
  const data = await getSampleRequirementList(
    props.currentTestRow.BATCHID,
    props.currentTestRow.ORDNO,
  );
  return data.items;
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};

const {
  Grid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
} = useLimsGridsConfig<BatcheManagerApi.BatchSamplingRequirement>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

function onRefresh() {
  gridApi.query();
}

// 添加取样要求
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddBatchRequirementForm,
});

async function onCreate() {
  if (props.currentTestRow === null) return;
  const batchid = props.currentTestRow.ORDNO;
  const sampleGroupCode = null;
  formModalApi
    .setData({ BATCHID: batchid, SAMPLEGROUPCODE: sampleGroupCode })
    .open();
}

// 移除取样要求
async function onRemove() {
  // 获取选中行

  const aOrigrec: number[] = gridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.ORIGREC);

  if (aOrigrec.length === 0) {
    message.warning('请先选择要移除的数据');
    return;
  }
  const aOrdNo: string[] = gridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.ORDNO);
  try {
    await confirm({
      title: '确认移除',
      content: `确定要移除选中的 ${aOrigrec.length} 条数据吗？`,
      icon: 'warning',
      centered: false,
    });

    const bRet = await removedAdHocSamplingRequirement(aOrigrec, '', aOrdNo);
    if (!bRet) {
      message.warn($t('notify-samplers.noSamplingRequirement'));
      return;
    }

    message.success('移除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: Detail,
  destroyOnClose: true,
  class: 'w-[900px]',
});

function onDetail(row: BatcheManagerApi.BatchSamplingRequirement) {
  formDrawerApi.setData(row).open();
}
</script>

<template>
  <FormDrawer @success="onRefresh" />
  <FormModal @success="onRefresh" />
  <div class="h-[350px] w-full">
    <Grid>
      <template #toolbar-actions>
        <Space>
          <Button type="primary" @click="onCreate">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="onRemove">
            {{ $t('login-options.remove') }}
          </Button>
          <Button type="default">
            {{ $t('login-options.printReqLabel') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent(row)">
            {{ $t('notify-samplers.save') }}
          </Button>
          <Button type="link" @click="cancelRowEvent(row)">
            {{ $t('notify-samplers.cancel') }}
          </Button>
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">
            {{ $t('notify-samplers.edit') }}
          </Button>
        </template>
        <Button type="link" @click="onDetail(row)">
          {{ $t('login-options.batchManager.reqTests') }}
        </Button>
      </template>
    </Grid>
  </div>
</template>
