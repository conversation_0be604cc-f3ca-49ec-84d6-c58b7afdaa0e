import type { Recordable, UserInfo } from '@vben/types';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { DEFAULT_HOME_PATH, LOGIN_PATH } from '@vben/constants';
import { resetAllStores, useAccessStore, useUserStore } from '@vben/stores';

import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';

import {
  getAccessCodesApi,
  getUserDepts,
  getUserInfoApi,
  getUserRoles,
  loginApi,
  logoutApi,
  refreshTokenApi,
} from '#/api';
import { $t } from '#/locales';

export const useAuthStore = defineStore('auth', () => {
  const accessStore = useAccessStore();
  const userStore = useUserStore();
  const router = useRouter();

  const loginLoading = ref(false);

  /**
   * 第一阶段登录：验证账号密码
   * @param params 登录表单数据（用户名和密码）
   */
  async function authLoginFirstStep(params: Recordable<any>) {
    try {
      loginLoading.value = true;
      const { userId, accessToken, refreshToken } = await loginApi(params);

      if (accessToken) {
        // 获取用户信息，但不设置到store中
        const [userInfo, userDepts, userRoles] = await Promise.all([
          getUserInfoApi(userId, accessToken),
          getUserDepts(userId, accessToken),
          getUserRoles(userId, accessToken),
        ]);

        const data = {
          continueLogin: true,
          userInfo: {
            ...userInfo,
            accessToken,
            refreshToken,
          },
          userDepts,
          userRoles,
        };

        if (userDepts.length === 1 && userRoles.length === 1) {
          await authLoginSecondStep({
            ...data.userInfo,
            deptCode: userDepts[0].code,
            roleCode: userRoles[0].code,
          });
          return {
            continueLogin: false,
            userInfo: null,
            userDepts: [],
            userRoles: [],
          };
        }

        return data;
      }
    } catch (error) {
      console.error('登录失败:', error);
      return { continueLogin: false, userInfo: null };
    } finally {
      loginLoading.value = false;
    }
  }

  /**
   * 第二阶段登录：选择部门角色完成登录
   * @param params 包含用户信息、部门和角色的完整数据
   */
  async function authLoginSecondStep(
    params: Recordable<any>,
    onSuccess?: () => Promise<void> | void,
  ) {
    try {
      loginLoading.value = true;

      const { accessToken, refreshToken } = await refreshTokenApi(
        params.accessToken,
        params.refreshToken,
        params.deptCode,
        params.roleCode,
      );

      accessStore.setAccessToken(accessToken);
      accessStore.setRefreshToken(refreshToken);

      // 构造完整的用户信息
      const completeUserInfo = {
        userId: params.userId,
        username: params.username,
        deptCode: params.deptCode,
        roleCode: params.roleCode,
        avatar: params.avatar || '',
        realName: params.realName || params.username,
        roles: params.roles || [],
      };

      userStore.setUserInfo(completeUserInfo);

      // 获取权限码
      const accessCodes = await getAccessCodesApi(params.userId);
      accessStore.setAccessCodes(accessCodes);

      if (accessStore.loginExpired) {
        accessStore.setLoginExpired(false);
      } else {
        onSuccess
          ? await onSuccess?.()
          : await router.push(params.homePath || DEFAULT_HOME_PATH);
      }

      if (completeUserInfo?.realName) {
        notification.success({
          description: `${$t('authentication.loginSuccessDesc')}:${completeUserInfo?.realName}`,
          duration: 3,
          message: $t('authentication.loginSuccess'),
        });
      }
    } catch (error) {
      console.error('登录失败:', error);
    } finally {
      loginLoading.value = false;
    }
  }

  /**
   * 异步处理登录操作
   * Asynchronously handle the login process
   * @param params 登录表单数据
   */
  async function authLogin(
    params: Recordable<any>,
    onSuccess?: () => Promise<void> | void,
  ) {
    // 异步处理用户登录操作并获取 accessToken
    let userInfo: null | UserInfo = null;
    try {
      loginLoading.value = true;
      const { userId, accessToken, refreshToken } = await loginApi(params);

      // 如果成功获取到 accessToken
      if (accessToken) {
        accessStore.setAccessToken(accessToken);
        accessStore.setRefreshToken(refreshToken);

        // 获取用户信息并存储到 accessStore 中
        const [fetchUserInfoResult, accessCodes] = await Promise.all([
          fetchUserInfo(userId),
          getAccessCodesApi(userId),
        ]);

        userInfo = fetchUserInfoResult;

        // 为了兼容现有的UserInfo类型，添加必需的字段
        const completeUserInfo = {
          ...userInfo,
          deptCode: '', // 添加默认值
          roleCode: '', // 添加默认值
        };

        userStore.setUserInfo(completeUserInfo);
        accessStore.setAccessCodes(accessCodes);

        if (accessStore.loginExpired) {
          accessStore.setLoginExpired(false);
        } else {
          onSuccess
            ? await onSuccess?.()
            : await router.push(userInfo.homePath || DEFAULT_HOME_PATH);
        }

        if (userInfo?.realName) {
          notification.success({
            description: `${$t('authentication.loginSuccessDesc')}:${userInfo?.realName}`,
            duration: 3,
            message: $t('authentication.loginSuccess'),
          });
        }
      }
    } finally {
      loginLoading.value = false;
    }

    return {
      userInfo,
    };
  }

  async function logout(redirect: boolean = true) {
    try {
      await logoutApi();
    } catch {
      // 不做任何处理
    }
    resetAllStores();
    accessStore.setLoginExpired(false);

    // 回登录页带上当前路由地址
    await router.replace({
      path: LOGIN_PATH,
      query: redirect
        ? {
            redirect: encodeURIComponent(router.currentRoute.value.fullPath),
          }
        : {},
    });
  }

  async function fetchUserInfo(userId: string) {
    let userInfo: null | UserInfo = null;
    userInfo = await getUserInfoApi(userId);
    // 为了兼容现有的UserInfo类型，添加必需的字段
    const completeUserInfo = {
      ...userInfo,
      deptCode: '', // 添加默认值
      roleCode: '', // 添加默认值
    };
    userStore.setUserInfo(completeUserInfo);
    return userInfo;
  }

  function $reset() {
    loginLoading.value = false;
  }

  return {
    $reset,
    authLogin,
    authLoginFirstStep,
    authLoginSecondStep,
    fetchUserInfo,
    loginLoading,
    logout,
  };
});
