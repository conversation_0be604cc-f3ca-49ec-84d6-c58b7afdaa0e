import type { Dayjs } from 'dayjs';

import { callServer, getDataSet, getDataSetNoPage } from '#/api/core/witlab';

export namespace MethodManagerApi {
  export interface TestMethod {
    [key: string]: any;
    ORIGREC: number;
    METHOD: string;
    METHODCATEGORY: string;
    DESCRIPTION: string;
    SOP_ENG: string;
    METHODTYPE: string;
    METHODNO: string;
    CLAUSE: string;
    CLAUSE_NAME: string;
    CLAUSE_ENG: string;
  }

  export interface TestMethodVersion {
    [key: string]: any;
    ORIGREC: number;
    STATUS: string; // 状态
    VERSION: number; // 版本号
    FILE_VERSION: string; // 线下版本号
    STARTDDATE: Dayjs; // 开始日期
    EXPDATE: Dayjs; // 结束日期
    REFNO: string; // 参考号
    LINK: string; // 链接地址
    STARDOC_ID: string; // 文件ID
    METHOD: string; // 方法
  }
}

const $getTestMethodListApi = async (method: string) => {
  const res = await getDataSet('METHOD_MANAGER.DS_METHOD_MANAGER_GRID', [
    method,
  ]);
  return res;
};

const $getTestMethodTypeApi = async () => {
  const res = await getDataSetNoPage(
    'METHOD_MANAGER.METHOD_MANAGER_dgdTestMeths_DataSource',
    [],
  );
  return res;
};

const $addTestMethodApi = async (
  data: Omit<MethodManagerApi.TestMethod, 'ORIGREC'>,
) => {
  const res = callServer('METHOD_MANAGER.ADD_TESTMETHOD', [
    data.METHOD,
    data.METHODTYPE,
    data.METHODCATEGORY,
    data.DESCRIPTION,
    data.SOP_ENG,
    data.METHODNO,
  ]);
  return res;
};

const $delTestMethodApi = async (methods: string[]) => {
  const res = callServer('METHOD_MANAGER.DEL_TESTMETHOD', [methods]);
  return res;
};

const $getTestMethodVersionListApi = async (method: string) => {
  const res = await getDataSet(
    'METHOD_MANAGER.DS_METHOD_MANAGER_VERSIONS_GRID',
    [method],
  );
  return res;
};

const $addTestMethodVersionApi = async (
  data: Omit<MethodManagerApi.TestMethodVersion, 'ORIGREC'>,
) => {
  const res = await callServer('METHOD_MANAGER.ADD_METHOD_VERSION', [
    data.METHOD,
    data.VERSION,
    data.REFNO,
    data.FILE_VERSION,
  ]);
  return res.result as Array<any>;
};

const $delTestMethodVersionApi = async (
  arrOrigrec: number[],
  method: string,
  arrVersion: number[],
  arrDocIds: string[],
) => {
  const res = await callServer('METHOD_MANAGER.DEL_METHOD_VERSION', [
    arrOrigrec,
    method,
    arrVersion,
    arrDocIds,
  ]);
  return res;
};

const $releaseTestMethodVersionApi = async (data: {
  expDate: Dayjs;
  method: string;
  startDate: Dayjs;
  version: number;
}) => {
  const res = await callServer('METHOD_MANAGER.VERSION_CREATED', [
    data.method,
    data.version,
    data.startDate,
    data.expDate,
  ]);
  return res;
};

const $extendExpDateApi = async (
  data: Partial<MethodManagerApi.TestMethodVersion>,
) => {
  const res = await callServer('METHOD_MANAGER.ExtendExpiryDate', [
    data.ORIGREC,
    data.EXPDATE,
  ]);
  return res;
};

export {
  $addTestMethodApi,
  $addTestMethodVersionApi,
  $delTestMethodApi,
  $delTestMethodVersionApi,
  $extendExpDateApi,
  $getTestMethodListApi,
  $getTestMethodTypeApi,
  $getTestMethodVersionListApi,
  $releaseTestMethodVersionApi,
};
