<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { $t } from '#/locales';

const emit = defineEmits(['success']);

// const formData = ref<BatcheManagerApi.BatchSamplingRequirement>();

// const sBatchId = ref<string>('');
// const sampleGroupCode = ref<string>('');

const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 提交函数
  handleSubmit: onSubmit,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'vertical',
  schema: [
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        // api: getSamplePosition,
        // labelField: 'Text',
        // valueField: 'Text',
      },
      fieldName: 'SAMPLINGPOSITION',
      label: $t('login-options.batchManager.sample'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        // api: getSampleSize,
        // labelField: 'Text',
        // valueField: 'Value',
      },
      fieldName: 'SAMPLESIZE',
      label: $t('login-options.batchManager.profile'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'PACKING_SPEC',
      label: $t('login-options.batchManager.packing'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'NUMBEROFCONTAINERS',
      label: $t('login-options.batchManager.numberOfSample'),
      rules: 'required',
    },
  ],
  // wrapperClass: 'grid-cols-1',
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  draggable: true,
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
    // modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      modalApi.getData<Record<string, any>>();
      // sBatchId.value = data.BATCHID;
      // sampleGroupCode.value = data.SAMPLEGROUPCODE;
      // formData.value = data;
      // formApi.setValues(formData.value);
    }
  },
  title: '新增样品',
});

async function onSubmit() {
  try {
    message.loading({
      content: '正在提交中...',
      duration: 0,
      key: 'is-form-submitting',
    });
    modalApi.lock();

    // const data =await formApi.getValues() ;

    emit('success');
    modalApi.close();
    message.success({
      content: '新增成功',
      duration: 2,
      key: 'is-form-submitting',
    });
  } catch (error) {
    message.error({
      content: `新增失败：${(error as Error).message}`,
      key: 'is-form-submitting',
    });
    modalApi.close();
  } finally {
    modalApi.unlock();
  }
}
</script>

<template>
  <Modal>
    <Form />
    <template #prepend-footer>
      <div class="flex-auto"></div>
    </template>
  </Modal>
</template>
