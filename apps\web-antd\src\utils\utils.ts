import { $t } from '@vben/locales';

import { Modal } from 'ant-design-vue';

export function confirm(content: string, title: string) {
  return new Promise((reslove, reject) => {
    Modal.confirm({
      content,
      onCancel() {
        reject(new Error('已取消'));
      },
      onOk() {
        reslove(true);
      },
      title,
    });
  });
}

export function traslateValue(value: string) {
  const traslateKey = `format-cell-traslate.${value}`;
  const traslateVal = $t(traslateKey);
  if (traslateVal === traslateKey) {
    return value;
  }
  return traslateVal;
}

/**
 * 将扁平数组转为树结构
 */
export function flatToTree(
  list: any[],
  key: string = 'VALUE',
  parentKey: string = 'PARENT',
) {
  const map = new Map();
  const roots: any[] = [];
  list.forEach((item) => {
    map.set(item[key], item);
    item.children = [];
  });
  list.forEach((item) => {
    if (item[parentKey]) {
      const parent = map.get(item[parentKey]);
      if (parent) {
        parent.children.push(item);
      }
    } else {
      roots.push(item);
    }
  });
  // 去除空children
  function cleanChildren(arr: any[]) {
    arr.forEach((i) => {
      if (i.children.length === 0) delete i.children;
      else cleanChildren(i.children);
    });
  }
  cleanChildren(roots);
  return roots;
}
