import { faker } from '@faker-js/faker';
import { usePageResponseSuccess } from '~/utils/response';

function generateMockDataList(count: number) {
  const dataList = [];

  for (let i = 0; i < count; i++) {
    const dataItem: Record<string, any> = {
      mattype: faker.string.uuid(),
      dept: faker.commerce.product(),
      batchcrysreportid: faker.commerce.product(),
    };

    dataList.push(dataItem);
  }

  return dataList;
}

const mockData = generateMockDataList(100);

export default eventHandler(async (event) => {
  // const userinfo = verifyAccessToken(event);
  // if (!userinfo) {
  //   return unAuthorizedResponse(event);
  // }

  const { page = 1, pageSize = 20, mattype, dept } = getQuery(event);
  let listData = structuredClone(mockData);
  if (mattype) {
    listData = listData.filter((item) =>
      item.mattype.toLowerCase().includes(String(mattype).toLowerCase()),
    );
  }
  if (dept) {
    listData = listData.filter((item) =>
      item.description.toLowerCase()?.includes(String(dept).toLowerCase()),
    );
  }
  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
