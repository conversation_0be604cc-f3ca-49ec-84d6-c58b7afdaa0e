<script lang="ts" setup>
import type LogicFlow from '@logicflow/core';

import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import LogicFlowCore from '@logicflow/core';
import { BpmnElement, Snapshot } from '@logicflow/extension';
import { Modal } from 'ant-design-vue';

import { toLogicflowData, toTurboData } from '#/utils/adpter-for-turbo';

import { nodeList as generalNodeList } from './config';
import demoData from './dataTurbo.json';
import Control from './LogicFlowComponents/Control.vue';
import DataDialog from './LogicFlowComponents/DataDialog.vue';
import NodePanel from './LogicFlowComponents/NodePanel.vue';

import '@logicflow/core/dist/index.css';
import '@logicflow/extension/dist/index.css';

defineOptions({
  name: 'LF',
});

const router = useRouter();

// 模板引用
const container = ref<HTMLDivElement>();

// 响应式数据
const lf = ref<LogicFlow>();
const graphData = ref<any>(null);
const dataVisible = ref(false);

const config = {
  grid: true,
  background: {
    color: '#f7f9ff',
  },
  keyboard: {
    enabled: true,
  },
};

const nodeList = generalNodeList;

// 初始化LogicFlow
const initLf = () => {
  // 画布配置
  LogicFlowCore.use(Snapshot);
  // 使用bpmn插件，引入bpmn元素，这些元素可以在turbo中转换后使用
  LogicFlowCore.use(BpmnElement);

  if (container.value) {
    const lfInstance = new LogicFlowCore({
      ...config,
      container: container.value,
    });
    lf.value = lfInstance;
    // 设置边类型bpmn:sequenceFlow为默认类型
    lfInstance.setDefaultEdgeType('bpmn:sequenceFlow');
    render();
  }
};

const render = () => {
  // Turbo数据转换为LogicFlow内部识别的数据结构
  const lFData = toLogicflowData(demoData);
  lf.value?.render(lFData);
};

const catData = () => {
  if (lf.value) {
    graphData.value = lf.value.getGraphData();
    dataVisible.value = true;
  }
};

const catTurboData = () => {
  if (lf.value) {
    const currentGraphData = lf.value.getGraphData();
    // 数据转化为Turbo识别的数据结构
    graphData.value = toTurboData(currentGraphData);
    dataVisible.value = true;
  }
};

const goto = () => {
  router.push('/');
};

onMounted(() => {
  initLf();
});
</script>
<template>
  <div class="logic-flow-view">
    <Control
      v-if="lf"
      class="demo-control"
      :lf="lf"
      :cat-turbo-data="true"
      @cat-data="catData"
      @cat-turbo-data="catTurboData"
    />
    <!-- 节点面板 -->
    <NodePanel :lf="lf" :node-list="nodeList" />
    <!-- 画布 -->
    <div id="lf-turbo" ref="container"></div>
    <!-- 数据查看面板 -->
    <Modal v-model:open="dataVisible" title="数据" width="50%" :footer="null">
      <DataDialog :graph-data="graphData" />
    </Modal>
  </div>
</template>
<style>
.logic-flow-view {
  position: relative;
  height: 100%;
}

.demo-title {
  margin: 20px;
  text-align: center;
}

.demo-control {
  position: absolute;
  top: 50px;
  right: 50px;
  z-index: 2;
}

#lf-turbo {
  width: 100%;
  height: 100%;
  outline: none;
}

.time-plus {
  cursor: pointer;
}

.add-panel {
  position: absolute;
  z-index: 11;
  padding: 10px 5px;
  background-color: white;
}
</style>
