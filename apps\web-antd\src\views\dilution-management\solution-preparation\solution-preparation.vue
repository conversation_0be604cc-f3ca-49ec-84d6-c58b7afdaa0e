<script lang="ts" setup>
import { ref } from 'vue';
import { Page } from '@vben/common-ui';
import { Button, Space, TabPane, Tabs, message } from 'ant-design-vue';
import TestSolution from './components/test-solution.vue';
import StandardSolution from './components/standard-solution.vue';
import TitrationSolution from './components/titration-solution.vue';
import DeviceModal from './components/device-modal.vue';
import AddMaterialModal from './components/add-material-modal.vue';

import { useVbenModal } from '@vben/common-ui';
const [DeviceFormModal, deviceFormModalApi] = useVbenModal({
  connectedComponent: DeviceModal,
  destroyOnClose: true,
});
const [AddMaterialFormModal, addMaterialFormModalApi] = useVbenModal({
  connectedComponent: AddMaterialModal,
  destroyOnClose: true,
});
const selectELNTemplate = () => {};
const runELN = () => {};
const viewELN = () => {};

const experimentalEquipment = () => {
  if (!clickRow.value.ORIGREC) {
    message.warn('请选择溶液');
    return;
  }
  deviceFormModalApi.setData({ clickRow: clickRow.value }).open();
};
const reagentMaterials = () => {
  if (!clickRow.value.ORIGREC) {
    message.warn('请选择溶液');
    return;
  }
  addMaterialFormModalApi
    .setData({
      clickRow: clickRow.value,
      type: tabList.find((item) => item.title === activeKey.value)?.type,
    })
    .open();
};
const activeKey = ref('试液');
const tabList = [
  {
    title: '试液',
    page: TestSolution,
    type: 'tbTestSolution',
  },
  {
    title: '标准溶液',
    page: StandardSolution,
    type: 'tbStandardSolution',
  },
  {
    title: '滴定液',
    page: TitrationSolution,
    type: 'titrant',
  },
];
const clickRow = ref({});
const handleClickRow = (row) => {
  clickRow.value = row;
};
const tabschange = () => {
  clickRow.value = {};
};
</script>
<template>
  <Page auto-content-height>
    <AddMaterialFormModal></AddMaterialFormModal>
    <DeviceFormModal></DeviceFormModal>
    <div class="w-full text-right">
      <Space :size="[8, 0]" wrap>
        <Button type="primary" @click="selectELNTemplate">
          {{ $t('dilution-management.solution-preparation.selectELNTemplate') }}
        </Button>
        <Button type="primary" @click="runELN">
          {{ $t('dilution-management.solution-preparation.runELN') }}
        </Button>
        <Button type="primary" @click="viewELN">
          {{ $t('dilution-management.solution-preparation.viewELN') }}
        </Button>
        <Button type="primary" @click="experimentalEquipment">
          {{
            $t('dilution-management.solution-preparation.experimentalEquipment')
          }}
        </Button>
        <Button type="primary" @click="reagentMaterials">
          {{ $t('dilution-management.solution-preparation.reagentMaterials') }}
        </Button>
      </Space>
    </div>
    <Tabs v-model:active-key="activeKey" class="w-full" @change="tabschange">
      <TabPane v-for="item in tabList" :key="item.title" :tab="item.title" />
    </Tabs>
    <component
      :is="
        tabList.find((item) => item.title === activeKey)?.page || TestSolution
      "
      @update:clickRow="handleClickRow"
    />
  </Page>
</template>
