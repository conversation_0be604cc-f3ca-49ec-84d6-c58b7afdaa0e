import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { SolutionTypeApi } from '#/api/basic-static-tables/solution-type';

import { $getSelectSolutionTypeApi } from '#/api/basic-static-tables/solution-type';
import { $t } from '#/locales';

export function useColumns(): VxeTableGridOptions<SolutionTypeApi.SolutionType>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SOLUTIONTYPE',
      title: $t('basic-static-tables.solutionType.solutionType'),
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SOLUTIONCODE',
      title: $t('basic-static-tables.solutionType.solutionCode'),
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'VALIDATEDAY',
      title: $t('basic-static-tables.solutionType.validateday'),
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'VALIDITY',
      title: $t('basic-static-tables.solutionType.validity'),
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'TYPE',
      title: $t('basic-static-tables.solutionType.type'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ISDAY',
      slots: { default: 'choose' },
      title: $t('basic-static-tables.solutionType.isDay'),
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('commons.action'),
      width: 140,
    },
  ];
}

export function useSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      componentProps: {
        api: $getSelectSolutionTypeApi,
        labelField: 'Text',
        valueField: 'Value',
        class: 'w-full',
      },
      fieldName: 'TYPE',
      label: $t('basic-static-tables.solutionType.type'),
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        disabled: true,
      },
      fieldName: 'SOLUTIONCODE',
      label: $t('basic-static-tables.solutionType.solutionCode'),
      rules: 'required',
      dependencies: {
        triggerFields: ['TYPE'],
        trigger: (values, formApi) => {
          const { TYPE } = values;
          let code = '';
          switch (TYPE) {
            case $t('basic-static-tables.solutionType.m'): {
              code = 'M';
              break;
            }
            case $t('basic-static-tables.solutionType.ss'): {
              code = 'SS';
              break;
            }
            case $t('basic-static-tables.solutionType.ts'): {
              code = 'TS';
              break;
            }
            case $t('basic-static-tables.solutionType.vs'): {
              code = 'VS';
              break;
            }
            default: {
              code = '';
            }
          }
          formApi.setFieldValue('SOLUTIONCODE', code);
        },
      },
    },
    {
      component: 'Input',
      fieldName: 'SOLUTIONTYPE',
      label: $t('basic-static-tables.solutionType.solutionType'),
      rules: 'required',
    },

    {
      component: 'Input',
      fieldName: 'VALIDATEDAY', // label: $t('basic-static-tables.solutionType.validateday'),
      label: '有效天数',
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'VALIDITY',
      label: $t('basic-static-tables.solutionType.validity'),
      rules: 'required',
    },

    {
      component: 'Input',
      fieldName: 'DESCRIPTION',
      label: $t('commons.description'),
    },
    {
      component: 'Checkbox',
      componentProps: {
        trueValue: 'Y',
        falseValue: 'N',
      },
      fieldName: 'ISDAY',
      label: $t('basic-static-tables.solutionType.isDay'),
    },
  ];
}

export function useFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'SOLUTIONTYPE',
      label: $t('basic-static-tables.solutionType.solutionType'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'SOLUTIONCODE',
      label: $t('basic-static-tables.solutionType.solutionCode'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'VALIDATEDAY',
      label: $t('basic-static-tables.solutionType.validateday'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'VALIDITY',
      label: $t('basic-static-tables.solutionType.validity'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'TYPE',
      label: $t('basic-static-tables.solutionType.type'),
    },
  ];
}
