<script setup lang="ts">
import type { Audittrl } from '#/api/run-create-results-entry-run-approv';

import { $signatures_dgApi } from '#/api/run-create-results-entry-run-approv';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import { useColumns } from './data';

const formArgs = defineModel<
  | undefined
  | {
      aDsParam: any[];
      sMode: string;
    }
>();
const queryData = async () => {
  if (!formArgs.value) {
    return [];
  }
  return await $signatures_dgApi([
    formArgs.value.sMode,
    formArgs.value.aDsParam,
  ]);
};

const { Grid, gridApi } = useLimsGridsConfig<Audittrl>(
  useColumns(),
  [],
  queryData,
);

async function LoadSignatures(
  oArgParam?: undefined | { aDsParam: any[]; sMode: string },
) {
  if (oArgParam) {
    formArgs.value = oArgParam;
  }
  gridApi.query();
}

defineExpose({
  LoadSignatures,
});
</script>

<template>
  <Grid />
</template>
