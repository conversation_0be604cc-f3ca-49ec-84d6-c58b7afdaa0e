<script lang="ts" setup>
import { computed } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { openDailyCalibApi } from '#/api/equipment/main-form';
import { useMainFormStore } from '#/store';

import { checkModalSchema } from '../main-form-data';

interface RowType {
  EQID?: string;
  [key: string]: any;
}
const emit = defineEmits(['success']);
const mainFormStore = useMainFormStore();
const currentRow = computed<RowType>(
  () => mainFormStore.getCurrentRow as unknown as RowType,
);
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: checkModalSchema(),
  showDefaultActions: false,
});
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    modalApi.lock();
    try {
      const params = [
        data.event,
        data.weight,
        currentRow.value.EQID,
        data.reason,
      ];
      const res = await openDailyCalibApi(params);
      if (res) {
        emit('success');
      } else {
        message.warning('发起校准失败，所选事件有未完成的记录');
      }
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      // const data = modalApi.getData<SampleTypeApi.SampleType>();
    }
  },
});
</script>
<template>
  <Modal title="添加发送信息">
    <Form class="mx-4" />
  </Modal>
</template>
