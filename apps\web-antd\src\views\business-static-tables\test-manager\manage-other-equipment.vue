<script lang="ts" setup>
import type { TestManagerApi } from '#/api/business-static-tables/test-manager';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, Space } from 'ant-design-vue';

import { $getOtherEquipmentListApi } from '#/api/business-static-tables/test-manager';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import { useOtherEquipmentColumns } from './data';
import SelectEquipmentTypes from './select-equipment-types.vue';

interface FormArgs {
  Method: string;
  TestCode: number;
}
const formArgs = ref<FormArgs>();

const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    // 确认逻辑
    modalApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      // 当模态框打开时，重新获取数据
      const data = modalApi.getData<FormArgs>();
      if (data) {
        formArgs.value = data;
      }
    }
  },
  title: $t('business-static-tables.testManager.otherEquipment'),
});
const [EditModal, editModalApi] = useVbenModal({
  connectedComponent: SelectEquipmentTypes,
  destroyOnClose: true,
  draggable: true,
});
function onEditOtherEquipment() {
  editModalApi
    .setData({
      methodRelatedData: {
        method: formArgs.value?.Method,
        testCode: formArgs.value?.TestCode.toString(),
      },
    })
    .open();
}

const getOutSourceLabList = async () => {
  if (!formArgs.value) return [];
  return await $getOtherEquipmentListApi({
    method: formArgs.value.Method,
    testCode: formArgs.value.TestCode.toString(),
  });
};

const colums = useOtherEquipmentColumns();
const queryData = async () => {
  if (!formArgs.value) return [];
  return await getOutSourceLabList();
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};
const {
  Grid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
} = useLimsGridsConfig<TestManagerApi.TESTMETHODOTHEREQUIPMENT>(
  colums,
  [],
  queryData,
  girdOption,
);

function onRefresh() {
  gridApi.query();
}
</script>
<template>
  <Modal>
    <EditModal @success="onRefresh" class="w-[800px]" />
    <div class="modal-content-full mb-4">
      <Grid>
        <template #toolbar-actions>
          <Space :size="[4, 0]" wrap>
            <Button type="primary" @click="onEditOtherEquipment">
              {{ $t('business-static-tables.testManager.editOtherEquipment') }}
            </Button>
          </Space>
        </template>
        <template #action="{ row }">
          <template v-if="hasEditStatus(row)">
            <Button type="link" @click="saveRowEvent(row)">
              {{ $t('commons.save') }}
            </Button>
            <Button type="link" @click="cancelRowEvent(row)">
              {{ $t('commons.cancel') }}
            </Button>
          </template>
          <template v-else>
            <Button type="link" @click="editRowEvent(row)">
              {{ $t('commons.edit') }}
            </Button>
          </template>
        </template>
      </Grid>
    </div>
  </Modal>
</template>

<style scoped>
.modal-content-full {
  /* 若需内容撑满弹窗可加以下样式 */
  display: flex;
  flex-direction: column;
  height: 100%;
}
</style>
