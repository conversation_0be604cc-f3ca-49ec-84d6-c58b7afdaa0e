import { callServer, getDataSet } from '#/api/core/witlab';

export namespace ConditionsApi {
  export interface PageFetchParams {
    [key: string]: any;
    page: number;
    pageSize: number;
  }

  export interface Conditions {
    ORIGREC: number;
    CONDITION: string;
    TEMPERATURE: string;
    TEMPERATURE_MAX: string;
    TEMPERATURE_STD: string;
    HUMIDITY: string;
    LUMINOSITY: string;
    OTHER: string;
    POSITION: string;
    ELAPSE_TIME_RETURN_SAMPLE: string;
  }

  export interface ConditionsTat {
    ORIGREC: number;
    DEPT: string;
    CONDITION: string;
    TAT: string;
    CONDITION_TAT_UNITS: string;
  }
}

/**
 * 获取存储条件列表数据
 */
async function getConditionsList() {
  return getDataSet('CONDITIONS.dsGetConditions', []);
}

/**
 * 添加存储条件
 * @param data 存储条件数据
 */
async function addCondition(data: ConditionsApi.Conditions) {
  return await callServer('CONDITIONS.scAddCondition', [
    data.CONDITION,
    'AddCondition',
  ]);
}

/**
 * 删除存储条件
 *
 * @param origrec 存储条件数据
 * @returns boolean
 */
async function deleteCondition(condition: string[]) {
  return await callServer('CONDITIONS.scDeleteCondition', [condition]);
}

/**
 * 获取检验周期列表数据
 */
async function getConditionsTatList() {
  return getDataSet('CONDITIONS.getConditionsTat', []);
}

/**
 * 添加检验周期
 * @param data 检验周期数据
 */
async function addConditionTat(data: ConditionsApi.ConditionsTat) {
  return await callServer('Conditions.addConditionsTat', [
    data.DEPT,
    data.CONDITION,
  ]);
}

/**
 * 添加检验周期
 * @param data 检验周期数据
 */
async function deleteConditionTat(origrec: number[]) {
  return await callServer('CONDITIONS.deleteConditionTat', [origrec]);
}

export {
  addCondition,
  addConditionTat,
  deleteCondition,
  deleteConditionTat,
  getConditionsList,
  getConditionsTatList,
};
