<script lang="ts" setup>
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { ProcessSpecificationsApi } from '#/api/business-static-tables/process-specifications';

import { ref } from 'vue'; // 添加了 onMounted 和 reactive 的导入

import { confirm, Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Button, message, Space, TabPane, Tabs } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteSpecification,
  getSpecificationsList,
  updateSpecificationState,
} from '#/api/business-static-tables/process-specifications';

import AddApecificationForm from './add-specification.vue';
import SpecAnalyte from './limits.vue';
import { useSpecificationColumns } from './spec-manager-data';

const activeKey = ref('1');

const isDeleteDisabled = ref(false);
const isReleaseDisabled = ref(false);
const isRetireDisabled = ref(false);
const isRowEditDisabled = ref(false);
const isAddVersionDisabled = ref(false);
const isSpreadDateDisabled = ref(false);

const specNo = ref<null | number>(null);
const status = ref<string>('Draft'); // 初始状态为 Draft

const currentSpecRow = ref<null | ProcessSpecificationsApi.Specifications>(
  null,
);

const specificationGridOptions: VxeTableGridOptions<ProcessSpecificationsApi.Specifications> =
  {
    columns: useSpecificationColumns(),
    stripe: true,
    border: true,
    keepSource: true,
    editConfig: {
      mode: 'row',
      trigger: 'manual',
    },
    height: 'auto',
    pagerConfig: {},
    proxyConfig: {
      ajax: {
        query: async () => {
          return await getSpecificationsList();
        },
      },
    },
    exportConfig: {},
    showOverflow: true,
    rowConfig: {
      keyField: 'ORIGREC',
      isCurrent: true,
      isHover: true,
    },
    toolbarConfig: {
      custom: true,
      export: true,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  };

const specificationGridEvents: VxeGridListeners<ProcessSpecificationsApi.Specifications> =
  {
    currentChange: async ({ row }) => {
      if (row) {
        currentSpecRow.value = row;
        // 行切换时绑定tab下的Grid
        // handleTabChange();
        status.value = row.STATUS;
        specNo.value = row.SPECNO;
        switch (row.STATUS) {
          case 'Active': {
            isDeleteDisabled.value = true;
            isReleaseDisabled.value = true;
            isRetireDisabled.value = false;
            isRowEditDisabled.value = true;
            isAddVersionDisabled.value = false;
            isSpreadDateDisabled.value = false;
            break;
          }
          case 'Draft': {
            isDeleteDisabled.value = false;
            isReleaseDisabled.value = false;
            isRetireDisabled.value = true;
            isRowEditDisabled.value = false;
            isAddVersionDisabled.value = true;
            isSpreadDateDisabled.value = true;
            break;
          }
          case 'Retired': {
            isDeleteDisabled.value = true;
            isReleaseDisabled.value = true;
            isRetireDisabled.value = true;
            isRowEditDisabled.value = true;
            isAddVersionDisabled.value = true;
            isSpreadDateDisabled.value = true;
            break;
          }
          // No default
        }
      }
    },
  };

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: specificationGridOptions,
  // formOptions,
  gridEvents: specificationGridEvents, // 确保事件被绑定
});

function hasEditStatus(row: ProcessSpecificationsApi.Specifications) {
  return gridApi.grid?.isEditByRow(row);
}

function editRowEvent(row: ProcessSpecificationsApi.Specifications) {
  gridApi.grid?.setEditRow(row);
}

async function saveRowEvent(row: ProcessSpecificationsApi.Specifications) {
  await gridApi.grid?.clearEdit();
  // addQcType(row);
  gridApi.grid.reloadRow(row);
  gridApi.setLoading(true);
  setTimeout(() => {
    gridApi.setLoading(false);
    message.success({
      content: `保存成功！`,
    });
  }, 600);
}

const cancelRowEvent = (row: ProcessSpecificationsApi.Specifications) => {
  gridApi.grid?.clearEdit().then(() => {
    // 还原行数据
    gridApi.grid.revertData(row);
  });
};

function onRefresh() {
  gridApi.query(); // 主动触发一次查询
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddApecificationForm,
  destroyOnClose: true,
});

function onCreate() {
  formModalApi.open();
}

function onCopy() {
  const currentRow = gridApi.grid?.getCurrentRecord();
  if (!currentRow) return;

  const specNoValue = currentRow.SPECNO;
  formModalApi.setData({ SPECNO: specNoValue, MODE: 'COPY' }).open();
}

async function onDelete() {
  // 获取选中行
  const aSpecNo: number[] = gridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.SPECNO);

  if (aSpecNo.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${aSpecNo.length} 条数据吗？`,
      icon: 'warning',
      centered: false,
    });

    await deleteSpecification(aSpecNo);

    message.success('删除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

// 发布/废弃
async function onUpdateStatus(sStatus: string) {
  // 获取选中行
  const currentRow = gridApi.grid?.getCurrentRecord();
  if (!currentRow) return;
  const sOrigrec = currentRow.ORIGREC;

  const opera: string = sStatus === 'Active' ? '发布' : '废弃';

  try {
    await confirm({
      title: `确认${opera}`,
      content: `确定要${opera}当前选中的质量标准吗？`,
      icon: 'warning',
      centered: false,
    });
    const ret = await updateSpecificationState(sOrigrec, sStatus);
    if (ret[0]) {
      message.success(`${opera}成功`);
      onRefresh();
    } else {
      message.error(ret[1]);
    }
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

defineExpose({ currentSpecRow }); // Vue 3 的 expose 语法
</script>
<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <div class="my-2 h-[450px] w-full">
      <Grid>
        <template #toolbar-actions>
          <Space>
            <Button type="primary" @click="onCreate">
              {{ $t('ui.actionTitle.create') }}
            </Button>
            <Button
              type="primary"
              danger
              @click="onDelete"
              :disabled="isDeleteDisabled"
            >
              {{ $t('ui.actionTitle.delete') }}
            </Button>
            <Button type="default" @click="onCopy">
              {{ $t('commons.copy') }}
            </Button>
            <Button
              type="default"
              @click="onUpdateStatus('Active')"
              :disabled="isReleaseDisabled"
            >
              {{ $t('commons.release') }}
            </Button>
            <Button
              type="default"
              @click="onUpdateStatus('Retired')"
              :disabled="isRetireDisabled"
            >
              {{ $t('commons.retire') }}
            </Button>
            <Button type="default" :disabled="isAddVersionDisabled">
              {{
                $t('business-static-tables.process-specifications.addversion')
              }}
            </Button>
            <Button type="default" :disabled="isSpreadDateDisabled">
              {{
                $t('business-static-tables.process-specifications.spreadDate')
              }}
            </Button>
          </Space>
        </template>
        <template #action="{ row }">
          <template v-if="hasEditStatus(row)">
            <Button type="link" @click="saveRowEvent(row)">
              {{ $t('business-static-tables.save') }}
            </Button>
            <Button type="link" @click="cancelRowEvent(row)">
              {{ $t('business-static-tables.cancel') }}
            </Button>
          </template>
          <template v-else>
            <Button
              type="link"
              @click="editRowEvent(row)"
              :disabled="isRowEditDisabled"
            >
              {{ $t('business-static-tables.edit') }}
            </Button>
          </template>
        </template>
      </Grid>
    </div>
    <Tabs v-model:active-key="activeKey">
      <TabPane key="1" tab="检项">
        <SpecAnalyte :spec-no="specNo" :status="status" />
      </TabPane>
    </Tabs>
  </Page>
</template>
