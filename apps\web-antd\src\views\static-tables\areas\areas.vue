<script lang="ts" setup>
import type { Recordable } from '@vben/types';

import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';

import { ref } from 'vue';

import { Page, useVbenForm, useVbenModal, z } from '@vben/common-ui';
import { isEmpty } from '@vben/utils';

import { Button, message } from 'ant-design-vue';
import { EditIcon } from 'lucide-vue-next';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { callServer, getDataSetNoPage, updateProvider } from '#/api';
import { getAllAreas } from '#/api/static-tables';
import { showAduitViewer } from '#/components/audit-viewer';
import { $t } from '#/locales';
import { saveEditingRowOriginalData } from '#/utils/lims-grids-config';

const currentAreaRow = ref<any>(null);

const isAreaNameExists = (dept: string, value: string) => {
  return callServer('Areas.validateExist', [dept, value]);
};

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: [
    {
      component: 'ApiSelect',
      fieldName: 'DEPT',
      label: $t('static-tables.areas.dept'),
      rules: 'required',
      componentProps: {
        api: () => getDataSetNoPage('Areas.getSites', []),
        fieldNames: {
          label: 'DEPT',
          value: 'DEPT',
        },
        class: 'w-full',
      },
    },
    {
      component: 'Input',
      fieldName: 'AREA_NAME',
      label: $t('static-tables.areas.areaName'),
      rules: z
        .string()
        .min(
          2,
          $t('ui.formRules.minLength', [$t('static-tables.areas.areaName'), 2]),
        )
        .max(
          20,
          $t('ui.formRules.maxLength', [
            $t('static-tables.areas.areaName'),
            20,
          ]),
        )
        .refine(
          async (_value: string): Promise<boolean> => {
            const values = await formApi.getValues();
            return !isEmpty(values?.DEPT);
          },
          () => ({
            message: $t('ui.formRules.required', [
              $t('static-tables.areas.dept'),
            ]),
          }),
        )
        .refine(
          async (value: string) => {
            const values = await formApi.getValues();
            return !(await isAreaNameExists(values.DEPT, value));
          },
          (value) => ({
            message: $t('ui.formRules.alreadyExists', [
              $t('static-tables.areas.areaName'),
              value,
            ]),
          }),
        ),
    },
    {
      component: 'Textarea',
      componentProps: {
        maxLength: 200,
        showCount: true,
        rows: 3,
        class: 'w-full',
      },
      fieldName: 'AREA_DESCRIPTION',
      label: $t('static-tables.areas.description'),
      rules: z
        .string()
        .max(
          500,
          $t('ui.formRules.maxLength', [
            $t('static-tables.areas.description'),
            500,
          ]),
        )
        .optional(),
    },
    {
      component: 'Input',
      fieldName: 'COST_CENTER',
      label: $t('static-tables.areas.costCenter'),
      rules: z.string().optional(),
    },
    {
      component: 'Input',
      fieldName: 'EXTERNAL_AREA_CODE',
      label: $t('static-tables.areas.externalAreaCode'),
      rules: z.string().optional(),
    },
    {
      component: 'Input',
      fieldName: 'OTHER_ID',
      label: $t('static-tables.areas.otherId'),
      rules: z.string().optional(),
    },
  ],
  showDefaultActions: false,
});

function resetForm() {
  formApi.resetForm();
  formApi.setValues({});
}

const [FormModal, formModalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (valid) {
      formModalApi.lock();
      const data = await formApi.getValues();
      try {
        const nOrigRec = await callServer('Areas.AddArea', [
          data.DEPT,
          data.AREA_NAME,
          data.AREA_DESCRIPTION,
          data.COST_CENTER,
          data.EXTERNAL_AREA_CODE,
          data.OTHER_ID,
        ]);
        if (nOrigRec) {
          message.success(
            $t('ui.actionMessage.operationSuccess', [data.AREA_NAME]),
          );
        }
        formModalApi.close();
        areaGridApi.query();
      } catch {
        message.error($t('ui.actionMessage.operationFailed', [data.AREA_NAME]));
        resetForm();
        return;
      } finally {
        formModalApi.lock(false);
      }
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      resetForm();
    }
  },
  draggable: true,
});

const [AreaGrid, areaGridApi] = useVbenVxeGrid({
  gridEvents: {
    currentRowChange: (params: any) => {
      currentAreaRow.value = params.row;
    },
    editActivated: saveEditingRowOriginalData,
    editClosed: updateProvider,
    cellMenu: ({ row, $grid }) => $grid.setCurrentRow(row),
    menuClick: ({ menu }) => {
      if (menu.code === 'viewAudit') {
        const currentRow = areaGridApi.grid?.getCurrentRecord();
        if (currentRow) {
          showAduitViewer({
            tableName: areaGridApi.grid.params.tableName,
            origrec: currentRow.ORIGREC,
          });
        }
      }
    },
  },
  gridOptions: {
    columns: [
      {
        align: 'left',
        field: 'ORIGREC',
        title: $t('commons.origrec'),
        visible: false,
        minWidth: 150,
      },
      {
        field: 'DEPT',
        title: $t('static-tables.areas.dept'),
        minWidth: 150,
      },
      {
        field: 'AREA_NAME',
        title: $t('static-tables.areas.areaName'),
        minWidth: 150,
      },
      {
        field: 'AREA_DESCRIPTION',
        title: $t('static-tables.areas.description'),
        minWidth: 200,
        editRender: { name: 'VxeInput' },
      },
      {
        field: 'COST_CENTER',
        title: $t('static-tables.areas.costCenter'),
        minWidth: 100,
        editRender: { name: 'VxeInput' },
      },
      {
        field: 'EXTERNAL_AREA_CODE',
        title: $t('static-tables.areas.externalAreaCode'),
        minWidth: 100,
        editRender: { name: 'VxeInput' },
      },
      {
        field: 'OTHER_ID',
        title: $t('static-tables.areas.otherId'),
        minWidth: 100,
        editRender: { name: 'VxeInput' },
      },
      {
        align: 'center',
        cellRender: {
          attrs: {
            nameField: 'name',
            nameTitle: $t('static-tables.areas.areaName'),
            onClick: onActionClick,
          },
          name: 'CellOperation',
          options: [
            {
              code: 'delete',
            },
          ],
        },
        field: 'operation',
        fixed: 'right',
        headerAlign: 'center',
        showOverflow: false,
        title: $t('system.dept.operation'),
        width: 100,
      },
    ],
    height: 'auto',
    pagerConfig: {
      enabled: false,
    },
    proxyConfig: {
      ajax: {
        query: async (_params) => {
          return await getAllAreas();
        },
      },
    },
    toolbarConfig: {
      slots: {
        buttons: 'toolbarButtons',
      },
      custom: true,
      export: false,
      refresh: { code: 'query' },
      zoom: true,
    },
    columnConfig: {
      resizable: true,
    },
    rowConfig: {
      isCurrent: true,
      keyField: 'ORIGREC',
    },
    editConfig: {
      trigger: 'click',
      mode: 'cell',
    },
    menuConfig: {
      body: {
        options: [
          [
            {
              code: 'viewAudit',
              name: '查看历史',
              prefixConfig: { icon: 'vxe-icon-table' },
              visible: true,
              disabled: false,
            },
          ],
        ],
      },
    },
    keepSource: true,
    params: {
      limsControlId: 'dgAreas',
      tableName: 'AREAS',
    },
  } as VxeTableGridOptions,
});

function onActionClick({ code, row }: OnActionClickParams) {
  switch (code) {
    case 'delete': {
      onDelete(row);
      break;
    }
  }
}

async function onDelete(row: Recordable<any>) {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting', [row.BUILDING_NAME]),
    duration: 0,
    key: 'action_process_msg',
  });

  callServer('Areas.DeleteArea', [row.ORIGREC])
    .then((msg) => {
      if (msg) {
        message.error({
          content: $t(`static-tables.areas.${msg}`, [row.AREA_NAME]),
          key: 'action_process_msg',
        });
        return;
      }

      message.success({
        content: $t('ui.actionMessage.deleteSuccess', [row.BUILDING_NAME]),
        key: 'action_process_msg',
      });

      areaGridApi?.query();
    })
    .catch(() => {
      hideLoading();
    });
}

function onCreate() {
  formModalApi.setData(null).open();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal
      :title="$t('ui.actionTitle.create', [$t('static-tables.areas.areaName')])"
    >
      <Form class="mx-4">
        <template #prepend-footer>
          <div class="flex-auto">
            <Button type="primary" danger @click="resetForm">
              {{ $t('common.reset') }}
            </Button>
          </div>
        </template>
      </Form>
    </FormModal>
    <AreaGrid class="mx-2">
      <template #toolbarButtons>
        <Button type="primary" @click="onCreate">
          <EditIcon class="size-5" />
          {{
            $t('ui.actionTitle.create', [$t('static-tables.areas.areaName')])
          }}
        </Button>
      </template>
    </AreaGrid>
  </Page>
</template>
