import type { Dayjs } from 'dayjs';

import { callServer, getDataSet, getDataSetNoPage } from '#/api/core';

export namespace XFolderApi {
  export interface XFolder {
    [key: string]: any;
    ORIGREC: number;
    FDISPSTAT: string;
    DISP_TEMPLATENO: number;
    DISP_PROTOCOLNO: number;
    DEPT: string;
    FOLDERNAM: string;
    VERSION: number;
    FNOTES1: string;
    DISPSTEPCODE: string;
    STARTDDATE: Dayjs;
    EXPDATE: Dayjs;
    CONDITION_TYPE: string;
    STABNO: number;
    WORKFLOWCODE: string;
    SP_CODE: null | number;
  }

  export interface ConditionTree {
    UN: string;
    VALUE: string;
    TEXT: string;
    PARENT: string;
    SELECTED: string;
    key: string;
    children?: ConditionTree[];
  }

  export interface Intervals {
    [key: string]: any;
    INTERVAL: string;
    CHECKED: string;
    SORTER1: number;
    SORTER2: number;
  }

  export interface CondIntTestMatrix {
    [key: string]: any;
    Col1: string;
    Col2: number | string;
    Col3: string;
    Col4: string;
    Col5: string;
    Col6: string;
    Col7: string;
    Col8: string;
    Col9: string;
    Col10: string;
    Col11: string;
    Col12: string;
    Col13: string;
    Col14: string;
    Col15: string;
    Col16: string;
    Col17: string;
    Col18: string;
    Col19: string;
    Col20: string;
    Col21: string;
    Col22: string;
    Col23: string;
    Col24: string;
    Col25: string;
    Col26: string;
    Col27: string;
    Col28: string;
    Col29: string;
    Col30: string;
    Col31: string;
  }

  export interface CopyTemplateSchame {
    [key: string]: any;
    ToDept: string;
    Name: string;
  }

  export interface XFolderIntervalTat {
    [key: string]: any;
    INTERVAL: string;
    TATBEFOREAFTER: number;
    DEPT: string;
  }

  export interface XFolderRelations {
    [key: string]: any;
    MATCODE: string;
    MATNAME: string;
    CONTAINER_MATCODE: string;
    PULL_CONTAINER_MATCODE: string;
    key: string;
  }

  export interface XStabTestQty {
    [key: string]: any;
    SP_TESTNO: string;
    UNITS_QTY: string;
    UNITS: number;
    STORED_QTY: string;
    CONTAINER_MATCODE: string;
    QTY: number;
    PULL_CONTAINER_MATCODE: string;
    MATCODE: string;
  }
}

const $getStudyTemplateApi = async ({
  sMode,
  nSpcode,
  sMatCode,
  sShowAll,
  sStabnoList,
  sProtocolName,
  sProtocolMatcode,
  nProtocolDispNo,
}: {
  nProtocolDispNo?: number;
  nSpcode: number;
  sMatCode: string;
  sMode: string;
  sProtocolMatcode?: string;
  sProtocolName?: string;
  sShowAll: 'false' | 'true';
  sStabnoList: string;
}) => {
  const res = await getDataSet('StudyConfiguration.STUDYTEMPALTES_DG', [
    sMode,
    nSpcode,
    sMatCode,
    sShowAll,
    sStabnoList,
    sProtocolName,
    sProtocolMatcode,
    nProtocolDispNo,
  ]);
  return res;
};

const $checkUniqueNameApi = async ({
  name,
  isProtocol,
}: {
  isProtocol: 'B' | 'Y';
  name: string;
}) => {
  return await callServer('StudyConfiguration.checkUniqueName', [
    name,
    isProtocol,
  ]);
};

const $getConditionsPositionsApi = async ({
  stabNo,
}: {
  stabNo: null | string | undefined;
}) => {
  return (await getDataSetNoPage('StudyConfiguration.getConditionsPositions', [
    stabNo,
  ])) as XFolderApi.ConditionTree[];
};

const $getIntervalsApi = async ({
  stabNo,
  dept,
}: {
  dept: string;
  stabNo: null | string | undefined;
}) => {
  return await getDataSetNoPage('StudyConfiguration.INTERVALS_CB', [
    stabNo,
    dept,
  ]);
};

// StudyConfiguration.AddEditStudyTemplate
const $addEditStudyTemplateApi = async ({
  spCode,
  conditions,
  intervals,
  testList,
  openingMode,
  name,
  stabNo,
  status,
  nFomStabNo,
  aMaterials,
  sCycle,
  sWorkflowCode,
}: {
  aMaterials?: string[];
  conditions: string[];
  intervals: string[];
  name: string;
  nFomStabNo?: number;
  openingMode: string;
  sCycle?: string;
  spCode: number;
  stabNo: null | number | string | undefined;
  status?: null | string;
  sWorkflowCode?: string;
  testList: number[] | string[];
}) => {
  return await callServer('StudyConfiguration.AddEditStudyTemplate', [
    spCode,
    conditions,
    intervals,
    testList,
    openingMode,
    stabNo,
    status,
    null,
    name,
    nFomStabNo,
    aMaterials,
    sCycle,
    sWorkflowCode,
  ]);
};

// StudyConfiguration.GetStabilityArrayData
const $getStabilityArrayDataApi = async ({ stabNo }: { stabNo: number }) => {
  return await callServer('StudyConfiguration.GetStabilityArrayData', [stabNo]);
};

// StudyConfiguration.DeleteProtocol
const $deleteProtocolApi = async ({ stabNo }: { stabNo: number }) => {
  return await callServer('StudyConfiguration.DeleteProtocol', [stabNo]);
};

/**
 *
 * @returns [{TESTCATCODE: string, PRODUCT_SPECIFIC: string}]
 */
const $cbTestCatApi = async () => {
  return await getDataSetNoPage('TEST_PLAN_MANAGER.CB_TEST_CAT', []);
};

const $executeGeneralWorkFlowStepApi = async ({
  currStepCode,
  nextStepCode,
  stabNo,
  sComment,
  dStartDate,
  spCode,
  dEndDate,
  workFlowCode,
  itemORIGREC,
}: {
  currStepCode: string;
  dEndDate: Dayjs | null;
  dStartDate: Dayjs | null;
  itemORIGREC: number;
  nextStepCode: string;
  sComment: null | string;
  spCode: null | number;
  stabNo: number;
  workFlowCode: string;
}) => {
  return await callServer('StudyConfiguration.ExecuteGeneralWorkFlowStep', [
    currStepCode,
    nextStepCode,
    stabNo,
    sComment,
    dStartDate,
    spCode,
    dEndDate,
    workFlowCode,
    itemORIGREC,
  ]);
};

const $closeStudyConfigurationApi = async ({
  mode,
  spCode,
}: {
  mode: string;
  spCode: null | number;
}) => {
  return await callServer('StudyConfiguration.CloseStudyConfiguration', [
    mode,
    spCode,
  ]);
};

const $copyTemplateOrProtocolApi = async ({
  nFromStabNo,
  sOpeningMode,
  sName,
  sProdGroup,
  bNewVersion,
  sSite,
}: {
  bNewVersion: boolean;
  nFromStabNo: number;
  sName: string;
  sOpeningMode: string;
  sProdGroup: string;
  sSite: string;
}) => {
  return await callServer('StudyConfiguration.copyTemplateOrProtocol', [
    nFromStabNo,
    sOpeningMode,
    sName,
    sProdGroup,
    bNewVersion,
    sSite,
  ]);
};

const $getSitesApi = async () => {
  return await getDataSetNoPage('StudyConfiguration.getSites', []);
};

const $getStabnoIntervalsTat = async ({ stabNo }: { stabNo: number }) => {
  return await getDataSetNoPage('StudyConfiguration.GetStabnoIntervalsTAT', [
    stabNo,
  ]);
};

const $updateStabnoIntervalTat = async ({
  stabNo,
  dept,
  interval,
  intervalTatOrigrec,
}: {
  dept: string;
  interval: string;
  intervalTatOrigrec: number | string;
  stabNo: number;
}) => {
  return await callServer('StudyConfiguration.UpdateStabnoIntervalTAT', [
    stabNo,
    dept,
    interval,
    intervalTatOrigrec,
  ]);
};

const $getIntervalTat_ssl = async ({
  dept,
  interval,
}: {
  dept: string;
  interval: string;
}) => {
  return await getDataSetNoPage('StudyConfiguration.getIntervalTat_ssl', [
    dept,
    interval,
  ]);
};

/**
 * 获取稳定性多选物料列表
 * @param param0
 * @param param0.matType 材料类型
 * @param param0.stabNo 稳定性编号
 * @param param0.materials 材料列表
 * @param param0.addMaterials 添加的材料
 * @returns any[]
 */
const $getMaterialsMultichoiceApi = async ({
  matType,
  stabNo,
  materials,
  addMaterials,
}: {
  addMaterials: string;
  materials: string;
  matType: string;
  stabNo: null | string | undefined;
}) => {
  return await getDataSetNoPage('StudyConfiguration.getMaterialsMultichoice', [
    matType,
    stabNo,
    materials,
    addMaterials,
  ]);
};

/**
 * 获取稳定性模板
 * @param param0
 * @param param0.dept 部门
 * @returns any[]
 */
const $getTemplatesApi = async ({ dept }: { dept: string }) => {
  return await getDataSetNoPage('StudyConfiguration.getTemplates', [dept]);
};

/**
 * 获取物料类型
 * @returns [{MATTYPE:string}]
 */
const $getMaterialTypesApi = async () => {
  return await getDataSetNoPage('StudyConfiguration.getMaterialTypes', []);
};

const $getTestMutiChoiceApi = async ({
  spCode,
  drawNo,
  testCat,
  profile,
  aSelTests,
}: {
  aSelTests: number[] | string[];
  drawNo: number;
  profile: string;
  spCode: number;
  testCat: string;
}) => {
  return await getDataSetNoPage('TEST_PLAN_MANAGER.DS_TESTS_MULTICHOICE', [
    spCode,
    drawNo,
    testCat,
    profile,
    aSelTests,
  ]);
};

/**
 * 新增草案
 */
const $addProtocolApi = async ({
  sProtocolName,
  sProdGroup,
  bNewVersion,
}: {
  bNewVersion: boolean;
  sProdGroup: string;
  sProtocolName: string;
}) => {
  return await callServer('StudyConfiguration.addProtocol', [
    sProtocolName,
    sProdGroup,
    bNewVersion,
  ]);
};

/**
 * 删除测试计划
 * @param param0
 * @param param0.spCode 测试计划编号
 * @returns null
 */
const $deleteSampleProgramApi = async ({ spCode }: { spCode: number }) => {
  return await callServer('StudyConfiguration.deleteSampleProgram', [spCode]);
};

/**
 * 获取稳定性研究测试列表
 * @param param0 {spCode,stabNo}
 * @param param0.spCode 测试计划编号
 * @param param0.stabNo 稳定性编号
 * @returns any[]
 */
const $getStablityTestListApi = async ({
  spCode,
  stabNo,
}: {
  spCode: number;
  stabNo: number;
}) => {
  return await getDataSetNoPage('StudyConfiguration.STABILITYTESTLIST_CBL', [
    spCode,
    stabNo,
  ]);
};

/**
 * 获取方案是否有隐藏测试
 * @param param0 {spCode,drawNo}
 * @param param0.spCode 测试计划编号
 * @param param0.drawNo drawNo
 * @returns boolean
 */
const $defaultProfileHasHiddenTestsApi = async ({
  spCode,
  drawNo,
}: {
  drawNo: number;
  spCode: number;
}) => {
  return await getDataSetNoPage(
    'TEST_PLAN_MANAGER.DefaultProfileHasHiddenTests',
    [spCode, drawNo],
  );
};

// TEST_PLAN_MANAGER.CHECK_DEFAULT
const $checkDefaultApi = async ({
  spCode,
  drawNo,
}: {
  drawNo: number;
  spCode: number;
}) => {
  return await callServer('TEST_PLAN_MANAGER.CHECK_DEFAULT', [spCode, drawNo]);
};

// TEST_PLAN_MANAGER.INSERT_TESTS
const $insertTestApi = async ({
  spCode,
  drawNo,
  profile,
  elements,
  NODELSYNC,
  callingApp,
  sTemplateCode,
}: {
  callingApp: string;
  drawNo: number;
  elements: string[];
  NODELSYNC: string;
  profile: string;
  spCode: number;
  sTemplateCode: string;
}) => {
  return await callServer('TEST_PLAN_MANAGER.INSERT_TESTS', [
    spCode,
    drawNo,
    profile,
    elements,
    NODELSYNC,
    callingApp,
    sTemplateCode,
  ]);
};

const $createXorders_XordTaskApi = async ({
  stabNo,
  condition,
  interval,
  testcode,
  action,
  reason,
  mode,
  subStudyNo,
}: {
  action: string;
  condition: string;
  interval: string;
  mode: null | string;
  reason: string;
  stabNo: number;
  subStudyNo: null | number;
  testcode: number | string;
}) => {
  return await callServer('StudyConfiguration.CreateXorders_XordTask', [
    stabNo,
    condition,
    interval,
    testcode,
    action,
    reason,
    mode,
    subStudyNo,
  ]);
};

const $scFillMatrixApi = async (data: any[]) => {
  return await callServer('StudyManager.scFillMatrix', data);
};

const $getStabilityMaterialsApi = async ({ stabNo }: { stabNo: number }) => {
  return await getDataSetNoPage('StudyConfiguration.getStabilityMaterials', [
    stabNo,
  ]);
};

const $getStoredContainersApi = async ({
  matCode,
}: {
  matCode: string;
  storedContainer?: string;
}) => {
  return await getDataSetNoPage('StudyConfiguration.getStoredContainers', [
    matCode,
  ]);
};

const $getChildContainersApi = async ({
  matCode,
  storedContainer,
}: {
  matCode: string;
  storedContainer?: string;
}) => {
  return await getDataSetNoPage('StudyConfiguration.getChildContainers', [
    matCode,
    storedContainer,
  ]);
};

const $getQtyMaterialsApi = async ({ stabNo }: { stabNo: number }) => {
  return await getDataSetNoPage('StudyConfiguration.getQtyMaterials', [stabNo]);
};

// QTYREQUIRED_GD_ssl
const $qtyRequired_GD_ssl = async ({
  stabNo,
  matCode,
  spCode,
}: {
  matCode: string;
  spCode: number;
  stabNo: number;
}) => {
  return await getDataSetNoPage('StudyConfiguration.QTYREQUIRED_GD_ssl', [
    stabNo,
    matCode,
    spCode,
  ]);
};

const $editStabMaterialsApi = async ({
  materials,
  stabNo,
}: {
  materials: any[];
  stabNo: number;
}) => {
  return await callServer('StudyConfiguration.editMaterials', [
    materials,
    stabNo,
  ]);
};

export {
  $addEditStudyTemplateApi,
  $addProtocolApi,
  $cbTestCatApi,
  $checkDefaultApi,
  $checkUniqueNameApi,
  $closeStudyConfigurationApi,
  $copyTemplateOrProtocolApi,
  $createXorders_XordTaskApi,
  $defaultProfileHasHiddenTestsApi,
  $deleteProtocolApi,
  $deleteSampleProgramApi,
  $editStabMaterialsApi,
  $executeGeneralWorkFlowStepApi,
  $getChildContainersApi,
  $getConditionsPositionsApi,
  $getIntervalsApi,
  $getIntervalTat_ssl,
  $getMaterialsMultichoiceApi,
  $getMaterialTypesApi,
  $getQtyMaterialsApi,
  $getSitesApi,
  $getStabilityArrayDataApi,
  $getStabilityMaterialsApi,
  $getStablityTestListApi,
  $getStabnoIntervalsTat,
  $getStoredContainersApi,
  $getStudyTemplateApi,
  $getTemplatesApi,
  $getTestMutiChoiceApi,
  $insertTestApi,
  $qtyRequired_GD_ssl,
  $scFillMatrixApi,
  $updateStabnoIntervalTat,
};
