<script lang="ts" setup>
import type { Recordable } from '@vben/types';

import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { SystemUserApi } from '#/api';

import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { Button, message, Modal, Space } from 'ant-design-vue';
import { Signature } from 'lucide-vue-next';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { deleteUser, getUserList, updateUserState } from '#/api';
import { $t } from '#/locales';

import { useColumns, useGridFormSchema } from './data';
import Form from './modules/form.vue';
import SignForm from './modules/sign-form.vue';

const [SignFormModal, signFormModalApi] = useVbenModal({
  connectedComponent: SignForm,
  destroyOnClose: true,
});

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: Form,
  destroyOnClose: true,
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['createTime', ['startTime', 'endTime']]],
    schema: useGridFormSchema(),
    submitOnChange: true,
  },
  gridOptions: {
    columns: useColumns(onActionClick, onStatusChange),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }: { page: any }, formValues: any) => {
          return await getUserList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
        },
      },
    },
    rowConfig: {
      keyField: 'userId',
      isCurrent: true,
    },
    toolbarConfig: {
      slots: {
        buttons: 'toolbarButtons',
      },
      custom: true,
      export: false, // 根据需要开启导出
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
    params: {
      tableName: 'User',
    },
  } as VxeTableGridOptions<SystemUserApi.User>,
});

function onActionClick(e: OnActionClickParams<SystemUserApi.User>) {
  switch (e.code) {
    case 'delete': {
      onDelete(e.row);
      break;
    }
    case 'edit': {
      onEdit(e.row);
      break;
    }
  }
}

/**
 * 将Antd的Modal.confirm封装为promise，方便在异步函数中调用。
 */
function confirm(content: string, title: string) {
  return new Promise((resolve, reject) => {
    Modal.confirm({
      content,
      onCancel() {
        reject(new Error('已取消'));
      },
      onOk() {
        resolve(true);
      },
      title,
    });
  });
}

/**
 * 状态开关即将改变
 * @param newState 期望改变的状态值
 * @param row 行数据
 * @returns 返回false则中止改变，返回其他值（undefined、true）则允许改变
 */
async function onStatusChange(newState: number, row: SystemUserApi.User) {
  const status: Recordable<string> = {
    0: $t('common.disabled'),
    1: $t('common.enabled'),
  };
  try {
    await confirm(
      `你要将${row.userName}的状态切换为 【${status[newState.toString()]}】 吗？`,
      `切换状态`,
    );
    // 调用更新用户状态的API
    await updateUserState(row.userId, newState); // 仅更新状态
    return true;
  } catch {
    return false;
  }
}

function onEdit(row: SystemUserApi.User) {
  // console.log(gridApi.grid.params);
  formDrawerApi.setData(row).open();
}

async function onDelete(row: SystemUserApi.User) {
  try {
    message.loading({
      content: $t('ui.actionMessage.deleting', [row.userName]),
      duration: 0,
      key: 'action_process_msg',
    });

    await deleteUser(row.userId);

    message.success({
      content: $t('ui.actionMessage.deleteSuccess', [row.userName]),
      key: 'action_process_msg',
    });

    onRefresh();
  } catch {
    message.error({
      content: $t('system.ui.actionMessage.deleteFailed', [row.userName]),
      key: 'action_process_msg',
    });
  }
}

function onRefresh() {
  gridApi.query();
}

function onCreate() {
  formDrawerApi.setData({}).open();
}

function onSignUpdate() {
  const currentRow = gridApi.grid.getCurrentRecord();
  if (!currentRow) {
    message.warning($t('commons.selectOne'));
    return;
  }
  signFormModalApi.setData(currentRow).open();
}
</script>
<template>
  <Page auto-content-height>
    <FormDrawer />
    <SignFormModal />
    <Grid>
      <template #toolbarButtons>
        <Space>
          <Button
            type="primary"
            @click="onCreate"
            v-access:code="['System.User.Create']"
          >
            <Plus class="size-5" />
            {{ $t('ui.actionTitle.create', [$t('system.user.name')]) }}
          </Button>
          <Button @click="onSignUpdate">
            <Signature class="size-5" />
            {{ $t('ui.actionTitle.view', [$t('system.user.signPic')]) }}
          </Button>
        </Space>
      </template>
    </Grid>
  </Page>
</template>
