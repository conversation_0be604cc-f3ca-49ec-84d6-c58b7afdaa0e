import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { EquipmentMgApi } from '#/api/equipment/equipment-mg';

import dayjs from 'dayjs';

import {
  getDCUFileType<PERSON>pi,
  getDCUMethodApi,
  getDsGetMeasureTypesApi,
  getDsGetUnitsApi,
  getEquipmentTypeApi,
  getPositionApi,
  getRESTScriptsApi,
  getUserApi,
} from '#/api/equipment/equipment-mg';
import { $t } from '#/locales';

export function useColumns(): VxeTableGridOptions<EquipmentMgApi.MetaDataEquipment>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'EQID',
      title: $t('equipment.equipment-mg.equipmentId'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'EQTYPE',
      title: $t('equipment.equipment-mg.equipmentType'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'EQNAME',
      title: $t('equipment.equipment-mg.equipmentName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MAINSTATUS',
      title: $t('equipment.equipment-mg.status'),
      filterRender: {
        name: 'TableFilterInput',
      },
      formatter: 'formatTranslate',
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}

export function useSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'EQID',
      label: $t('equipment.equipment-mg.equipmentId'),
    },
    {
      component: 'Input',
      fieldName: 'EQNAME',
      label: $t('equipment.equipment-mg.equipmentName'),
    },
    {
      component: 'Select',
      fieldName: 'EQTYPE',
      label: $t('equipment.equipment-mg.equipmentType'),
      componentProps: {
        class: 'w-full',
        allowClear: true,
        filterOption: true,
        options: [
          {
            label: '选项1',
            value: '1',
          },
          {
            label: '选项2',
            value: '2',
          },
        ],
        placeholder: '请选择',
        showSearch: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'MFG',
      label: $t('equipment.equipment-mg.manufacturer'),
    },
    {
      component: 'Input',
      fieldName: 'SPEC',
      label: $t('equipment.equipment-mg.model'),
    },
    {
      component: 'Input',
      fieldName: 'MODELNO',
      label: $t('equipment.equipment-mg.modelNo'),
    },
    {
      component: 'Input',
      fieldName: 'EQUIPMANAGER',
      label: $t('equipment.equipment-mg.primaryResponsibleParty'),
    },
    {
      component: 'Input',
      fieldName: 'SERIALNO',
      label: $t('equipment.equipment-mg.serialNumber'),
    },
    {
      component: 'Input',
      fieldName: 'EQPATH',
      label: $t('equipment.equipment-mg.storageLocation'),
    },

    {
      component: 'Input',
      fieldName: 'EQUIPMANAGER_SECOND',
      label: $t('equipment.equipment-mg.secondaryResponsibleParty'),
    },
    {
      component: 'Select',
      fieldName: 'STATUS',
      label: $t('equipment.equipment-mg.equipmentStatus'),
      componentProps: {
        class: 'w-full',
        allowClear: true,
        filterOption: true,
        options: [
          {
            label: 'In Service',
            value: 'In Service',
          },
          {
            label: 'Not In Service',
            value: 'Not In Service',
          },
        ],
        placeholder: '请选择',
        showSearch: true,
      },
    },
    {
      component: 'DatePicker',
      fieldName: 'INSRVDATE',
      label: $t('equipment.equipment-mg.installationDate'),
      componentProps: {
        type: 'date',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      component: 'DatePicker',
      fieldName: 'PRODUCTIONDATE',
      label: $t('equipment.equipment-mg.activationDate'),
      componentProps: {
        type: 'date',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      component: 'Input',
      fieldName: 'OFFLINESTATUS',
      label: $t('equipment.equipment-mg.offlineStatus'),
      componentProps: {
        class: 'w-full',
        allowClear: true,
        filterOption: true,
        options: [
          {
            label: '完好',
            value: '完好',
          },
          {
            label: '停用',
            value: '停用',
          },
          {
            label: '维修',
            value: '维修',
          },
          {
            label: '报废',
            value: '报废',
          },
        ],
        placeholder: '请选择',
        showSearch: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'RELATIONEQID',
      label: $t('equipment.equipment-mg.associatedEquipment'),
    },
    {
      component: 'Input',
      fieldName: 'CALMECHANISM',
      label: $t('equipment.equipment-mg.calibrationInstitution'),
    },
    {
      component: 'Input',
      fieldName: 'CALPARAM',
      label: $t('equipment.equipment-mg.calibrationParameters'),
    },

    {
      component: 'Input',
      fieldName: 'SOFTNM',
      label: $t('equipment.equipment-mg.softwareName'),
    },
    {
      component: 'Input',
      fieldName: 'SOFTVERSION',
      label: $t('equipment.equipment-mg.softwareVersion'),
    },
    {
      component: 'Input',
      fieldName: 'RANGE',
      label: $t('equipment.equipment-mg.scaleCapacity'),
    },
    {
      component: 'Input',
      fieldName: 'PRECISIONS',
      label: $t('equipment.equipment-mg.scalePrecision'),
    },
    {
      component: 'Input',
      fieldName: 'MEASUREMENTCODE',
      label: $t('equipment.equipment-mg.equipmentCode'),
    },
    {
      component: 'Input',
      fieldName: 'DIVISIONVALUE',
      label: $t('equipment.equipment-mg.verificationSeparation'),
    },
    {
      component: 'Input',
      fieldName: 'ELN_ID',
      label: $t('equipment.equipment-mg.usageRecordELN'),
    },
  ];
}

export function eventColumns(): VxeTableGridOptions<EquipmentMgApi.MetaDataEquipment>['columns'] {
  return [
    {
      align: 'center',
      field: 'MAINTENANCEEVENT',
      title: $t('equipment.equipment-mg.deviceEvent'),
      filterRender: {
        name: 'MAINTENANCEEVENT',
      },
      editRender: { name: 'input' },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'DESCRIPTION',
      title: $t('equipment.equipment-mg.description'),
      filterRender: {
        name: 'TableFilterInput',
      },
      editRender: { name: 'input' },
      filters: [{ data: '' }],
      sortable: true,
    },

    {
      align: 'center',
      field: 'LASTTIMEEXPDATE',
      title: $t('equipment.equipment-mg.lastMaintenanceTime'),
      filterRender: {
        name: 'TableFilterInput',
      },
      formatter: 'formatDate',
      editRender: { name: 'input', attrs: { type: 'date' } },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MAINEXPDATE',
      title: $t('equipment.equipment-mg.mainexpdate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      formatter: 'formatDate',
      editRender: { name: 'input', attrs: { type: 'date' } },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MAINFREQUENCY',
      title: $t('equipment.equipment-mg.maintenanceFrequency'),
      filterRender: {},
      formatter: 'formatTranslate',
      editRender: {
        name: 'select',
        options: [
          { label: '每天', value: 'Daily' },
          { label: '每周', value: 'Weekly' },
          { label: '每月', value: 'Monthly' },
          { label: '每季', value: 'Quarterly' },
          { label: '每年', value: 'Yearly' },
          { label: '每两年', value: 'TwoYearly' },
          { label: '每三年', value: 'ThreeYearly' },
          { label: '每半年', value: 'Semiannually' },
        ],
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'REMINDERWINDOW',
      title: $t('equipment.equipment-mg.reminderwindow'),
      filterRender: {
        name: 'TableFilterInput',
      },
      editRender: {
        name: 'VxeNumberInput',
        props: { type: 'number', align: 'right', showCurrency: true },
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'BINDING_STATUS',
      title: $t('equipment.equipment-mg.affectinstrumentstatus'),
      filterRender: {
        name: 'TableFilterInput',
      },
      editRender: {
        name: 'select',
        options: [
          { label: '是', value: 'Y' },
          { label: '否', value: 'N' },
        ],
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MAINSTATUS',
      title: $t('equipment.equipment-mg.mainStatus'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}

export function positionColumns(): VxeTableGridOptions<EquipmentMgApi.MetaDataEquipment>['columns'] {
  return [
    {
      align: 'center',
      field: 'SERVGRP',
      title: $t('equipment.equipment-mg.position'),
      filterRender: {
        name: 'TableFilterInput',
      },
      editRender: { name: 'input' },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'OWNER',
      title: $t('equipment.equipment-mg.isOwner'),
      filterRender: {},
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      filters: [{ data: '' }],
      sortable: true,
    },

    {
      align: 'center',
      slots: { default: 'action' },
      field: 'operation',
      fixed: 'right',
      title: $t('basic-static-tables.operation'),
      width: 180,
    },
  ];
}

export function eventRecordColumns(): VxeTableGridOptions<EquipmentMgApi.MetaDataEquipment>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('equipment.equipment-mg.recordID'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MAINTENANCETYPE',
      title: $t('equipment.equipment-mg.maintenancetype'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      formatter: 'formatTranslate',
      sortable: true,
    },
    {
      align: 'center',
      field: 'MAINTENANCEEVENT',
      title: $t('equipment.equipment-mg.maintenanceEvent'),
      filterRender: {
        name: 'TableFilterInput',
      },
      formatter: 'formatTranslate',
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'STATUS',
      title: $t('equipment.equipment-mg.status'),
      filterRender: {
        name: 'TableFilterInput',
      },
      formatter: 'formatTranslate',

      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'OPENDATE',
      title: $t('equipment.equipment-mg.opendate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },

    {
      align: 'center',
      field: 'OPENREASON',
      title: $t('equipment.equipment-mg.openReason'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },

    {
      align: 'center',
      field: 'OPENEDBY',
      title: $t('equipment.equipment-mg.openBy'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MAINTDATE',
      title: $t('equipment.equipment-mg.maintDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'MAINTBY',
      title: $t('equipment.equipment-mg.maintBy'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'REMARKS',
      title: $t('equipment.equipment-mg.remarks'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },

    {
      align: 'center',
      field: 'ACTION_TAKEN',
      title: $t('equipment.equipment-mg.actionTaken'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ELN_ID',
      title: $t('equipment.equipment-mg.ElNID'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ELN_ORIGREC',
      title: $t('equipment.equipment-mg.ELN_ORIGREC'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}

export function certificateColumns(): VxeTableGridOptions<EquipmentMgApi.MetaDataEquipment>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'FILENAME',
      title: $t('equipment.equipment-mg.certificateName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: { name: 'input' },
      sortable: true,
    },
    {
      align: 'center',
      field: 'DESCRIPTION',
      title: $t('equipment.equipment-mg.certificateDescription'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: { name: 'input' },
      sortable: true,
    },
  ];
}

export function analysisColumns(): VxeTableGridOptions<EquipmentMgApi.MetaDataEquipment>['columns'] {
  return [
    {
      align: 'center',
      field: 'ANALYTE',
      title: $t('equipment.equipment-mg.analysisItem'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: { name: 'input' },
      sortable: true,
    },
    {
      align: 'center',
      field: 'INSTANALYTE',
      title: $t('equipment.equipment-mg.controlName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: { name: 'input' },
      sortable: true,
    },
  ];
}

export function logColumns(): VxeTableGridOptions<EquipmentMgApi.MetaDataEquipment>['columns'] {
  return [
    {
      align: 'center',
      field: 'TESTCODE',
      title: $t('equipment.equipment-mg.inspectionCode'),
      filterRender: {
        name: 'TableFilterInput',
      },
      width: '120',
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'TESTNO',
      title: $t('equipment.equipment-mg.inspectionName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
      width: '120',
    },
    {
      align: 'center',
      field: 'ANALYTE',
      title: $t('equipment.equipment-mg.analysisItem'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
      width: '120',
    },
    {
      align: 'center',
      field: 'SYNONYM',
      title: $t('equipment.equipment-mg.analysisItemSynonyms'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
      width: '120',
    },
    {
      align: 'center',
      field: 'ELNSAMPID',
      title: $t('equipment.equipment-mg.sampleCollectionNumber'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
      width: '120',
    },
    {
      align: 'center',
      field: 'STARDOC_ID',
      title: $t('equipment.equipment-mg.documentId'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
      width: '120',
    },
    {
      align: 'center',
      field: 'ADDED_DT',
      title: $t('equipment.equipment-mg.creationTime'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
      width: '120',
    },
    {
      align: 'center',
      field: 'ADDED_BY',
      title: $t('equipment.equipment-mg.creator'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
      width: '120',
    },
    {
      align: 'center',
      field: 'COMMENTS',
      title: $t('equipment.equipment-mg.remarks'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
      width: '120',
    },
    {
      align: 'center',
      field: 'ORDNO',
      title: $t('equipment.equipment-mg.sampleNumber'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'EQID',
      title: $t('equipment.equipment-mg.equipmentId'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN1',
      title: $t('equipment.equipment-mg.absorbance'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN2',
      title: $t('equipment.equipment-mg.blank'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN3',
      title: $t('equipment.equipment-mg.dwellTime'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN4',
      title: $t('equipment.equipment-mg.correctedConcentration'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN5',
      title: $t('equipment.equipment-mg.peakArea'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN6',
      title: $t('equipment.equipment-mg.peakAreaPercentage'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN7',
      title: $t('equipment.equipment-mg.symmetryFactor'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN8',
      title: $t('equipment.equipment-mg.plateCount'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN9',
      title: $t('equipment.equipment-mg.resolution'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN10',
      title: $t('equipment.equipment-mg.signalNoise'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN11',
      title: $t('equipment.equipment-mg.signalToNoiseRatio'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN12',
      title: $t('equipment.equipment-mg.retentionTimeRSD'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN13',
      title: $t('equipment.equipment-mg.areaRSD'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN14',
      title: $t('equipment.equipment-mg.USPTailing'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN15',
      title: $t('equipment.equipment-mg.calculatedIonRatio'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN16',
      title: $t('equipment.equipment-mg.wavelength'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN17',
      title: $t('equipment.equipment-mg.peakHeight'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN18',
      title: $t('equipment.equipment-mg.intercept'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN19',
      title: $t('equipment.equipment-mg.slope'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN20',
      title: $t('equipment.equipment-mg.correlationCoefficient'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN21',
      title: $t('equipment.equipment-mg.scanCount'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN22',
      title: $t('equipment.equipment-mg.resolutionPower'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN23',
      title: $t('equipment.equipment-mg.absorbance2'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN24',
      title: $t('equipment.equipment-mg.absorbance3'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN25',
      title: $t('equipment.equipment-mg.diameter1'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN26',
      title: $t('equipment.equipment-mg.diameter2'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN27',
      title: $t('equipment.equipment-mg.sampleDetectionValue'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN28',
      title: $t('equipment.equipment-mg.SDValue'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN29',
      title: $t('equipment.equipment-mg.meanValue'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN30',
      title: $t('equipment.equipment-mg.crackLength2'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN31',
      title: $t('equipment.equipment-mg.crackLength3'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN32',
      title: $t('equipment.equipment-mg.negativeControl'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN33',
      title: $t('equipment.equipment-mg.positiveControl'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN34',
      title: $t('equipment.equipment-mg.linearEquation'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN35',
      title: $t('equipment.equipment-mg.standard1Concentration'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN36',
      title: $t('equipment.equipment-mg.standard2Concentration'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN37',
      title: $t('equipment.equipment-mg.standard3Concentration'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN38',
      title: $t('equipment.equipment-mg.standard4Concentration'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN39',
      title: $t('equipment.equipment-mg.standard5Concentration'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN40',
      title: $t('equipment.equipment-mg.standard6Concentration'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN41',
      title: $t('equipment.equipment-mg.standard1Absorbance'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN42',
      title: $t('equipment.equipment-mg.standard2Absorbance'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN43',
      title: $t('equipment.equipment-mg.standard3Absorbance'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN44',
      title: $t('equipment.equipment-mg.standard4Absorbance'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN45',
      title: $t('equipment.equipment-mg.standard5Absorbance'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN46',
      title: $t('equipment.equipment-mg.standard6Absorbance'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN47',
      title: $t('equipment.equipment-mg.standard1CycleNumber'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN48',
      title: $t('equipment.equipment-mg.standard2CycleNumber'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN49',
      title: $t('equipment.equipment-mg.standard3CycleNumber'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN50',
      title: $t('equipment.equipment-mg.processNegativeCycle1'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN51',
      title: $t('equipment.equipment-mg.processNegativeCycle2'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN52',
      title: $t('equipment.equipment-mg.processNegativeCycle3'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN53',
      title: $t('equipment.equipment-mg.sampleNegativeCycle1'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN54',
      title: $t('equipment.equipment-mg.sampleNegativeCycle2'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN555',
      title: $t('equipment.equipment-mg.sampleNegativeCycle3'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN56',
      title: $t('equipment.equipment-mg.standard1Diameter'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN57',
      title: $t('equipment.equipment-mg.standard2Diameter'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN58',
      title: $t('equipment.equipment-mg.standard3Diameter'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN59',
      title: $t('equipment.equipment-mg.standard4Diameter'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN60',
      title: $t('equipment.equipment-mg.standard5Diameter'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN61',
      title: $t('equipment.equipment-mg.standard6Diameter'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN62',
      title: $t('equipment.equipment-mg.standard7Diameter'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN63',
      title: $t('equipment.equipment-mg.standard8Diameter'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN64',
      title: $t('equipment.equipment-mg.standard9Diameter'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN65',
      title: $t('equipment.equipment-mg.standard10Diameter'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN66',
      title: $t('equipment.equipment-mg.standard11Diameter'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN67',
      title: $t('equipment.equipment-mg.standard12Diameter'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN68',
      title: $t('equipment.equipment-mg.standard13Diameter'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN69',
      title: $t('equipment.equipment-mg.standard14Diameter'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN70',
      title: $t('equipment.equipment-mg.standard15Diameter'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN71',
      title: $t('equipment.equipment-mg.standard16Diameter'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN72',
      title: $t('equipment.equipment-mg.standard17Diameter'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN73',
      title: $t('equipment.equipment-mg.standard18Diameter'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN74',
      title: $t('equipment.equipment-mg.standard19Diameter'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN75',
      title: $t('equipment.equipment-mg.standard20Diameter'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN76',
      title: $t('equipment.equipment-mg.TOCResult'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN77',
      title: $t('equipment.equipment-mg.TOCAverage'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN78',
      title: $t('equipment.equipment-mg.name'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN79',
      title: $t('equipment.equipment-mg.freezingPointName'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN80',
      title: $t('equipment.equipment-mg.number'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN81',
      title: $t('equipment.equipment-mg.ICAverage'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN82',
      title: $t('equipment.equipment-mg.TCAverage'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN83',
      title: $t('equipment.equipment-mg.CPMValue'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN84',
      title: $t('equipment.equipment-mg.CPMAverage'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN85',
      title: $t('equipment.equipment-mg.tailingFactor'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
    {
      align: 'center',
      field: 'RN86',
      title: $t('equipment.equipment-mg.temperatureRise'),
      filterRender: {
        name: 'FilterInput',
      },
      filters: [
        {
          data: '',
        },
      ],
      sortable: true,
    },
  ];
}

export function documentColumns(): VxeTableGridOptions<EquipmentMgApi.MetaDataEquipment>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'FILENAME',
      title: $t('equipment.equipment-mg.documentName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: { name: 'input' },
      sortable: true,
    },
    {
      align: 'center',
      field: 'DESCRIPTION',
      title: $t('equipment.equipment-mg.description'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: { name: 'input' },
      sortable: true,
    },
    {
      align: 'center',
      field: 'STARDOC_ID',
      title: $t('equipment.equipment-mg.attachmentID'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: { name: 'input' },
      sortable: true,
    },
  ];
}
export function usedRocordColumns(): VxeTableGridOptions<EquipmentMgApi.MetaDataEquipment>['columns'] {
  return [
    {
      align: 'center',
      field: 'DT_USAGE',
      title: $t('equipment.equipment-mg.usedDate'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: 'formatDate',
    },
    {
      align: 'center',
      field: 'EQSTARTDATE',
      title: $t('equipment.equipment-mg.deviceStartTime'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss') : '';
      },
      sortable: true,
    },
    {
      align: 'center',
      field: 'EQENDDATE',
      title: $t('equipment.equipment-mg.deviceStopTime'),
      filterRender: {
        name: 'TableFilterInput',
      },
      editRender: { name: 'input', attrs: { type: 'date' } },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },

    {
      align: 'center',
      field: 'REL_ORDERS',
      title: $t('equipment.equipment-mg.usedSample'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'REL_BATCHNO',
      title: $t('equipment.equipment-mg.checkID'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'REL_SPECIFICATION',
      title: $t('equipment.equipment-mg.checkSpec'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },

    {
      align: 'center',
      field: 'REL_TESTS',
      title: $t('equipment.equipment-mg.usedProject'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'INSPECTIONITEM',
      title: $t('equipment.equipment-mg.checkProject'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'USRNAM',
      title: $t('equipment.equipment-mg.usedPerson'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },

    {
      align: 'center',
      field: 'COMMENTS',
      title: $t('equipment.equipment-mg.remarks'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ELN_ID',
      title: $t('equipment.equipment-mg.ELNModelID'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ELN_ORIGREC',
      title: $t('equipment.equipment-mg.ELNRunID'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}

export function fileSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      fieldName: 'DCUMETHOD',
      label: $t('equipment.equipment-mg.sampleFile'),
      componentProps: {
        api: getDCUMethodApi,
        afterFetch: (data: Array<any>) => {
          const res = data.map((item: any) => ({
            label: item.SCRIPTNAME,
            value: item.SCRIPTID,
          }));
          return res;
        },
        class: 'w-full',
      },
    },
    {
      component: 'Input',
      fieldName: 'DCUFOLDER',
      label: $t('equipment.equipment-mg.fileDirectory'),
    },
    {
      component: 'ApiSelect',
      fieldName: 'DCUFILETYPE',
      label: $t('equipment.equipment-mg.sampleFile'),
      componentProps: {
        api: getDCUFileTypeApi,
        afterFetch: (data: Array<any>) => {
          const res = data.map((item: any) => ({
            label: item.SAMPS,
            value: item.SAMPS,
          }));
          return res;
        },
        class: 'w-full',
      },
    },
  ];
}

export function restSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'REST_BASE_URL',
      label: $t('equipment.equipment-mg.baseUrl'),
    },
    {
      component: 'ApiSelect',
      fieldName: 'resultRequestScript',
      label: $t('equipment.equipment-mg.resultRequestScript'),
      componentProps: {
        api: getRESTScriptsApi,
        afterFetch: (data: Array<any>) => {
          const res = data.map((item: any) => ({
            label: item.SCRIPTNAME,
            value: item.SCRIPTNAME,
          }));
          return res;
        },
        class: 'w-full',
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'calibrationRequestScript',
      label: $t('equipment.equipment-mg.calibrationRequestScript'),
      componentProps: {
        api: getRESTScriptsApi,
        afterFetch: (data: Array<any>) => {
          const res = data.map((item: any) => ({
            label: item.SCRIPTNAME,
            value: item.SCRIPTNAME,
          }));
          return res;
        },
        class: 'w-full',
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'measurementTypeFilter',
      label: $t('equipment.equipment-mg.measurementTypeFilter'),
      componentProps: {
        api: getDsGetMeasureTypesApi,
        afterFetch: (data: Array<any>) => {
          const res = data.map((item: any) => ({
            label: item.DisplayMember,
            value: item.ValueMember,
          }));
          return res;
        },
        class: 'w-full',
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'unitFilter',
      label: $t('equipment.equipment-mg.unitFilter'),
      componentProps: {
        api: getDsGetUnitsApi,
        afterFetch: (data: Array<any>) => {
          const res = data.map((item: any) => ({
            label: item.DISPLAYMEMBER,
            value: item.VALUEMEMBER,
          }));
          return res;
        },
        class: 'w-full',
      },
    },
  ];
}

export function serialSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Select',
      fieldName: 'BAUDRATE',
      label: $t('equipment.equipment-mg.baudRate'),
      componentProps: {
        class: 'w-full',
        allowClear: true,
        filterOption: true,
        options: [
          { label: '110', value: '110' },
          { label: '300', value: '300' },
          { label: '1200', value: '1200' },
          { label: '2400', value: '2400' },
          { label: '4800', value: '4800' },
          { label: '9600', value: '9600' },
          { label: '19200', value: '19200' },
          { label: '38400', value: '38400' },
          { label: '57600', value: '57600' },
          { label: '115200', value: '115200' },
          { label: '230400', value: '230400' },
          { label: '460800', value: '460800' },
          { label: '921600', value: '921600' },
        ],
        placeholder: '请选择',
        showSearch: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'STOPBITS',
      label: $t('equipment.equipment-mg.stopBit'),
      componentProps: {
        class: 'w-full',
        allowClear: true,
        filterOption: true,
        options: [
          { label: '1', value: '1' },
          { label: '2', value: '2' },
        ],
        placeholder: '请选择',
        showSearch: true,
      },
    },
    {
      component: 'Checkbox',
      fieldName: 'RTS',
    },
    {
      component: 'Select',
      fieldName: 'DATABITS',
      label: $t('equipment.equipment-mg.databits'),
      componentProps: {
        class: 'w-full',
        allowClear: true,
        filterOption: true,
        options: [
          { label: '7', value: '7' },
          { label: '8', value: '8' },
        ],
        placeholder: '请选择',
        showSearch: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'TERMINATORSTRING',
      label: $t('equipment.equipment-mg.stop'),
    },
    {
      component: 'Checkbox',
      fieldName: 'DTR',
    },
    {
      component: 'Select',
      fieldName: 'PARITY',
      label: $t('equipment.equipment-mg.parityCheck'),
      componentProps: {
        class: 'w-full',
        allowClear: true,
        filterOption: true,
        options: [
          { label: 'Even', value: 'Even' },
          { label: 'Odd', value: 'Odd' },
          { label: 'None', value: 'None' },
        ],
        placeholder: '请选择',
        showSearch: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'PORT',
      label: $t('equipment.equipment-mg.COMPort'),
      componentProps: {
        class: 'w-full',
        allowClear: true,
        filterOption: true,
        options: [
          { label: 'COM1', value: 'COM1' },
          { label: 'COM2', value: 'COM2' },
          { label: 'COM3', value: 'COM3' },
          { label: 'COM4', value: 'COM4' },
        ],
        placeholder: '请选择',
        showSearch: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'SERIALPARSER',
      label: $t('equipment.equipment-mg.parsingMethod'),
    },
  ];
}

export function websocketSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'ipAddress',
      label: $t('equipment.equipment-mg.ipAddress'),
    },
    {
      component: 'Input',
      fieldName: 'port',
      label: $t('equipment.equipment-mg.port'),
    },
    {
      component: 'Input',
      fieldName: 'parseMethod',
      label: $t('equipment.equipment-mg.parseMethod'),
    },
  ];
}

export function tcpSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'IP_ADDRESS',
      label: $t('equipment.equipment-mg.IPAdress'),
    },
    {
      component: 'Input',
      fieldName: 'PORT',
      label: $t('equipment.equipment-mg.portNumber'),
    },
    {
      component: 'Input',
      fieldName: 'TERMINATORSTRING',
      label: $t('equipment.equipment-mg.terminator'),
    },
  ];
}
export function receiveDataSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'IP_ADDRESS',
      label: $t('equipment.equipment-mg.scriptTriggerCharacter'),
    },
    {
      component: 'Input',
      fieldName: 'PORT',
      label: $t('equipment.equipment-mg.analysisScript'),
    },
  ];
}

export function commandColumns(): VxeTableGridOptions<EquipmentMgApi.MetaDataEquipment>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'COMMANDNAME',
      title: $t('equipment.equipment-mg.commandName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      editRender: { name: 'input' },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'Command_Display',
      title: $t('equipment.equipment-mg.command'),
      filterRender: {
        name: 'TableFilterInput',
      },
      editRender: { name: 'input' },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'COMMAND',
      title: $t('equipment.equipment-mg.commandHexView'),
      filterRender: {
        name: 'TableFilterInput',
      },
      editRender: { name: 'input' },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}

export function commandModalSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'COMMANDNAME',
      label: $t('equipment.equipment-mg.commandName'),
      rules: 'required',
    },
    {
      component: 'Textarea',
      fieldName: 'Command_Display',
      label: $t('equipment.equipment-mg.command'),
      rules: 'required',
    },
  ];
}

export function deviceModalSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'equipId',
      label: $t('equipment.equipment-mg.equipmentId'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: getEquipmentTypeApi,
        afterFetch: (data: { items: any[]; total: number }) => {
          const res = data.items.map((item: any) => ({
            label: item.EQTYPES,
            value: item.EQTYPES,
          }));
          return res;
        },
        class: 'w-full',
      },
      fieldName: 'equipType',
      label: $t('equipment.equipment-mg.equipmentType'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      fieldName: 'sOwner',
      label: $t('equipment.equipment-mg.position'),
      rules: 'required',
      componentProps: {
        api: getPositionApi,
        afterFetch: (data: { items: any[]; total: number }) => {
          const res = data.items.map((item: any) => ({
            label: item.TEXT,
            value: item.VALUE,
          }));
          return res;
        },
        class: 'w-full',
      },
    },
  ];
}

export function maintainModalSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'addBtn',
      label: '',
    },
    {
      component: 'Textarea',
      fieldName: 'aEqIdList',
      label: $t('equipment.equipment-mg.deviceList'),
    },
    {
      component: 'Select',
      fieldName: 'sType',
      label: $t('equipment.equipment-mg.maintainType'),
      rules: 'required',
    },
    {
      component: 'Select',
      fieldName: 'sEvent',
      label: $t('equipment.equipment-mg.event'),
      rules: 'required',
      // componentProps: {
      //   class: 'w-full',
      //   allowClear: true,
      //   filterOption: true,
      //   options: [
      //     {
      //       label: '选项1',
      //       value: '1',
      //     },
      //     {
      //       label: '选项2',
      //       value: '2',
      //     },
      //   ],
      //   placeholder: '请选择',
      //   showSearch: true,
      // },
    },
    {
      component: 'Textarea',
      fieldName: 'reason',
      label: $t('equipment.equipment-mg.reason'),
      rules: 'required',
    },
  ];
}
export function reasonModalSchema(): VbenFormSchema[] {
  return [
    {
      component: 'DatePicker',
      fieldName: 'MAINTDATE',
      label: $t('equipment.equipment-mg.maintDate'),
      rules: 'required',
    },
    {
      component: 'Textarea',
      fieldName: 'OPENREASON',
      label: $t('equipment.equipment-mg.reason'),
      rules: 'required',
    },
    {
      component: 'Textarea',
      fieldName: 'ACTION_TAKEN',
      label: $t('equipment.equipment-mg.actionTaken'),
    },
  ];
}

export function stopDeviceModalSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Textarea',
      fieldName: 'OPENREASON',
      label: $t('equipment.equipment-mg.reason'),
      rules: 'required',
    },
  ];
}

export function searchConditionColumns(): VxeTableGridOptions<EquipmentMgApi.MetaDataEquipment>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'event',
      title: $t('equipment.equipment-mg.event'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'Not',
      title: $t('equipment.equipment-mg.not'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: {
        name: 'select',
        options: [
          { label: 'Y', value: 'Y' },
          { label: 'N', value: 'N' },
        ],
      },

      sortable: true,
    },
    {
      align: 'center',
      field: 'operateSymbol',
      title: $t('equipment.equipment-mg.operateSymbol'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: {
        name: 'select',
        options: [
          { value: '=', label: '=' },
          { value: '<', label: '<' },
          { value: '>', label: '>' },
          { value: '<=', label: '<=' },
          { value: '>=', label: '>=' },
          { value: '<>', label: '<>' },
          { value: 'IN', label: 'IN' },
          { value: 'BETWEEN', label: 'BETWEEN' },
          { value: 'IS NULL', label: 'IS NULL' },
          { value: 'STARTS WITH', label: 'STARTS WITH' },
          { value: 'CONTAINS', label: 'CONTAINS' },
          { value: 'ENDS WITH', label: 'ENDS WITH' },
          { value: 'SOUNDEX', label: 'SOUNDEX' },
        ],
      },

      sortable: true,
    },
    {
      align: 'center',
      field: 'value',
      title: $t('equipment.equipment-mg.value'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: { name: 'input' },
      sortable: true,
    },
    {
      align: 'center',
      field: 'logic',
      title: $t('equipment.equipment-mg.logic'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: {
        name: 'select',
        options: [
          { label: 'Amd', value: 'And' },
          { label: 'Or', value: 'Or' },
        ],
      },

      sortable: true,
    },
  ];
}

export function comModalSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Select',
      fieldName: 'PORT',
      label: $t('equipment.equipment-mg.selectCOMPort'),
      rules: 'required',
      componentProps: {
        class: 'w-full',
        allowClear: true,
        filterOption: true,
        options: [
          { label: 'COM1', value: 'COM1' },
          { label: 'COM2', value: 'COM2' },
          { label: 'COM3', value: 'COM3' },
          { label: 'COM4', value: 'COM4' },
        ],
        placeholder: '请选择',
        showSearch: true,
      },
    },
  ];
}

export function addUseRecordSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'EQID',
      label: $t('equipment.equipment-mg.equipmentId'),
      rules: 'required',
      disabled: true,
    },
    {
      component: 'ApiSelect',
      fieldName: 'USRNAM',
      label: $t('equipment.equipment-mg.usedPerson'),
      rules: 'required',
      componentProps: {
        api: getUserApi,
        labelField: 'TEXT',
        valueField: 'VALUE',
        class: 'w-full',
      },
    },
    {
      component: 'DatePicker',
      fieldName: 'DT_USAGE',
      label: $t('equipment.equipment-mg.usedDate'),
      componentProps: {
        class: 'w-full',
      },
      rules: 'required',
    },
    {
      component: 'DatePicker',
      fieldName: 'EQSTARTDATE',
      label: $t('equipment.equipment-mg.deviceStartTime'),
      componentProps: {
        class: 'w-full',
      },
    },
    {
      component: 'DatePicker',
      fieldName: 'EQENDDATE',
      label: $t('equipment.equipment-mg.deviceStopTime'),
      componentProps: {
        class: 'w-full',
      },
    },
    {
      component: 'Input',
      fieldName: 'REL_ORDERS',
      label: $t('equipment.equipment-mg.usedSample'),
    },

    {
      component: 'Input',
      fieldName: 'REL_BATCHNO',
      label: $t('equipment.equipment-mg.checkID'),
    },
    {
      component: 'Input',
      fieldName: 'REL_SPECIFICATION',
      label: $t('equipment.equipment-mg.checkSpec'),
    },
    {
      component: 'Input',
      fieldName: 'REL_TESTS',
      label: $t('equipment.equipment-mg.usedProject'),
    },

    {
      component: 'Input',
      fieldName: 'INSPECTIONITEM',
      label: $t('equipment.equipment-mg.checkProject'),
    },
    {
      component: 'Textarea',
      fieldName: 'COMMENTS',
      label: $t('equipment.equipment-mg.remarks'),
    },
  ];
}
export function eventFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        // placeholder: 'Please enter code',
      },
      fieldName: 'CATEGORY',
      label: $t('basic-static-tables.client-categories.category'),
    },
  ];
}
