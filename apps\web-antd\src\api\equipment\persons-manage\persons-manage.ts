import { callServer, getDataSet, getDataSetNoPage } from '#/api/core/witlab';

export namespace FormApi {
  export interface Form {
    [key: string]: any;
    ORIGREC: number;
    NAME: string;
    EFFECT: string;
    TYPE: string;
    SORTER: number;
  }
  export interface RowType {
    [key: string]: any;
  }
}

async function getPersonnelNewApi(params: Array<any>) {
  return getDataSet('ANALYST_CERTIFICATION.DS_PERSONNEL_NEW', params);
}
async function getPersonnelRemindApi(params: Array<any>) {
  return getDataSet('ANALYST_CERTIFICATION.DS_Personnel_Remind', params);
}
async function updateMethodApi(params: Array<any>) {
  return await callServer('ANALYST_CERTIFICATION.UPDATE_METHODS', params);
}
async function getDeptListApi() {
  return getDataSetNoPage('ANALYST_CERTIFICATION.ds_CboDept', []);
}
async function getPersonnelInfoApi(params: Array<any>) {
  return getDataSetNoPage('ANALYST_CERTIFICATION.DS_PERSONNEL_INFO', params);
}
async function getListCertificationsApi(params: Array<any>) {
  return getDataSet('ANALYST_CERTIFICATION.ListCertifications', params);
}
async function getTestDocApi(params: Array<any>) {
  return getDataSet('ANALYST_CERTIFICATION.DS_TestDoc', params);
}
async function getEqcertApi(params: Array<any>) {
  return getDataSet('ANALYST_CERTIFICATION.DS_EQCERT', params);
}
async function getEquipDocApi(params: Array<any>) {
  return getDataSet('ANALYST_CERTIFICATION.DS_EquipDoc', params);
}
async function getTrainApi(params: Array<any>) {
  return getDataSet('ANALYST_CERTIFICATION.DS_Train', params);
}
async function getCourseMethodApi(params: Array<any>) {
  return getDataSet('ANALYST_CERTIFICATION.DS_CourseMethod', params);
}
async function getCredentialsApi(params: Array<any>) {
  return getDataSet('ANALYST_CERTIFICATION.DS_Credentials', params);
}
async function getCredentialsDocApi(params: Array<any>) {
  return getDataSet('ANALYST_CERTIFICATION.DS_CredentialsDoc', params);
}
async function getSearchDataApi(params: Array<any>) {
  return getDataSet('ANALYST_CERTIFICATION.SearchData', params);
}
async function getUsersListApi(params: Array<any>) {
  return getDataSetNoPage('ANALYST_CERTIFICATION.mcUsers', params);
}
async function updateUserToPersonApi(params: Array<any>) {
  return await callServer('ANALYST_CERTIFICATION.updateUserToPerson', params);
}
async function batchUpdateCertApi(params: Array<any>) {
  return await callServer('ANALYST_CERTIFICATION.batchUpdateCert', params);
}
async function getServgrpApi(params: Array<any>) {
  return getDataSetNoPage('ANALYST_CERTIFICATION.DS_SERVGRP', params);
}
async function getTestCategoryApi() {
  return getDataSetNoPage('ANALYST_CERTIFICATION.dsTestCategory', []);
}
async function getCertifiMethodApi(params: Array<any>) {
  return getDataSetNoPage('ANALYST_CERTIFICATION.DS_CertifiMethod', params);
}

async function chkMethodCertApi(params: Array<any>) {
  return await callServer('ANALYST_CERTIFICATION.ChkMethodCert', params);
}
async function addMethodCertApi(params: Array<any>) {
  return await callServer('ANALYST_CERTIFICATION.AddMethodCert', params);
}
async function deleteRowsApi(params: Array<any>) {
  return await callServer('ANALYST_CERTIFICATION.DeleteRows', params);
}
async function getTestUsrnamApi(params: Array<any>) {
  return await callServer('ANALYST_CERTIFICATION.GetTestUsrnam', params);
}
async function getDataByIdApi(params: Array<any>) {
  return getDataSetNoPage('CommonApp.CBO_ListDept', params);
}
async function getServgrpByDeptApi(params: Array<any>) {
  return getDataSetNoPage('CommonApp.GETSERVGRPBYDEPT', params);
}
async function getCommonMcUsersApi(params: Array<any>) {
  return getDataSetNoPage('CommonApp.mcUsers', params);
}
async function addOtherMethodCertApi(params: Array<any>) {
  return await callServer('ANALYST_CERTIFICATION.AddOtherMethodCert', params);
}
async function getCertifyEQApi(params: Array<any>) {
  return getDataSetNoPage('ANALYST_CERTIFICATION.DS_CertifyEQ', params);
}
async function getEQtypeApi(params: Array<any>) {
  return getDataSetNoPage('ANALYST_CERTIFICATION.CB_EQtype', params);
}
async function chkEQCertApi(params: Array<any>) {
  return await callServer('ANALYST_CERTIFICATION.ChkEQCert', params);
}
async function addEQCertApi(params: Array<any>) {
  return await callServer('ANALYST_CERTIFICATION.AddEQCert', params);
}
async function addOtherEQCertApi(params: Array<any>) {
  return await callServer('ANALYST_CERTIFICATION.AddOtherEQCert', params);
}
async function getCourseNameApi() {
  return await getDataSetNoPage('ANALYST_CERTIFICATION.DS_GetCourseName', []);
}
async function getScoreApi() {
  return await getDataSetNoPage('ANALYST_CERTIFICATION.DS_GetScore', []);
}
async function addTrainApi(params: Array<any>) {
  return await callServer('ANALYST_CERTIFICATION.ADD_TRAINNING', params);
}
async function getTrainPlanUsernamApi(params: Array<any>) {
  return await callServer('ANALYST_CERTIFICATION.GetTrainPlanUsernam', params);
}
async function addTraningOthersApi(params: Array<any>) {
  return await callServer('ANALYST_CERTIFICATION.ADD_TRAINNING_OTHERS', params);
}
async function addCredentialsApi(params: Array<any>) {
  return await callServer('ANALYST_CERTIFICATION.AddCredentials', params);
}
async function updateProviderApi(params: Array<any>) {
  return await callServer('Runtime_Support.WS_UPDATEPROVIDER', params);
}
async function deleteDocumentationApi(params: Array<any>) {
  return await callServer('ANALYST_CERTIFICATION.DeleteDocumentation', params);
}

async function deleteCredentialsApi(params: Array<any>) {
  return await callServer('ANALYST_CERTIFICATION.deleteCredentials', params);
}
async function deleteRowsapi(params: Array<any>) {
  return await callServer('ANALYST_CERTIFICATION.DeleteRows', params);
}

export {
  getDeptListApi,
  getPersonnelNewApi,
  getPersonnelRemindApi,
  updateMethodApi,
  getPersonnelInfoApi,
  getListCertificationsApi,
  getTestDocApi,
  getEqcertApi,
  getEquipDocApi,
  getTrainApi,
  getCourseMethodApi,
  getCredentialsApi,
  getCredentialsDocApi,
  getSearchDataApi,
  getUsersListApi,
  updateUserToPersonApi,
  batchUpdateCertApi,
  getServgrpApi,
  getTestCategoryApi,
  getCertifiMethodApi,
  chkMethodCertApi,
  addMethodCertApi,
  deleteRowsApi,
  getTestUsrnamApi,
  getDataByIdApi,
  getServgrpByDeptApi,
  getCommonMcUsersApi,
  addOtherMethodCertApi,
  getCertifyEQApi,
  getEQtypeApi,
  chkEQCertApi,
  addEQCertApi,
  addOtherEQCertApi,
  getCourseNameApi,
  getScoreApi,
  addTrainApi,
  getTrainPlanUsernamApi,
  addTraningOthersApi,
  addCredentialsApi,
  updateProviderApi,
  deleteDocumentationApi,
  deleteCredentialsApi,
  deleteRowsapi,
};
