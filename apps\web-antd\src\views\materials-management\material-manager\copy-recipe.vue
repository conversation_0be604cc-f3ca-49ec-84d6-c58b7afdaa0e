<script lang="ts" setup>
import type { MaterialManagerApi } from '#/api/materials-management/material-manager';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { copyRecipe } from '#/api/materials-management/material-manager';
import { $t } from '#/locales';

const emit = defineEmits(['success']);

const formData = ref<MaterialManagerApi.Recipes>();

const recipeCode = ref<number>(0);

const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 提交函数
  handleSubmit: onSubmit,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'vertical',
  schema: [
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Input',
      // 字段名
      fieldName: 'RECIPENAME',
      // 界面显示的label
      label: $t('materials-management.material-manager.recipename'),
      rules: 'required',
    },
  ],
  // wrapperClass: 'grid-cols-1',
  showDefaultActions: false,
});

/* function resetForm() {
  formApi.resetForm();
  formApi.setValues(formData.value || {});
} */

const [Modal, modalApi] = useVbenModal({
  draggable: true,
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
    // modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData<MaterialManagerApi.Recipes>();
      if (data) {
        recipeCode.value = data.RECIPECODE;
        formData.value = data;
        formApi.setValues(formData.value);
      }
    }
  },
  title: '复制配方',
});

async function onSubmit() {
  try {
    message.loading({
      content: '正在提交中...',
      duration: 0,
      key: 'is-form-submitting',
    });
    modalApi.lock();

    const data = (await formApi.getValues()) as MaterialManagerApi.Recipes;
    data.RECIPECODE = recipeCode.value;
    // 调用添加分类 API
    await copyRecipe(data);
    emit('success');
    modalApi.close();
    message.success({
      content: '复制成功',
      duration: 2,
      key: 'is-form-submitting',
    });
  } catch (error) {
    message.error({
      content: `复制失败：${(error as Error).message}`,
      key: 'is-form-submitting',
    });
    modalApi.close();
  } finally {
    modalApi.unlock();
  }
}
</script>

<template>
  <Modal>
    <Form />
    <template #prepend-footer>
      <div class="flex-auto">
        <!--      <Button type="primary" danger @click="resetForm">
          {{ $t('common.reset') }}
        </Button> -->
      </div>
    </template>
  </Modal>
</template>
