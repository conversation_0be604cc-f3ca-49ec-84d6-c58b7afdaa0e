<script lang="ts" setup>
import type { SampleGroupsApi } from '#/api/business-static-tables/sample-groups';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { copySamplingReq } from '#/api/business-static-tables/sample-groups';
import { $t } from '#/locales';

const emit = defineEmits(['success']);

const formData = ref<SampleGroupsApi.SamplingRequirements>();

const sOrigrec = ref<number>(0);

const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 提交函数
  handleSubmit: onSubmit,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'vertical',
  schema: [
    {
      component: 'Input',
      fieldName: 'NUM',
      label: $t('business-static-tables.sampleGroups.num'),
      rules: 'required',
    },
  ],
  // wrapperClass: 'grid-cols-1',
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  draggable: true,
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
    // modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData<SampleGroupsApi.SamplingRequirements>();
      if (data) {
        sOrigrec.value = data.ORIGREC;
        formData.value = data;
        formApi.setValues(formData.value);
      }
    }
  },
  title: '复制取样要求',
});

async function onSubmit() {
  try {
    message.loading({
      content: '正在提交中...',
      duration: 0,
      key: 'is-form-submitting',
    });
    modalApi.lock();

    const data = await formApi.getValues();
    // 调用添加分类 API
    const num = data?.NUM;
    await copySamplingReq(sOrigrec.value, num);

    emit('success');
    modalApi.close();
    message.success({
      content: '操作成功',
      duration: 2,
      key: 'is-form-submitting',
    });
  } catch (error) {
    message.error({
      content: `操作失败：${(error as Error).message}`,
      key: 'is-form-submitting',
    });
    modalApi.close();
  } finally {
    modalApi.unlock();
  }
}
</script>

<template>
  <Modal>
    <Form />
    <template #prepend-footer>
      <div class="flex-auto">
        <!--      <Button type="primary" danger @click="resetForm">
          {{ $t('common.reset') }}
        </Button> -->
      </div>
    </template>
  </Modal>
</template>
