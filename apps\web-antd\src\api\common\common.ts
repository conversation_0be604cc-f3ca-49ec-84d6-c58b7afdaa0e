import { getDataSet } from '#/api/core/witlab';

export namespace MaterialManagerApi {
  export interface MaterialManager {
    ORIGREC: number;
    TEXT: string;
    VALUE: string;
  }
}

/**
 * 根据参数获取选择内容数据
 */
async function getLookupValues(params: string) {
  return getDataSet('Common.LookupValues', [params]);
}

/**
 * 根据参数获取选择内容数据
 */
async function getLookupValuesSimple(params: string) {
  return getDataSet('GenericMetadata.LookupValuesSimple', [params]);
}

export { getLookupValues, getLookupValuesSimple };
