<script setup lang="ts">
import type { SampleGroupsApi } from '#/api/business-static-tables/sample-groups';

import { ref, watch } from 'vue';

import { Button, Space } from 'ant-design-vue';

import {
  GetMethodEquiType,
  updateMethodEquType,
} from '#/api/business-static-tables/sample-groups';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import {
  useMethodsRelSpTestsEqTypeColumns,
  useMethodsRelSpTestsEqTypeFilterSchema,
} from './sample-groups-data';
import TransferModal from './select-eqtypes.vue';

const props = defineProps<{
  currentTestRow: null | SampleGroupsApi.MethodsRelSpTests;
}>();

// import SampleReqDetail from './sample-requirement-detail.vue';

const transferModalVisible = ref(false);
const transferModalRef = ref();
const eqTypes = ref<string>('');
const aEqTypes = ref<string[]>([]);

watch(
  () => props.currentTestRow,
  (_val) => {
    onRefresh();
  },
);
const colums = useMethodsRelSpTestsEqTypeColumns();
const filterSchema = useMethodsRelSpTestsEqTypeFilterSchema();
const queryData = async () => {
  if (!props.currentTestRow) return [];
  return GetMethodEquiType(props.currentTestRow.ORIGREC);
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};

const { Grid, gridApi } =
  useLimsGridsConfig<SampleGroupsApi.MethodsRelSpTestsEqType>(
    colums,
    filterSchema,
    queryData,
    girdOption,
  );

function onRefresh() {
  gridApi.query();
}

function openTransferModal() {
  const allData = gridApi.grid?.getData(); // 获取表格所有行数据
  if (allData.length === 0) return;

  aEqTypes.value = allData.map((item) => item.EQTYPE) as string[];
  eqTypes.value = allData.map((item) => item.EQTYPE || '').join(',');

  transferModalRef.value?.open();
}

async function handleTransferSubmit(data: {
  selectedItems: any[];
  selectedKeys: string[];
}) {
  if (props.currentTestRow === null) return;
  await updateMethodEquType(
    props.currentTestRow.SP_CODE,
    props.currentTestRow.TESTCODE,
    props.currentTestRow.METHOD,
    props.currentTestRow.DEPT,
    props.currentTestRow.PROFILE,
    props.currentTestRow.SERVGRP,
    props.currentTestRow.DRAWNO,
    data.selectedKeys,
  );
  onRefresh();
}
</script>

<template>
  <FormModal @success="onRefresh" />
  <TransferModal
    ref="transferModalRef"
    :current-test-row="props.currentTestRow"
    :eqtypes="eqTypes"
    :a-eq-types="aEqTypes"
    @submit="handleTransferSubmit"
    @cancel="transferModalVisible = false"
  />
  <Grid>
    <template #toolbar-actions>
      <Space :size="[4, 0]" wrap>
        <Button type="primary" @click="openTransferModal">
          {{ $t('business-static-tables.edit') }}
        </Button>
      </Space>
    </template>
  </Grid>
</template>
