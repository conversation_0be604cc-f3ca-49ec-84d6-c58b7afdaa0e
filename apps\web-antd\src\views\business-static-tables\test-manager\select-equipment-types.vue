<script lang="ts" setup>
import type { Recordable } from '@vben/types';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Transfer } from 'ant-design-vue';

import {
  $editOtherEquipmentListApi,
  $mcEquipTypesApi,
} from '#/api/business-static-tables';
import { $t } from '#/locales';

interface FormArgs {
  testCode: string;
  method: string;
}
const emit = defineEmits(['success']);
const eqTypeList = ref<Recordable<string>[]>([]);
const targetKeys = ref<string[]>([]);
const formArgs = ref<FormArgs>();
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    if (!formArgs.value) return;
    await $editOtherEquipmentListApi({
      testCode: formArgs.value?.testCode,
      method: formArgs.value?.method,
      selectEqTypes: targetKeys.value,
    });
    emit('success');
    modalApi.close();
  },
  onCancel: () => {
    modalApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      // 当模态框打开时，重新获取数据
      const data = modalApi.getData();
      if (data) {
        formArgs.value = data.methodRelatedData;
        getEqTypeList();
      }
    }
  },
  title: $t('business-static-tables.testManager.selectEquipmentTypes'),
});

const getEqTypeList = async () => {
  const eData = formArgs.value ? await $mcEquipTypesApi(formArgs.value) : [];
  const selectKeys = eData
    .filter((item) => item.selected === 'true')
    .map((item) => item.EQTYPE);
  eqTypeList.value = eData;
  targetKeys.value = selectKeys;
};
</script>
<template>
  <Modal>
    <div
      class="flex h-full w-full items-center justify-center"
      style="width: 100%; min-height: 500px; padding: 20px"
    >
      <Transfer
        v-model:target-keys="targetKeys"
        :data-source="eqTypeList"
        show-search
        :list-style="{
          width: '100%',
          height: '500px',
        }"
        :render="(item) => `${item.EQTYPE}`"
        style="width: 100%"
        :row-key="(record) => record.EQTYPE"
      >
        <template #notFoundContent>
          <span>没数据</span>
        </template>
      </Transfer>
    </div>
  </Modal>
</template>
