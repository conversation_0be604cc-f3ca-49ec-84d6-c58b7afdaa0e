<script lang="ts" setup>
import type { TestManagerApi } from '#/api/business-static-tables/test-manager';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import {
  $deleteOutSourceLabApi,
  $getOutSourceLabsApi,
} from '#/api/business-static-tables/test-manager';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';
import { confirm } from '#/utils/utils';

import AddOutSourceLabsForm from './add-out-source-labs-form.vue';
import { useOutSourceLabsColumns } from './data';

interface FormArgs {
  Method: string;
  TestCode: number;
  TestNo: string;
}
const formArgs = ref<FormArgs>();

const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    // 确认逻辑
    modalApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      // 当模态框打开时，重新获取数据
      const data = modalApi.getData<FormArgs>();
      if (data) {
        formArgs.value = data;
      }
    }
  },
  title: $t('business-static-tables.testManager.outSourceLabs'),
});

const [AddModal, addModalApi] = useVbenModal({
  connectedComponent: AddOutSourceLabsForm,
  destroyOnClose: true,
});

const getOutSourceLabList = async () => {
  if (!formArgs.value) return [];
  return await $getOutSourceLabsApi({
    method: formArgs.value.Method,
    testCode: formArgs.value.TestCode.toString(),
  });
};

const colums = useOutSourceLabsColumns();
const queryData = async () => {
  if (!formArgs.value) return [];
  return await getOutSourceLabList();
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};
const { Grid, gridApi } = useLimsGridsConfig<TestManagerApi.OutSourceLabs>(
  colums,
  [],
  queryData,
  girdOption,
);
function onCreate() {
  addModalApi
    .setData({
      methodRelatedData: {
        Method: formArgs.value?.Method,
        TestCode: formArgs.value?.TestCode,
      },
    })
    .open();
}
async function onDelete() {
  const checkRecords: TestManagerApi.OutSourceLabs[] =
    gridApi.grid?.getCheckboxRecords();
  if (checkRecords.length === 0) {
    message.warning($t('commons.selectOne'));
    return;
  }
  await confirm($t('commons.deleteConfirm'), $t('commons.deleteConfirmTitle'));
  const origrecs = checkRecords.map((item) => item.ORIGREC);
  const strOrigrec = `(${origrecs.toString()})`;
  // 删除逻辑
  await $deleteOutSourceLabApi({ strOrigrec });
  message.success($t('commons.deleteSuccess'));
  onRefresh();
}

function onRefresh() {
  gridApi.query();
}
</script>

<template>
  <Modal>
    <AddModal @success="onRefresh" />
    <div class="modal-content-full mb-4">
      <Grid>
        <template #toolbar-actions>
          <Space :size="[4, 0]" wrap>
            <Button type="primary" @click="onCreate">
              {{ $t('ui.actionTitle.create') }}
            </Button>
            <Button type="primary" danger @click="onDelete">
              {{ $t('ui.actionTitle.delete') }}
            </Button>
          </Space>
        </template>
        <!-- <template #action="{ row }">
          <template v-if="hasEditStatus(row)">
            <Button type="link" @click="saveRowEvent(row)">
              {{ $t('commons.save') }}
            </Button>
            <Button type="link" @click="cancelRowEvent(row)">
              {{ $t('commons.cancel') }}
            </Button>
          </template>
          <template v-else>
            <Button type="link" @click="editRowEvent(row)">
              {{ $t('commons.edit') }}
            </Button>
          </template>
        </template> -->
      </Grid>
    </div>
  </Modal>
</template>

<style scoped>
.modal-content-full {
  display: flex;
  flex-direction: column;
  height: 100%;
}
</style>
