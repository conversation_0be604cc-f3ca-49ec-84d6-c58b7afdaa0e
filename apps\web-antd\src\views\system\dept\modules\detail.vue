<script lang="ts" setup>
import type { SystemDeptApi } from '#/api';

import { ref } from 'vue';

import { TabPane, Tabs } from 'ant-design-vue';

import Building from './building.vue';
import Plant from './plant.vue';
import ServiceGroup from './service-group.vue';

const props = defineProps<{
  dept?: SystemDeptApi.SystemDept;
}>();

const activeKey = ref<string>('serviceGroups');

// watch(
//   () => props.dept?.code,
//   (currentCode, oldCode) => {
//     if (currentCode !== oldCode) {
//       activeKey.value = 'serviceGroups';
//     }
//   },
//   {
//     immediate: true,
//     deep: true,
//   },
// );
</script>

<template>
  <Tabs
    v-model:active-key="activeKey"
    class="flex h-full flex-col [&_.ant-tabs-content-holder]:flex-1 [&_.ant-tabs-content]:h-full [&_.ant-tabs-tabpane]:h-full"
  >
    <TabPane key="serviceGroups" :tab="$t('system.dept.detail.serviceGroups')">
      <ServiceGroup class="h-full" :dept-code="props.dept?.code" />
    </TabPane>
    <TabPane key="plants" :tab="$t('system.dept.detail.plants')">
      <Plant class="h-full" :dept-code="props.dept?.code" />
    </TabPane>
    <TabPane key="buildings" :tab="$t('system.dept.detail.buildings')">
      <Building class="h-full" :dept-code="props.dept?.code" />
    </TabPane>
  </Tabs>
</template>
