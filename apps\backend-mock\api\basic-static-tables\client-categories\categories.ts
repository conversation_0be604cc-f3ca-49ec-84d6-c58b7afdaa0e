import { faker } from '@faker-js/faker';
import { usePageResponseSuccess } from '~/utils/response';

function generateMockDataList(count: number) {
  const dataList = [];

  for (let i = 0; i < count; i++) {
    const dataItem: Record<string, any> = {
      category: faker.commerce.product(),
    };

    dataList.push(dataItem);
  }

  return dataList;
}

const mockData = generateMockDataList(100);

export default eventHandler(async (event) => {
  // const userinfo = verifyAccessToken(event);
  // if (!userinfo) {
  //   return unAuthorizedResponse(event);
  // }

  const { page = 1, pageSize = 20, category } = getQuery(event);
  let listData = structuredClone(mockData);
  if (category) {
    listData = listData.filter((item) =>
      item.category.toLowerCase().includes(String(category).toLowerCase()),
    );
  }
  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
