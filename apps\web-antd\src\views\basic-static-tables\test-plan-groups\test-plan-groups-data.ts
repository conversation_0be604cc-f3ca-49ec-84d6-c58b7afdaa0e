import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { TestPlanGroupsApi } from '#/api/basic-static-tables/test-plan-groups';

import { $t } from '#/locales';

export function useTestPlanGroupsColumns(): VxeTableGridOptions<TestPlanGroupsApi.TestPlanGroup>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'origrec',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'PRODGROUP',
      title: $t('basic-static-tables.test-plan-groups.prodgroup'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'PRODGRPDESC',
      title: $t('basic-static-tables.test-plan-groups.prodgrpdesc'),
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'PRODTYPE',
      title: $t('basic-static-tables.test-plan-groups.prodtype'),
      visible: false,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('basic-static-tables.operation'),
      width: 210,
    },
  ];
}

export function useTestPlanGroupsFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        // placeholder: 'Please enter code',
      },
      fieldName: 'PRODGROUP',
      label: $t('basic-static-tables.test-plan-groups.prodgroup'),
    },
  ];
}

export function useDeptFilterSchema(): VbenFormSchema[] {
  return [];
}

export function useDeptColumns(): VxeTableGridOptions<TestPlanGroupsApi.TestPlanGroup>['columns'] {
  return [
    {
      align: 'center',
      field: 'origrec',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'DEPT',
      title: $t('basic-static-tables.dept'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}
