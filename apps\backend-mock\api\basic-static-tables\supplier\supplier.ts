import { faker } from '@faker-js/faker';
import { usePageResponseSuccess } from '~/utils/response';

function generateMockDataList(count: number) {
  const dataList = [];

  for (let i = 0; i < count; i++) {
    const dataItem: Record<string, any> = {
      ORIGREC: faker.number.int({ min: 1, max: 1000 }),
      SUPTYPE: faker.commerce.product(),
      SUPPCODE: faker.commerce.product(),
      SUPPNAM: faker.commerce.productMaterial(),
      SUPPADD: faker.commerce.productDescription(),
      SUPPADD_A: faker.commerce.productDescription(),
      SUPPCITY: faker.commerce.product(),
      SUPPSTNAM: faker.commerce.product(),
      SUPPST: faker.commerce.product(),
      SUPZIP: faker.commerce.product(),
      COUNTRYNAME: faker.commerce.product(),
      PRIMARYPHONE: faker.commerce.product(),
      PRIMARYFAX: faker.commerce.product(),
      DEFAULTCONTACT: faker.commerce.product(),
      EMAIL: faker.internet.email(),
      URL: faker.internet.url(),
      COMMENTS: faker.commerce.product(),
      CITY_CODE: faker.commerce.product(),
    };

    dataList.push(dataItem);
  }

  return dataList;
}

const mockData = generateMockDataList(100);

export default eventHandler(async (event) => {
  const { page, pageSize, sortBy, sortOrder } = getQuery(event);
  const listData = structuredClone(mockData);
  if (sortBy && Reflect.has(listData[0], sortBy as string)) {
    listData.sort((a, b) => {
      if (sortOrder === 'asc') {
        return a[sortBy as string] > b[sortBy as string] ? 1 : -1;
      } else {
        return a[sortBy as string] < b[sortBy as string] ? 1 : -1;
      }
    });
  }
  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
