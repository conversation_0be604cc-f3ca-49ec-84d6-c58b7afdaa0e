<script lang="ts" setup>
import { computed } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { $addHolidayApi } from '#/api/basic-static-tables/holidays';
import { $t } from '#/locales';

import { useCopyHolidaySchema } from './holidays-calendar-data';

const emit = defineEmits(['success']);

const getTitle = computed(() => {
  return $t('ui.actionTitle.create');
});

const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  schema: useCopyHolidaySchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    modalApi.lock();
    try {
      if (typeof data.ISWORKDAY === 'boolean') {
        data.ISWORKDAY = data.ISWORKDAY ? 'Y' : 'N';
      }
      await $addHolidayApi(data);
      emit('success');
      message.success($t('ui.actionMessage.operationSuccess'));
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<{ DEPT: string; YEAR: string }>();
      if (data) {
        formApi.setValues({
          DEPT: data.DEPT,
          YEAR: data.YEAR,
        });
      }
    }
  },
});
</script>
<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
  </Modal>
</template>
