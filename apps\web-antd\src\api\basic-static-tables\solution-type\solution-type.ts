import { callServer, getDataSet, getDataSetNoPage } from '#/api/core/witlab';

export namespace SolutionTypeApi {
  export interface SolutionType {
    [key: string]: any;
    ORIGREC: number;
    ISDAY: string;
    SOLUTIONCODE: string;
    SOLUTIONTYPE: string;
    TYPE: string;
    VALIDATEDAY: number;
    VALIDITY: number;
  }
}

const $getSolutionTypeApi = async () => {
  const data = await getDataSet('DilutionManagement.getSolutionType', []);
  return data;
};

const $getSelectSolutionTypeApi = async () => {
  const data = await getDataSetNoPage('DilutionManagement.GetType', []);
  return data;
};
const $addSolutionTyoeApi = async (
  data: Omit<SolutionTypeApi.SolutionType, 'ORIGREC'>,
  eventCode: string = 'AddSolutionType',
  comment: string = '',
) => {
  const result = await callServer('DilutionManagement.Add_SolutionType', [
    data.SOLUTIONTYPE,
    data.SOLUTIONCODE,
    data.VALIDITY,
    data.TYPE,
    data.VALIDATEDAY,
    data.ISDAY,
    eventCode,
    comment,
  ]);
  return result;
};
const $deleteSolutionTypeApi = async (ORIGREC: number[]) => {
  const result = await callServer('DilutionManagement.DeleteSolutionType', [
    ORIGREC,
  ]);
  return result;
};

export {
  $addSolutionTyoeApi,
  $deleteSolutionTypeApi,
  $getSelectSolutionTypeApi,
  $getSolutionTypeApi,
};
