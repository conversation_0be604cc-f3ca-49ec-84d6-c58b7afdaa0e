<script lang="ts" setup>
import type { TabsProps } from 'ant-design-vue';
import type { DefaultOptionType, SelectValue } from 'ant-design-vue/es/select';

import type { Recordable } from '@vben/types';

import type { VxeGridListeners } from '#/adapter/vxe-table';
import type { LocationManageApi } from '#/api/materials-management/location-management';

import { onMounted, ref } from 'vue';

import { confirm, Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import {
  Button,
  Card,
  Col,
  message,
  Row,
  Select,
  SelectOption,
  Space,
  TabPane,
  Tabs,
} from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteLocation,
  getBuildingDetail,
  getDeptDetail,
  getLocationDetail,
  getLocationList,
  getRoomDetail,
  getSiteList,
} from '#/api/materials-management/location-management';
import { createGridOptions } from '#/utils/grid-option';

import AddLocationModal from './add-location.vue';
import {
  useBuildingSchema,
  useDeptSchema,
  useLocationManagementColumns,
  useLocationSchema,
  useRoomSchema,
} from './location-data';

// 当前选中的位置数据
const selectedLocation = ref<LocationManageApi.LocationManage>();
// 当前激活的Tab
const activeTab = ref('tabDept');

const showDeptTab = ref(true);
const showLocationTab = ref(false);
const showBuildingTab = ref(false);
const showRoomTab = ref(false);

const isAddDisabled = ref(true);
const isDeleteDisabled = ref(true);

// 左侧树形表格配置
const locationGridOptions = {
  ...createGridOptions<LocationManageApi.LocationManage>(),
  columns: useLocationManagementColumns(),
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  pageConfig: {},
  treeConfig: {
    transform: true, // 指定表格为树形表格
    parentField: 'PARENTMEMBER', // 父节点字段名
    rowField: 'NAMEMEMBER', // 行数据字段名
    // expandAll: true, // 展开全部
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!selectedDept.value) {
          return [];
        }
        return await getLocationList(selectedDept.value);
      },
    },
  },
};

// 表格事件处理
const locationGridEvents: VxeGridListeners<LocationManageApi.LocationManage> = {
  currentChange: ({ row }) => {
    if (row) {
      selectedLocation.value = row;
      switch (row.SEQ) {
        case 1: {
          activeTab.value = 'tabDept';
          showDeptTab.value = true;
          showBuildingTab.value = false;
          showRoomTab.value = false;
          showLocationTab.value = false;
          loadDeptDetail(row.SEQ);
          isAddDisabled.value = true;
          isDeleteDisabled.value = true;
          break;
        }
        case 2: {
          activeTab.value = 'tabBuilding';
          showDeptTab.value = false;
          showBuildingTab.value = true;
          showRoomTab.value = false;
          showLocationTab.value = false;
          loadBuildingDetail(row.VALUEMEMBER);
          isAddDisabled.value = true;
          isDeleteDisabled.value = true;
          break;
        }
        case 3: {
          activeTab.value = 'tabRoom';
          showDeptTab.value = false;
          showBuildingTab.value = false;
          showRoomTab.value = true;
          showLocationTab.value = false;
          loadRoomDetail(row.VALUEMEMBER);
          isAddDisabled.value = false;
          isDeleteDisabled.value = true;
          break;
        }
        case 4: {
          activeTab.value = 'tabLocation';
          showDeptTab.value = false;
          showBuildingTab.value = false;
          showRoomTab.value = false;
          showLocationTab.value = true;
          loadLocationDetail(row.VALUEMEMBER);
          isAddDisabled.value = false;
          isDeleteDisabled.value = false;
          break;
        }
      }
    }
  },
};

// 初始化左侧表格
const [LocationGrid, locationGridApi] = useVbenVxeGrid({
  gridOptions: locationGridOptions,
  gridEvents: locationGridEvents,
});

// Tab切换处理
const handleTabChange: TabsProps['onChange'] = (key) => {
  activeTab.value = `${key}`;
};

// 位置表单配置
const [LocationForm, locationFormApi] = useVbenForm({
  layout: 'vertical',
  schema: useLocationSchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 gap-x-6',
  /* handleSubmit: (values) => {
    if (!selectedLocation.value) {
      message.warning($t('commons.pleaseSelectFirst'));
      return;
    }
    message.success($t('commons.saveSuccess'));
    // console.log('基本信息表单数据:', values);
  }, */
});

// 站点配置表单配置
const [DeptForm, deptFormApi] = useVbenForm({
  layout: 'vertical',
  schema: useDeptSchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 gap-x-6',
  /* handleSubmit: (values) => {
    if (!selectedLocation.value) {
      message.warning($t('commons.pleaseSelectFirst'));
      return;
    }
    message.success($t('commons.saveSuccess'));
    // console.log('详细配置表单数据:', values);
  }, */
});

// 建筑表单配置
const [BuildingForm, buildingFormApi] = useVbenForm({
  layout: 'vertical',
  schema: useBuildingSchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 gap-x-6',
  /* handleSubmit: (values) => {
    if (!selectedLocation.value) {
      message.warning($t('commons.pleaseSelectFirst'));
      return;
    }
    message.success($t('commons.saveSuccess'));
    // console.log('关联数据表单数据:', values);
  }, */
});

// 房间表单配置
const [RoomForm, roomFormApi] = useVbenForm({
  layout: 'vertical',
  schema: useRoomSchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 gap-x-6',
  /* handleSubmit: (values) => {
    if (!selectedLocation.value) {
      message.warning($t('commons.pleaseSelectFirst'));
      return;
    }
    message.success($t('commons.saveSuccess'));
    // console.log('关联数据表单数据:', values);
  }, */
});

async function loadLocationDetail(sLocationId: string) {
  const data = await getLocationDetail(sLocationId);
  const location = data.items[0] as LocationManageApi.LocationManage;

  locationFormApi.setValues({
    LOCATIONCODE: location.LOCATIONCODE,
    LOCATION_NAME: location.LOCATION_NAME,
    DESCRIPTION: location.DESCRIPTION,
    IS_STORABLE: location.IS_STORABLE,
    IS_GXP: location.IS_GXP,
    CONDITION: location.CONDITION,
    TEMPERATURE: location.TEMPERATURE,
    LUMINOSITY: location.LUMINOSITY,
    TEMPERATURE_MAX: location.TEMPERATURE_MAX,
    OTHER: location.OTHER,
    HUMIDITY: location.HUMIDITY,
    NAME: location.NAME,
    SEQUENCE: location.SEQUENCE,
    NUMBERING_METHOD: location.NUMBERING_METHOD,
    SUBLOCATION_SIZE: location.SUBLOCATION_SIZE,
  });
}

async function loadDeptDetail(sOrigrec: number) {
  const data = await getDeptDetail(sOrigrec);
  const dept = data.items[0] as LocationManageApi.DeptManage;

  deptFormApi.setValues({
    DEPTCODE: dept.DEPTCODE,
    DEPT: dept.DEPT,
  });
}

async function loadBuildingDetail(sBuildingId: string) {
  const data = await getBuildingDetail(sBuildingId);
  const building = data.items[0] as LocationManageApi.BuildingManage;

  buildingFormApi.setValues({
    BUILDING_CODE: building.BUILDING_CODE,
    BUILDING_NAME: building.BUILDING_NAME,
    DESCRIPTION: building.DESCRIPTION,
  });
}

async function loadRoomDetail(sRoomId: string) {
  const data = await getRoomDetail(sRoomId);
  const room = data.items[0] as LocationManageApi.RoomManage;

  roomFormApi.setValues({
    ROOM_CODE: room.ROOM_CODE,
    ROOM_NAME: room.ROOM_NAME,
    DESCRIPTION: room.DESCRIPTION,
    CLASS: room.CLASS,
  });
}

// 刷新数据
function onRefresh() {
  locationGridApi.query();
}

// 展开所有节点
function expandAll() {
  locationGridApi.grid?.setAllTreeExpand(true);
}

const [AddLocationModalComponent, locationFormModalApi] = useVbenModal({
  title: '新增位置',
  // width: 1000, // 使用标准 width 属性
  footer: false, // 禁用默认底部，使用自定义底部
  destroyOnClose: true, // 关闭时销毁组件
  onCancel() {
    locationFormModalApi.close();
  },
});

const selectedRoomId = ref();
// 添加位置
function onCreateLocation() {
  const location = locationGridApi.grid?.getCurrentRecord();
  if (!location) return;
  const room = location.VALUEMEMBER;
  selectedRoomId.value = room;
  locationFormModalApi.open();
}

// 处理添加位置成功
function handleAddLocationSuccess() {
  // message.success('位置添加成功');
  onRefresh();
  locationFormModalApi.close();
}

// 删除位置
async function onDeleteLocation() {
  // 获取选中行
  const location = locationGridApi.grid?.getCurrentRecord();
  if (!location) return;

  const origrec = location.LOCATIONID;
  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的位置吗？`,
      icon: 'warning',
      centered: false,
    });

    await deleteLocation(origrec);

    message.success('删除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

// 组件挂载时展开所有节点
onMounted(async () => {
  expandAll();
  await getDeptList();
});

const selectedDept = ref<string>();

const onSelectChange = (
  _value: SelectValue,
  _option: DefaultOptionType | DefaultOptionType[],
) => {
  onRefresh();
};

const deptOptions = ref<Recordable<string>[]>([]);
const getDeptList = async () => {
  try {
    const res = await getSiteList('HHY');
    if (res && res.items && res.items.length > 0) {
      deptOptions.value = res.items;
    } else {
      console.warn('站点数据为空，请检查接口返回结果');
    }
  } catch (error) {
    console.error('获取站点列表失败:', error);
  }
};
</script>

<template>
  <Row :gutter="5">
    <!-- 左侧树形表格 -->
    <Col span="6">
      <Page auto-content-height>
        <div class="mb-2">
          <Select
            placeholder="请选择站点"
            style="width: 100%"
            v-model:value="selectedDept"
            @change="onSelectChange"
          >
            <SelectOption
              v-for="item in deptOptions"
              :key="item.DISPLAYMEMBER"
              :value="item.DISPLAYMEMBER"
            >
              {{ item.DISPLAYMEMBER }}
            </SelectOption>
          </Select>
        </div>
        <LocationGrid>
          <template #toolbar-actions>
            <Space :size="[4, 0]" wrap>
              <Button
                type="primary"
                @click="onCreateLocation"
                :disabled="isAddDisabled"
              >
                {{ $t('ui.actionTitle.create') }}
              </Button>
              <Button
                type="primary"
                danger
                @click="onDeleteLocation"
                :disabled="isDeleteDisabled"
              >
                {{ $t('ui.actionTitle.delete') }}
              </Button>
            </Space>
          </template>
        </LocationGrid>
      </Page>
    </Col>

    <!-- 右侧表单区域 -->
    <Col :span="18">
      <Page auto-content-height>
        <Card :bordered="false">
          <div v-if="selectedLocation" class="mb-4">
            <h2 class="text-lg font-bold">
              {{ selectedLocation.LOCATION_NAME }}
              <span class="ml-2 text-sm font-normal text-gray-500">
                {{ selectedLocation.LOCATIONCODE }}
              </span>
            </h2>
          </div>

          <div
            v-if="!selectedLocation"
            class="flex h-64 items-center justify-center"
          >
            <div class="text-center text-gray-500">
              <div class="mb-2 text-lg">位置管理</div>
              <div>请在左侧的树中选择一个元素以查看详细信息。</div>
            </div>
          </div>

          <div v-else>
            <Tabs v-model:active-key="activeTab" @change="handleTabChange">
              <TabPane key="tabLocation" tab="位置" v-if="showLocationTab">
                <div class="p-4">
                  <LocationForm>
                    <template #formFooter>
                      <div class="flex justify-end">
                        <Button type="primary">
                          {{ $t('commons.save') }}
                        </Button>
                      </div>
                    </template>
                  </LocationForm>
                </div>
              </TabPane>

              <TabPane key="tabDept" tab="站点" v-if="showDeptTab">
                <div class="p-4">
                  <DeptForm>
                    <template #formFooter>
                      <div class="flex justify-end">
                        <Button type="primary">
                          {{ $t('commons.save') }}
                        </Button>
                      </div>
                    </template>
                  </DeptForm>
                </div>
              </TabPane>
              <TabPane key="tabBuilding" tab="建筑" v-if="showBuildingTab">
                <div class="p-4">
                  <BuildingForm>
                    <template #formFooter>
                      <div class="flex justify-end">
                        <Button type="primary">
                          {{ $t('commons.save') }}
                        </Button>
                      </div>
                    </template>
                  </BuildingForm>
                </div>
              </TabPane>
              <TabPane key="tabRoom" tab="房间" v-if="showRoomTab">
                <div class="p-4">
                  <RoomForm>
                    <template #formFooter>
                      <div class="flex justify-end">
                        <Button type="primary">
                          {{ $t('commons.save') }}
                        </Button>
                      </div>
                    </template>
                  </RoomForm>
                </div>
              </TabPane>
            </Tabs>
          </div>
        </Card>
      </Page>
    </Col>
  </Row>
  <!-- 添加位置弹窗 -->
  <AddLocationModalComponent class="w-[1050px]">
    <AddLocationModal
      :room-id="selectedRoomId"
      class="w-[1000px]"
      @success="handleAddLocationSuccess"
    />
  </AddLocationModalComponent>
</template>

<style>
.ant-tabs-content {
  height: 100%;
}
</style>
