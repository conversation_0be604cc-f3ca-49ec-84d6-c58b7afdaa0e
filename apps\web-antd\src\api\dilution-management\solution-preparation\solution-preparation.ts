import { callServer, getDataSet, getDataSetNoPage } from '#/api/core/witlab';
export namespace SolutionPreparationApi {
  export interface Form {
    [key: string]: any;
    ORIGREC: number;
    NAME: string;
    EFFECT: string;
    TYPE: string;
    SORTER: number;
  }
  export interface RowType {
    [key: string]: any;
  }
}
async function getTestSoultionApi(params: Array<any>) {
  return getDataSet('DilutionManagement.getTestSoultion', params);
}
async function getTestsolutionRecordApi(params: Array<any>) {
  return getDataSet('DilutionManagement.getTestsolutionRecod', params);
}

async function getStandSolutionApi(params: Array<any>) {
  return getDataSet('DilutionManagement.getStandSolution', params);
}

async function getTitrantSolutionApi(params: Array<any>) {
  return getDataSet('DilutionManagement.getTitrantSolution', params);
}
async function updateProviderApi(params: Array<any>) {
  return await callServer('Runtime_Support.WS_UPDATEPROVIDER', params);
}
async function getListAttachmentsApi(params: Array<any>) {
  return getDataSet('Common.ListAttachments_New', params);
}
async function getSoluteApi(params: Array<any>) {
  return getDataSet('DilutionManagement.getSolute', params);
}
async function deleteSolutionApi(params: Array<any>) {
  return await callServer('DilutionManagement.DelDilutionStore', params);
}
async function getUsersApi() {
  return getDataSetNoPage('BacteriaManage.DS_USERS', []);
}
async function getTitranSoluteApi(params: Array<any>) {
  return getDataSetNoPage('BacteriaManage.getTitranSolute', params);
}
async function addTestSubmitRecordApi(params: Array<any>) {
  return await callServer('DilutionManagement.AddTestSubmitRecord', params);
}
async function updateApproverApi(params: Array<any>) {
  return await callServer('DilutionManagement.UpdateApprover', params);
}
async function getJobApi() {
  return getDataSetNoPage('DilutionManagement.getJob',[]);
}
async function getSolutionConditionApi() {
  return getDataSetNoPage('DilutionManagement.getSolutionCondition', []);
}
async function getAfterManApi() {
  return getDataSetNoPage('DilutionManagement.getAfterMan', []);
}
async function getSoultionNameApi(params:{type:string}) {
  return getDataSetNoPage('DilutionManagement.getSoultionName', [params.type]);
}
async function getMeasureTypeApi() {
  return getDataSetNoPage('UNITS_MANAGEMENT.DS_MEASURE_TYPE', []);
}
async function getMeasureUnitApi(params: Array<any>) {
  return getDataSetNoPage('UNITS_MANAGEMENT.DS_UNITS_OF_MEASURE', params);
}
async function getSolutionNameApi() {
  return getDataSetNoPage('DilutionManagement.getSolutionName', []);
}
async function addTestSolutionApi(params: Array<any>) {
  return await callServer('DilutionManagement.AddTestSolution', params);
}
async function addStandSolutionApi(params: Array<any>) {
  return await callServer('DilutionManagement.AddStandSolution', params);
}
async function addTitarantSelfSolutionApi(params: Array<any>) {
  return await callServer('DilutionManagement.AddTitarantSelfSolution', params);
}
async function getMaterialDataApi() {
  return getDataSet('DilutionManagement.getMaterialData', []);
}
async function addSoluteApi(params: Array<any>) {
  return await callServer('DilutionManagement.ADD_Solute', params);
}
async function getReserveDataApi() {
  return getDataSet('DilutionManagement.getReserveData', []);
}
async function deleteSoluteApi(params: Array<any>) {
  return await callServer('DilutionManagement.Delete_Solute', params);
}
async function getEquipmentApi(params: Array<any>) {
  return getDataSet('DilutionManagement.dgEquipment', params);
}
async function getOtherEquipmentUsedtimeApi(params: Array<any>) {
  return getDataSet('DilutionManagement.dg_GetOtherEquipmentUsedtime', params);
}
async function getAllEquipmenttimeApi(params: Array<any>) {
  return getDataSetNoPage('DilutionManagement.getAllEquipment', params);
}
async function editEquimentListApi(params: Array<any>) {
  return await callServer('DilutionManagement.editEquipmentList', params);
}
async function deleteRowsApi(params: Array<any>) {
  return await callServer('Common.DeleteRows', params);
}
async function addOtherEquipmentUsedTimeApi(params: Array<any>) {
  return await callServer('DilutionManagement.Add_OtherEquipmentUsedTime', params);
}
async function updateEndDateApi(params: Array<any>) {
  return await callServer('DilutionManagement.UpdateENDDATE', params);
}
async function getReagentsApi(params: Array<any>) {
  return getDataSet('DilutionManagement.dg_Reagents', params);
}
async function getCobMatTypeApi(params: Array<any>) {
  return getDataSetNoPage('RUNBUILD_RESENT_APPROVE.cobMatType', params);
}
async function getSelectReagentsApi(params: Array<any>) {
  return getDataSet('RUNBUILD_RESENT_APPROVE.dg_SelectReagents', params);
}
async function addReagentsByRunApi(params: Array<any>) {
  return await callServer('DilutionManagement.Add_ReagentsByRun', params);
}
async function getSoluctionTypeApi(params: Array<any>) {
  return getDataSetNoPage('RUNBUILD_RESENT_APPROVE.CB_SoluctionType', params);
}
async function getSelectSoluctionApi(params: Array<any>) {
  return getDataSet('RUNBUILD_RESENT_APPROVE.dg_SelectSoluction', params);
}
async function getBacteriaTypeApi(params: Array<any>) {
  return getDataSetNoPage('RUNBUILD_RESENT_APPROVE.Cb_BacteriaType', params);
}
async function getSelectBacteriaApi(params: Array<any>) {
  return getDataSet('RUNBUILD_RESENT_APPROVE.dg_SelectBacteria', params);
}

export {
  getTestSoultionApi,
  getStandSolutionApi,
  getTestsolutionRecordApi,
  getTitrantSolutionApi,
  getListAttachmentsApi,
  updateProviderApi,
  getSoluteApi,
  deleteSolutionApi,
  getUsersApi,
  getTitranSoluteApi,
  addTestSubmitRecordApi,
  updateApproverApi,
  getJobApi,
  getSolutionConditionApi,
  getAfterManApi,
  getSoultionNameApi,
  getMeasureTypeApi,
  getMeasureUnitApi,
  getSolutionNameApi,
  addTestSolutionApi,
  addStandSolutionApi,
  addTitarantSelfSolutionApi,
  getMaterialDataApi,
  addSoluteApi,
  getReserveDataApi,
  deleteSoluteApi,
  getEquipmentApi,
  getOtherEquipmentUsedtimeApi,
  getAllEquipmenttimeApi,
  editEquimentListApi,
  deleteRowsApi,
  addOtherEquipmentUsedTimeApi,
  updateEndDateApi,
  getReagentsApi,
  getCobMatTypeApi,
  getSelectReagentsApi,
  addReagentsByRunApi,
  getSoluctionTypeApi,
  getSelectSoluctionApi,
  getBacteriaTypeApi,
  getSelectBacteriaApi
};
