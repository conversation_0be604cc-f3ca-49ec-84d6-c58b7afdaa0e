<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import { trainModalSchema } from '../persons-manage-data';
import dayjs from 'dayjs';
import { addTrainApi } from '#/api/equipment/persons-manage';

const emit = defineEmits(['success']);
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: trainModalSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    console.warn(data);

    modalApi.lock();
    try {
      const clickRow = modalApi.getData().clickRow || [];
      const data = await formApi.getValues();
      const courseNameRef = formApi.getFieldComponentRef('courseName');
      const options = courseNameRef.getOptions();
      const startTime = dayjs(data.startTime).format();
      const enddate = dayjs(data.enddate).format();
      const courseName = options.find(
        (item: any) => item.value === data.courseName,
      );
      const params = [
        clickRow.USRNAM,
        data.courseName,
        data.trainingLocation,
        startTime,
        enddate,
        data.trainingMethod,
        data.assessmentMethod,
        data.remark,
        data.instructor,
        data.score,
        courseName.label,
        data.courseId.trim(),
      ];
      await addTrainApi(params);
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      const date = dayjs();
      const date1 = dayjs();

      await formApi.setFieldValue('startTime', date);
      await formApi.setFieldValue('endTime', date1);
    }
  },
});
</script>
<template>
  <Modal title="添加培训经历">
    <Form class="mx-4" />
  </Modal>
</template>
