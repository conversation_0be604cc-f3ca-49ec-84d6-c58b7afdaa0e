<script lang="ts" setup>
import type { XIntervalsApi } from '#/api/basic-static-tables/intervals';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Select, Spin } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  $addXIntervalsApi,
  $getSitesApi,
} from '#/api/basic-static-tables/intervals';
import { $t } from '#/locales';

import { useSchema } from './data';

interface FormArgs {
  deptId: string;
}
const emit = defineEmits(['success']);
const formArgs = ref<FormArgs>();
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: useSchema(),
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    data.eventCode = 'addInterval';
    data.comment = '添加时间间隔';
    modalApi.lock();
    try {
      await $addXIntervalsApi(data);
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  title: $t('ui.actionTitle.create', [
    $t('basic-static-tables.intervals.interval'),
  ]),
  async onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<FormArgs>();
      formArgs.value = data;
      if (depts.value.length === 0) {
        await loadDepts();
      }
      if (formArgs.value.deptId) {
        formApi.setFieldValue('DEPT', [formArgs.value.deptId]);
      }
    }
  },
});
const loadingDepts = ref(false);
const depts = ref<XIntervalsApi.DeptTree[]>([]);
async function loadDepts() {
  loadingDepts.value = true;
  try {
    depts.value = await $getSitesApi();
  } finally {
    loadingDepts.value = false;
  }
}
</script>

<template>
  <Modal>
    <Form class="mx-4">
      <template #DEPT="slotProps">
        <Spin :spinning="loadingDepts" wrapper-class-name="w-full">
          <Select
            :options="depts"
            mode="multiple"
            class="w-full"
            v-bind="slotProps"
            :field-names="{ label: 'DEPT', value: 'DEPT' }"
          />
        </Spin>
      </template>
    </Form>
  </Modal>
</template>
