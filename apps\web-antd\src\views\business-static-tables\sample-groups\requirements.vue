<script setup lang="ts">
import type { SampleGroupsApi } from '#/api/business-static-tables/sample-groups';

import { ref, watch } from 'vue';

import { confirm, useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import {
  delSamplingReq,
  getSampleReqList,
} from '#/api/business-static-tables/sample-groups';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import AddSampleReqForm from './add-sample-requirement.vue';
import CopySampleReqForm from './copy-sample-requirement.vue';
import {
  useSampleRequirementColumns,
  useSampleRequirementFilterSchema,
} from './sample-groups-data';
// import SampleReqDetail from './sample-requirement-detail.vue';

const props = defineProps<{
  profile: string;
  sampleGroupCode: string;
  spCode: number;
}>();

const isRowEditDisabled = ref(false);

watch(
  () => props.spCode,
  (_val) => {
    onRefresh();
  },
);
const colums = useSampleRequirementColumns();
const filterSchema = useSampleRequirementFilterSchema();
const queryData = async () => {
  if (!props.spCode) return [];
  const data = await getSampleReqList(
    props.spCode,
    props.sampleGroupCode,
    props.profile,
  );
  return data.items;
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};

const {
  Grid: SampleRequirementGrid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
} = useLimsGridsConfig<SampleGroupsApi.SamplingRequirements>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

function onRefresh() {
  gridApi.query();
}

// 添加取样要求
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddSampleReqForm,
});

async function onCreate() {
  const spCode = props.spCode;
  const sampleGroupCode = props.sampleGroupCode;
  if (!spCode) return;

  formModalApi
    .setData({
      SP_CODE: spCode,
      SAMPLEGROUPCODE: sampleGroupCode,
    })
    .open();
}

async function onDelete() {
  const sampleReq = gridApi.grid?.getCurrentRecord();
  if (!sampleReq) return;

  const sOrigrec = sampleReq.ORIGREC;
  const sSampleType = sampleReq.SAMPLE_TYPE;
  if (gridApi.grid.getData().length === 1 && sSampleType !== 'LOTTEMPLATE') {
    message.warning(
      $t('business-static-tables.sampleGroups.LAST_SAMPLING_REQ'),
    );
    return;
  }

  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的数据吗？`,
      icon: 'warning',
      centered: false,
    });

    await delSamplingReq(sOrigrec);
    message.success('删除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

// 复制取样要求
const [CopyFormModal, copyFormModalApi] = useVbenModal({
  connectedComponent: CopySampleReqForm,
});

async function onCopy() {
  const sampleReq = gridApi.grid?.getCurrentRecord();
  if (!sampleReq) return;
  const sOrigrec = sampleReq.ORIGREC;
  copyFormModalApi
    .setData({
      ORIGREC: sOrigrec,
    })
    .open();
}

// const [FormDrawer, formDrawerApi] = useVbenDrawer({
//   connectedComponent: SampleReqDetail,
//   destroyOnClose: true,
//   class: 'w-[900px]',
// });

// function onDetails(row: SampleGroupsApi.SamplingRequirements) {
//   formDrawerApi.setData(row).open();
// }
</script>

<template>
  <FormDrawer @success="onRefresh" />
  <FormModal @success="onRefresh" />
  <CopyFormModal @success="onRefresh" />
  <SampleRequirementGrid>
    <template #toolbar-actions>
      <Space :size="[4, 0]" wrap>
        <Button type="primary" @click="onCreate">
          {{ $t('ui.actionTitle.create') }}
        </Button>
        <Button type="primary" danger @click="onDelete">
          {{ $t('ui.actionTitle.delete') }}
        </Button>
        <Button type="default" @click="onCopy">
          {{ $t('business-static-tables.copy') }}
        </Button>
        <Button type="default">
          {{ $t('business-static-tables.sampleGroups.aliquot') }}
        </Button>
      </Space>
    </template>
    <template #action="{ row }">
      <template v-if="hasEditStatus(row)">
        <Button type="link" @click="saveRowEvent(row)">
          {{ $t('business-static-tables.save') }}
        </Button>
        <Button type="link" @click="cancelRowEvent(row)">
          {{ $t('business-static-tables.cancel') }}
        </Button>
      </template>
      <template v-else>
        <Button
          type="link"
          @click="editRowEvent(row)"
          :disabled="isRowEditDisabled"
        >
          {{ $t('business-static-tables.edit') }}
        </Button>
      </template>
      <Button type="link">
        {{ $t('business-static-tables.detail') }}
      </Button>
    </template>
  </SampleRequirementGrid>
</template>
