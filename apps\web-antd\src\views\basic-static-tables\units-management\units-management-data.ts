import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { UnitsManagementApi } from '#/api/basic-static-tables/units-management';

import { $t } from '#/locales';

export function useMeasureTypesColumns(): VxeTableGridOptions<UnitsManagementApi.MeasureTypes>['columns'] {
  return [
    { field: 'select', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'MEASURE_TYPE',
      title: $t('basic-static-tables.measure-types.measure_type'),
      minWidth: 500,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('basic-static-tables.operation'),
      minWidth: 140,
    },
  ];
}

export function useMeasureTypesFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        // placeholder: 'Please enter code',
      },
      fieldName: 'MEASURE_TYPE',
      label: $t('basic-static-tables.measure-types.measure_type'),
    },
  ];
}

export function useUnitsOfMeasureColumns(): VxeTableGridOptions<UnitsManagementApi.UnitsOfMeasure>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 100,
      visible: false,
    },
    {
      align: 'center',
      field: 'MEASURE_TYPE',
      title: $t('basic-static-tables.units-of-measure.measure_type'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'UNIT_CODE',
      title: $t('basic-static-tables.units-of-measure.unit_code'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'UNIT_NAME',
      title: $t('basic-static-tables.units-of-measure.unit_name'),
      editRender: { name: 'input' },
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'UOM_LONG_NAME',
      title: $t('basic-static-tables.units-of-measure.uom_long_name'),
      editRender: { name: 'input' },
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'UOM_SHORT_NAME',
      title: $t('basic-static-tables.units-of-measure.uom_short_name'),
      editRender: { name: 'input' },
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'IS_SI',
      title: $t('basic-static-tables.units-of-measure.is_si'),
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('basic-static-tables.operation'),
      minWidth: 140,
    },
  ];
}

export function useUnitsConversionColumns(): VxeTableGridOptions<UnitsManagementApi.UnitsConversion>['columns'] {
  return [
    { field: 'select', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'MEASURE_TYPE',
      title: $t('basic-static-tables.units-conversion.measure_type'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'UNIT_CODE',
      title: $t('basic-static-tables.units-conversion.unit_code'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'FACTOR',
      title: $t('basic-static-tables.units-conversion.factor'),
      editRender: { name: 'input' },
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'TO_UNIT_CODE',
      title: $t('basic-static-tables.units-conversion.to_unit_code'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'IS_NA',
      title: $t('basic-static-tables.units-conversion.is_na'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('basic-static-tables.operation'),
      minWidth: 140,
    },
  ];
}
