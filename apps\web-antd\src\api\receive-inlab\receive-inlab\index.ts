import { callServer, getDataSet } from '#/api/core/witlab';

export namespace ReceiveInLabApi {
  export interface ReceiveOrders {
    ORIGREC: number;
    TAT: string;
    FLDISPSTATUS: string;
    RECEIVEDSTS: string;
    BATCHNO: string;
    SAMPLE_NAME: string;
    MATNAME: string;
    STATUS: string;
    ORDNO: string;
    LOGDATE: Date;
    SAMPDATE: Date;
    COMPDAT: Date;
    SAMPLEDBY: string;
    SAMPGRTYPE: string;
    FLSTATUS: string;
    ACTUALVOL: string;
    CLSAMPNO: string;
    RETEST_BATCHID: string;
    XORDNO: number;
    Value: number;
    Text: string;
    LOCATIONCODE: string;
    LOCATION_NAME: string;
    SAMPLE_TYPE: string;
    FOLDERNO: string;
  }
  export interface ReceiveTest {
    ORIGREC: number;
    TS: string;
    TESTNO: string;
    CONTAINERID: string;
    DEPT: string;
    SERVGRP: string;
    METHOD: string;
    PROFILE: string;
    USRNAM: string;
    TESTCODE: number;
  }
}

async function getPendingReceiptdg() {
  return getDataSet('ReceiveInLab.getPendingReceiptdg', []);
}

async function getSampleData(
  sOpeningMode: string,
  nOrigrecs: number[],
  sLocationCode: string,
) {
  return await callServer('ReceiveInLab.getSampleData', [
    sOpeningMode,
    nOrigrecs,
    sLocationCode,
  ]);
}

async function receiveInLabMulti(
  sType: string,
  aSampleData: string[][],
  dReceiveDate: Date,
  sOpeningMode: string,
) {
  return await callServer('ReceiveInLab.receiveInLabMulti', [
    sType,
    aSampleData,
    dReceiveDate,
    sOpeningMode,
  ]);
}

async function updReceiveAll(aOrdNos: string[]) {
  return await callServer('ReceiveInLab.UpdReceiveAll', [aOrdNos]);
}

async function returnToLogin(sOrdNo: string) {
  return await callServer('ReceiveInLab.returnToLogin', [sOrdNo]);
}

async function getPromptForSample(sOpeningMode: string, sType: string) {
  return getDataSet('ReceiveInLab.getPromptForSample', [sOpeningMode, sType]);
}

async function getSamplingRequirements(sOrdNo: string) {
  return getDataSet('ReceiveInLab.samplingRequirements', [sOrdNo]);
}

async function getTests(sOrdNo: string) {
  return getDataSet('ReceiveInLab.getTests', [sOrdNo]);
}

async function removedSamplingRequirement(
  sOrigrec: number[],
  sComment: string,
  sOrdNo: string,
) {
  return await callServer('ReceiveInLab.removedSamplingRequirement', [
    sOrigrec,
    sComment,
    sOrdNo,
  ]);
}

async function chkActiveNum(sOrdNo: string) {
  return await callServer('ReceiveInLab.ChkActiveNum', [sOrdNo]);
}

async function finishAliquot(sOrdNo: string) {
  return await callServer('ReceiveInLab.FinishAliquot', [sOrdNo]);
}

async function chkAliquoted(sOrdNo: string) {
  return await callServer('ReceiveInLab.ChkAliquoted', [sOrdNo]);
}

async function convertSampleNoToInventoryId(sInventoryId: string) {
  return await callServer('Common.ConvertSampleNoToInventoryId', [
    sInventoryId,
  ]);
}

async function getInventoryId(
  sOpeningMode: string,
  rbByServGrp: string,
  sInventoryId: string,
  rdtStability: string,
) {
  return await getDataSet('ReceiveInLab.getInventoryId', [
    sOpeningMode,
    rbByServGrp,
    'N',
    sInventoryId,
    rdtStability,
  ]);
}

async function getParentBatchSamplingRequirement(sParentInventoryId: number) {
  return await getDataSet('ReceiveInLab.getParentBatchSamplingRequirement', [
    sParentInventoryId,
  ]);
}

export {
  chkActiveNum,
  chkAliquoted,
  convertSampleNoToInventoryId,
  finishAliquot,
  getInventoryId,
  getParentBatchSamplingRequirement,
  getPendingReceiptdg,
  getPromptForSample,
  getSampleData,
  getSamplingRequirements,
  getTests,
  receiveInLabMulti,
  removedSamplingRequirement,
  returnToLogin,
  updReceiveAll,
};
