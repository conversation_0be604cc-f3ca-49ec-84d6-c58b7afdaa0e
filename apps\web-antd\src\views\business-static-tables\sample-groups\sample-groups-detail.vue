<script setup lang="ts">
import type { SampleGroupsApi } from '#/api/business-static-tables/sample-groups';

import { ref, watch } from 'vue';

import { confirm, useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import {
  createNewVersionTestPlan,
  deleteTestPlan,
  getSampleGrpDetailList,
} from '#/api/business-static-tables/sample-groups';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import AddTestPlanFromExistForm from './add-test-plan-from-existing.vue';
import AddTestPlanForm from './add-test-plan.vue';
import {
  useSampleGrpDetailColumns,
  useSampleGrpDetailFilterSchema,
} from './sample-groups-data';

const props = defineProps<{
  currentTestRow: null | SampleGroupsApi.IpSampleGroups;
}>();

const isRowEditDisabled = ref(false);

watch(
  () => props.currentTestRow,
  (_val) => {
    onRefresh();
  },
);
const colums = useSampleGrpDetailColumns();
const filterSchema = useSampleGrpDetailFilterSchema();
const queryData = async () => {
  if (!props.currentTestRow) return [];
  const data = await getSampleGrpDetailList(
    props.currentTestRow.SAMPLEGROUPCODE,
    'false',
    'Create',
  );
  return data.items;
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};

const {
  Grid: SampleGroupDetailGrid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
} = useLimsGridsConfig<SampleGroupsApi.IpSampleGroupDetails>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

function onRefresh() {
  gridApi.query();
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddTestPlanForm,
  destroyOnClose: true,
});

function onCreate() {
  if (props.currentTestRow === null) return;
  const sampleGroupGode = props.currentTestRow.SAMPLEGROUPCODE;
  const prodGroup = props.currentTestRow.PRODGROUP;
  const matCode = props.currentTestRow.MATCODE;
  formModalApi
    .setData({
      SAMPLEGROUPCODE: sampleGroupGode,
      PRODGROUP: prodGroup,
      MATCODE: matCode,
    })
    .open();
}

const [AddFormModal, addFormModalApi] = useVbenModal({
  connectedComponent: AddTestPlanFromExistForm,
  destroyOnClose: true,
});

function onFromExistingCreate() {
  if (props.currentTestRow === null) return;
  const sampleGroupGode = props.currentTestRow.SAMPLEGROUPCODE;
  const prodGroup = props.currentTestRow.PRODGROUP;
  addFormModalApi
    .setData({
      SAMPLEGROUPCODE: sampleGroupGode,
      PRODGROUP: prodGroup,
    })
    .open();
}

async function onDelete() {
  // 获取选中行
  const sample = gridApi.grid?.getCurrentRecord();
  if (!sample) return;

  if (props.currentTestRow === null) return;

  const sSamplegroupCode = props.currentTestRow.SAMPLEGROUPCODE;
  const sSpCode = sample.SP_CODE;

  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的数据吗？`,
      icon: 'warning',
      centered: false,
    });

    const sRet = await deleteTestPlan(sSamplegroupCode, sSpCode);
    if (!sRet) {
      message.warning(
        $t('business-static-tables.sampleGroups.testPlanExistsInOrders'),
      );
      return;
    }
    message.success('删除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}
async function onCreateVersion() {
  // 获取选中行
  const sample = gridApi.grid?.getCurrentRecord();
  if (!sample) return;

  if (props.currentTestRow === null) return;

  const sSamplegroupCode = props.currentTestRow.SAMPLEGROUPCODE;
  const sSpCode = sample.SP_CODE;

  try {
    await confirm({
      title: '新建版本',
      content: `确定要新建版本吗？`,
      icon: 'warning',
      centered: false,
    });

    const nNewSpcode = await createNewVersionTestPlan(
      sSpCode,
      sSamplegroupCode,
    );
    if (nNewSpcode === -100) {
      message.warning(
        $t('business-static-tables.sampleGroups.existingDraftVersions'),
      );
      return;
    }
    message.success('新建成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}
</script>

<template>
  <FormModal @success="onRefresh" />
  <AddFormModal @success="onRefresh" />
  <div class="h-[350px] w-full">
    <SampleGroupDetailGrid>
      <template #toolbar-actions>
        <Space :size="[4, 0]" wrap>
          <Button type="primary" @click="onCreate">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="onDelete">
            {{ $t('ui.actionTitle.delete') }}
          </Button>
          <Button type="default" @click="onFromExistingCreate">
            {{
              $t('business-static-tables.sampleGroups.AddTestPlanFromExisting')
            }}
          </Button>
          <Button type="default">
            {{ $t('business-static-tables.copy') }}
          </Button>
          <Button type="default" @click="onCreateVersion">
            {{ $t('business-static-tables.sampleGroups.newVersion') }}
          </Button>
          <!-- <Button type="default">
            {{ $t('business-static-tables.sampleGroups.usedInOtherProdGrp') }}
          </Button> -->
          <Button type="default">
            {{ $t('business-static-tables.release') }}
          </Button>
          <Button type="default">
            {{ $t('business-static-tables.sampleGroups.spreadStage') }}
          </Button>
          <!-- <Button type="default">
            {{ $t('business-static-tables.sampleGroups.dataValidation') }}
          </Button> -->
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent(row)">
            {{ $t('business-static-tables.save') }}
          </Button>
          <Button type="link" @click="cancelRowEvent(row)">
            {{ $t('business-static-tables.cancel') }}
          </Button>
        </template>
        <template v-else>
          <Button
            type="link"
            @click="editRowEvent(row)"
            :disabled="isRowEditDisabled"
          >
            {{ $t('business-static-tables.edit') }}
          </Button>
        </template>
      </template>
    </SampleGroupDetailGrid>
  </div>
</template>

<style scoped></style>
