// import { requestClient } from '#/api/request';
import { callServer, getDataSet } from '#/api/core/witlab';

export namespace JobDescriptionsTableApi {
  export interface PageFetchParams {
    [key: string]: any;
    page: number;
    pageSize: number;
  }

  export interface JobDescriptions {
    [key: string]: any;
    ORIGREC: number;
    JOBDESCRIPTION: string;
  }
}

// 异步函数，用于获取职位描述表格API
async function getJobDescriptionsTableApi() {
  return getDataSet('JobDescriptions.JobDescriptions', []);
}

export const ADD_JOB_DESCRIPTIONS_EVENT = 'AddJobDescription'; // 添加职位描述事件

// 异步函数，用于添加职位描述
async function addJobDescriptionsApi(
  data: Omit<JobDescriptionsTableApi.JobDescriptions, 'ORIGREC'>,
) {
  return callServer('JobDescriptions.AddJobDescription', [
    data.JOBDESCRIPTION,
    ADD_JOB_DESCRIPTIONS_EVENT,
  ]);
}

// 异步函数，用于删除职位描述
async function deleteJobDescriptionsApi(delJob: string) {
  return callServer('JobDescriptions.DeleteJobDescription', [delJob]);
}

/**
 * 更新职位描述
 * @param data - 职位描述数据
 * @description 更新职位描述
 * @returns boolean
 */
async function updateJobDescriptionsApi(
  data: JobDescriptionsTableApi.JobDescriptions,
) {
  return requestClient.post<boolean>(
    '/job-descriptions/job-descriptions',
    data,
  );
}

export {
  addJobDescriptionsApi,
  deleteJobDescriptionsApi,
  getJobDescriptionsTableApi,
  updateJobDescriptionsApi,
};
