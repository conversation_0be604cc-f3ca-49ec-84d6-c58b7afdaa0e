import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { XPackagingTypeApi } from '#/api/basic-static-tables/packaging-type';

import { $t } from '#/locales';
import { createColumn } from '#/utils/lims-grids-config';

export function useColumns(): VxeTableGridOptions<XPackagingTypeApi.XPackagingType>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 60 },
    createColumn({
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 80,
    }),
    createColumn({
      field: 'PACKAGINGTYPECODE',
      title: $t('basic-static-tables.xpackagingtype.packagingTypeCode'),
      minWidth: 120,
    }),
    // {
    //   field: 'action',
    //   title: $t('commons.action'),
    //   minWidth: 150,
    //   slots: { default: 'action' },
    // },
  ];
}

export function useSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'PACKAGINGTYPECODE',
      label: $t('basic-static-tables.xpackagingtype.packagingTypeCode'),
      rules: 'required',
    },
  ];
}

export function useFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'PACKAGINGTYPECODE',
      label: $t('basic-static-tables.xpackagingtype.packagingTypeCode'),
    },
  ];
}
