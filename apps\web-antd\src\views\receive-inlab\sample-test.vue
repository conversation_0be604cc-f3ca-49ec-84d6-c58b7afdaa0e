<script setup lang="ts">
import type { ReceiveInLabApi } from '#/api/receive-inlab/receive-inlab';

import { watch } from 'vue';

import { getTests } from '#/api/receive-inlab/receive-inlab';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import {
  useReceiveTestColumns,
  useReceiveTestFilterSchema,
} from './receive-inlab-data';

const props = defineProps<{
  currentTestRow: null | ReceiveInLabApi.ReceiveOrders;
}>();

watch(
  () => props.currentTestRow,
  (_val) => {
    onRefresh();
  },
);
const colums = useReceiveTestColumns();
const filterSchema = useReceiveTestFilterSchema();
const queryData = async () => {
  if (!props.currentTestRow) return [];
  const data = await getTests(props.currentTestRow.ORDNO);
  return data.items;
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};

const { Grid, gridApi } = useLimsGridsConfig<ReceiveInLabApi.ReceiveTest>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

function onRefresh() {
  gridApi.query();
}
</script>

<template>
  <div class="h-[350px] w-full">
    <Grid />
  </div>
</template>
