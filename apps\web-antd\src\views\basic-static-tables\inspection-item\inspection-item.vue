<script lang="ts" setup>
import type { VxeColumnPropTypes } from 'vxe-table';

import type { VbenFormProps } from '#/adapter/form';
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { InspectionItemApi } from '#/api/basic-static-tables/inspection-item';

import { onMounted, reactive } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { $getLookupValuesSimple } from '#/api/basic-static-tables/generic-meta-data/lookup';
import {
  $delInspectionItemApi,
  $getInspectionItemListApi,
} from '#/api/basic-static-tables/inspection-item';
import { confirm } from '#/utils/utils';

import AddInspectionItemForm from './add-inspection-item-from.vue';
import { useColumns, useFilterSchema } from './inspection-item-data';

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddInspectionItemForm,
  destroyOnClose: true,
});

const fileNameEditRender = reactive<VxeColumnPropTypes.EditRender>({
  name: 'select',
  options: [],
});

onMounted(async () => {
  const result = await $getLookupValuesSimple('InspectionFileName');
  fileNameEditRender.options = result.map((item) => ({
    label: item.TEXT,
    value: item.VALUE,
  }));
});

const gridOptions: VxeTableGridOptions<InspectionItemApi.InspectionItem> = {
  columns: useColumns(fileNameEditRender),
  stripe: true,
  border: true,
  keepSource: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
    showStatus: true,
    autoClear: false,
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        return await $getInspectionItemListApi();
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    isCurrent: true,
    drag: true,
  },
  rowDragConfig: {
    trigger: 'row',
    showGuidesStatus: true,
  },
  columnConfig: {
    drag: true,
    isCurrent: true,
  },
  columnDragConfig: {
    isCrossDrag: true,
    showGuidesStatus: true,
    showIcon: false,
    trigger: 'cell',
  },
  checkboxConfig: {
    range: true,
  },
  resizableConfig: {
    isDblclickAutoWidth: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'copy',
            name: '复制内容（Ctrl+C）',
            prefixConfig: { icon: 'vxe-icon-copy' },
            visible: true,
            disabled: false,
          },
        ],
      ],
    },
  },
};

const gridEvents: VxeGridListeners<InspectionItemApi.InspectionItem> = {
  cellMenu({ row, column }) {
    const $grid = gridApi.grid;
    if ($grid) {
      $grid.setCurrentRow(row);
      $grid.setCurrentColumn(column);
    }
  },
  menuClick({ menu }) {
    if (menu.code === 'copy') {
      const col = gridApi.grid.getCurrentColumn();
      const row = gridApi.grid.getCurrentRecord();
      if (col && row) {
        navigator.clipboard.writeText(row[col.field]);
      }
    }
  },
};

const formOptions: VbenFormProps = {
  schema: useFilterSchema(),
  submitOnChange: false,
  collapsed: true,
  showCollapseButton: true,
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents,
  formOptions,
});

function hasEditStatus(row: InspectionItemApi.InspectionItem) {
  return gridApi.grid?.isEditByRow(row);
}

function editRowEvent(row: InspectionItemApi.InspectionItem) {
  gridApi.grid?.setEditRow(row);
}

async function saveRowEvent(row: InspectionItemApi.InspectionItem) {
  await gridApi.grid?.clearEdit();
  gridApi.grid.reloadRow(row);
  gridApi.setLoading(true);
  setTimeout(() => {
    gridApi.setLoading(false);
    message.success({
      content: `保存成功！`,
    });
  }, 600);
}

const cancelRowEvent = (row: InspectionItemApi.InspectionItem) => {
  gridApi.grid?.clearEdit().then(() => {
    // 还原行数据
    gridApi.grid.revertData(row);
  });
};

function onRefresh() {
  gridApi.query();
}

function onCreate() {
  formModalApi.setData(null).open();
}

async function onDelete() {
  const checkOrig: number[] =
    (gridApi.grid
      ?.getCheckboxRecords()
      .map((item) => item.ORIGREC) as number[]) || [];

  if (checkOrig.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }
  await confirm('是否确认删除选中的数据？', '删除确认');
  await $delInspectionItemApi(checkOrig);
  message.success('删除成功！');
  onRefresh();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <Grid>
      <template #toolbar-actions>
        <Space :size="[4, 0]" wrap>
          <Button type="primary" @click="onCreate">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="onDelete">
            {{ $t('ui.actionTitle.delete') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent(row)">保存</Button>
          <Button type="link" @click="cancelRowEvent(row)">取消</Button>
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">编辑</Button>
        </template>
      </template>
    </Grid>
  </Page>
</template>
