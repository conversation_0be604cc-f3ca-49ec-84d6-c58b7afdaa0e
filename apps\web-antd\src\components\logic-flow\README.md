# 审批工作流组件

基于LogicFlow的制药质控LIMS系统审批工作流设计器组件，使用Vue 3 + TypeScript + Ant Design Vue技术栈开发。

## ✨ 特性

- 🎨 **数据驱动渲染** - 接收JSON格式的工作流数据，自动生成可视化流程图
- 📤 **数据导出** - 支持将当前流程图状态导出为JSON格式，便于数据持久化
- ✏️ **交互式编辑** - 支持添加/删除节点、编辑节点属性、连接线的创建和删除、拖拽调整节点位置
- 👁️ **双模式支持** - 编辑模式和查看模式，满足不同场景需求
- 🔍 **流程验证** - 检查流程的完整性和逻辑正确性
- 📋 **节点模板** - 预设常用的审批节点类型
- ↩️ **历史记录** - 支持撤销/重做操作
- 🔍 **缩放和平移** - 大型流程图的导航功能

## 🚀 快速开始

### 基础用法

```vue
<template>
  <WorkflowDesigner
    v-model:flow-data="flowData"
    :mode="mode"
    :approver-data-source="approverDataSource"
    @node-select="handleNodeSelect"
    @validate="handleValidate"
  />
</template>

<script setup>
import { ref } from 'vue';
import { WorkflowDesigner } from '@/components/logic-flow';

const flowData = ref({
  nodes: [],
  edges: []
});

const mode = ref('edit');

const approverDataSource = [
  { id: '1', name: '张三', department: '质控部' },
  { id: '2', name: '李四', department: '质控部' },
];

const handleNodeSelect = (node) => {
  console.log('选中节点:', node);
};

const handleValidate = (result) => {
  console.log('验证结果:', result);
};
</script>
```

### 高级用法

```vue
<template>
  <WorkflowDesigner
    ref="workflowRef"
    v-model:flow-data="flowData"
    :mode="mode"
    :readonly="readonly"
    :config="config"
    :approver-data-source="approverDataSource"
    @node-add="handleNodeAdd"
    @node-update="handleNodeUpdate"
    @edge-add="handleEdgeAdd"
    @validate="handleValidate"
  />
</template>

<script setup>
import { ref, onMounted } from 'vue';

const workflowRef = ref();
const config = {
  width: 1200,
  height: 800,
  background: '#f0f2f5',
  grid: true,
  miniMap: true,
};

// 使用组件方法
onMounted(() => {
  // 验证工作流
  const result = workflowRef.value.validate();
  
  // 导出数据
  const jsonData = workflowRef.value.exportData('json');
  
  // 适应画布
  workflowRef.value.fitView();
});
</script>
```

## 📚 API 文档

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| flowData | `WorkflowData` | `{}` | 工作流数据 |
| mode | `'edit' \| 'view'` | `'edit'` | 工作模式 |
| readonly | `boolean` | `false` | 是否只读 |
| config | `Object` | `{}` | 画布配置 |
| approverDataSource | `Approver[]` | `[]` | 审批人数据源 |

### Events

| 事件 | 参数 | 说明 |
|------|------|------|
| update:flowData | `data: WorkflowData` | 数据变更时触发 |
| node-select | `node: WorkflowNode \| null` | 节点选择时触发 |
| edge-select | `edge: WorkflowEdge \| null` | 连线选择时触发 |
| node-add | `node: WorkflowNode` | 节点添加时触发 |
| node-update | `node: WorkflowNode` | 节点更新时触发 |
| edge-add | `edge: WorkflowEdge` | 连线添加时触发 |
| validate | `result: ValidationResult` | 验证时触发 |

### Methods

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| getFlowData | - | `WorkflowData` | 获取工作流数据 |
| setFlowData | `data: WorkflowData` | `void` | 设置工作流数据 |
| validate | - | `ValidationResult` | 验证工作流 |
| exportData | `format?: string` | `string` | 导出数据 |
| importData | `data: string, format?: string` | `boolean` | 导入数据 |
| clear | - | `void` | 清空画布 |
| fitView | - | `void` | 适应画布 |
| zoom | `ratio: number` | `void` | 缩放 |
| undo | - | `void` | 撤销 |
| redo | - | `void` | 重做 |

## 🎯 节点类型

### 开始节点 (start)
- 工作流的起始点
- 不能作为连线的终点
- 每个工作流只能有一个开始节点

### 审批节点 (approval)
- 需要人工审批的节点
- 可配置审批人、审批类型、时限等
- 支持单人审批、全部审批、多数审批

### 条件节点 (condition)
- 根据条件进行分支判断
- 支持多种操作符：等于、大于、包含等
- 可配置多个条件规则

### 并行节点 (parallel)
- 同时执行多个分支
- 可配置分支名称
- 用于并行处理场景

### 合并节点 (merge)
- 等待所有分支完成后继续
- 与并行节点配合使用
- 用于同步多个分支

### 结束节点 (end)
- 工作流的结束点
- 不能作为连线的起点
- 可以有多个结束节点

## 🔧 配置选项

### 画布配置

```typescript
const config = {
  width: 1200,           // 画布宽度
  height: 800,           // 画布高度
  background: '#f7f9ff', // 背景色
  grid: true,            // 是否显示网格
  miniMap: true,         // 是否显示缩略图
  keyboard: true,        // 是否启用键盘快捷键
};
```

### 审批人数据结构

```typescript
interface Approver {
  id: string;
  name: string;
  email?: string;
  department?: string;
  role?: string;
}
```

### 工作流数据结构

```typescript
interface WorkflowData {
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
}
```

## 🎨 主题定制

组件使用Ant Design Vue的主题系统，可以通过CSS变量进行定制：

```css
:root {
  --workflow-primary-color: #1890ff;
  --workflow-success-color: #52c41a;
  --workflow-warning-color: #fa8c16;
  --workflow-error-color: #f5222d;
}
```

## 📝 示例模板

组件内置了多个工作流模板：

1. **简单审批流程** - 包含开始、审批、结束的基础流程
2. **条件审批流程** - 根据条件进行不同的审批路径
3. **并行审批流程** - 多个部门同时审批

## 🔍 验证规则

组件内置了完整的验证系统：

- **基础结构验证** - 检查开始节点、结束节点
- **节点属性验证** - 检查必填属性、数据格式
- **连线规则验证** - 检查连接规则、循环依赖
- **流程完整性验证** - 检查孤立节点、可达性

## 🚀 性能优化

- 虚拟滚动支持大量节点
- 按需加载节点组件
- 防抖处理频繁操作
- 内存泄漏防护

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

MIT License
