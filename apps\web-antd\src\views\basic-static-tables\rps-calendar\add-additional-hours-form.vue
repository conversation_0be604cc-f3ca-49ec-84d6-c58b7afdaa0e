<script lang="ts" setup>
import type { RpswaCalendarApi } from '#/api/basic-static-tables/rps-calendar';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { $addAdditionalHoursApi } from '#/api/basic-static-tables/rps-calendar';
import { $t } from '#/locales';

import { useAdditionalHoursSchema } from './rps-calendar-data';

const emit = defineEmits(['success']);

const formData = ref();
const getTitle = computed(() => {
  return $t('ui.actionTitle.create', [
    $t('basic-static-tables.rpswaCalendar.additionalWorkHours'),
  ]);
});

const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'vertical',
  schema: useAdditionalHoursSchema(),
  showDefaultActions: false,
});

function resetForm() {
  formApi.resetForm();
  formApi.setValues(formData.value || {});
}

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    data.CALENDAR_ORIGREC = formData.value?.calendarId;
    data.TYPE = formData.value?.type;
    modalApi.lock();
    try {
      await $addAdditionalHoursApi(data);
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },

  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<RpswaCalendarApi.RpswaCalendarTree>();
      if (data) {
        formData.value = data;
        // formApi.setValues(formData.value);
      }
    }
  },
});
</script>

<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
    <template #prepend-footer>
      <div class="flex-auto">
        <Button type="primary" danger @click="resetForm">
          {{ $t('common.reset') }}
        </Button>
      </div>
    </template>
  </Modal>
</template>
