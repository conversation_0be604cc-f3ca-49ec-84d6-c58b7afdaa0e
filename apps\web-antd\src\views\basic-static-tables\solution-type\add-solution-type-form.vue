<script lang="ts" setup>
import { computed } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import { $addSolutionTyoeApi } from '#/api/basic-static-tables/solution-type';
import { $t } from '#/locales';

import { useSchema } from './solution-type-data';

const emit = defineEmits(['success']);
const getTitle = computed(() => {
  return $t('ui.actionTitle.create', [$t('basicStatic.regions.title')]);
});
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: useSchema(),
  showDefaultActions: false,
});
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    modalApi.lock();
    try {
      data.ISDAY = data.ISDAY ? 'Y' : 'N';
      await $addSolutionTyoeApi(data);
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      // const data = modalApi.getData<SampleTypeApi.SampleType>();
    }
  },
});
</script>

<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
  </Modal>
</template>
