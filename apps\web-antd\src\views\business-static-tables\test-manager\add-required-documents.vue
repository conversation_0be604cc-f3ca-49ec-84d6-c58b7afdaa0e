<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import { $addRequiredDocumentsApi } from '#/api/business-static-tables';
import { $t } from '#/locales';

interface FormArgs {
  method: string;
  testCode: number;
}
const emit = defineEmits(['success']);
const formAgrs = ref<FormArgs>();
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: [
    {
      fieldName: 'NAME',
      component: 'Input',
      componentProps: {},
      label: $t('business-static-tables.testManager.docName'),
      rules: 'required',
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    modalApi.lock();
    try {
      data.TESTCODE = formAgrs.value?.testCode;
      data.METHOD = formAgrs.value?.method;
      await $addRequiredDocumentsApi(data);
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<FormArgs>();
      if (data) {
        formAgrs.value = data;
      }
    }
  },
  title: $t('ui.actionTitle.create', [
    $t('business-static-tables.testManager.relatedFile'),
  ]),
});
</script>
<template>
  <Modal>
    <Form class="mx-4" />
  </Modal>
</template>
