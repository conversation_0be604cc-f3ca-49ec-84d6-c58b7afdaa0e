import { callServer, getDataSet, getDataSetNoPage } from '#/api/core/witlab';

export namespace ChsEngTranslateApi {
  export interface ChsEngTranslate {
    [key: string]: any;
    ORIGREC: number;
    TYPE: string;
    CHSVAL: string;
    ENGVAL: string;
  }
}

const $getTransDataApi = async () => {
  const data = await getDataSet('Translate.GetTransData', []);
  return data;
};

const $addTransDataApi = async (
  data: Omit<ChsEngTranslateApi.ChsEngTranslate, 'ORIGREC'>,
) => {
  const res = await callServer('Translate.AddTranslate', [
    data.CHSVAL,
    data.ENGVAL,
    data.TYPE,
  ]);
  return res;
};
const $deleteTransDataApi = async (origrec: number[]) => {
  const res = await callServer('Translate.DelTranslate', [origrec]);
  return res;
};

const $checkExistsTransDataApi = async (data: string) => {
  const res = await callServer('Translate.CheckExistsTransData', [data]);
  return res;
};

const $getTransDataApiTypeApi = async () => {
  const data = await getDataSetNoPage('Translate.GetTranslateType', []);
  return data;
};

export {
  $addTransDataApi,
  $checkExistsTransDataApi,
  $deleteTransDataApi,
  $getTransDataApi,
  $getTransDataApiTypeApi,
};
