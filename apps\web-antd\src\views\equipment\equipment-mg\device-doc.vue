<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { EquipmentMgApi } from '#/api/equipment/equipment-mg';

import { computed, watch } from 'vue';

import { Button, message, Modal, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteDocumentationApi,
  getDocumentationApi,
} from '#/api/equipment/equipment-mg';
import { $t } from '#/locales';
import { useEquipmentStore } from '#/store';

import { documentColumns } from './equipment-mg-data';

interface RowType {
  [key: string]: any;
}
const equipmentStore = useEquipmentStore();
const currentRow: EquipmentMgApi.RowType = computed(
  () => equipmentStore.getCurrentRow,
);
watch(
  currentRow,
  async (newRow: RowType) => {
    if (newRow) {
      gridApi.query();
    }
  },
  { deep: true },
);
const gridOptions: VxeTableGridOptions<EquipmentMgApi.MetaDataEquipment> = {
  columns: documentColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!currentRow.value) {
          return [];
        }
        const data = await getDocumentationApi([currentRow.value.EQID]);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: {},
});
const hasEditStatus = (row: EquipmentMgApi.RowType) => {
  return gridApi.grid?.isEditByRow(row);
};
const editRowEvent = (row: EquipmentMgApi.RowType) => {
  gridApi.grid?.setEditRow(row);
};

const saveRowEvent = async (row: EquipmentMgApi.RowType) => {
  await gridApi.grid?.clearEdit();
  console.warn(row);
  gridApi.setLoading(true);
  setTimeout(() => {
    gridApi.setLoading(false);
  }, 600);
};
const cancelRowEvent = (_row: EquipmentMgApi.RowType) => {
  gridApi.grid?.clearEdit();
};
const onAdd = () => {
  // TODO: 上传文档
};
const onDelete = () => {
  const checkRows = gridApi.grid.getCheckboxRecords();
  if (checkRows.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }
  if (checkRows.length > 1) {
    message.warning('只能选择一条数据！');
    return;
  }
  Modal.confirm({
    title: '询问',
    content: '确实要删除所选文档吗?',
    cancelText: '否',
    okText: '是',
    async onOk() {
      await deleteDocumentationApi(checkRows[0].ORIGREC);
      message.success('删除成功！');
      gridApi.query();
    },
  });
};
const viewDocument = () => {
  // TODO 文档下载
};
</script>
<template>
  <div class="h-screen">
    <Grid class="h-5/6">
      <template #toolbar-actions>
        <Space :size="[8, 0]" wrap>
          <Button type="primary" @click="onAdd">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="onDelete">
            {{ $t('ui.actionTitle.delete') }}
          </Button>
          <Button type="default" @click="viewDocument">
            {{ $t('equipment.equipment-mg.viewDocument') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent(row)">
            {{ $t('basic-static-tables.save') }}
          </Button>
          <Button type="link" @click="cancelRowEvent(row)">
            {{ $t('equipment.cancel') }}
          </Button>
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">
            {{ $t('basic-static-tables.edit') }}
          </Button>
        </template>
      </template>
    </Grid>
  </div>
</template>
