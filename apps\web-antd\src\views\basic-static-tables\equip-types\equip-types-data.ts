import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { EquipmentTypeApi } from '#/api/basic-static-tables/equip-types';

import { $t } from '#/locales';

export function useEquipmentTypeColumns(): VxeTableGridOptions<EquipmentTypeApi.EquipmentType>['columns'] {
  return [
    { field: 'select', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'EQTYPE',
      title: $t('basic-static-tables.equip-types.eqtype'),
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'DESCRIPTION',
      title: $t('basic-static-tables.equip-types.description'),
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'IS_SAMECALL',
      title: $t('basic-static-tables.equip-types.is_samecall'),
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue === 'Y' ? '是' : '否';
      },
    },

    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('basic-static-tables.operation'),
      width: 140,
    },
  ];
}

export function useEquipmentTypeFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        // placeholder: 'Please enter code',
      },
      fieldName: 'EQTYPE',
      label: $t('basic-static-tables.equip-types.eqtype'),
    },
    {
      component: 'Input',
      fieldName: 'DESCRIPTION',
      componentProps: {
        // placeholder: 'Please enter name',
      },
      label: $t('basic-static-tables.equip-types.description'),
    },
  ];
}

export function useEquipmentEventColumns(): VxeTableGridOptions<EquipmentTypeApi.EquipmentEvent>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'MAINTENANCEEVENT',
      title: $t('basic-static-tables.equip-events.maintenanceevent'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'DESCRIPTION',
      title: $t('basic-static-tables.equip-events.description'),
      minWidth: 200,
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'BINDING_STATUS',
      title: $t('basic-static-tables.equip-events.binding_status'),
      minWidth: 150,
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue === 'Y' ? '是' : '否';
      },
    },
    {
      align: 'center',
      field: 'REMINDERWINDOW',
      title: $t('basic-static-tables.equip-events.reminderWindow'),
      minWidth: 150,
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ELN_ID',
      title: $t('basic-static-tables.equip-events.eln_id'),
      minWidth: 150,
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },

    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('basic-static-tables.operation'),
      minWidth: 140,
    },
  ];
}
