<script setup lang="ts">
import type { SampleGroupsApi } from '#/api/business-static-tables/sample-groups';

import { ref, watch } from 'vue';

import { confirm, useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import {
  deleteSpecFromSpCode,
  getSpecsList,
  insertTestsByProfile,
} from '#/api/business-static-tables/sample-groups';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import AddApecificationForm from '../process-specifications/add-specification.vue';
import { useSpecsColumns, useSpecsFilterSchema } from './sample-groups-data';
import SpecificationForm from './specification.vue';

const props = defineProps<{
  spCode: number;
}>();

const emit = defineEmits(['success']);

const sTable = ref<string>('SP_SPECS');
const sField = ref<string>('SP_CODE');
const specNo = ref<number>(0);

watch(
  () => props.spCode,
  (_val) => {
    // console.log(props.spCode);
    onRefresh();
  },
);
const colums = useSpecsColumns();
const filterSchema = useSpecsFilterSchema();
const queryData = async () => {
  if (!props.spCode) return [];
  return await getSpecsList(
    sTable.value,
    props.spCode.toString(),
    '',
    false,
    sField.value,
  );
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};

const {
  Grid: SpecsGrid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
} = useLimsGridsConfig<SampleGroupsApi.Specs>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

function onRefresh() {
  gridApi.query();
}

const [AddFormModal, addFormModalApi] = useVbenModal({
  connectedComponent: AddApecificationForm,
  destroyOnClose: true,
});

function onCreate() {
  addFormModalApi.open();
}

async function onDelete() {
  // 获取选中行
  const sample = gridApi.grid?.getCurrentRecord();
  if (!sample) return;

  if (!props.spCode) return;
  const speco = sample.SPECIFICATION_O;
  const sSpecNo = sample.SPECNO;

  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的数据吗？`,
      icon: 'warning',
      centered: false,
    });

    const sRet = await deleteSpecFromSpCode(
      sTable.value,
      props.spCode.toString(),
      speco,
      sSpecNo,
    );
    if (!sRet[0] && sRet[1] === 'SpecificationAssigned') {
      if (sTable.value === 'SP_SPECS') {
        message.warning(
          $t(
            'business-static-tables.sampleGroups.sSpecificationAssignedToSample',
          ),
        );
        return;
      }
      if (sTable.value === 'IPSAMPLEGROUPSPECS') {
        message.warning(
          $t(
            'business-static-tables.sampleGroups.sSpecificationAssignedToTemplate',
          ),
        );
        return;
      }
    }
    message.success('删除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: SpecificationForm,
  destroyOnClose: true,
});

// 选择现有的
async function onSelectExist() {
  if (props.spCode === null) return;
  formModalApi
    .setData({
      SP_CODE: props.spCode,
    })
    .open();
}

async function onNext() {
  const dataList = gridApi.grid.getData();
  if (dataList.length === 0) {
    message.warn($t('business-static-tables.sampleGroups.NoSpecs'));
    return;
  }
  for (const item of dataList) {
    if (item.PRIMARY_SPEC === 'Y') {
      specNo.value = item.SPECNO;
      break;
    }
  }

  const addTests = await insertTestsByProfile(
    props.spCode,
    specNo.value,
    1,
    'Default',
  );
  if (addTests === 'EXISTS') {
    message.warn($t('business-static-tables.sampleGroups.profileExists'));
  } else {
    if (addTests === 'SAMPLES') {
      message.warn($t('business-static-tables.sampleGroups.SAMPLES'));
    }

    if (addTests === 'OTHER_PROFILE') {
      message.warn($t('business-static-tables.sampleGroups.OTHER_PROFILE'));
    }
  }
  emit('success');
}
</script>

<template>
  <AddFormModal @success="onRefresh" />
  <FormModal @success="onRefresh" />
  <div class="h-[550px] w-full">
    <SpecsGrid>
      <template #toolbar-actions>
        <Space>
          <Button type="primary" @click="onCreate">
            {{ $t('business-static-tables.sampleGroups.addNewSpec') }}
          </Button>
          <Button type="primary" danger @click="onDelete">
            {{ $t('business-static-tables.sampleGroups.deleteSpec') }}
          </Button>
          <Button type="default" @click="onSelectExist">
            {{ $t('business-static-tables.sampleGroups.selectExist') }}
          </Button>
          <Button type="default">
            {{ $t('business-static-tables.sampleGroups.viewSpecs') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent(row)">
            {{ $t('business-static-tables.save') }}
          </Button>
          <Button type="link" @click="cancelRowEvent(row)">
            {{ $t('business-static-tables.cancel') }}
          </Button>
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">
            {{ $t('business-static-tables.edit') }}
          </Button>
        </template>
      </template>
    </SpecsGrid>
    <div class="mt-4 flex justify-end">
      <Button type="primary" @click="onNext"> 下一步 </Button>
    </div>
  </div>
</template>
