import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { CourseFormApi } from '#/api/equipment/course-form';

import { $t } from '#/locales';

export function courseColumns(): VxeTableGridOptions<CourseFormApi.CourseForm>['columns'] {
  return [
    {
      align: 'center',
      field: 'COURSECODE',
      title: $t('equipment.course-form.courseCode'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'COURSENAME',
      title: $t('equipment.course-form.courseName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'OFFEREDBY',
      title: $t('equipment.course-form.provider'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: { name: 'input' },
      sortable: true,
    },
    {
      align: 'center',
      field: 'COURSECOST',
      title: $t('equipment.course-form.fee'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}

export function methodColumns(): VxeTableGridOptions<CourseFormApi.CourseForm>['columns'] {
  return [
    {
      align: 'center',
      field: 'METHOD',
      title: $t('equipment.course-form.method'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'TESTNO',
      title: $t('equipment.course-form.testName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SERVGRP',
      title: $t('equipment.course-form.position'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}

export function courseSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'courseCode',
      rules: 'required',
      label: $t('equipment.course-form.courseCode'),
    },
    {
      component: 'Input',
      fieldName: 'courseName',
      label: $t('equipment.course-form.courseName'),
      rules: 'required',
    },

    {
      component: 'Input',
      fieldName: 'offeredby',
      label: $t('equipment.course-form.provider'),
    },
    {
      component: 'Input',
      fieldName: 'cost',
      label: $t('equipment.course-form.fee'),
    },
  ];
}
