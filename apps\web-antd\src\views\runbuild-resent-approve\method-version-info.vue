<script lang="ts" setup>
import type { MethodManagerApi } from '#/api/business-static-tables';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { $getTestMethodVersionInfoApi } from '#/api/runbuild-resent-approve';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import { useMethodRelateTestColumns } from './method-version-info-data';

// const props = defineProps<{
//   method: string;
// }>();
const method = ref<string>();
const colums = useMethodRelateTestColumns();
const queryData = async () => {
  if (!method.value) return [];
  return $getTestMethodVersionInfoApi(method.value);
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    enabled: false,
  },
};

const { Grid: MethodVersionGrid } =
  useLimsGridsConfig<MethodManagerApi.TestMethodVersion>(
    colums,
    [],
    queryData,
    girdOption,
  );

const [MethodVersionInfoModal, methodVersionInfoModalApi] = useVbenModal({
  showConfirmButton: false,
  cancelText: $t('commons.close'),
  showCancelButton: true,
  destroyOnClose: true,
  onCancel() {
    methodVersionInfoModalApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      // 当模态框打开时，重新获取数据
      const data = methodVersionInfoModalApi.getData();
      if (data) {
        method.value = data.method;
      }
    }
  },
});
</script>

<template>
  <MethodVersionInfoModal
    :title="$t('business-static-tables.methodVersionManager.methodVersion')"
  >
    <Page>
      <MethodVersionGrid />
    </Page>
  </MethodVersionInfoModal>
</template>
