import { callServer, getDataSet } from '#/api/core/witlab';
import { requestClient } from '#/api/request';

export namespace EquipmentTypeApi {
  export interface PageFetchParams {
    [key: string]: any;
    page: number;
    pageSize: number;
  }

  export interface EquipmentType {
    ORIGREC: number;
    EQTYPE: string;
    DESCRIPTION: string;
    METADATA_TEMPLATE_EQUIPMENT: string;
    METADATA_TEMPLATE: string;
    IS_SAMECALL: boolean;
  }

  export interface EquipmentEvent {
    ORIGREC: number;
    EQTYPE: string;
    MAINTENANCEEVENT: string;
    DESCRIPTION: string;
    BINDING_STATUS: string;
    REMINDERWINDOW: number;
    EQUIPMENT_METHOD: string;
    ELN_ID: string;
  }
}

/**
 * 获取设备类型列表数据
 */
async function getEquipmentTypeList() {
  return getDataSet('EQUIPMENT_TYPES.DS_EQUIPMENTTYPES_TYPESGRID', []);
}

/**
 * 添加设备类型
 * @param data 设备类型数据
 */
async function addEquipmentType(data: EquipmentTypeApi.EquipmentType) {
  return await callServer('EQUIPMENT_TYPES.ADD_EQTYPE', [
    data.EQTYPE,
    data.DESCRIPTION,
    'AddEquipType',
  ]);
}

/**
 * 更新设备类型
 *
 * @param data 设备类型数据
 * @returns boolean
 */
async function updateEquipmentType(data: EquipmentTypeApi.EquipmentType) {
  return requestClient.post<boolean>('/equip-types/updateEquipmentType', data);
}

/**
 * 删除设备类型
 *
 * @param origrec 设备类型数据
 * @returns boolean
 */
async function deleteEquipmentType(eqType: string[]) {
  return await callServer('EQUIPMENT_TYPES.DEL_EQTYPE', [eqType]);
}

/**
 * 根据设备类型获取设备维护事件列表数据
 */
async function getEquipmentEventList(eqType: string) {
  return getDataSet('EQUIPMENT_TYPES.DS_EQUIPMENTMAINTENANCE', [eqType]);
}

/**
 * 添加设备维护事件
 * @param data 设备维护事件数据
 */
async function addEquipmentEvent(data: EquipmentTypeApi.EquipmentEvent) {
  return await callServer('EQUIPMENT_TYPES.ADD_MAINT', [
    data.EQTYPE,
    data.MAINTENANCEEVENT,
    data.DESCRIPTION,
    data.REMINDERWINDOW,
    data.EQUIPMENT_METHOD,
  ]);
}

/**
 * 更新设备维护事件
 *
 * @param data 设备维护事件数据
 * @returns boolean
 */
async function updateEquipmentEvent(data: EquipmentTypeApi.EquipmentEvent) {
  return requestClient.post<boolean>('/equip-types/updateEquipmentEvent', data);
}

/**
 * 删除设备维护事件
 *
 * @param origrec 设备维护事件数据
 * @returns boolean
 */
async function deleteEquipmentEvent(
  origrec: number,
  sMaintEvent: string,
  sEqType: string,
) {
  return await callServer('EQUIPMENT_TYPES.DELETE_MAINT', [
    origrec,
    sMaintEvent,
    sEqType,
  ]);
}

export {
  addEquipmentEvent,
  addEquipmentType,
  deleteEquipmentEvent,
  deleteEquipmentType,
  getEquipmentEventList,
  getEquipmentTypeList,
  updateEquipmentEvent,
  updateEquipmentType,
};
