<script setup lang="ts">
import type {
  ValidationR<PERSON>ult,
  WorkflowComponentEvents,
  WorkflowComponentProps,
  WorkflowData,
  WorkflowEdge,
  WorkflowNode,
} from './types/workflow';

import { computed, nextTick, onMounted, ref, watch } from 'vue';

import LogicFlowCore from '@logicflow/core';
import { Menu, MiniMap, Snapshot } from '@logicflow/extension';
import { message } from 'ant-design-vue';

import WorkflowNodePanel from './components/WorkflowNodePanel.vue';
import WorkflowPropertyPanel from './components/WorkflowPropertyPanel.vue';
import { DEFAULT_WORKFLOW_DATA, getNodePanelConfig } from './config/nodes';
import { validateWorkflow } from './utils/validator';

import '@logicflow/core/dist/style/index.css';
import '@logicflow/extension/lib/style/index.css';

// Props
interface Props extends WorkflowComponentProps {}

// Events
interface Events extends WorkflowComponentEvents {
  nodeSelect: [node: WorkflowNode];
  edgeSelect: [edge: WorkflowEdge];
  nodeAdd: [node: WorkflowNode];
  nodeDelete: [nodeId: string];
  edgeAdd: [edge: WorkflowEdge];
  edgeDelete: [edgeId: string];
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'edit',
  readonly: false,
  config: () => ({
    width: undefined,
    height: undefined,
    background: '#f7f9ff',
    grid: true,
    miniMap: true,
    keyboard: true,
  }),
  flowData: () => DEFAULT_WORKFLOW_DATA,
  nodeTemplates: () => [],
  approverDataSource: () => [],
});

const emit = defineEmits<Events>();

// 响应式数据
const canvasContainer = ref<HTMLDivElement>();
const lf = ref<LogicFlowCore>();
const currentFlowData = ref<WorkflowData>(props.flowData);
const canUndo = ref(false);
const canRedo = ref(false);

// UI状态
const propertyDrawerVisible = ref(false);
const showDataModal = ref(false);
const showValidationModal = ref(false);
const selectedElement = ref<null | WorkflowEdge | WorkflowNode>(null);
const selectedElementType = ref<'edge' | 'node' | null>(null);
const validationResult = ref<null | ValidationResult>(null);

// 配置数据
const nodeConfigs = computed(() => getNodePanelConfig());

// 表格列定义
const nodeColumns = [
  { title: 'ID', dataIndex: 'id', key: 'id' },
  { title: '类型', dataIndex: 'type', key: 'type' },
  { title: '名称', dataIndex: ['properties', 'name'], key: 'name' },
  { title: 'X坐标', dataIndex: 'x', key: 'x' },
  { title: 'Y坐标', dataIndex: 'y', key: 'y' },
];

const edgeColumns = [
  { title: 'ID', dataIndex: 'id', key: 'id' },
  { title: '类型', dataIndex: 'type', key: 'type' },
  { title: '源节点', dataIndex: 'sourceNodeId', key: 'sourceNodeId' },
  { title: '目标节点', dataIndex: 'targetNodeId', key: 'targetNodeId' },
];

// 生命周期
onMounted(() => {
  initLogicFlow();
});

// 监听数据变化
watch(
  () => props.flowData,
  (newData) => {
    if (newData && lf.value) {
      currentFlowData.value = newData;
      lf.value.render(newData);
    }
  },
  { deep: true },
);

// 方法
const initLogicFlow = async () => {
  if (!canvasContainer.value) return;

  await nextTick();

  const lfInstance = new LogicFlowCore({
    container: canvasContainer.value,
    width: props.config?.width,
    height: props.config?.height,
    background: {
      backgroundColor: props.config?.background || '#f7f9ff',
    },
    grid: {
      size: 10,
      visible: props.config?.grid ?? true,
    },
    keyboard: {
      enabled: props.config?.keyboard ?? true,
    },
    plugins: [Menu, MiniMap, Snapshot],
    edgeTextDraggable: true,
    hoverOutline: false,
    // 只读模式配置
    isSilentMode: props.readonly,
    stopScrollGraph: props.readonly,
    stopZoomGraph: props.readonly,
    stopMoveGraph: props.readonly,
  });

  lf.value = lfInstance;

  // 注册自定义节点
  registerCustomNodes();

  // 设置主题
  setTheme();

  // 绑定事件
  bindEvents();

  // 渲染初始数据
  if (currentFlowData.value) {
    lfInstance.render(currentFlowData.value);
  }
};

const registerCustomNodes = () => {
  if (!lf.value) return;

  // 注册开始节点
  lf.value.register('start', ({ CircleNode, CircleNodeModel, h }) => {
    class StartNode extends CircleNode {
      getShape() {
        const { model } = this.props;
        const { x, y, r } = model;
        const { fill, stroke, strokeWidth } = model.getNodeStyle();

        return h('g', {}, [
          h('circle', {
            cx: x,
            cy: y,
            r,
            fill,
            stroke,
            strokeWidth,
          }),
          h(
            'text',
            {
              x,
              y: y + 4,
              textAnchor: 'middle',
              fill: '#fff',
              fontSize: 12,
            },
            '开始',
          ),
        ]);
      }
    }

    class StartModel extends CircleNodeModel {
      getConnectedTargetRules() {
        const rules = super.getConnectedTargetRules();
        rules.push({
          message: '开始节点不能作为连线的终点',
          validate: () => false,
        });
        return rules;
      }

      getNodeStyle() {
        const style = super.getNodeStyle();
        style.fill = '#52c41a';
        style.stroke = '#389e0d';
        return style;
      }

      initNodeData(data: any) {
        data.text = {
          value: data.properties?.name || '开始',
          x: data.x,
          y: data.y + 35,
          draggable: false,
          editable: true,
        };
        super.initNodeData(data);
        this.r = 20;
      }
    }

    return { view: StartNode, model: StartModel };
  });

  // 注册结束节点
  lf.value.register('end', ({ CircleNode, CircleNodeModel, h }) => {
    class EndNode extends CircleNode {
      getShape() {
        const { model } = this.props;
        const { x, y, r } = model;
        const { fill, stroke, strokeWidth } = model.getNodeStyle();

        return h('g', {}, [
          h('circle', {
            cx: x,
            cy: y,
            r,
            fill,
            stroke,
            strokeWidth,
          }),
          h(
            'text',
            {
              x,
              y: y + 4,
              textAnchor: 'middle',
              fill: '#fff',
              fontSize: 12,
            },
            '结束',
          ),
        ]);
      }
    }

    class EndModel extends CircleNodeModel {
      getConnectedSourceRules() {
        const rules = super.getConnectedSourceRules();
        rules.push({
          message: '结束节点不能作为连线的起点',
          validate: () => false,
        });
        return rules;
      }

      getNodeStyle() {
        const style = super.getNodeStyle();
        style.fill = '#f5222d';
        style.stroke = '#cf1322';
        return style;
      }

      initNodeData(data: any) {
        data.text = {
          value: data.properties?.name || '结束',
          x: data.x,
          y: data.y + 35,
          draggable: false,
          editable: true,
        };
        super.initNodeData(data);
        this.r = 20;
      }
    }

    return { view: EndNode, model: EndModel };
  });

  // 注册审批节点
  lf.value.register('approval', ({ RectNode, RectNodeModel, h }) => {
    class ApprovalNode extends RectNode {
      getShape() {
        const { model } = this.props;
        const { x, y, width, height } = model;
        const { fill, stroke, strokeWidth } = model.getNodeStyle();

        return h('g', {}, [
          h('rect', {
            x: x - width / 2,
            y: y - height / 2,
            width,
            height,
            fill,
            stroke,
            strokeWidth,
            rx: 4,
            ry: 4,
          }),
          h(
            'text',
            {
              x,
              y: y + 4,
              textAnchor: 'middle',
              fill: '#fff',
              fontSize: 12,
            },
            '审批',
          ),
        ]);
      }
    }

    class ApprovalModel extends RectNodeModel {
      getNodeStyle() {
        const style = super.getNodeStyle();
        style.fill = '#1890ff';
        style.stroke = '#096dd9';
        return style;
      }

      initNodeData(data: any) {
        data.text = {
          value: data.properties?.name || '审批',
          x: data.x,
          y: data.y + 35,
          draggable: false,
          editable: true,
        };
        super.initNodeData(data);
        this.width = 80;
        this.height = 40;
      }
    }

    return { view: ApprovalNode, model: ApprovalModel };
  });

  // 注册条件节点
  lf.value.register('condition', ({ PolygonNode, PolygonNodeModel, h }) => {
    class ConditionNode extends PolygonNode {
      getShape() {
        const { model } = this.props;
        const { x, y, points } = model;
        const { fill, stroke, strokeWidth } = model.getNodeStyle();

        return h('g', {}, [
          h('polygon', {
            points: points.map((point) => `${point[0]},${point[1]}`).join(' '),
            fill,
            stroke,
            strokeWidth,
          }),
          h(
            'text',
            {
              x,
              y: y + 4,
              textAnchor: 'middle',
              fill: '#fff',
              fontSize: 12,
            },
            '条件',
          ),
        ]);
      }
    }

    class ConditionModel extends PolygonNodeModel {
      getNodeStyle() {
        const style = super.getNodeStyle();
        style.fill = '#fa8c16';
        style.stroke = '#d46b08';
        return style;
      }

      initNodeData(data: any) {
        data.text = {
          value: data.properties?.name || '条件',
          x: data.x,
          y: data.y + 35,
          draggable: false,
          editable: true,
        };
        super.initNodeData(data);
        // 菱形形状
        this.points = [
          [data.x, data.y - 25],
          [data.x + 40, data.y],
          [data.x, data.y + 25],
          [data.x - 40, data.y],
        ];
      }
    }

    return { view: ConditionNode, model: ConditionModel };
  });
};

const setTheme = () => {
  if (!lf.value) return;

  lf.value.setTheme({
    circle: {
      stroke: '#000000',
      strokeWidth: 1,
      outlineColor: '#88f',
    },
    rect: {
      outlineColor: '#88f',
      strokeWidth: 1,
    },
    polygon: {
      strokeWidth: 1,
      outlineColor: '#88f',
    },
    polyline: {
      stroke: '#000000',
      hoverStroke: '#000000',
      selectedStroke: '#000000',
      outlineColor: '#88f',
      strokeWidth: 1,
    },
    nodeText: {
      color: '#000000',
      fontSize: 12,
    },
    edgeText: {
      color: '#000000',
      fontSize: 12,
      textWidth: 100,
      background: {
        fill: '#f7f9ff',
      },
    },
  });
};

const bindEvents = () => {
  if (!lf.value) return;

  // 节点点击事件
  lf.value.on('node:click', ({ data }) => {
    selectedElement.value = data as WorkflowNode;
    selectedElementType.value = 'node';
    propertyDrawerVisible.value = true;
    emit('node-select', data as WorkflowNode);
  });

  // 连线点击事件
  lf.value.on('edge:click', ({ data }) => {
    selectedElement.value = data as WorkflowEdge;
    selectedElementType.value = 'edge';
    propertyDrawerVisible.value = true;
    emit('edge-select', data as WorkflowEdge);
  });

  // 空白区域点击事件
  lf.value.on('blank:click', () => {
    selectedElement.value = null;
    selectedElementType.value = null;
    propertyDrawerVisible.value = false;
    emit('node-select', null);
    emit('edge-select', null);
  });

  // 节点添加事件
  lf.value.on('node:add', ({ data }) => {
    updateFlowData();
    emit('node-add', data as WorkflowNode);
  });

  // 节点删除事件
  lf.value.on('node:delete', ({ data }) => {
    updateFlowData();
    emit('node-delete', data.id);
  });

  // 连线添加事件
  lf.value.on('edge:add', ({ data }) => {
    updateFlowData();
    emit('edge-add', data as WorkflowEdge);
  });

  // 连线删除事件
  lf.value.on('edge:delete', ({ data }) => {
    updateFlowData();
    emit('edge-delete', data.id);
  });

  // 历史记录变化事件
  lf.value.on('history:change', ({ data: { undoAble, redoAble } }) => {
    canUndo.value = undoAble;
    canRedo.value = redoAble;
  });

  // 连接验证失败事件
  lf.value.on('connection:not-allowed', (data) => {
    message.error(data.msg);
  });
};

// 工具栏方法
const undo = () => {
  lf.value?.undo();
};

const redo = () => {
  lf.value?.redo();
};

const zoomIn = () => {
  lf.value?.zoom(true);
};

const zoomOut = () => {
  lf.value?.zoom(false);
};

const fitView = () => {
  lf.value?.fitView();
};

const showMiniMap = () => {
  if (lf.value?.extension?.miniMap) {
    lf.value.extension.miniMap.show(lf.value.graphModel.width - 150, 40);
  }
};

const validateFlow = () => {
  validationResult.value = validateWorkflow(currentFlowData.value);
  showValidationModal.value = true;
  emit('validate', validationResult.value);
};

const exportData = () => {
  const dataStr = JSON.stringify(currentFlowData.value, null, 2);
  const blob = new Blob([dataStr], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = 'workflow.json';
  link.click();
  URL.revokeObjectURL(url);
};

const clearCanvas = () => {
  if (lf.value) {
    lf.value.clearData();
    updateFlowData();
  }
};

// 数据更新方法
const updateFlowData = () => {
  if (lf.value) {
    currentFlowData.value = lf.value.getGraphData() as WorkflowData;
    emit('update:flowData', currentFlowData.value);
  }
};

// 拖拽节点处理
const handleDragNode = (nodeType: string) => {
  if (lf.value) {
    lf.value.dnd.startDrag({ type: nodeType });
  }
};

// 元素更新处理
const handleElementUpdate = (updatedElement: WorkflowEdge | WorkflowNode) => {
  if (!lf.value) return;

  if (selectedElementType.value === 'node') {
    const node = updatedElement as WorkflowNode;
    lf.value.setProperties(node.id, node.properties);
    emit('node-update', node);
  }

  updateFlowData();
};

// 关闭属性面板
const closePropertyDrawer = () => {
  propertyDrawerVisible.value = false;
  selectedElement.value = null;
  selectedElementType.value = null;
};

// 暴露的方法
defineExpose({
  getFlowData: () => currentFlowData.value,
  setFlowData: (data: WorkflowData) => {
    currentFlowData.value = data;
    if (lf.value) {
      lf.value.render(data);
    }
  },
  validate: () => validateWorkflow(currentFlowData.value),
  exportData: (format = 'json') => {
    if (format === 'json') {
      return JSON.stringify(currentFlowData.value, null, 2);
    }
    return '';
  },
  importData: (data: string, format = 'json') => {
    try {
      if (format === 'json') {
        const parsedData = JSON.parse(data);
        currentFlowData.value = parsedData;
        if (lf.value) {
          lf.value.render(parsedData);
        }
        return true;
      }
    } catch {
      message.error('数据格式错误');
      return false;
    }
    return false;
  },
  clear: clearCanvas,
  fitView,
  zoom: (ratio: number) => {
    if (lf.value) {
      lf.value.zoom(ratio > 1);
    }
  },
  undo,
  redo,
  getSelectedElements: () => {
    if (!lf.value) return { nodes: [], edges: [] };
    const selectedElements = lf.value.getSelectElements();
    return {
      nodes: selectedElements.nodes as WorkflowNode[],
      edges: selectedElements.edges as WorkflowEdge[],
    };
  },
  deleteSelectedElements: () => {
    if (lf.value) {
      const selectedElements = lf.value.getSelectElements();
      selectedElements.nodes.forEach((node) => lf.value?.deleteNode(node.id));
      selectedElements.edges.forEach((edge) => lf.value?.deleteEdge(edge.id));
      updateFlowData();
    }
  },
});
</script>

<template>
  <div class="workflow-designer">
    <!-- 工具栏 -->
    <div class="workflow-toolbar">
      <div class="toolbar-left">
        <a-space>
          <a-button-group size="small">
            <a-button @click="undo" :disabled="!canUndo">
              <template #icon><UndoOutlined /></template>
              撤销
            </a-button>
            <a-button @click="redo" :disabled="!canRedo">
              <template #icon><RedoOutlined /></template>
              重做
            </a-button>
          </a-button-group>

          <a-button-group size="small">
            <a-button @click="zoomIn">
              <template #icon><ZoomInOutlined /></template>
            </a-button>
            <a-button @click="zoomOut">
              <template #icon><ZoomOutOutlined /></template>
            </a-button>
            <a-button @click="fitView">
              <template #icon><ExpandOutlined /></template>
              适应画布
            </a-button>
          </a-button-group>

          <a-button size="small" @click="showMiniMap">
            <template #icon><PictureOutlined /></template>
            缩略图
          </a-button>
        </a-space>
      </div>

      <div class="toolbar-right">
        <a-space>
          <a-button size="small" @click="validateFlow">
            <template #icon><CheckCircleOutlined /></template>
            验证
          </a-button>
          <a-button size="small" @click="exportData">
            <template #icon><DownloadOutlined /></template>
            导出
          </a-button>
          <a-button size="small" @click="showDataModal = true">
            <template #icon><EyeOutlined /></template>
            查看数据
          </a-button>
          <a-button size="small" @click="clearCanvas" danger>
            <template #icon><ClearOutlined /></template>
            清空
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="workflow-content">
      <!-- 节点面板 -->
      <div class="workflow-sidebar" v-if="!readonly">
        <WorkflowNodePanel
          :node-configs="nodeConfigs"
          @drag-node="handleDragNode"
        />
      </div>

      <!-- 画布区域 -->
      <div class="workflow-canvas">
        <div ref="canvasContainer" class="canvas-container"></div>
      </div>

      <!-- 属性面板 -->
      <a-drawer
        v-model:open="propertyDrawerVisible"
        title="属性设置"
        placement="right"
        width="400"
        @close="closePropertyDrawer"
      >
        <WorkflowPropertyPanel
          v-if="selectedElement"
          :element="selectedElement"
          :element-type="selectedElementType"
          :approver-data-source="approverDataSource"
          @update="handleElementUpdate"
        />
      </a-drawer>
    </div>

    <!-- 数据查看模态框 -->
    <a-modal
      v-model:open="showDataModal"
      title="工作流数据"
      width="80%"
      :footer="null"
    >
      <a-tabs>
        <a-tab-pane key="json" tab="JSON格式">
          <pre class="data-preview">{{
            JSON.stringify(currentFlowData, null, 2)
          }}</pre>
        </a-tab-pane>
        <a-tab-pane key="nodes" tab="节点列表">
          <a-table
            :columns="nodeColumns"
            :data-source="currentFlowData.nodes"
            size="small"
            :pagination="false"
          />
        </a-tab-pane>
        <a-tab-pane key="edges" tab="连线列表">
          <a-table
            :columns="edgeColumns"
            :data-source="currentFlowData.edges"
            size="small"
            :pagination="false"
          />
        </a-tab-pane>
      </a-tabs>
    </a-modal>

    <!-- 验证结果模态框 -->
    <a-modal
      v-model:open="showValidationModal"
      title="验证结果"
      width="600"
      :footer="null"
    >
      <div class="validation-result">
        <a-result
          :status="validationResult?.valid ? 'success' : 'warning'"
          :title="validationResult?.valid ? '验证通过' : '发现问题'"
          :sub-title="
            validationResult?.valid
              ? '工作流配置正确'
              : `发现 ${validationResult?.errors.length} 个问题`
          "
        />

        <div
          v-if="validationResult && validationResult.errors.length > 0"
          class="validation-errors"
        >
          <a-list :data-source="validationResult.errors" size="small">
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #avatar>
                    <a-tag :color="item.type === 'error' ? 'red' : 'orange'">
                      {{ item.type === 'error' ? '错误' : '警告' }}
                    </a-tag>
                  </template>
                  <template #title>{{ item.message }}</template>
                  <template #description v-if="item.nodeId || item.edgeId">
                    {{
                      item.nodeId
                        ? `节点: ${item.nodeId}`
                        : `连线: ${item.edgeId}`
                    }}
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<style scoped>
.workflow-designer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
}

.workflow-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 1px 4px rgb(0 0 0 / 10%);
}

.workflow-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.workflow-sidebar {
  width: 240px;
  overflow-y: auto;
  background: white;
  border-right: 1px solid #e8e8e8;
}

.workflow-canvas {
  position: relative;
  flex: 1;
}

.canvas-container {
  width: 100%;
  height: 100%;
}

.data-preview {
  max-height: 400px;
  padding: 16px;
  overflow: auto;
  font-size: 12px;
  background: #f5f5f5;
  border-radius: 4px;
}

.validation-result {
  text-align: center;
}

.validation-errors {
  margin-top: 16px;
  text-align: left;
}
</style>
