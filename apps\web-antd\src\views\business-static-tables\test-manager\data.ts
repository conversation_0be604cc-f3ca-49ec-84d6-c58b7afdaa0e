import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { TestManagerApi } from '#/api/business-static-tables/test-manager';

import {
  $getAnalyteTypeApi,
  $getCbLoggedInApi,
  $getCboMethodApi,
  $getCbOutSourceApi,
  $getComboSpecSchemaCalcItemApi,
  $getLoginFlagApi,
  $getResultStatusApi,
  $getShareDBSitesApi,
  $getShareDBSitesApi2,
  $getTestCatApi,
  $getTestCatNoAllApi,
} from '#/api/business-static-tables';
import { $t } from '#/locales';
import { createColumn } from '#/utils/lims-grids-config';

export function useColumns(): VxeTableGridOptions<TestManagerApi.Test>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 60 },
    createColumn({
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 80,
    }),
    createColumn({
      field: 'TESTCATCODE',
      title: $t('business-static-tables.testManager.testCatCode'),
      minWidth: 120,
    }),
    createColumn({
      field: 'TESTCODE',
      title: $t('business-static-tables.testManager.testCode'),
      minWidth: 120,
    }),
    createColumn({
      field: 'TESTNO',
      title: $t('business-static-tables.testManager.testNo'),
      minWidth: 120,
      editRender: {
        name: 'input',
      },
    }),
    createColumn({
      field: 'TESTNO_ENG',
      title: $t('business-static-tables.testManager.testNoEng'),
      minWidth: 120,
      editRender: {
        name: 'input',
      },
    }),
    createColumn({
      field: 'TESTNO_ALIAS',
      title: $t('business-static-tables.testManager.testNoAlias'),
      minWidth: 120,
      editRender: {
        name: 'input',
      },
    }),
    createColumn({
      field: 'TESTDESC',
      title: $t('business-static-tables.testManager.testDesc'),
      minWidth: 120,
      editRender: {
        name: 'input',
      },
    }),
    createColumn({
      field: 'TESTFLAG',
      title: $t('business-static-tables.testManager.testFlag'),
      minWidth: 120,
      editRender: {
        name: 'ApiSelectEdit',
        props: {
          api: $getComboSpecSchemaCalcItemApi,
          labelField: 'CALC_ITEM',
          valueField: 'CALC_ITEM',
        },
      },
    }),
    createColumn({
      field: 'CREATERUNTYPE',
      title: $t('business-static-tables.testManager.createRunType'),
      minWidth: 120,
      editRender: {
        name: 'ApiSelectEdit',
        props: {
          api: $getComboSpecSchemaCalcItemApi,
          labelField: 'CALC_ITEM',
          valueField: 'CALC_ITEM',
        },
      },
    }),
    createColumn({
      field: 'TESTTYPE',
      title: $t('business-static-tables.testManager.testType'),
      minWidth: 150,
      editRender: {
        name: 'ApiSelectEdit',
        props: {
          api: $getComboSpecSchemaCalcItemApi,
          labelField: 'CALC_ITEM',
          valueField: 'CALC_ITEM',
        },
      },
    }),
    createColumn({
      field: 'SCORETYPE',
      title: $t('business-static-tables.testManager.scoreType'),
      minWidth: 120,
      editRender: {
        name: 'input',
        attrs: {
          type: 'number',
        },
      },
    }),
    {
      field: 'action',
      title: $t('commons.action'),
      minWidth: 120,
      fixed: 'right',
      slots: {
        default: 'action',
      },
    },
  ];
}

export function useSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      componentProps: {
        api: $getTestCatApi,
        labelField: 'TESTCATCODE',
        valueField: 'TESTCATCODE',
        class: 'w-full',
      },
      fieldName: 'TESTCATCODE',
      label: $t('business-static-tables.testManager.testCatCode'),
      rules: 'required',
    },
  ];
}

export function useFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      componentProps: {
        api: $getTestCatApi,
        labelField: 'TESTCATCODE',
        valueField: 'TESTCATCODE',
        class: 'w-full',
      },
      fieldName: 'TESTCATCODE',
      label: $t('business-static-tables.testManager.testCatCode'),
    },
    {
      component: 'Input',
      fieldName: 'TESTNO',
      label: $t('business-static-tables.testManager.testNo'),
    },
    {
      component: 'Input',
      fieldName: 'TESTNO_ENG',
      label: $t('business-static-tables.testManager.testNoEng'),
    },
    {
      component: 'Input',
      fieldName: 'TESTCODE',
      label: $t('business-static-tables.testManager.testCode'),
    },
    {
      component: 'Input',
      fieldName: 'TESTNO_ALIAS',
      label: $t('business-static-tables.testManager.testNoAlias'),
    },
    {
      component: 'Input',
      fieldName: 'TESTDESC',
      label: $t('business-static-tables.testManager.testDesc'),
    },
  ];
}

export function useAnalyteColumns(): VxeTableGridOptions<TestManagerApi.Analyte>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 60 },
    createColumn({
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 80,
    }),
    createColumn({
      field: 'SORTER',
      title: $t('commons.sort'),
      width: 100,
      editRender: {
        name: 'input',
        props: {
          type: 'number',
        },
      },
    }),
    createColumn({
      field: 'ANALYTE',
      title: $t('business-static-tables.testManager.analyte'),
      minWidth: 120,
    }),
    createColumn({
      field: 'ANALYTE_ENG',
      title: $t('business-static-tables.testManager.analyteEng'),
      minWidth: 200,
      editRender: {
        name: 'input',
      },
    }),
    createColumn({
      field: 'SCHEMANAME',
      title: $t('business-static-tables.testManager.schemaName'),
      minWidth: 150,
      editRender: {
        name: 'input',
      },
    }),
    createColumn({
      field: 'CALCNAME',
      title: $t('business-static-tables.testManager.calcName'),
      minWidth: 150,
      editRender: {
        name: 'input',
      },
    }),
    createColumn({
      field: 'PICTURE',
      title: $t('business-static-tables.testManager.picture'),
      minWidth: 150,
      editRender: {
        name: 'input',
      },
    }),
    createColumn({
      field: 'LOW',
      title: $t('business-static-tables.testManager.low'),
      minWidth: 120,
      editRender: {
        name: 'input',
      },
    }),
    createColumn({
      field: 'HIGH',
      title: $t('business-static-tables.testManager.high'),
      minWidth: 120,
      editRender: {
        name: 'input',
      },
    }),
    createColumn({
      field: 'UNITS',
      title: $t('business-static-tables.testManager.units'),
      minWidth: 120,
      editRender: {
        name: 'input',
      },
    }),
    createColumn({
      field: 'NOREP',
      title: $t('business-static-tables.testManager.noRep'),
      minWidth: 150,
      editRender: {
        name: 'input',
        attrs: {
          type: 'number',
        },
      },
    }),
    createColumn({
      field: 'ANALPRINT',
      title: $t('business-static-tables.testManager.analPrint'),
      width: 150,
      editRender: {
        name: 'ApiSelectEdit',
        props: {
          api: $getLoginFlagApi,
          labelField: 'LOGINFLAG',
          valueField: 'LOGINFLAG',
        },
      },
    }),
    createColumn({
      field: 'ANALTYPE',
      title: $t('business-static-tables.testManager.analType'),
      width: 150,
      editRender: {
        name: 'ApiSelectEdit',
        props: {
          api: $getAnalyteTypeApi,
          labelField: 'ANALYTE_TYPE_TEXT',
          valueField: 'ANALYTE_TYPE',
        },
      },
    }),
    {
      field: 'action',
      title: $t('commons.action'),
      minWidth: 150,
      fixed: 'right',
      slots: {
        default: 'action',
      },
    },
  ];
}

export function useAnlyteFilterSchema(): VbenFormSchema[] {
  return [];
}

// useMethodRelateTestColumns
export function useMethodRelateTestColumns(): VxeTableGridOptions<TestManagerApi.MethodRelatedToTests>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 60 },
    createColumn({
      field: 'DEFAULTMETHOD',
      title: $t('business-static-tables.testManager.defaultMethod'),
      minWidth: 120,
      editRender: {
        name: 'SwitchEdit',
        props: {
          trueValue: 'Y',
          falseValue: 'N',
        },
      },
    }),
    createColumn({
      field: 'METHOD',
      title: $t('business-static-tables.testManager.method'),
      minWidth: 120,
    }),
    createColumn({
      field: 'DEPT',
      title: $t('commons.dept'),
      minWidth: 120,
    }),
    createColumn({
      field: 'METHODSTATUS',
      title: $t('business-static-tables.testManager.methodStatus'),
      minWidth: 120,
    }),
    createColumn({
      field: 'SERVGRP',
      title: $t('commons.servGrp'),
      minWidth: 120,
      editRender: {
        name: 'input',
      },
    }),
    createColumn({
      field: 'TEAM',
      title: $t('business-static-tables.testManager.team'),
      minWidth: 120,
      editRender: {
        name: 'input',
      },
    }),
    createColumn({
      field: 'EQTYPE',
      title: $t('business-static-tables.testManager.eqType'),
      minWidth: 150,
      editRender: {
        name: 'input',
      },
    }),
    createColumn({
      field: 'ALL_EQUIPMENT',
      title: $t('business-static-tables.testManager.allEquipment'),
      minWidth: 150,
      editRender: {
        name: 'SwitchEdit',
        props: {
          trueValue: 'Y',
          falseValue: 'N',
        },
      },
    }),
    createColumn({
      field: 'INSTVERIFY',
      title: $t('business-static-tables.testManager.instVerify'),
      minWidth: 150,
      editRender: {
        name: 'SwitchEdit',
        props: {
          trueValue: 'Y',
          falseValue: 'N',
        },
      },
    }),
    createColumn({
      field: 'ASSIGN_TYPE',
      title: $t('business-static-tables.testManager.assignType'),
      minWidth: 150,
      editRender: {
        name: 'SwitchEdit',
        props: {
          trueValue: 'Y',
          falseValue: 'N',
        },
      },
    }),
    // createColumn({
    //   field: 'ELNREFERENCE',
    //   title: $t('business-static-tables.testManager.elnReference'),
    //   minWidth: 120,
    // }),
    // createColumn({
    //   field: 'DESIREDTAT',
    //   title: $t('business-static-tables.testManager.desiredTat'),
    //   minWidth: 120,
    // }),
    createColumn({
      field: 'STARTDATE',
      title: $t('business-static-tables.testManager.startDate'),
      minWidth: 150,
      editRender: {
        name: 'input',
        attrs: {
          type: 'date',
        },
      },
      formatter: 'formatDate',
    }),
    createColumn({
      field: 'TODATE',
      title: $t('business-static-tables.testManager.toDate'),
      minWidth: 150,
      editRender: {
        name: 'input',
        attrs: {
          type: 'datetime',
        },
      },
      formatter: 'formatDate',
    }),
    createColumn({
      field: 'TESTCODE',
      title: $t('business-static-tables.testManager.testCode'),
      minWidth: 120,
      visible: false,
    }),
    createColumn({
      field: 'ELN_ID',
      title: $t('business-static-tables.testManager.elnId'),
      minWidth: 120,
    }),
    createColumn({
      field: 'MAX_ALLOCATION_SAMPLE_QUANTITY',
      title: $t(
        'business-static-tables.testManager.maxAllocationSampleQuantity',
      ),
      minWidth: 180,
      editRender: {
        name: 'input',
        attrs: {
          type: 'number',
        },
      },
    }),
    createColumn({
      field: 'ALLOCATION_SAMPLE_UNIT',
      title: $t('business-static-tables.testManager.allocationSampleUnit'),
      minWidth: 150,
      editRender: {
        name: 'input',
      },
    }),
    {
      field: 'action',
      title: $t('commons.action'),
      minWidth: 150,
      slots: { default: 'action' },
      fixed: 'right',
    },
  ];
}

export function useMethodRelateTestSchema(): VbenFormSchema[] {
  return [];
}

export function useMethodTestPlanSchema(): VbenFormSchema[] {
  return [];
}

// useTestPlanColumns
export function useTestPlanColumns(): VxeTableGridOptions<TestManagerApi.TestPlan>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 60 },
    createColumn({
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 80,
      width: 80,
      visible: false,
    }),
    createColumn({
      field: 'PROGNAME',
      title: $t('business-static-tables.testManager.progName'),
      minWidth: 120,
    }),
  ];
}

export function useMoveCatSchema(): VbenFormSchema[] {
  return [
    {
      fieldName: 'TESTCATCODE',
      component: 'ApiSelect',
      componentProps: {
        api: $getTestCatNoAllApi,
        labelField: 'TESTCATCODE',
        valueField: 'TESTCATCODE',
        class: 'w-full',
      },
      label: $t('business-static-tables.testManager.testCatCode'),
    },
  ];
}

export function usePassibleResultsColumns(): VxeTableGridOptions<TestManagerApi.PassibleResults>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 60 },
    createColumn({
      field: 'SORTER',
      title: $t('commons.sorter'),
      minWidth: 100,
      editRender: {
        name: 'input',
        props: {
          type: 'number',
        },
      },
    }),
    createColumn({
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 80,
    }),
    createColumn({
      field: 'RESULT',
      title: $t('business-static-tables.testManager.result'),
      minWidth: 120,
      editRender: {
        name: 'input',
      },
    }),
    createColumn({
      field: 'SNOMEDCODE',
      title: $t('business-static-tables.testManager.snomedCode'),
      minWidth: 120,
      editRender: {
        name: 'input',
      },
    }),
    createColumn({
      field: 'RESULT_STATUS',
      title: $t('business-static-tables.testManager.resultStatus'),
      minWidth: 120,
      editRender: {
        name: 'ApiSelectEdit',
        props: {
          api: $getResultStatusApi,
          labelField: 'STATUS',
          valueField: 'STATUS',
          class: 'w-full',
        },
      },
    }),
    createColumn({
      field: 'CONCLUSION_REPORT',
      title: $t('business-static-tables.testManager.conclusionReport'),
      minWidth: 120,
      editRender: {
        name: 'input',
      },
    }),
    {
      field: 'action',
      title: $t('commons.action'),
      minWidth: 150,
      slots: { default: 'action' },
      fixed: 'right',
    },
  ];
}

export function usePassibleResultsFilterSchema(): VbenFormSchema[] {
  return [];
}

export function usePossibleResultSchema(): VbenFormSchema[] {
  return [
    {
      fieldName: 'RESULT',
      component: 'Input',
      label: $t('business-static-tables.testManager.result'),
      rules: 'required',
    },
    {
      fieldName: 'SNOMEDCODE',
      component: 'Input',
      label: $t('business-static-tables.testManager.snomedCode'),
      rules: 'required',
    },
    {
      fieldName: 'CONCLUSION_REPORT',
      component: 'Input',
      label: $t('business-static-tables.testManager.conclusionReport'),
      rules: 'required',
    },
    {
      fieldName: 'RESULT_STATUS',
      component: 'ApiSelect',
      componentProps: {
        api: $getResultStatusApi,
        labelField: 'STATUS',
        valueField: 'STATUS',
        class: 'w-full',
      },
      label: $t('business-static-tables.testManager.resultStatus'),
      rules: 'required',
    },
  ];
}

export function useMethodRelatedTestSchema(): VbenFormSchema[] {
  return [
    {
      fieldName: 'METHOD',
      component: 'ApiSelect',
      componentProps: {
        api: $getCboMethodApi,
        labelField: 'METHOD',
        valueField: 'METHOD',
        class: 'w-full',
        showSearch: true,
      },
    },
    {
      fieldName: 'DEPT',
      component: 'ApiSelect',
      componentProps: {
        api: $getShareDBSitesApi,
        labelField: 'DEPT',
        valueField: 'DEPT',
        class: 'w-full',
        showSearch: true,
      },
    },
    {
      fieldName: 'SERVGRP',
      component: 'Select',
      componentProps: {
        // api: $getShareDBSitesApi2,
        // labelField: 'SERVGRP',
        // valueField: 'SERVGRP',
        // params: p,
        class: 'w-full',
        showSearch: true,
        options: [],
      },
      dependencies: {
        async componentProps(values) {
          const optionsGet = await $getShareDBSitesApi2({ dept: values.DEPT });
          // label、value
          const options = optionsGet.map((item) => ({
            label: item.SERVGRP,
            value: item.SERVGRP,
          }));
          return {
            options,
          };
        },
        triggerFields: ['DEPT'],
      },
    },
  ];
}

export function useOutSourceLabsColumns(): VxeTableGridOptions<TestManagerApi.OutSourceLabs>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 60 },
    createColumn({
      field: 'LOGGEDBYDEPT',
      title: $t('business-static-tables.testManager.loggedByDept'),
      minWidth: 100,
    }),
    createColumn({
      field: 'DEPT',
      title: $t('business-static-tables.testManager.dept'),
      minWidth: 100,
    }),
    createColumn({
      field: 'PREFERENCE',
      title: $t('business-static-tables.testManager.preference'),
      minWidth: 100,
    }),
    createColumn({
      field: 'CERTIFIEDTILL',
      title: $t('business-static-tables.testManager.certifiedTill'),
      minWidth: 100,
    }),
    {
      field: 'action',
      title: $t('commons.action'),
      minWidth: 150,
      slots: { default: 'action' },
      fixed: 'right',
    },
  ];
}

// useOutSourceLabsSchema
export function useOutSourceLabsSchema(): VbenFormSchema[] {
  return [
    {
      fieldName: 'LOGGEDBYDEPT',
      component: 'ApiSelect',
      componentProps: {
        api: $getCbLoggedInApi,
        labelField: 'Text',
        valueField: 'Value',
        class: 'w-full',
        showSearch: true,
      },
      label: $t('business-static-tables.testManager.loggedByDept'),
      rules: 'required',
    },
    {
      fieldName: 'DEPT',
      component: 'Select',
      componentProps: {
        options: [],
        class: 'w-full',
      },
      label: $t('business-static-tables.testManager.dept'),
      rules: 'required',
      dependencies: {
        triggerFields: ['LOGGEDBYDEPT'],
        async componentProps(values) {
          let options: { label: string; value: string }[];
          if (values.LOGGEDBYDEPT) {
            const optionsGet = await $getCbOutSourceApi({
              loggedbydept: values.LOGGEDBYDEPT,
            });
            options = optionsGet.map((item) => ({
              label: item.DEPT,
              value: item.DEPT,
            }));
          } else {
            options = [];
          }
          return {
            options,
          };
        },
      },
    },
    {
      fieldName: 'PREFERENCE',
      component: 'Select',
      componentProps: {
        options: [
          { label: '1', value: '1' },
          { label: '2', value: '2' },
          { label: '3', value: '3' },
          { label: '4', value: '4' },
          { label: '5', value: '5' },
        ],
        class: 'w-full',
      },
      label: $t('business-static-tables.testManager.preference'),
      rules: 'required',
    },
    {
      fieldName: 'CERTIFIEDTILL',
      component: 'DatePicker',
      componentProps: {
        class: 'w-full',
      },
      label: $t('business-static-tables.testManager.certifiedTill'),
      rules: 'required',
    },
  ];
}

export function useOtherEquipmentColumns(): VxeTableGridOptions<TestManagerApi.TESTMETHODOTHEREQUIPMENT>['columns'] {
  return [
    // { field: 'checkbox', type: 'checkbox', width: 60 },
    createColumn({
      field: 'EQTYPE',
      title: $t('business-static-tables.testManager.eqType'),
      width: 180,
    }),
    createColumn({
      field: 'TESTINGEQUIPMENT',
      title: $t('business-static-tables.testManager.testingEquipment'),
      width: 120,
      cellRender: {
        name: 'CellSwitch',
        props: {
          trueValue: 'Y',
          falseValue: 'N',
        },
      },
    }),
    createColumn({
      field: 'TIMEUSEDINMINUTES',
      title: $t('business-static-tables.testManager.timeUsedInMinutes'),
      width: 180,
      editRender: {
        name: 'input',
        props: {
          type: 'number',
        },
      },
    }),
    {
      field: 'action',
      title: $t('commons.action'),
      minWidth: 150,
      slots: { default: 'action' },
      fixed: 'right',
    },
  ];
}

export function useDocColumns(): VxeTableGridOptions<TestManagerApi.TEST_METHOD_REQ_DOCS>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 60 },
    createColumn({
      field: 'NAME',
      title: $t('business-static-tables.testManager.docName'),
      width: 150,
    }),
    createColumn({
      field: 'URL',
      title: $t('business-static-tables.testManager.docUrl'),
      width: 200,
      // cellRender: {
      //   name: 'CellLink',
      //   props: {},
      // },
      editRender: {
        name: 'CellLink',
      },
    }),
    createColumn({
      field: 'STARDOC_ID',
      title: $t('commons.starDocId'),
    }),
    {
      field: 'action',
      title: $t('commons.action'),
      minWidth: 150,
      slots: { default: 'action' },
      fixed: 'right',
    },
  ];
}
