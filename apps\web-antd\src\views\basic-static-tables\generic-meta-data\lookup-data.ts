import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { LookupTableApi } from '#/api/basic-static-tables/generic-meta-data';

import { z } from '#/adapter/form';
import { $t } from '#/locales';

export function useColumns(): VxeTableGridOptions<LookupTableApi.MetaDataLookups>['columns'] {
  return [
    // { field: 'seq', type: 'seq', width: 80 },
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'NAME',
      title: $t('basicStatic.lookups.dataTable.columns.lookupName'),
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' },
      sortable: true,
    },
    {
      align: 'center',
      field: 'TYPE',
      title: $t('basicStatic.lookups.dataTable.columns.category'),
      width: 200,
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'EFFECT',
      title: $t('basicStatic.lookups.dataTable.columns.description'),
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('basicStatic.lookups.dataTable.columns.actions'),
      width: 140,
    },
  ];
}

export function useLookupValuesColumns(): VxeTableGridOptions<LookupTableApi.MetaDataLookupValue>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('basicStatic.lookups.dataTable.columns.origrec'),
      width: 80,
      visible: false,
    },
    {
      align: 'center',
      field: 'SORTER',
      title: $t('basicStatic.lookups.dataTable.columns.sortOrder'),
      width: 100,
      sortable: true,
      editRender: { name: 'input' },
    },
    {
      align: 'center',
      field: 'LOOKUP_NAME',
      title: $t('basicStatic.lookups.dataTable.columns.lookupName'),
      width: 200,
      visible: false,
    },
    {
      align: 'center',
      field: 'VALUE',
      title: $t('basicStatic.lookups.dataTable.columns.lookupValue'),
      width: 200,
    },
    {
      align: 'center',
      field: 'TEXT',
      title: $t('basicStatic.lookups.dataTable.columns.lookupText'),
      editRender: { name: 'input' },
    },
  ];
}

export function useSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'NAME',
      label: $t('basicStatic.lookups.dataTable.columns.lookupName'),
      rules: z
        .string()
        .min(
          2,
          $t('ui.formRules.minLength', [
            $t('basicStatic.lookups.dataTable.columns.lookupName'),
            2,
          ]),
        )
        .max(
          20,
          $t('ui.formRules.maxLength', [
            $t('basicStatic.lookups.dataTable.columns.lookupName'),
            20,
          ]),
        ),
    },
    {
      component: 'Input',
      fieldName: 'TYPE',
      label: $t('basicStatic.lookups.dataTable.columns.category'),
      rules: z
        .string()
        .max(
          50,
          $t('ui.formRules.maxLength', [
            $t('basicStatic.lookups.dataTable.columns.category'),
            50,
          ]),
        )
        .optional(),
    },
    {
      component: 'Textarea',
      componentProps: {
        maxLength: 500,
        rows: 5,
        // showCount: true,
      },
      fieldName: 'EFFECT',
      label: $t('basicStatic.lookups.dataTable.columns.description'),
      rules: z
        .string()
        .max(
          500,
          $t('ui.formRules.maxLength', [
            $t('basicStatic.lookups.dataTable.columns.description'),
            500,
          ]),
        )
        .optional(),
    },
  ];
}

export function useFilterLookupSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        // placeholder: 'Please enter name',
      },
      fieldName: 'lookupName',
      label: $t('basicStatic.lookups.dataTable.columns.lookupName'),
    },
    {
      component: 'Input',
      fieldName: 'category',
      componentProps: {
        // placeholder: 'Please enter category',
      },
      label: $t('basicStatic.lookups.dataTable.columns.category'),
    },
    // {
    //   component: 'Select',
    //   componentProps: {
    //     allowClear: true,
    //     options: [
    //       { label: $t('common.enabled'), value: 1 },
    //       { label: $t('common.disabled'), value: 0 },
    //     ],
    //   },
    //   fieldName: 'status',
    //   label: $t('system.role.status'),
    // },
  ];
}

export function useLooupValueSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'VALUE',
      label: $t('basicStatic.lookups.dataTable.columns.lookupValue'),
      rules: z
        .string()
        .min(
          1,
          $t('ui.formRules.minLength', [
            $t('basicStatic.lookups.dataTable.columns.lookupValue'),
            2,
          ]),
        )
        .max(
          20,
          $t('ui.formRules.maxLength', [
            $t('basicStatic.lookups.dataTable.columns.lookupValue'),
            20,
          ]),
        ),
    },
    {
      component: 'Input',
      fieldName: 'TEXT',
      label: $t('basicStatic.lookups.dataTable.columns.lookupText'),
      rules: z
        .string()
        .min(
          1,
          $t('ui.formRules.minLength', [
            $t('basicStatic.lookups.dataTable.columns.lookupText'),
            2,
          ]),
        )
        .max(
          20,
          $t('ui.formRules.maxLength', [
            $t('basicStatic.lookups.dataTable.columns.lookupText'),
            20,
          ]),
        ),
    },
    {
      component: 'Input',
      fieldName: 'SORTER',
      label: $t('basicStatic.lookups.dataTable.columns.sortOrder'),
      rules: z.preprocess(
        (val) => (typeof val === 'string' ? Number.parseFloat(val) : val),
        z
          .number()
          .min(
            0,
            $t('ui.formRules.minValue', [
              $t('basicStatic.lookups.dataTable.columns.sortOrder'),
              0,
            ]),
          ),
      ),
    },
  ];
}
