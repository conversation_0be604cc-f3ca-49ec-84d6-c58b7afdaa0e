{"title": "Materials Management", "operation": "Operation", "add": "Add", "delete": "Delete", "edit": "Edit", "save": "Save", "cancel": "Cancel", "detail": "Detail", "removed": "Removed", "material-manager": {"title": "Material Manager", "matcode": "Material Code", "matname": "Material Name", "mattype": "Material Type", "default_unit_code": "Default Unit Code", "spec": "Spec", "packing_spec": "Packing Spec", "dosageform": "Dosage Form", "product_name": "Product Name", "generic_name": "Generic Name", "retestdate_m": "Retest Date M", "exdate_m": "Exdate M", "savecondition": "Save Condition", "sampleperiod": "Sample Period", "inspection_level": "Inspection Level", "matsynonims": "Synonyms", "suppcode": "Suppcode", "suppnam": "Su<PERSON><PERSON>", "suptype": "Supptype", "recipecode": "Recipecode", "recipename": "Recipename", "startddate": "Startddate", "expdate": "Expdate", "created_by": "Created By", "approvedat": "Approved At", "retireddat": "Retired <PERSON><PERSON>", "stardoc_id": "Stardoc Id", "addsupplier": "Add Supplier", "deletesupplier": "Delete Supplier", "addrecipe": "Add Recipe", "deleterecipe": "Delete Recipe", "copyrecipe": "<PERSON><PERSON> Recipe", "approve": "Approve", "retire": "Retire", "text": "Text", "value": "Value", "factor": "Factor", "parent": "Parent", "parentmattype": "Parent Material Type", "parent_container_matcode": "Parent Container Material Code", "childmattype": "Child Material Type", "container_matcode": "Child Container Material Code", "conversion_factor": "Conversion Factor", "uploadfile": "Upload File", "viewfile": "View File"}, "location-manage": {"title": "Location Management", "locationcode": "Location Code", "location_name": "Location Name", "description": "Description", "is_storage": "Is Storage", "is_gxp": "Is GXP", "condition": "Condition", "temperature": "Temperature", "luminosity": "Luminosity", "temperature_max": "Temperature Max", "other": "Other", "humidity": "<PERSON><PERSON><PERSON><PERSON>", "name": "Name", "sequence": "Sequence", "numbering_method": "Numbering Method", "sublocation_size": "Sublocation Size", "deptcode": "Deptcode", "dept": "Dept", "building_code": "Building Code", "building_name": "Building Name", "room_code": "Room Code", "room_name": "Room Name", "class": "Class", "stcondition_code": "Stcondition Code", "layoutname": "Layoutname", "prefix": "Prefix", "loctype_size": "Loctype <PERSON>", "loctype_order": "Loctype Order", "is_storable": "Is Storable", "location_type_id": "Location Type Id"}}