<template>
  <div class="flex h-full justify-around overflow-hidden">
    <ul class="h-full w-2/5 overflow-auto">
      <li
        v-for="item in leftItems"
        :key="item.value"
        class="list-item hover:bg-blue-200"
        :style="{
          backgroundColor:
            leftSelectedItem.value === item.value ? '#f0f0f0' : 'white',
        }"
        @click="clickLeftItem(item)"
      >
        {{ item.text }}
      </li>
    </ul>

    <div class="flex flex-col justify-evenly">
      <div>
        <Button type="primary" @click="removeToRight">
          <ChevronRight class="ml-auto h-6 w-6" />
        </Button>
      </div>
      <div>
        <Button type="primary" @click="removeToLeft">
          <ChevronLeft class="ml-auto h-6 w-6" />
        </Button>
      </div>

      <div>
        <Button type="primary" @click="removeAllToRight">
          <ChevronsRight class="ml-auto h-6 w-6" />
        </Button>
      </div>
      <div>
        <Button type="primary" @click="removeAllToLeft">
          <ChevronsLeft class="ml-auto h-6 w-6" />
        </Button>
      </div>
    </div>
    <ul class="h-full w-2/5 overflow-auto">
      <li
        v-for="item in rightItems"
        :key="item.value"
        class="list-item hover:bg-blue-200"
        :style="{
          backgroundColor:
            rightSelectedItem.value === item.value ? '#f0f0f0' : 'white',
        }"
        @click="clickRightItem(item)"
      >
        {{ item.text }}
      </li>
    </ul>
  </div>
</template>

<script lang="ts" setup>
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-vue-next';
import { Button } from 'ant-design-vue';
interface Item {
  value: string;
  text: string;
}
const props = defineProps<{
  leftItems:Item[] ,
  rightItems: Item[] ,
  leftSelectedItem: Item,
  rightSelectedItem: Item ,
}>();

const emit = defineEmits(['update:leftSelectedItem', 'update:rightSelectedItem', 'removeToRight', 'removeToLeft', 'removeAllToRight', 'removeAllToLeft']);

const clickLeftItem = (item: any) => {
  emit('update:leftSelectedItem', item);
};

const clickRightItem = (item: any) => {
  emit('update:rightSelectedItem', item);
};

const removeToRight = () => {
  emit('removeToRight');
};

const removeToLeft = () => {
  emit('removeToLeft');
};

const removeAllToRight = () => {
  emit('removeAllToRight');
};

const removeAllToLeft = () => {
  emit('removeAllToLeft');
};
</script>

<style>
.list-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #ccc;
  transition: background-color 0.3s;
}

.list-item:hover {
  background-color: #f0f0f0;
}
</style>
