import type { Recordable } from '@vben/types';

import { backendRequestClient } from '#/api/request';

export namespace SystemRoleApi {
  export interface SystemRole {
    [key: string]: any;
    id: string;
    name: string;
    code: string;
    permissions: string[];
    remark?: string;
    status: 0 | 1;
  }
}

/**
 * 获取角色列表数据
 */
async function getRoleList(params: Recordable<any>) {
  const items = await backendRequestClient.get<Array<SystemRoleApi.SystemRole>>(
    '/platform/roles',
    { params },
  );
  return {
    items: items ?? [],
    total: items?.length ?? 0,
  };
}

/**
 * 创建角色
 * @param data 角色数据
 */
async function createRole(data: Omit<SystemRoleApi.SystemRole, 'id'>) {
  return backendRequestClient.post('/platform/roles', data);
}

/**
 * 更新角色
 *
 * @param id 角色 ID
 * @param data 角色数据
 */
async function updateRole(
  id: string,
  data: Omit<SystemRoleApi.SystemRole, 'id'>,
) {
  return backendRequestClient.put(`/platform/roles/${id}`, data);
}

/**
 * 删除角色
 * @param id 角色 ID
 */
async function deleteRole(id: string) {
  return backendRequestClient.delete(`/platform/roles/${id}`);
}

export { createRole, deleteRole, getRoleList, updateRole };
