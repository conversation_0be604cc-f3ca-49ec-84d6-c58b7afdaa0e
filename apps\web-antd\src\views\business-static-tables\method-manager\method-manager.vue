<script lang="ts" setup>
import type { MethodManagerApi } from '#/api/business-static-tables';

import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import {
  $delTestMethodApi,
  $getTestMethodListApi,
} from '#/api/business-static-tables';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';
import { confirm } from '#/utils/utils';

import AddMethodForm from './add-method-form.vue';
import { useColumns, useFilterSchema } from './data';
import MethodManagerVersion from './method-manager-version.vue';

const colums = useColumns();
const filterSchema = useFilterSchema();
const queryData = async () => {
  return await $getTestMethodListApi('All');
};

const {
  Grid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
} = useLimsGridsConfig<MethodManagerApi.TestMethod>(
  colums,
  filterSchema,
  queryData,
);

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: MethodManagerVersion,
  destroyOnClose: true,
  class: 'w-[900px]',
});

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddMethodForm,
  destroyOnClose: true,
  class: 'w-[900px]',
});

function onRefresh() {
  gridApi.query();
}

async function onDelete() {
  const checkOrig: string[] =
    (gridApi.grid
      ?.getCheckboxRecords()
      .map((item) => item.METHOD) as string[]) || [];

  if (checkOrig.length === 0) {
    message.warning($t('commons.selectOne'));
    return;
  }
  await confirm($t('commons.deleteConfirm'), $t('commons.deleteConfirmTitle'));
  await $delTestMethodApi(checkOrig);
  message.success($t('commons.deleteSuccess'));
  onRefresh();
}

function onCreate() {
  formModalApi.setData(null).open();
}

function versionMangerEvent(row: MethodManagerApi.TestMethod) {
  formDrawerApi.setData(row).open();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <FormDrawer />
    <Grid>
      <template #toolbar-actions>
        <Space :size="[4, 0]" wrap>
          <Button type="primary" @click="onCreate">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="onDelete">
            {{ $t('ui.actionTitle.delete') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent(row)">
            {{ $t('commons.save') }}
          </Button>
          <Button type="link" @click="cancelRowEvent(row)">
            {{ $t('commons.cancel') }}
          </Button>
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">
            {{ $t('commons.edit') }}
          </Button>
          <Button type="link" @click="versionMangerEvent(row)">
            版本管理
          </Button>
        </template>
      </template>
    </Grid>
  </Page>
</template>
