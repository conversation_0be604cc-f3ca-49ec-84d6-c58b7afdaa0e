<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Select, SelectOption } from 'ant-design-vue';
import {
  getServgrpApi,
  getCertifiMethodApi,
  getTestCategoryApi,
  chkMethodCertApi,
  addMethodCertApi,
} from '#/api/equipment/persons-manage';
import DoubleList from '#/adapter/component/common/double-list.vue';
import { authorizeModalSchema } from '../persons-manage-data';
import { useVbenForm } from '#/adapter/form';
import dayjs from 'dayjs';

interface RowType {
  [key: string]: any;
}
const emit = defineEmits(['success']);
interface PersonItem {
  text: string;
  value: string;
}
import { message } from 'ant-design-vue';
const rightItems = ref<PersonItem[]>([]);
const leftItems = ref<PersonItem[]>([]);
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    if (rightItems.value.length === 0) {
      modalApi.close();
      return;
    }
    modalApi.lock();
    try {
      if (!clickRow.value) {
        modalApi.close();
        return;
      }
      let context = [];
      for (const item of rightItems.value) {
        const checkRes = await chkMethodCertApi([
          item.VALUE,
          clickRow.value.USRNAM,
        ]);
        if (!checkRes[0]) {
          context.push(checkRes[1]);
          continue;
        } else {
          const data = await formApi.getValues();
          await addMethodCertApi([
            item.VALUE,
            clickRow.value.USRNAM,
            data.authorizationDate,
          ]);
        }
      }
      if (context.length > 0) {
        message.warning(`已拥有方法授权或方法未发布：${context.join(', ')}`);
      }
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },

  async onOpenChange(isOpen) {
    if (isOpen) {
      clickRow.value = modalApi.getData().clickRow;
      if (clickRow.value && clickRow.value.USRNAM) {
        const res = await getServgrpApi([clickRow.value.USRNAM]);
        positionOptions.value = res.map((item: any) => ({
          value: item.VALUE,
          label: item.TEXT,
        }));
      }
      const res = await getTestCategoryApi();
      categoryOptions.value = res.map((item: any) => ({
        value: item.VALUE,
        label: item.TEXT,
      }));

      formApi.setFieldValue('authorizationDate', dayjs());
    }
  },
});
const clickRow = ref<RowType | null>(null);
const leftSelectedItem = ref<PersonItem>({
  text: '',
  value: '',
});
const rightSelectedItem = ref<PersonItem>({
  text: '',
  value: '',
});
const removeToRight = () => {
  if (leftSelectedItem.value.VALUE) {
    leftItems.value = leftItems.value.filter(
      (item) => item.VALUE !== leftSelectedItem.value.VALUE,
    );

    rightItems.value.push(leftSelectedItem.value);
  }
};
const removeToLeft = () => {
  if (rightSelectedItem.value.VALUE) {
    rightItems.value = rightItems.value.filter(
      (item) => item.VALUE !== rightSelectedItem.value.VALUE,
    );
    leftItems.value.push(rightSelectedItem.value);
  }
};
const removeAllToRight = () => {
  rightItems.value.push(...leftItems.value);
  leftItems.value = [];
};
const removeAllToLeft = () => {
  leftItems.value.push(...rightItems.value);
  rightItems.value = [];
};
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: authorizeModalSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});
const positionChange = () => {
  getListData();
};
const positionOptions = ref<{ value: string; label: string }[]>([]);
const categoryChange = () => {
  getListData();
};
const getListData = async () => {
  if (!clickRow.value || !clickRow.value.USRNAM) {
    return;
  }
  const data = await formApi.getValues();
  let whereText = '';
  if (data.testCategory) {
    whereText += `and MRT.SERVGRP ='${data.position}'`;
  }
  if (data.inspectionItem) {
    whereText += `and T.TESTNO + TM.DESCRIPTION like '%${data.sTestNo}%' `;
  }
  let params = [data.testCategory || '', clickRow.value.USRNAM, whereText];
  const res = await getCertifiMethodApi(params);
  leftItems.value = res.map((item: any) => ({
    text: item.Text,
    value: item.Value,
  }));
};
const categoryOptions = ref<{ value: string; label: string }[]>([]);
</script>
<template>
  <Modal title="方法授权" class="h-1/2 w-2/5">
    <Form class="mx-4">
      <template #position="slotProps">
        <Select v-bind="slotProps" class="w-full" @change="positionChange">
          <SelectOption
            v-for="item in positionOptions"
            :value="item.value"
            :key="item.value"
          >
            {{ item.label }}
          </SelectOption>
        </Select>
      </template>
      <template #testCategory="slotProps">
        <Select v-bind="slotProps" class="w-full" @change="categoryChange">
          <SelectOption
            v-for="item in categoryOptions"
            :value="item.value"
            :key="item.value"
          >
            {{ item.label }}
          </SelectOption>
        </Select>
      </template>
    </Form>
    <DoubleList
      :leftItems="leftItems"
      :rightItems="rightItems"
      :leftSelectedItem="leftSelectedItem"
      :rightSelectedItem="rightSelectedItem"
      @update:leftSelectedItem="leftSelectedItem = $event"
      @update:rightSelectedItem="rightSelectedItem = $event"
      @removeToRight="removeToRight"
      @removeToLeft="removeToLeft"
      @removeAllToRight="removeAllToRight"
      @removeAllToLeft="removeAllToLeft"
    />
  </Modal>
</template>
