<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { EquipmentTypeApi } from '#/api/basic-static-tables/equip-types';

import { confirm, Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteEquipmentType,
  getEquipmentTypeList,
  updateEquipmentType,
} from '#/api/basic-static-tables/equip-types';

import AddEquipTypeForm from './add-equip-type.vue';
import EquipEvents from './equip-events.vue';
import {
  useEquipmentTypeColumns,
  useEquipmentTypeFilterSchema,
} from './equip-types-data';

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: EquipEvents,
  destroyOnClose: true,
  class: 'w-[900px]',
});

const gridOptions: VxeTableGridOptions<EquipmentTypeApi.EquipmentType> = {
  columns: useEquipmentTypeColumns(),
  stripe: true,
  border: true,
  checkboxConfig: {
    highlight: true,
    range: true,
    labelField: 'select',
  },
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getEquipmentTypeList();
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const formOptions: VbenFormProps = {
  schema: useEquipmentTypeFilterSchema(),
  submitOnChange: false,
  collapsed: true,
  showCollapseButton: true,
  submitButtonOptions: {
    content: '查询',
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: {},
  formOptions,
});

function hasEditStatus(row: EquipmentTypeApi.EquipmentType) {
  return gridApi.grid?.isEditByRow(row);
}

function editRowEvent(row: EquipmentTypeApi.EquipmentType) {
  gridApi.grid?.setEditRow(row);
}

function viewDetails(row: EquipmentTypeApi.EquipmentType) {
  formDrawerApi.setData(row).open();
}

async function saveRowEvent(row: EquipmentTypeApi.EquipmentType) {
  await gridApi.grid?.clearEdit();
  updateEquipmentType(row);
  gridApi.setLoading(true);
  setTimeout(() => {
    gridApi.setLoading(false);
    message.success({
      content: `保存成功！`,
    });
  }, 600);
}

const cancelRowEvent = (_row: EquipmentTypeApi.EquipmentType) => {
  gridApi.grid?.clearEdit();
};

function onRefresh() {
  gridApi.query();
}

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddEquipTypeForm,
  destroyOnClose: true,
});

function onCreate() {
  formModalApi.setData(null).open();
}

async function onDelete() {
  // 获取选中行
  const aEqType: string[] = gridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.EQTYPE) as string[];

  if (aEqType.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${aEqType.length} 条数据吗？`,
      icon: 'warning',
      centered: false,
    });

    await deleteEquipmentType(aEqType);

    message.success('删除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}
</script>

<template>
  <Page auto-content-height>
    <FormDrawer @success="onRefresh" />
    <FormModal @success="onRefresh" class="w-[600px]" />
    <Grid>
      <template #toolbar-actions>
        <Space>
          <Button type="primary" @click="onCreate">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="onDelete">
            {{ $t('ui.actionTitle.delete') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent(row)">
            {{ $t('basic-static-tables.save') }}
          </Button>
          <Button type="link" @click="cancelRowEvent(row)">
            {{ $t('basic-static-tables.cancel') }}
          </Button>
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">
            {{ $t('basic-static-tables.edit') }}
          </Button>
          <Button type="link" @click="viewDetails(row)">
            {{ $t('basic-static-tables.detail') }}
          </Button>
        </template>
      </template>
    </Grid>
  </Page>
</template>
