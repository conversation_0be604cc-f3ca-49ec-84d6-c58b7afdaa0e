<script lang="ts" setup>
import { computed, onMounted, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, Card, Checkbox, Input, Space } from 'ant-design-vue';
import { Ellipsis } from 'lucide-vue-next';

import { useVbenForm } from '#/adapter/form';
import { getSerialPortdApi } from '#/api/equipment/equipment-mg';
import { useEquipmentStore } from '#/store';

import TestModal from '../components/test-modal.vue';
import { serialSchema } from '../equipment-mg-data';

const equipmentStore = useEquipmentStore();
interface RowType {
  [key: string]: any;
}
const currentRow = computed<null | RowType>(
  () => equipmentStore.getCurrentRow as null | RowType,
);
onMounted(() => {
  getData();
});
watch(
  currentRow,
  () => {
    getData();
  },
  { deep: true },
);
const getData = async () => {
  if (currentRow.value && currentRow.value.EQID) {
    const data = await getSerialPortdApi([currentRow.value.EQID]);
    formApi.setFieldValue('BAUDRATE', data[0].BAUDRATE);
    formApi.setFieldValue('STOPBITS', data[0].STOPBITS);
    const RTS = data[0].RTS === 'Y';
    formApi.setFieldValue('RTS', RTS);
    const DTR = data[0].DTR === 'Y';
    formApi.setFieldValue('DTR', DTR);
    formApi.setFieldValue('DATABITS', data[0].DATABITS);
    formApi.setFieldValue('TERMINATORSTRING', data[0].TERMINATORSTRING);

    formApi.setFieldValue('PARITY', data[0].PARITY);
    formApi.setFieldValue('PORT', data[0].PORT);
    formApi.setFieldValue('SERIALPARSER', data[0].SERIALPARSER);
  }
};
const [Form, formApi] = useVbenForm({
  layout: 'horizontal',
  schema: serialSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3',
});
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: TestModal,
  destroyOnClose: true,
});
const testMethod = async () => {
  formModalApi.setData(null).open();
};
const onRefresh = () => {};
const viewScript = () => {
  // TODO:脚本查看修改
};
</script>
<template>
  <FormModal @success="onRefresh" />
  <Card title="串行端口设置" class="h-2/3">
    <Form class="mx-4">
      <template #RTS="slotProps">
        <Checkbox v-bind="slotProps">
          {{ $t('equipment.equipment-mg.RTSStopSend') }}
        </Checkbox>
      </template>
      <template #DTR="slotProps">
        <Checkbox v-bind="slotProps">
          {{ $t('equipment.equipment-mg.DTRDataTerminalRead') }}
        </Checkbox>
      </template>
      <template #SERIALPARSER="slotProps">
        <Input v-bind="slotProps" placeholder="">
          <template #suffix>
            <Ellipsis class="ml-auto h-6 w-6" @click="viewScript()" />
          </template>
        </Input>
      </template>
    </Form>
    <Space :size="[4, 0]">
      <Button type="primary" @click="testMethod"> 测试 </Button>
    </Space>
  </Card>
</template>
