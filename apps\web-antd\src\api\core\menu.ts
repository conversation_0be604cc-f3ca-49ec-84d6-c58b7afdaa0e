import type { RouteRecordStringComponent } from '@vben/types';

import { backendRequestClient } from '#/api/request';

/**
 * 获取用户所有菜单
 */
export async function getAllMenusApi() {
  // return requestClient.get<RouteRecordStringComponent[]>('/menu/all');
  return backendRequestClient.get<RouteRecordStringComponent[]>(
    '/platform/menus/routes',
  );
}

export async function getUserMenuApi() {
  // return requestClient.get<RouteRecordStringComponent[]>('/menu/all');
  return backendRequestClient.get<RouteRecordStringComponent[]>(
    `/platform/menus/user-routes`,
  );
}
