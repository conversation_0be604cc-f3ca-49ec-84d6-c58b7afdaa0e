<script lang="ts" setup>
import type { LookupTableApi } from '#/api/basic-static-tables/generic-meta-data';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { addLookupValueApi } from '#/api/basic-static-tables/generic-meta-data';
import { $t } from '#/locales';

import { useLooupValueSchema } from './lookup-data';

const emit = defineEmits(['success']);

const formData = ref<LookupTableApi.MetaDataLookupValue>();
const lookupName = ref<string>();
const getTitle = computed(() => {
  return $t('ui.actionTitle.create');
});

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: useLooupValueSchema(),
  showDefaultActions: false,
});

function resetForm() {
  formApi.resetForm();
  formApi.setValues(formData.value || {});
}

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    modalApi.lock();
    try {
      data.LOOKUP_NAME = lookupName.value;
      await addLookupValueApi(data);
      emit('success');
      message.success($t('ui.actionMessage.operationSuccess'));
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<LookupTableApi.MetaDataLookupValue>();
      if (data) {
        formData.value = data;
        lookupName.value = data.LOOKUP_NAME;
        formApi.setValues(formData.value);
      }
    }
  },
});
</script>

<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
    <template #prepend-footer>
      <div class="flex-auto">
        <Button type="primary" danger @click="resetForm">
          {{ $t('common.reset') }}
        </Button>
      </div>
    </template>
  </Modal>
</template>
