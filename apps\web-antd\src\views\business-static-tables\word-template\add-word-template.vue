<script lang="ts" setup>
import type { WordTemplateApi } from '#/api/business-static-tables/word-template';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  addWordTemplate,
  getFunctionName,
} from '#/api/business-static-tables/word-template';
import { getLookupValues } from '#/api/common';
import { $t } from '#/locales';

const emit = defineEmits(['success']);

const formData = ref<WordTemplateApi.WordTemplate>();

const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 提交函数
  handleSubmit: onSubmit,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'vertical',
  schema: [
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'ApiSelect',
      // 对应组件的参数
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: async () => {
          const res = await getLookupValues('WordTemplateType');
          return res.items.map((item) => ({
            label: item.TEXT,
            value: item.VALUE,
          }));
        },
        // autoSelect: 'first',
        immediate: true,
      },
      // 字段名
      fieldName: 'TYPE',
      // 界面显示的label
      label: $t('business-static-tables.word-template.type'),
      rules: 'required',
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Input',
      // 字段名
      fieldName: 'NAME',
      // 界面显示的label
      label: $t('business-static-tables.word-template.name'),
      rules: 'required',
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'ApiSelect',
      // 对应组件的参数
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: async () => {
          const data =
            (await formApi.getValues()) as WordTemplateApi.WordTemplate;
          const res = await getFunctionName(data.TYPE);
          return res.items.map((item) => ({
            label: item.Text,
            value: item.Value,
          }));
        },
        // autoSelect: 'first',
        immediate: true,
      },
      // 字段名
      fieldName: 'FUNCTION_NAME',
      // 界面显示的label
      label: $t('business-static-tables.word-template.function_name'),
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Input',
      // 字段名
      fieldName: 'STARDOC_ID',
      // 界面显示的label
      label: $t('business-static-tables.word-template.stardoc_id'),
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'ApiSelect',
      // 对应组件的参数
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: async () => {
          const res = await getLookupValues('ReportLanguage');
          return res.items.map((item) => ({
            label: item.TEXT,
            value: item.VALUE,
          }));
        },
        // autoSelect: 'first',
        immediate: true,
      },
      // 字段名
      fieldName: 'LANGUAGE',
      // 界面显示的label
      label: $t('business-static-tables.word-template.language'),
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Input',

      // 字段名
      fieldName: 'REMARK',
      // 界面显示的label
      label: $t('business-static-tables.word-template.remark'),
    },
  ],
  // wrapperClass: 'grid-cols-1',
  showDefaultActions: false,
});

/* function resetForm() {
  formApi.resetForm();
  formApi.setValues(formData.value || {});
} */

const [Modal, modalApi] = useVbenModal({
  draggable: true,
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
    // modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData<WordTemplateApi.WordTemplate>();
      if (data) {
        formData.value = data;
        formApi.setValues(formData.value);
      }
    }
  },
  title: '新增字段',
});

async function onSubmit() {
  try {
    message.loading({
      content: '正在提交中...',
      duration: 0,
      key: 'is-form-submitting',
    });
    modalApi.lock();

    const data = (await formApi.getValues()) as WordTemplateApi.WordTemplate;
    // 调用添加分类 API
    await addWordTemplate(data);
    emit('success');
    modalApi.close();
    message.success({
      content: '新增成功',
      duration: 2,
      key: 'is-form-submitting',
    });
  } catch (error) {
    message.error({
      content: `新增失败：${(error as Error).message}`,
      key: 'is-form-submitting',
    });
    modalApi.close();
  } finally {
    modalApi.unlock();
  }
}
</script>

<template>
  <Modal>
    <Form />
    <template #prepend-footer>
      <div class="flex-auto">
        <!--      <Button type="primary" danger @click="resetForm">
          {{ $t('common.reset') }}
        </Button> -->
      </div>
    </template>
  </Modal>
</template>
