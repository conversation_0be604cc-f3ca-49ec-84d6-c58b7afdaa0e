<script setup lang="ts">
import type { BatcheManagerApi } from '#/api/login-options/batch-manager';

import { ref, watch } from 'vue';

import { confirm, useVbenModal } from '@vben/common-ui';

import { Button, Col, message, Row, Space } from 'ant-design-vue';

import {
  getSampleDgList,
  removedSample,
} from '#/api/login-options/batch-manager';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import AddSampleForm from './add-sample.vue';
import {
  useBatchOrdersColumns,
  useBatchOrdersFilterSchema,
} from './batch-manager-data';
import SampleOrdtask from './batch-sample-ordtask.vue';
import CopySampleForm from './prompt-copy-sample.vue';

const props = defineProps<{
  currentTestRow: BatcheManagerApi.Batches | null;
  mode: '' | string;
}>();

const sampleOrdtaskGridRef = ref();
// const spCode = ref();

watch(
  () => props.currentTestRow,
  (_val) => {
    onRefresh();
  },
);
const colums = useBatchOrdersColumns();
const filterSchema = useBatchOrdersFilterSchema();
const queryData = async () => {
  if (!props.currentTestRow) return [];
  const data = await getSampleDgList(
    props.currentTestRow.BATCHID,
    props.mode,
    '',
  );
  return data.items;
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};

const { Grid, gridApi, CurrentRow } =
  useLimsGridsConfig<BatcheManagerApi.BatchOrders>(
    colums,
    filterSchema,
    queryData,
    girdOption,
  );

function onRefresh() {
  gridApi.query();
}

// 添加样品
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddSampleForm,
});

async function onCreate() {
  if (props.currentTestRow === null) return;

  const batchid = props.currentTestRow.BATCHID;
  const sampleGroupCode = props.currentTestRow.SAMPLEGROUPCODE;
  // const type = props.currentTestRow.TYPE;
  // if (type === 'RAW') {
  //   if (CurrentRow === null) return;
  //   spCode.value = CurrentRow?.SP_CODE;
  // }
  const mode = props.mode;
  formModalApi
    .setData({
      BATCHID: batchid,
      SAMPLEGROUPCODE: sampleGroupCode,
      // SP_CODE: spCode,
      MODE: mode,
    })
    .open();
}

// 复制样品
const [CopyFormModal, copyFormModalApi] = useVbenModal({
  connectedComponent: CopySampleForm,
});

async function onCopy() {
  if (props.currentTestRow === null) return;
  const batchId = props.currentTestRow.BATCHID;

  const current = gridApi.grid?.getCurrentRecord();
  if (current === null) return;

  const spCode = current.SP_CODE;
  const ordno = current.ORDNO;
  if (sampleOrdtaskGridRef.value.CurrentRow === null) {
    message.warn($t('login-options.batchManager.noCurrentTestData'));
    return;
  }
  const profile = sampleOrdtaskGridRef.value.CurrentRow.PROFILE;

  copyFormModalApi
    .setData({
      BATCHID: batchId,
      SP_CODE: spCode,
      PROFILE: profile,
      ORDNO: ordno,
    })
    .open();
}

// 移除样品
async function onRemove() {
  // 获取选中行

  const sampleOrder = gridApi.grid?.getCurrentRecord();
  if (sampleOrder === null) return;

  const sOrdNo = sampleOrder.ORDNO;
  try {
    await confirm({
      title: '确认移除',
      content: `确定要移除样品 ${sOrdNo} 吗？`,
      icon: 'warning',
      centered: false,
    });

    await removedSample(sOrdNo);

    message.success('移除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}
</script>

<template>
  <FormModal @success="onRefresh" />
  <CopyFormModal @success="onRefresh" />
  <div class="h-[350px] w-full">
    <!-- 使用 a-row 实现行布局 -->
    <Row :gutter="10" class="h-full flex-nowrap">
      <!-- 左侧新 Grid 区域 -->
      <Col :span="12" class="h-full overflow-hidden">
        <Grid>
          <template #toolbar-actions>
            <Space>
              <Button type="primary" @click="onCreate">
                {{ $t('ui.actionTitle.create') }}
              </Button>
              <Button type="primary" danger @click="onRemove">
                {{ $t('login-options.remove') }}
              </Button>
              <Button type="default" @click="onCopy">
                {{ $t('login-options.copy') }}
              </Button>
            </Space>
          </template>
        </Grid>
      </Col>
      <!-- 右侧Grid -->
      <Col :span="12" class="h-full overflow-hidden">
        <SampleOrdtask
          ref="sampleOrdtaskGridRef"
          :current-test-row="CurrentRow"
          :mode="props.mode"
        />
      </Col>
    </Row>
  </div>
</template>
