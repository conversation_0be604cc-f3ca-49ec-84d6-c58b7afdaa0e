<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { SolutionPreparationApi } from '#/api/dilution-management/solution-preparation';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { addReserveColumns } from '../solution preparation-data';
import {
  addSoluteApi,
  getReserveDataApi,
} from '#/api/dilution-management/solution-preparation';
import { message } from 'ant-design-vue';
import { ref } from 'vue';
const emit = defineEmits(['success']);
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const soluteType = '储备液';
    const standardRow = modalApi.getData().standardRow || [];
    const params = [standardRow.SEQUENCY, soluteType, clickRow.value.ORIGREC];
    const res = await addSoluteApi(params);
    if (!res) {
      message.error('添加失败');
      modalApi.close();
      return;
    }
    emit('success');
    modalApi.close();
  },
});
const gridOptions: VxeTableGridOptions<SolutionPreparationApi.Form> = {
  columns: addReserveColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        const data = await getReserveDataApi();
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
interface RowType {
  [key: string]: any;
}
const clickRow = ref<RowType>({});

const gridEvents: VxeGridListeners<SolutionPreparationApi.Form> = {
  currentChange: async ({ row }) => {
    if (row) {
      clickRow.value = row;
    }
  },
};
const [Grid] = useVbenVxeGrid({
  gridOptions,
  gridEvents: gridEvents,
});
</script>
<template>
  <Modal title="选择储备试剂" class="h-4/5"><Grid></Grid> </Modal>
</template>
