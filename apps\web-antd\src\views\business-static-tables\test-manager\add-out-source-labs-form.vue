<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { $addOutSourceLabsApi } from '#/api/business-static-tables';
import { $t } from '#/locales';

import { useOutSourceLabsSchema } from './data';

interface FormArgs {
  Method: string;
  TestCode: number;
}
const emit = defineEmits(['success']);

const methodRelatedData = ref<FormArgs>();

const getTitle = $t('ui.actionTitle.create', [
  $t('business-static-tables.testManager.outSourceLabs'),
]);

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: useOutSourceLabsSchema(),
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    modalApi.lock();
    try {
      data.TESTCODE = methodRelatedData.value?.TestCode;
      data.METHOD = methodRelatedData.value?.Method;
      const ret = await $addOutSourceLabsApi(data);
      if (ret === 'Exists') {
        message.error($t('commons.recordExists'));
        return;
      }
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<{
        methodRelatedData?: FormArgs;
      }>();
      if (data) {
        methodRelatedData.value = data.methodRelatedData;
      }
    }
  },
});
</script>
<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
  </Modal>
</template>
