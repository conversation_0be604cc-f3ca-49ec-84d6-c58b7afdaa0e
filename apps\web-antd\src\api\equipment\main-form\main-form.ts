import { computed } from 'vue';

import { callServer, getDataSet, getDataSetNoPage } from '#/api/core/witlab';
import { useMainFormStore } from '#/store';

interface RowType {
  EQID?: string;
  [key: string]: any;
}
const mainFormStore = useMainFormStore();
const currentRow = computed<RowType>(
  () => mainFormStore.getCurrentRow as unknown as RowType,
);
export namespace MainFormApi {
  export interface MainForm {
    [key: string]: any;
    ORIGREC: number;
    NAME: string;
    EFFECT: string;
    TYPE: string;
    SORTER: number;
  }
  export interface RowType {
    [key: string]: any;
  }
}
async function getBlanceListApi() {
  return getDataSet('BalanceDailyCalibration.GetBlanceList', []);
}

async function getCalibrationPointApi(params: Array<any>) {
  return getDataSet('BalanceDailyCalibration.GetCalibrationPoint', params);
}

async function getDailyCailbApi(params: Array<any>) {
  return getDataSet('BalanceDailyCalibration.GetDailyCailb', params);
}

async function getTrueValueApi(params: Array<any>) {
  return getDataSet('BalanceDailyCalibration.GetTrueValue', params);
}
async function getDSCertificateApi(params: Array<any>) {
  return getDataSet('BalanceDailyCalibration.DS_Certificate', params);
}
async function getCBWeightListApi() {
  return getDataSetNoPage('BalanceDailyCalibration.CB_WeightList', []);
}
async function getEventApi() {
  return getDataSetNoPage('BalanceDailyCalibration.CB_GetEvent', [
    currentRow.value?.EQID,
  ]);
}
async function openDailyCalibApi(params: Array<string>) {
  return await callServer('BalanceDailyCalibration.OpenDailyCalib', params);
}
async function checkFinishApi(params: Array<string>) {
  return await callServer('BalanceDailyCalibration.CheckFinish', params);
}
async function addPointApi(params: Array<string>) {
  return await callServer('BalanceDailyCalibration.AddPoint', params);
}
async function delPointValueApi(params: Array<string>) {
  return await callServer('BalanceDailyCalibration.DelPointValue', params);
}

export {
  addPointApi,
  checkFinishApi,
  delPointValueApi,
  getBlanceListApi,
  getCalibrationPointApi,
  getCBWeightListApi,
  getDailyCailbApi,
  getDSCertificateApi,
  getEventApi,
  getTrueValueApi,
  openDailyCalibApi,
};
