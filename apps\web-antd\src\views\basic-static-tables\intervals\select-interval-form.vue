<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message, Select, Spin } from 'ant-design-vue';

import { $getSampleCalcIntervalsApi } from '#/api/basic-static-tables';
import { $t } from '#/locales';

interface FormArgs {
  deptId: string;
}
const emit = defineEmits(['success']);
const formArgs = ref<FormArgs>();
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    if (!selectedInterval.value) {
      message.warning($t('basic-static-tables.intervals.selectInterval'));
      return;
    }
    try {
      emit('success', selectedInterval.value);
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  title: $t('basic-static-tables.intervals.updateInterval'),
  async onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<FormArgs>();
      formArgs.value = data;
      handleGetIntervals();
    }
  },
});

const loadingInterval = ref(false);
const intervals = ref([]);
const handleGetIntervals = async () => {
  if (!formArgs.value?.deptId) return;
  loadingInterval.value = true;
  try {
    const data = await $getSampleCalcIntervalsApi({
      deptId: formArgs.value.deptId,
    });
    intervals.value = data;
  } finally {
    loadingInterval.value = false;
  }
};
const selectedInterval = ref<string>();
</script>

<template>
  <Modal>
    <Spin :spinning="loadingInterval" wrapper-class-name="w-full">
      <Select
        :options="intervals"
        class="w-full"
        :field-names="{ label: 'INTERVAL', value: 'INTERVAL' }"
        v-model:value="selectedInterval"
      />
    </Spin>
  </Modal>
</template>
