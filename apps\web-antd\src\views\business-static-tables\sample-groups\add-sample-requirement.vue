<script lang="ts" setup>
import type { SampleGroupsApi } from '#/api/business-static-tables/sample-groups';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  addSamplingRequirements,
  getCondition,
  getSampleForLab,
  getSamplePosition,
  getSampleReqUnit,
  getSampleSize,
  getSampleType,
} from '#/api/business-static-tables/sample-groups';
import { $t } from '#/locales';

const emit = defineEmits(['success']);

const formData = ref<SampleGroupsApi.SamplingRequirements>();

const spCode = ref<number>(0);
const sampleGroupCode = ref<string>('');

const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 提交函数
  handleSubmit: onSubmit,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'vertical',
  schema: [
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: getSamplePosition,
        labelField: 'Text',
        valueField: 'Text',
      },
      fieldName: 'SAMPLINGPOSITION',
      label: $t('business-static-tables.sampleGroups.samplingposition'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: getSampleSize,
        labelField: 'Text',
        valueField: 'Value',
      },
      fieldName: 'SAMPLESIZE',
      label: $t('business-static-tables.sampleGroups.samplesize'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'NUMBEROFCONTAINERS',
      label: $t('business-static-tables.sampleGroups.numberofcontainers'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: getSampleType,
        labelField: 'TEXT',
        valueField: 'VALUE',
      },
      fieldName: 'SAMPLE_TYPE',
      label: $t('business-static-tables.sampleGroups.sampleType'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'CONTAINERQTY',
      label: $t('business-static-tables.sampleGroups.containerqty'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: getSampleReqUnit,
        labelField: 'UNIT_NAME',
        valueField: 'UNIT_CODE',
      },
      fieldName: 'CONTAINER_UNITS',
      label: $t('business-static-tables.sampleGroups.containerUnits'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: getSampleForLab.bind(null, 'SITE1'),
        labelField: 'SERVGRP',
        valueField: 'SERVGRP',
      },
      fieldName: 'FORLAB',
      label: $t('business-static-tables.sampleGroups.forlab'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      // 对应组件的参数
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: getCondition,
        labelField: 'CONDITION',
        valueField: 'CONDITION',
      },
      fieldName: 'CONDITION',
      label: $t('business-static-tables.sampleGroups.condition'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'PURPOSE',
      label: $t('business-static-tables.sampleGroups.purpose'),
    },
    {
      component: 'Checkbox',
      fieldName: 'CREATEINVENTORYID',
      label: $t('business-static-tables.sampleGroups.createInventoryId'),
    },
    {
      component: 'Checkbox',
      fieldName: 'ALIQUOT',
      label: $t('business-static-tables.sampleGroups.aliquot'),
    },
  ],
  // wrapperClass: 'grid-cols-1',
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  draggable: true,
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
    // modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData<SampleGroupsApi.SamplingRequirements>();
      if (data) {
        spCode.value = data.SP_CODE;
        sampleGroupCode.value = data.SAMPLEGROUPCODE;
        formData.value = data;
        formApi.setValues(formData.value);
      }
    }
  },
  title: '新增样品组模板',
});

async function onSubmit() {
  try {
    message.loading({
      content: '正在提交中...',
      duration: 0,
      key: 'is-form-submitting',
    });
    modalApi.lock();

    const data =
      (await formApi.getValues()) as SampleGroupsApi.SamplingRequirements;
    // 调用添加分类 API
    data.SP_CODE = spCode.value;
    data.SAMPLEGROUPCODE = sampleGroupCode.value;
    data.DRAWNO = 1;
    data.CREATEINVENTORYID = data.CREATEINVENTORYID ? 'Y' : 'N';
    data.ALIQUOT = data.ALIQUOT ? 'Y' : 'N';
    const aAddSamplingReq = await addSamplingRequirements(data);

    if (aAddSamplingReq && aAddSamplingReq.length > 0 && !aAddSamplingReq[0]) {
      message.warning($t('business-static-tables.sampleGroups.sExist'));
      return;
    }

    emit('success');
    modalApi.close();
    message.success({
      content: '新增成功',
      duration: 2,
      key: 'is-form-submitting',
    });
  } catch (error) {
    message.error({
      content: `新增失败：${(error as Error).message}`,
      key: 'is-form-submitting',
    });
    modalApi.close();
  } finally {
    modalApi.unlock();
  }
}
</script>

<template>
  <Modal>
    <Form />
    <template #prepend-footer>
      <div class="flex-auto"></div>
    </template>
  </Modal>
</template>
