import { callServer, getDataSet } from '#/api/core/witlab';

export namespace SolutionInfoApi {
  export interface SolutionInfo {
    [key: string]: any;
    ORIGREC: number;
    SOLUCTIONNAME: string;
    TYPE: string;
    CONFIGURE_METHOD: string;
  }
}

const $getSolutionInfoApi = async () => {
  const data = await getDataSet('SoluctionInfomation.Dg_Soluction', []);
  return data;
};

const $addSolutionInfoApi = async (
  data: Omit<SolutionInfoApi.SolutionInfo, 'ORIGREC'>,
) => {
  const result = await callServer('SoluctionInfomation.Add_SoluctionInfor', [
    data.SOLUCTIONNAME,
    data.CONFIGURE_METHOD,
    data.TYPE,
  ]);
  return result;
};

const $delSolutionInfoApi = async (data: {
  origrecs: number[];
  tableName: string;
}) => {
  const result = await callServer('Common.DeleteRows', [
    data.tableName,
    data.origrecs,
  ]);
  return result;
};

export { $addSolutionInfoApi, $delSolutionInfoApi, $getSolutionInfoApi };
