<script setup lang="ts">
import type { ProcessSpecificationsApi } from '#/api/business-static-tables/process-specifications';

import { ref, watch } from 'vue';

import { Button, message, Space } from 'ant-design-vue';

import {
  editAnalyteList,
  editTestList,
  getSpecAnalyteList,
} from '#/api/business-static-tables/process-specifications';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import AnalyteTransferModal from './edit-analyte.vue';
import TestTransferModal from './edit-test.vue';
import {
  useSpecAnalyteColumns,
  useSpecAnalyteFilterSchema,
} from './spec-manager-data';

const props = defineProps<{
  specNo: null | number;
  status: string;
}>();
const testTransferModalVisible = ref(false);
const currentSpecNo = ref<null | number>(null); // 修改定义处
const testTransferModalRef = ref();
const aTestCodes = ref<string[]>([]);
const currentTestCode = ref<number>(0); // 修改定义处
const analyteTransferModalVisible = ref(false);
const analyteTransferModalRef = ref();
const aAnalytes = ref<string[]>([]);

const isEditTestDisabled = ref(false);
const isEditAnalyteDisabled = ref(false);
const isRowEditDisabled = ref(false);

watch(
  () => props.specNo,
  (_val) => {
    if (props.status === 'Active' || props.status === 'Retired') {
      isEditTestDisabled.value = true;
      isEditAnalyteDisabled.value = true;
      isRowEditDisabled.value = true;
    } else {
      isEditTestDisabled.value = false;
      isEditAnalyteDisabled.value = false;
      isRowEditDisabled.value = false;
    }
    onRefresh();
  },
);
const colums = useSpecAnalyteColumns();
const filterSchema = useSpecAnalyteFilterSchema();
const queryData = async () => {
  if (!props.specNo) return [];
  return getSpecAnalyteList(props.specNo);
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};

const {
  Grid: LimitsGrid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
} = useLimsGridsConfig<ProcessSpecificationsApi.SpecAnalytes>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

function onRefresh() {
  gridApi.query();
}

function openTestTransferModal() {
  if (!props.specNo) {
    message.warning('请先选择一个质量标准！');
    return;
  }

  aTestCodes.value = getTestCodes();
  currentSpecNo.value = props.specNo;

  // console.log('specno', currentSpecNo.value);
  // console.log('aTestCodes', aTestCodes.value);

  testTransferModalRef.value?.open();
}

async function handleTestTransferSubmit(data: {
  selectedItems: any[];
  selectedKeys: string[];
}) {
  // message.success(`已提交选中项: ${data.selectedKeys.join(', ')}`);
  // 这里可以调用保存接口
  // 刷新检项列表
  await editTestList(currentSpecNo.value ?? 0, data.selectedKeys, false);
  onRefresh();
}

function getTestCodes() {
  const allData = gridApi.grid?.getData(); // 获取表格所有行数据

  const testCodes = allData.map((item) => item.TESTCODE) as string[]; // 提取 TESTCODE 字段
  // console.log('所有 TESTCODE:', testCodes);
  return testCodes;
}

function openAnalyteTransferModal() {
  const currentRow = gridApi.grid?.getCurrentRecord();
  if (!currentRow) return;

  currentTestCode.value = currentRow.TESTCODE;

  aAnalytes.value = [];

  const allData = gridApi.grid?.getData(); // 获取表格所有行数据

  aAnalytes.value = allData
    .filter((item) => item.TESTCODE === currentRow.TESTCODE)
    .map((item) => item.ANALYTE);

  // console.log('testcode', currentTestCode.value);
  // console.log('aAnalytes', aAnalytes.value);
  analyteTransferModalRef.value?.open();
}

async function handleAnalyteTransferSubmit(data: {
  selectedItems: any[];
  selectedKeys: string[];
}) {
  // message.success(`已提交选中项: ${data.selectedKeys.join(', ')}`);
  // 这里可以调用保存接口
  // 刷新检项列表

  if (!props.specNo) {
    message.warning('质量标准编号不能为空');
    return;
  }

  await editAnalyteList(
    props.specNo,
    currentTestCode.value ?? 0,
    data.selectedKeys,
  );
  onRefresh();
}

async function HasLimitData() {
  return gridApi.grid.getData().length > 0;
}

// 暴露方法给父组件
defineExpose({
  HasLimitData,
});
</script>

<template>
  <TestTransferModal
    ref="testTransferModalRef"
    :spec-no="currentSpecNo"
    :a-test-codes="aTestCodes"
    @submit="handleTestTransferSubmit"
    @cancel="testTransferModalVisible = false"
  />
  <AnalyteTransferModal
    ref="analyteTransferModalRef"
    :test-code="currentTestCode"
    :a-analytes="aAnalytes"
    @submit="handleAnalyteTransferSubmit"
    @cancel="analyteTransferModalVisible = false"
  />
  <div class="h-[500px] w-full">
    <LimitsGrid>
      <template #toolbar-actions>
        <Space :size="[4, 0]" wrap>
          <Button
            type="default"
            @click="openTestTransferModal"
            :disabled="isEditTestDisabled"
          >
            {{
              $t('business-static-tables.process-specifications.edit-testno')
            }}
          </Button>
          <Button
            type="default"
            @click="openAnalyteTransferModal"
            :disabled="isEditAnalyteDisabled"
          >
            {{
              $t('business-static-tables.process-specifications.edit-analyte')
            }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent(row)">
            {{ $t('business-static-tables.save') }}
          </Button>
          <Button type="link" @click="cancelRowEvent(row)">
            {{ $t('business-static-tables.cancel') }}
          </Button>
        </template>
        <template v-else>
          <Button
            type="link"
            @click="editRowEvent(row)"
            :disabled="isRowEditDisabled"
          >
            {{ $t('business-static-tables.edit') }}
          </Button>
        </template>
      </template>
    </LimitsGrid>
  </div>
</template>

<style scoped></style>
