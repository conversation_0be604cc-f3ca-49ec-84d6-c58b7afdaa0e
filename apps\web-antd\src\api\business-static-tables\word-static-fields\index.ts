import { callServer, getDataSet } from '#/api/core/witlab';

export namespace WordStaticFieldsApi {
  export interface WordStaticFields {
    ORIGREC: number;
    GROUP_BY: string;
    MARKING_NAME: string;
    REPLACE_NAME: string;
    SORTER: number;
  }
}

/**
 * 获取WORD替换字段列表数据
 */
async function getWordFieldsList() {
  return getDataSet('SC_WORD.dgFields', []);
}

/**
 * 创建WORD替换字段
 * @param data WORD替换字段数据
 */
async function addWordField(data: WordStaticFieldsApi.WordStaticFields) {
  return await callServer('SC_WORD.addStaticField', [data.MARKING_NAME]);
}

/**
 * 删除WORD替换字段
 */
async function deleteWordField(origrec: number[]) {
  return await callServer('Common.DeleteRows', [
    'WORD_TEMPLATE_FIELDS',
    origrec,
  ]);
}

export { addWord<PERSON>ield, deleteWordField, getWordFieldsList };
