import { callServer, getDataSet } from '#/api/core';

export namespace XPositionApi {
  export interface XPosition {
    [key: string]: any;
    POSITION: string;
  }
}

const $getXPositionApi = async () => {
  return await getDataSet('Positions.dsGetPositions', []);
};

const $addXPositionApi = async (
  data: Omit<XPositionApi.XPosition, 'ORIGREC'>,
) => {
  return await callServer('Positions.scAddPosition', [
    data.POSITION,
    data.eventCode,
    data.comment,
  ]);
};

const $delXPositionApi = async (positions: string[]) => {
  return await callServer('Positions.scDeletePosition', [positions]);
};

export { $addXPositionApi, $delXPositionApi, $getXPositionApi };
