<script setup lang="ts">
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { CourseSchedulesFormApi } from '#/api/equipment/course-schedules';
import { ref, watch, computed } from 'vue';
import {
  getSchedule<PERSON>pi,
  getMethod<PERSON>pi,
  getParticpantsApi,
  getHistoryApi,
  scDeleteTrainingApi,
  inviteParticiPantApi,
  certifyApi,
  updateProviderApi,
} from '#/api/equipment/course-schedules';
import { Page } from '@vben/common-ui';
import dayjs, { Dayjs } from 'dayjs';

import {
  Button,
  DatePicker,
  Select,
  SelectOption,
  Space,
  TabPane,
  Tabs,
  message,
  Modal as AModal,
  Calendar,
} from 'ant-design-vue';
import { useVbenModal } from '@vben/common-ui';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { $t } from '#/locales';
import AddModal from './components/add-modal.vue';
import PersonModal from './components/person-modal.vue';

import {
  courseColumns,
  partColumns,
  methodColumns,
  historyColumns,
} from './course-schedules-data';
interface RowType {
  [key: string]: any;
}

const [AddFormModal, addFormModalApi] = useVbenModal({
  connectedComponent: AddModal,
  destroyOnClose: true,
});
const [PersonFormModal, personFormModalApi] = useVbenModal({
  connectedComponent: PersonModal,
  destroyOnClose: true,
});
const clickRow = ref<RowType | null>(null);
const clickPartRow = ref<RowType | null>(null);

const viewType = ref('网格');
const options = ref([
  { value: '网格', label: '网格' },
  { value: '月', label: '月' },
  // { value: '周', label: '周' },
  // { value: '工作周', label: '工作周' },
  // { value: '天', label: '天' },
]);
watch(
  clickRow,
  (newRow: RowType) => {
    if (newRow) {
    }
  },
  { deep: true },
);
const tabsChange = (activeKey: string | number) => {
  if (activeKey === '课程方法') {
    methodGridApi.query();
  } else {
    historyGridApi.query();
  }
};
const gridEvents: VxeGridListeners<CourseSchedulesFormApi.CourseSchedulesForm> =
  {
    currentChange: async ({ row }) => {
      if (row) {
        clickRow.value = row;
        partGridApi.query();
        if (activeKey.value === '课程方法') {
          methodGridApi.query();
        } else {
          historyGridApi.query();
        }
      }
    },
  };
const partGridEvents: VxeGridListeners<CourseSchedulesFormApi.CourseSchedulesForm> =
  {
    currentChange: async ({ row }) => {
      if (row) {
        clickPartRow.value = row;
      }
    },
  };
const gridOptions: VxeTableGridOptions<CourseSchedulesFormApi.CourseSchedulesForm> =
  {
    columns: courseColumns(),
    stripe: true,
    border: true,
    editConfig: {
      mode: 'row',
      trigger: 'click',
    },
    height: 'auto',
    pagerConfig: {},
    proxyConfig: {
      ajax: {
        query: async () => {
          const startDate = startTime.value.toISOString();
          const endDate = endTime.value.toISOString();
          let params = [
            null,
            null,
            startDate,
            endDate,
            null,
            null,
            null,
            null,
            null,
          ];
          if (searchParams.value.courseName) {
            params = [
              searchParams.value.courseName,
              searchParams.value.course,
              searchParams.value.startTime.toISOString(),
              searchParams.value.endTime.toISOString(),
              searchParams.value.position,
              searchParams.value.participant,
              searchParams.value.participantStatus,
              searchParams.value.trainingStatus,
              null,
            ];
          }
          const data = await getScheduleApi(params);
          calendarData.value = data.items;
          return data;
        },
      },
    },
    exportConfig: {},
    showOverflow: true,
    rowConfig: {
      keyField: 'origrec',
      isCurrent: true,
    },
    keepSource: true,
    toolbarConfig: {
      custom: true,
      export: true,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  };
const partGridOptions: VxeTableGridOptions<CourseSchedulesFormApi.CourseSchedulesForm> =
  {
    columns: partColumns(),
    stripe: true,
    border: true,
    editConfig: {
      mode: 'row',
      trigger: 'click',
    },
    height: 'auto',
    pagerConfig: {},

    proxyConfig: {
      ajax: {
        query: async () => {
          if (!clickRow.value) {
            return [];
          }
          const params = [clickRow.value.COURSENO];
          const data = await getParticpantsApi(params);
          return data;
        },
      },
    },
    exportConfig: {},
    showOverflow: true,
    rowConfig: {
      keyField: 'origrec',
      isCurrent: true,
    },
    toolbarConfig: {
      custom: true,
      export: true,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  };
const methodGridOptions: VxeTableGridOptions<CourseSchedulesFormApi.CourseSchedulesForm> =
  {
    columns: methodColumns(),
    stripe: true,
    border: true,
    editConfig: {
      mode: 'row',
      trigger: 'manual',
    },
    height: 'auto',
    pagerConfig: {},

    proxyConfig: {
      ajax: {
        query: async () => {
          if (!clickRow.value) {
            return [];
          }
          const params = [clickRow.value.COURSECODE];
          const data = await getMethodApi(params);
          return data;
        },
      },
    },
    exportConfig: {},
    showOverflow: true,
    rowConfig: {
      keyField: 'origrec',
    },
    toolbarConfig: {
      custom: true,
      export: true,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  };
const historyGridOptions: VxeTableGridOptions<CourseSchedulesFormApi.CourseSchedulesForm> =
  {
    columns: historyColumns(),
    stripe: true,
    border: true,
    editConfig: {
      mode: 'row',
      trigger: 'manual',
    },
    height: 'auto',
    pagerConfig: {},

    proxyConfig: {
      ajax: {
        query: async () => {
          if (!clickRow.value) {
            return [];
          }
          const params = [clickRow.value.ORIGREC];
          const data = await getHistoryApi(params);
          return data;
        },
      },
    },
    exportConfig: {},
    showOverflow: true,
    rowConfig: {
      keyField: 'origrec',
    },
    toolbarConfig: {
      custom: true,
      export: true,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  };
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: gridEvents,
  tableTitle: '培训计划',
});
const [PartGrid, partGridApi] = useVbenVxeGrid({
  gridOptions: partGridOptions,
  gridEvents: partGridEvents,
  tableTitle: '参与者',
});
const [MethodGrid, methodGridApi] = useVbenVxeGrid({
  gridOptions: methodGridOptions,
  gridEvents: {},
});
const [HistoryGrid, historyGridApi] = useVbenVxeGrid({
  gridOptions: historyGridOptions,
  gridEvents: {},
});

const startTime = ref(dayjs().startOf('month'));
const endTime = ref(dayjs().endOf('month'));
const activeKey = ref('课程方法');
const tabList = ref([
  { title: '课程方法', page: 'CourseMethod' },
  { title: '操作历史', page: 'CourseContent' },
]);
const searchParams = ref<Partial<CourseSchedulesFormApi.CourseSchedulesForm>>(
  {},
);
const addCourse = () => {
  addFormModalApi.setData(null).open();
};
const deleteCourse = () => {
  AModal.confirm({
    title: '您确定吗？',
    content: '您确定要删除选定的记录吗?',
    cancelText: '否',
    okText: '是',
    async onOk() {
      const params = [
        clickRow.value?.ORIGREC,
        clickRow.value?.COURSENO,
        clickRow.value?.STATUS,
      ];
      const res = await scDeleteTrainingApi(params);
      if (res[0]) {
        gridApi.query();
      } else {
        message.warning(`错误暂时不能删除`);
      }
      console.warn('删除');
    },
    onCancel() {},
  }); // Delete logic here
};
const printReport = () => {
  //TODO ：打印报表
};
const participant = () => {
  const data = clickRow.value;
  if (!clickRow.value) {
    message.warning('请选择课程');
    return;
  }
  if (!data?.TRAINWAY) {
    message.warning('请填写培训方式');
    return;
  }
  if (!data?.EVALUATIONMODE) {
    message.warning('请填写考核方式');
    return;
  }
  if (!data?.COURSELOCATION) {
    message.warning('请填写培训地点');
    return;
  }
  if (!data?.TRAINER) {
    message.warning('请填写培训讲师');
    return;
  }
  personFormModalApi
    .setData({
      clickRow: clickRow.value,
      tableData: partGridApi.grid.getTableData().tableData,
    })
    .open();
};
const sendInvitation = async () => {
  if (!clickPartRow.value) {
    message.warning('请选择参与者');
    return;
  }
  if (!clickRow.value) {
    message.warning('请选择课程');
    return;
  }
  const startDate = clickRow.value.STARTDATE;
  if (dayjs().isAfter(startDate)) {
    message.warning('在过去的培训执行中你不能发送邀请！');
    return;
  }
  const params = [[clickPartRow.value.ORIGREC]];
  const res = await inviteParticiPantApi(params);
  if (res) {
    partGridApi.query();
  }
};
const authentication = async () => {
  if (!clickPartRow.value) {
    message.warning('请选择参与者');
    return;
  }
  if (!clickPartRow.value.STUDENTSCORE) {
    message.warning('请填写培训分数');
    return;
  }
  const params = [[clickPartRow.value.ORIGREC], clickPartRow.value.COURSENO];
  const res = await certifyApi(params);
  if (res) {
    partGridApi.query();
  }
};
const onRefreshGrid = () => {
  gridApi.query();
};
const onRefreshParticipant = () => {
  partGridApi.query();
};
const attachCertificate = () => {};
const viewCertificate = () => {};
const isShowInvitation = computed(() => {
  return !(clickPartRow.value != null && clickPartRow.value.STATUS === 'Draft');
});
const isShowCertificate = computed(() => {
  return !(clickPartRow.value != null && clickPartRow.value.STATUS === 'Taken');
});
const isAllowFile = computed(() => {
  return !(
    clickPartRow.value != null && clickPartRow.value.STATUS === 'Certified'
  );
});
const hasEditStatus = (row: RowType) => {
  return gridApi.grid?.isEditByRow(row);
};

const editRowEvent = (row: RowType) => {
  gridApi.grid?.setEditRow(row);
};

const saveRowEvent = async () => {
  await gridApi.grid?.clearEdit();
  if (!clickRow.value) {
    message.warning('未选择行，无法保存');
    return;
  }
  const row = clickRow.value;
  const rowList = Object.keys(row).map((key) => {
    const isNum = typeof row[key] === 'number' ? 'N' : 'S';
    return [key, row[key], isNum, ''];
  });
  const params = [
    'dgdCourseSched',
    'COURSE_SCHEDULES',
    rowList,
    row.ORIGREC,
    null,
  ];
  await updateProviderApi(params);
};

const cancelRowEvent = (row: RowType) => {
  gridApi.grid?.clearEdit().then(() => {
    // 还原行数据
    gridApi.grid.revertData(row);
  });
};
const monthValue = ref<Dayjs>(dayjs());
const getListData = (value: Dayjs) => {
  const tableData = calendarData.value;
  const currentMonth = monthValue.value.month() + 1;
  const currentYear = monthValue.value.year();
  if (value.month() + 1 !== currentMonth) return;
  if (value.year() !== currentYear) return;

  const filteredDates = tableData.filter((item) => {
    const dateObj = dayjs(item.STARTDATE);
    return dateObj.month() + 1 === currentMonth;
  });
  for (const item of filteredDates) {
    item.itemDay = dayjs(item.STARTDATE).date();
  }
  const listData = filteredDates
    .filter((item) => item.itemDay === value.date())
    .map((item) => {
      return {
        content: item.COURSENAME,
      };
    });
  return listData;
};
const viewChange = (value: string) => {
  if (value === '月') {
    calendarDate.value = startTime.value;
  }
};
const calendarData = ref();
const calendarDate = ref();
const dateChange = async () => {
  const year = calendarDate.value.year();
  const month = calendarDate.value.month() + 1;
  const firstDay = dayjs(`${year}-${month}-01`).startOf('month'); // 获取当月第一天
  const lastDay = firstDay.endOf('month'); // 获取当月最后一天
  const startDate = firstDay.toISOString();
  const endDate = lastDay.toISOString();
  let params = [null, null, startDate, endDate, null, null, null, null, null];

  calendarData.value = await getScheduleApi(params);
};
</script>
<template>
  <Page auto-content-height>
    <AddFormModal @success="onRefreshGrid" />
    <PersonFormModal @success="onRefreshParticipant" />
    <Space
      :size="[8, 0]"
      wrap
      v-show="viewType === '月'"
      class="flex h-[5%] w-full justify-end p-2.5"
    >
      <span> {{ $t('equipment.course-schedules.view') }}</span>
      <Select
        v-model:value="viewType"
        style="width: 150px"
        @change="viewChange"
      >
        <SelectOption
          v-for="item in options"
          :value="item.value"
          :key="item.value"
        >
          {{ item.label }}
        </SelectOption>
      </Select>
    </Space>
    <Calendar
      v-model:value="calendarDate"
      v-if="viewType === '月'"
      :fullscreen="true"
      mode="month"
    >
      <template
        #headerRender="{ value: current, type, onChange, onTypeChange }"
      >
        <div class="w-full text-right">
          <DatePicker
            v-model:value="calendarDate"
            picker="month"
            @change="dateChange"
          />
        </div>
      </template>
      <template #dateCellRender="{ current }">
        <ul class="events">
          <li
            v-for="item in getListData(current)"
            :key="item.content"
            style="
              background: rgb(11, 153, 213);
              color: white;
              font-weight: 500;
              border-radius: 5px;
            "
            class="bg-[rgb(11, 153, 213)]"
          >
            {{ item.content }}
          </li>
        </ul>
      </template>
    </Calendar>
    <Grid class="h-2/3" v-show="viewType === '网格'">
      <template #toolbar-actions>
        <Space :size="[8, 0]" wrap>
          <Button type="primary" @click="addCourse">
            {{ $t('equipment.add') }}
          </Button>
          <Button type="primary" @click="deleteCourse">
            {{ $t('equipment.delete') }}
          </Button>
          <Button type="primary" @click="printReport">
            {{ $t('equipment.course-form.printReport') }}
          </Button>
        </Space>
      </template>
      <template #toolbar-tools>
        <Space :size="[8, 0]" wrap>
          <span> {{ $t('equipment.course-schedules.view') }}</span>
          <Select
            v-model:value="viewType"
            style="width: 150px"
            @change="viewChange"
          >
            <SelectOption
              v-for="item in options"
              :value="item.value"
              :key="item.value"
            >
              {{ item.label }}
            </SelectOption>
          </Select>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent()">
            {{ $t('business-static-tables.save') }}
          </Button>
          <Button type="link" @click="cancelRowEvent(row)">
            {{ $t('business-static-tables.cancel') }}
          </Button>
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">
            {{ $t('business-static-tables.edit') }}
          </Button>
        </template>
      </template>
    </Grid>
    <div class="flex h-1/3" v-show="viewType === '网格'">
      <PartGrid height="100%" class="w-1/2">
        <template #toolbar-actions>
          <Space :size="[8, 0]" wrap>
            <Button type="primary" @click="participant">
              {{ $t('equipment.course-schedules.participant') }}
            </Button>
            <Button
              type="primary"
              @click="sendInvitation"
              :disabled="isShowInvitation"
            >
              {{ $t('equipment.course-schedules.sendInvitation') }}
            </Button>
            <Button
              type="primary"
              @click="authentication"
              :disabled="isShowCertificate"
            >
              {{ $t('equipment.course-schedules.authentication') }}
            </Button>
            <Button
              type="primary"
              @click="attachCertificate"
              :disabled="isAllowFile"
            >
              {{ $t('equipment.course-schedules.attachCertificate') }}
            </Button>
            <Button
              type="primary"
              @click="viewCertificate"
              :disabled="isAllowFile"
            >
              {{ $t('equipment.course-schedules.viewCertificate') }}
            </Button>
          </Space>
        </template>
      </PartGrid>
      <div class="h-5/6 w-1/2">
        <Tabs
          v-model:active-key="activeKey"
          class="w-full"
          @change="tabsChange"
        >
          <TabPane
            v-for="item in tabList"
            :key="item.title"
            :tab="item.title"
            class="w-full"
          />
        </Tabs>
        <MethodGrid v-show="activeKey === '课程方法'" height class="h-full" />
        <HistoryGrid v-show="activeKey === '操作历史'" height class="h-full" />
      </div>
    </div>
  </Page>
</template>
