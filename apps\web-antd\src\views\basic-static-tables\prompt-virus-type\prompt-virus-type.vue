<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { VirusTypeApi } from '#/api/basic-static-tables/prompt-virus-type';

import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  $delCommonApi,
  $getVirusTypeApi,
} from '#/api/basic-static-tables/prompt-virus-type';
import { confirm } from '#/utils/utils';

import AddvirusTypeForm from './add-virus-type-form.vue';
import PromptVirusInfo from './prompt-virus-info.vue';
import { useColumns, useFilterSchema } from './prompt-virus-type-data';

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddvirusTypeForm,
  destroyOnClose: true,
});
const gridOptions: VxeTableGridOptions<VirusTypeApi.VirusType> = {
  columns: useColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        return await $getVirusTypeApi();
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const formOptions: VbenFormProps = {
  schema: useFilterSchema(),
  submitOnChange: false,
  collapsed: true,
  showCollapseButton: true,
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: {},
  formOptions,
});

function onCreate() {
  formModalApi.setData(null).open();
}

async function onDelete() {
  const checkOrig: number[] =
    (gridApi.grid
      ?.getCheckboxRecords()
      .map((item) => item.ORIGREC) as number[]) || [];

  if (checkOrig.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }
  await confirm('是否确认删除选中的数据？', '删除确认');
  await $delCommonApi({
    origrecs: checkOrig,
    tableName: 'VIRUSTYPE',
  });
  message.success('删除成功！');
  onRefresh();
}

function hasEditStatus(row: VirusTypeApi.VirusType) {
  return gridApi.grid?.isEditByRow(row);
}

function editRowEvent(row: VirusTypeApi.VirusType) {
  gridApi.grid?.setEditRow(row);
}

async function saveRowEvent(_row: VirusTypeApi.VirusType) {
  await gridApi.grid?.clearEdit();
  gridApi.setLoading(true);
  setTimeout(() => {
    gridApi.setLoading(false);
    message.success({
      content: `保存成功！`,
    });
  }, 600);
}

const cancelRowEvent = (_row: VirusTypeApi.VirusType) => {
  gridApi.grid?.clearEdit();
};

function onRefresh() {
  gridApi.query();
}

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: PromptVirusInfo,
  destroyOnClose: true,
  class: 'w-[900px]',
});

const showVirusInfo = (row: VirusTypeApi.VirusType) => {
  formDrawerApi.setData(row).open();
};
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <FormDrawer />
    <Grid>
      <template #toolbar-actions>
        <Space :size="[4, 0]" wrap>
          <Button type="primary" @click="onCreate">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="onDelete">
            {{ $t('ui.actionTitle.delete') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent(row)">保存</Button>
          <Button type="link" @click="cancelRowEvent(row)">取消</Button>
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">编辑</Button>
          <Button type="link" @click="showVirusInfo(row)">详情</Button>
        </template>
      </template>
    </Grid>
  </Page>
</template>
