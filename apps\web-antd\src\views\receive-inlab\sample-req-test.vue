<script lang="ts" setup>
import type { BatcheManagerApi } from '#/api/login-options/batch-manager';

import { ref } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';

import { getSampleReqTestsList } from '#/api/login-options/batch-manager';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import {
  useBatchReqTestsColumns,
  useBatchReqTestsFilterSchema,
} from '../login-options/batch-manager/batch-manager-data';

const requirement = ref<BatcheManagerApi.BatchSamplingRequirement | null>(null);

const colums = useBatchReqTestsColumns();
const filterSchema = useBatchReqTestsFilterSchema();
const queryData = async () => {
  const data = drawerApi.getData<BatcheManagerApi.BatchSamplingRequirement>();
  if (!data) {
    return;
  }
  const sOrdNo = data.ORDNO;
  const inventoryId = data.INVENTORYID;
  const dataResult = await getSampleReqTestsList(sOrdNo, inventoryId);
  return dataResult.items;
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};

const { Grid } = useLimsGridsConfig<BatcheManagerApi.BatchSamplingRequTests>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

const [Drawer, drawerApi] = useVbenDrawer({
  onConfirm: onSubmit,
  onOpenChange(isOpen) {
    if (isOpen) {
      const data =
        drawerApi.getData<BatcheManagerApi.BatchSamplingRequirement>();
      if (data) {
        requirement.value = data;
        // gridApi?.query();
      } else {
        // gridApi?.reload();
      }
    }
  },
  showCancelButton: false,
  showConfirmButton: false,
});

async function onSubmit() {
  drawerApi.close();
}
</script>

<template>
  <Drawer class="w-full max-w-[800px]" title="关联测试">
    <Page auto-content-height>
      <Grid />
    </Page>
  </Drawer>
</template>
