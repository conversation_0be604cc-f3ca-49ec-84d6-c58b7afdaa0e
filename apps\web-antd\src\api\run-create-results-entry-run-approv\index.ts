import type { Dayjs } from 'dayjs';

import { getDataSet } from '../core';

// AUDITTRL
export interface Audittrl {
  [key: string]: any;
  EVENT_LEVEL: string;
  EVENT_TYPE: string;
  EVENT: string;
  FULLNAME: string;
  AUDIT_DT: Dayjs | string;
  CHANGE_COMMENT: string;
  ESIG: string;
  ESIG_COMMENT: string;
  ESIG_PASSWORD: string;
  ESIG_WITNESS: string;
  ESIG_WITNESS_NAME: string;
  ESIG_AGREEMENT: string;
  ESIG_AGREEMENT_TEXT: string;
}

const $signatures_dgApi = async (data: any[]) => {
  return await getDataSet('TraceabilityView.Signatures_dg', data);
};

export { $signatures_dgApi };
