import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { CourseSchedulesFormApi } from '#/api/equipment/course-schedules';

import { $t } from '#/locales';
import {
  getCboScheduledCoursesApi,
  getDsUsersInACourseApi,
  getCboStatusApi,
  getServiceGroupApi,
  getDsStatusApi,
} from '#/api/equipment/course-schedules';
import dayjs from 'dayjs';

export function courseColumns(): VxeTableGridOptions<CourseSchedulesFormApi.CourseSchedulesForm>['columns'] {
  return [
    {
      align: 'center',
      field: 'COURSENO',
      title: $t('equipment.course-schedules.courseId'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'DISPSTATUS',
      title: $t('equipment.course-schedules.status'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'COURSENAME',
      title: $t('equipment.course-schedules.courseName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'STARTDATE',
      title: $t('equipment.course-schedules.startDateTime'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ENDDATE',
      title: $t('equipment.course-schedules.endDateTime'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'COURSELOCATION',
      title: $t('equipment.course-schedules.location'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      editRender: { name: 'input' },
    },
    {
      align: 'center',
      field: 'TRAINER',
      title: $t('equipment.course-schedules.instructor'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      editRender: { name: 'input' },
    },
    {
      align: 'center',
      field: 'TRAINWAY',
      title: $t('equipment.course-schedules.trainingMethod'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      editRender: { name: 'input' },
    },
    {
      align: 'center',
      field: 'EVALUATIONMODE',
      title: $t('equipment.course-schedules.assessmentMethod'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      editRender: { name: 'input' },
    },
    {
      align: 'center',
      field: 'COMMENTS',
      title: $t('equipment.course-schedules.remarks'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      editRender: { name: 'input' },
    },
  ];
}
export function partColumns(): VxeTableGridOptions<CourseSchedulesFormApi.CourseSchedulesForm>['columns'] {
  return [
    {
      align: 'center',
      field: 'STATUS',
      title: $t('equipment.course-schedules.status'),
      filterRender: {
        name: 'TableFilterInput',
      },
      formatter: 'formatTranslate',
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'USRNAM',
      title: $t('equipment.course-schedules.userCode'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'FULLNAME',
      title: $t('equipment.course-schedules.username'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'STARDOC_IMG',
      title: $t('equipment.course-schedules.hasAttachment'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'STUDENTSCORE',
      title: $t('equipment.course-schedules.score'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'OVERALLRATING',
      title: $t('equipment.course-schedules.overallRating'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'GUIDERATING',
      title: $t('equipment.course-schedules.instructorRating'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'EXERCISESRATING',
      title: $t('equipment.course-schedules.exerciseRating'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'PRESENTATIONRATING',
      title: $t('equipment.course-schedules.demoRating'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'PARTICIPANTCOMMENT',
      title: $t('equipment.course-schedules.remarks'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}
export function methodColumns(): VxeTableGridOptions<CourseSchedulesFormApi.CourseSchedulesForm>['columns'] {
  return [
    {
      align: 'center',
      field: 'METHOD',
      title: $t('equipment.course-schedules.method'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'TESTNO',
      title: $t('equipment.course-schedules.testName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'SERVGRP',
      title: $t('equipment.course-schedules.position'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: { name: 'input' },
      sortable: true,
    },
  ];
}
export function historyColumns(): VxeTableGridOptions<CourseSchedulesFormApi.CourseSchedulesForm>['columns'] {
  return [
    {
      align: 'center',
      field: 'STEPNAME',
      title: $t('equipment.course-schedules.stepName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'ACTIONNAME',
      title: $t('equipment.course-schedules.actionName'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'AUDITFULLNAME',
      title: $t('equipment.course-schedules.operator'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: { name: 'input' },
      sortable: true,
    },
    {
      align: 'center',
      field: 'AUDITDATE',
      title: $t('equipment.course-schedules.operationTime'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: { name: 'input' },
      sortable: true,
    },
    {
      align: 'center',
      field: 'AUDITCOMMENT',
      title: $t('equipment.course-schedules.submitRemark'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      editRender: { name: 'input' },
      sortable: true,
    },
  ];
}
export function searchSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      componentProps: {
        api: getCboScheduledCoursesApi,
        labelField: 'COURSENAME',
        valueField: 'COURSECODE',
        class: 'w-full',
      },

      fieldName: 'course',
      rules: 'required',
      label: $t('equipment.course-schedules.course'),
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: getDsUsersInACourseApi,
        labelField: 'FULLNAME',
        valueField: 'USRNAM',
        class: 'w-full',
      },

      fieldName: 'participant',
      label: $t('equipment.course-schedules.participant'),
      rules: 'required',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        api: getCboStatusApi,
        labelField: 'STATUS',
        valueField: 'STATUS',
        class: 'w-full',
      },

      fieldName: 'participantStatus',
      label: $t('equipment.course-schedules.participantStatus'),
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: getServiceGroupApi,
        labelField: 'SERVGRP',
        valueField: 'SERVGRP',
        class: 'w-full',
      },
      fieldName: 'position',
      label: $t('equipment.course-schedules.position'),
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: getDsStatusApi,
        labelField: 'DISPSTATUS',
        valueField: 'STATUS',
        class: 'w-full',
      },
      fieldName: 'trainingStatus',
      label: $t('equipment.course-schedules.trainingStatus'),
      rules: 'required',
    },

    {
      component: 'DatePicker',
      fieldName: 'startTime',
      label: $t('equipment.course-schedules.startTime'),
      componentProps: {
        type: 'date',
        // format: 'YYYY-MM-DD',
        // valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      component: 'DatePicker',
      fieldName: 'endTime',
      label: $t('equipment.course-schedules.endTime'),
      componentProps: {
        type: 'date',
        // format: 'YYYY-MM-DD',
        // valueFormat: 'YYYY-MM-DD',
      },
    },
  ];
}
export function addSchema(): VbenFormSchema[] {
  return [
    {
      component: 'DatePicker',
      fieldName: 'startTime',
      label: $t('equipment.course-schedules.startTime'),
      componentProps: {
        type: 'date',
        format: 'YYYY-MM-DD HH:mm:ss',
        showTime: {
          defaultValue: dayjs('00:00:00', 'HH:mm:ss'),
        },
        // valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      component: 'DatePicker',
      fieldName: 'endTime',
      label: $t('equipment.course-schedules.endTime'),
      componentProps: {
        type: 'date',
        format: 'YYYY-MM-DD HH:mm:ss',
        showTime: {
          defaultValue: dayjs('00:00:00', 'HH:mm:ss'),
        },
        // valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: getCboScheduledCoursesApi,
        labelField: 'COURSENAME',
        valueField: 'COURSECODE',
        class: 'w-full',
      },

      fieldName: 'course',
      rules: 'required',
      label: $t('equipment.course-schedules.course'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'location',
      label: $t('equipment.course-schedules.location'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'instructor',
      label: $t('equipment.course-schedules.instructor'),
    },

    {
      component: 'Textarea',
      componentProps: {},
      fieldName: 'remarks',
      label: $t('equipment.course-schedules.remarks'),
    },
  ];
}
