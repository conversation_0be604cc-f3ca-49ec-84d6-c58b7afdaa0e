---
description:
globs:
alwaysApply: false
---
# 系统管理模块开发规范

## 目录结构

系统管理模块位于 `apps/web-antd/src/views/system/` 目录下，包含以下子模块：

- `dept/`: 部门管理
- `menu/`: 菜单管理
- `role/`: 角色管理
- `user/`: 用户管理

每个子模块的标准目录结构：
```
module/
├── data.ts          # 数据定义和配置
├── list.vue         # 列表页面
└── modules/         # 子模块组件
    └── form.vue     # 表单组件
```

## 代码规范

### 1. 数据定义 (data.ts)

- 使用 TypeScript 类型定义
- 导出函数而不是常量，以支持动态配置
- 表单配置使用 `VbenFormSchema` 类型
- 表格配置使用 `VxeTableGridOptions` 类型

示例：
```typescript
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'name',
      label: $t('system.xxx.name'),
      rules: 'required',
    }
  ];
}
```

### 2. 列表页面 (list.vue)

- 使用 `Page` 组件作为页面容器
- 使用 `useVbenVxeGrid` 处理表格逻辑
- 实现标准的 CRUD 操作
- 支持搜索、刷新、导出等功能

标准功能：
- 新增按钮
- 表格操作列（编辑、删除）
- 状态切换
- 分页查询
- 条件搜索

### 3. 表单组件 (form.vue)

- 使用 `useVbenDrawer` 或 `useVbenModal` 作为容器
- 使用 `useVbenForm` 处理表单逻辑
- 实现表单验证
- 支持创建和编辑模式

表单规范：
- 必填字段使用 `rules: 'required'`
- 使用 `z` 进行复杂验证
- 支持表单重置
- 统一的提交和取消操作

## 国际化规范

- 所有文本使用 `$t()` 函数包装
- 翻译键遵循 `system.模块名.字段名` 的格式
- 通用文本使用 `common.xxx` 格式

示例：
```typescript
$t('system.user.userName')
$t('common.enabled')
```

## 权限控制

- 使用 `v-access:code` 指令控制按钮权限
- 权限码格式：`System.模块名.操作名`

示例：
```vue
<Button v-access:code="['System.User.Create']">
```

## API 调用规范

- API 函数命名规范：
  - 获取列表：`getXxxList`
  - 创建：`createXxx`
  - 更新：`updateXxx`
  - 删除：`deleteXxx`
  - 状态更新：`updateXxxState`

- 请求参数规范：
  - 列表查询支持分页
  - 支持条件搜索
  - 统一的响应格式

## 组件使用规范

### 表格组件
- 使用 `VbenVxeGrid` 组件
- 配置 `proxyConfig` 处理数据加载
- 使用 `toolbarConfig` 配置工具栏
- 支持树形数据展示

### 表单组件
- 使用 `VbenForm` 组件
- 配置 `schema` 定义表单结构
- 使用 `useVbenForm` 处理表单逻辑
- 支持表单验证和提交

### 弹窗组件
- 使用 `useVbenDrawer` 或 `useVbenModal`
- 统一的标题和操作按钮
- 支持加载状态
- 支持成功回调
