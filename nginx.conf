#user  nobody;
worker_processes 1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;
#pid        logs/nginx.pid;


events {
  worker_connections 1024;
}


http {
  include mime.types;
  default_type application/octet-stream;

  types {
    application/javascript  js mjs;
    text/css                css;
    text/html               html;
  }

  sendfile on;
  # tcp_nopush     on;

  #keepalive_timeout  0;
  # keepalive_timeout 65;

  # gzip on;
  # gzip_buffers 32 16k;
  # gzip_comp_level 6;
  # gzip_min_length 1k;
  # gzip_static on;
  # gzip_types text/plain
  #   text/css
  #   application/javascript
  #   application/json
  #   application/x-javascript
  #   text/xml
  #   application/xml
  #   application/xml+rss
  #   text/javascript; #设置压缩的文件类型
  # gzip_vary on;


server {
        #监听的端口
        listen  8088;
        server_name  localhost;
        #设置日志
        #access_log /var/log/nginx/access.log;
        #error_log /var/log/nginx/error.log;
 
        #定位到index.html
           location / {
               #linux下HTML文件夹,就是你的前端项目文件夹
               root  /usr/share/nginx/html;
               # 解决history模式
               try_files $uri $uri/ /index.html;
               #输入网址（server_name：port）后，默认的访问页面
               index  index.html;
               # Enable CORS
              add_header 'Access-Control-Allow-Origin' '*';
              add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
              add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
              if ($request_method = 'OPTIONS') {
                   add_header 'Access-Control-Max-Age' 1728000;
                   add_header 'Content-Type' 'text/plain charset=UTF-8';
                   add_header 'Content-Length' 0;
                   return 204;
              }
           }
    }
}