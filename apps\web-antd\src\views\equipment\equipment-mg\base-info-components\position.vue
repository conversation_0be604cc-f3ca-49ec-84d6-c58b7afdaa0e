<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { EquipmentMgApi } from '#/api/equipment/equipment-mg';

import { computed, ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getEqServGrpsApi } from '#/api/equipment/equipment-mg';
import { useEquipmentStore } from '#/store';

import PositionModal from '../components/position-modal.vue';
import { positionColumns } from '../equipment-mg-data';

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: PositionModal,
  destroyOnClose: true,
});
const equipmentStore = useEquipmentStore();
const currentRow: EquipmentMgApi.RowType = computed(
  () => equipmentStore.getCurrentRow,
);
interface RowType {
  [key: string]: any;
}
watch(
  currentRow,
  async (newRow: RowType) => {
    if (newRow) {
      gridApi.query();
    }
  },
  { deep: true },
);
const tableData = ref<EquipmentMgApi.MetaDataEquipment[]>([]);

const gridOptions: VxeTableGridOptions<EquipmentMgApi.MetaDataEquipment> = {
  columns: positionColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!currentRow.value) {
          return [];
        }
        const data = await getEqServGrpsApi([currentRow.value.EQID]);
        tableData.value = data;

        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: {},
    search: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: {},
});
const onRefresh = () => {
  gridApi.query();
};
const editPosition = () => {
  formModalApi.setData({ tableData: tableData.value }).open();
};
</script>

<template>
  <FormModal @success="onRefresh" />

  <Grid height="auto" class="w-full">
    <template #toolbar-actions>
      <Space :size="[0, 4]">
        <Button type="primary" @click="editPosition()">
          {{ $t('equipment.edit') }}
        </Button>
      </Space>
    </template>
  </Grid>
</template>
