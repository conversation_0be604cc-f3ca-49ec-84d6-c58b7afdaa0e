<script lang="ts" setup>
import type { TestManagerApi } from '#/api/business-static-tables';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { $addPossibleResultApi } from '#/api/business-static-tables';
import { $t } from '#/locales';

import { usePossibleResultSchema } from './data';

const emit = defineEmits(['success']);
const analyte = ref<TestManagerApi.Analyte>();
const mode = ref<string>(''); // 'Wizard'

const getTitle = computed(() => {
  return $t('ui.actionTitle.create', [
    $t('business-static-tables.testManager.possibleResult'),
  ]);
});

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: usePossibleResultSchema(),
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    modalApi.lock();
    try {
      data.ANALYTE = analyte.value?.ANALYTE;
      data.TESTCODE = analyte.value?.TESTCODE;
      data.mode = mode.value === 'Wizard' ? 'TestManager' : mode.value; // Wizard --> TestManager TODO：接口只识别TestManager，需要修改接口;
      const res = await $addPossibleResultApi(data);
      if (!res) {
        message.error($t('commons.existsData'));
        return;
      }
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<{
        analyte: TestManagerApi.Analyte;
        mode: string;
      }>();
      if (data) {
        analyte.value = data.analyte;
        mode.value = data.mode;
      }
    }
  },
});
</script>

<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
  </Modal>
</template>
