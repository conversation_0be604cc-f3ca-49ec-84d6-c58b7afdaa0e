import { callServer, getDataSet } from '#/api/core/witlab';

export namespace MaterialManagerApi {
  export interface MaterialManager {
    ORIGREC: number;
    MATCODE: string;
    MATNAME: string;
    MATTYPE: string;
    DEFAULT_UNIT_CODE: string;
    SPEC: string;
    PACKING_SPEC: string;
    DOSAGEFORM: string;
    PRODUCT_NAME: string;
    GENERIC_NAME: string;
    RETESTDATE_M: string;
    EXDATE_M: string;
    SAVECONDITION: string;
    SAMPLEPERIOD: number;
    INSPECTION_LEVEL: string;
  }
  export interface MaterialsSynonims {
    ORIGREC: number;
    MATCODE: string;
    MATSYNONIM: string;
  }

  export interface ProdSuppliers {
    ORIGREC: number;
    MATCODE: string;
    SUPPCODE: string;
    SUPPNAM: string;
    SUPTYPE: string;
  }

  export interface Recipes {
    ORIGREC: number;
    MATCODE: string;
    RECIPECODE: number;
    RECIPENAME: string;
    FORMULAQTY: string;
    UNITS: string;
    STARTDDATE: Date;
    EXPDATE: Date;
    STATUS: string;
    CREATED_BY: string;
    APPROVEDAT: Date;
    RETIREDDAT: Date;
    STARDOC_ID: string;
    MATNAME: string;
  }

  export interface MaterialUnitConversion {
    ORIGREC: number;
    MATCODE: string;
    MATNAME: string;
    NAME: string;
    PARENT: string;
    TEXT: string;
    VALUE: string;
    FACTOR: string;
    PARENT_CONTAINER_MATCODE: string;
    CONTAINER_MATCODE: string;
    CONVERSION_FACTOR: string;
  }
}

/**
 * 获取物料列表数据
 */
/* async function getMaterialsList(pageNum: number, pageSize: number) {
  const data = await getDataSet('MATERIAL_MANAGER.GET_MATERIALS', [
    'MATERIALS.ORIGREC>0',
  ]);
  const start = (pageNum - 1) * pageSize;
  return data.items.slice(start, start + pageSize);
} */

/**
 * 获取物料列表数据
 */
async function getMaterialsList() {
  return await getDataSet('MATERIAL_MANAGER.GET_MATERIALS', [
    'MATERIALS.ORIGREC>0',
  ]);
}

/**
 * 获取物料类型数据
 */
async function getMatTypeList() {
  return getDataSet('MATERIAL_MANAGER.GET_MATTYPE', []);
}

/**
 * 获取检验等级数据
 */
async function getInspectionLevels() {
  return getDataSet('MATERIAL_MANAGER.getInspectionLevels', []);
}

/**
 * 创建物料
 * @param data 物料数据
 */
async function addMaterial(data: MaterialManagerApi.MaterialManager) {
  return await callServer('MATERIAL_MANAGER.ADD_MATERIAL', [
    data.MATCODE,
    data.MATNAME,
    data.MATTYPE,
    data.DEFAULT_UNIT_CODE,
  ]);
}

/**
 * 删除物料
 */
async function deleteMaterial(aMatCode: string[]) {
  return await callServer('Material_Manager.DeleteMaterials', [aMatCode]);
}

/**
 * 获取同义词列表数据
 */
async function getSynonymsList(sMatCode: string) {
  return getDataSet('MATERIAL_MANAGER.GET_SYNONYMS', [sMatCode]);
}

/**
 * 添加同义词
 */
async function addSynonym(data: MaterialManagerApi.MaterialsSynonims) {
  return await callServer('MATERIAL_MANAGER.ADD_SYNONYM', [
    data.MATCODE,
    data.MATSYNONIM,
  ]);
}

/**
 * 删除同义词
 */
async function deleteSynonyms(aSynonym: string[], sMatCode: string) {
  return await callServer('MATERIAL_MANAGER.DELETE_SYNONYM', [
    aSynonym,
    sMatCode,
  ]);
}

/**
 * 获取厂商列表数据
 */
async function getProdSuppliersList(sMatCode: string) {
  return getDataSet('MATERIAL_MANAGER.GET_LABELS', [sMatCode]);
}

/**
 * 获取厂商数据
 */
async function getSupplierCodeAndName(sSupType: string, sSuppcode: string) {
  return getDataSet('MATERIAL_MANAGER.GET_SUPPLIER_CODE', [
    sSupType,
    sSuppcode,
  ]);
}

/**
 * 添加厂商
 */
async function addProdSupplier(data: MaterialManagerApi.ProdSuppliers) {
  return await callServer('MATERIAL_MANAGER.ADD_LABEL', [
    data.MATCODE,
    0,
    null,
    0,
    data.SUPPCODE,
  ]);
}

/**
 * 删除厂商
 */
async function deleteProdSupplier(sOrigrec: number) {
  return await callServer('MATERIAL_MANAGER.DEL_LABEL', [sOrigrec]);
}

/**
 * 获取配方列表数据
 */
async function getRecipesList(sMatCode: string) {
  return getDataSet('MATERIAL_MANAGER.GET_RECIPE', [sMatCode, '', '']);
}

/**
 * 添加配方
 */
async function addRecipe(data: MaterialManagerApi.Recipes) {
  return await callServer('MATERIAL_MANAGER.ADD_RECIPE', [
    data.MATCODE,
    data.RECIPENAME,
  ]);
}

/**
 * 删除配方
 */
async function deleteRecipes(sRecipeCode: number) {
  return await callServer('MATERIAL_MANAGER.DELETE_RECIPE', [
    sRecipeCode,
    '',
    '',
  ]);
}

/**
 * 复制配方
 */
async function copyRecipe(data: MaterialManagerApi.Recipes) {
  return await callServer('MATERIAL_MANAGER.COPY_RECIPE', [
    data.RECIPECODE,
    data.RECIPENAME,
  ]);
}

/**
 * 批准配方
 */
async function approveRecipe(
  startDate: Date,
  endDate: Date,
  sRecipeCode: number,
) {
  return await callServer('MATERIAL_MANAGER.RECIPE_APPROVE', [
    startDate,
    endDate,
    sRecipeCode,
  ]);
}

/**
 * 停用配方
 */
async function retireRecipe(sRecipeCode: number) {
  return await callServer('MATERIAL_MANAGER.RECIPE_RETIRE', [sRecipeCode]);
}

/**
 * 获取配方详情数据
 */
async function getRecipesDetail(sRecipeCode: number) {
  return getDataSet('MATERIAL_MANAGER.GET_RECIPE_DETAILS', [sRecipeCode]);
}

/**
 * 根据配方代码获取配方物料列表
 */
async function getRecipesMaterialByRecipeCode(sRecipeCode: number) {
  return getDataSet('MATERIAL_MANAGER.getRecipeMaterials', [
    sRecipeCode,
    'Load',
    '',
    'RECIPES',
  ]);
}

/**
 * 添加配方详情
 */
async function addRecipeDetail(sMatCode: string[], sRecipeCode: number) {
  return await callServer('MATERIAL_MANAGER.EDIT_RECIPE_DETAILS', [
    sMatCode,
    sRecipeCode,
  ]);
}
/**
 * 获取容器单位转换列表数据
 */
async function getMaterialUnitConvList(sMatCode: string) {
  return getDataSet('MATERIAL_MANAGER.getMaterialUnitsConversion', [
    sMatCode,
    '',
    '',
  ]);
}

/**
 * 根据物料类型获取物料信息
 */
async function getMatCodeAndNameByMatType(sMatType: string) {
  return getDataSet('MATERIAL_MANAGER.getMatcodeMatname', [sMatType]);
}

/**
 * 添加容器单位
 */
async function addMaterialUnitConv(
  data: MaterialManagerApi.MaterialUnitConversion,
) {
  return await callServer('MATERIAL_MANAGER.addContainerUnitConv', [
    data.MATCODE,
    data.PARENT_CONTAINER_MATCODE,
    data.CONTAINER_MATCODE,
    data.CONVERSION_FACTOR,
  ]);
}

/**
 * 删除容器单位转换
 */
async function deleteMaterialUnitConv(origrec: number) {
  return await callServer('MATERIAL_MANAGER.delContainerUnitConv', [origrec]);
}

export {
  addMaterial,
  addMaterialUnitConv,
  addProdSupplier,
  addRecipe,
  addRecipeDetail,
  addSynonym,
  approveRecipe,
  copyRecipe,
  deleteMaterial,
  deleteMaterialUnitConv,
  deleteProdSupplier,
  deleteRecipes,
  deleteSynonyms,
  getInspectionLevels,
  getMatCodeAndNameByMatType,
  getMaterialsList,
  getMaterialUnitConvList,
  getMatTypeList,
  getProdSuppliersList,
  getRecipesDetail,
  getRecipesList,
  getRecipesMaterialByRecipeCode,
  getSupplierCodeAndName,
  getSynonymsList,
  retireRecipe,
};
