import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { LocationManageApi } from '#/api/materials-management/location-management';

import { $t } from '@vben/locales';

export function useLocationManagementColumns(): VxeTableGridOptions<LocationManageApi.LocationManage>['columns'] {
  return [
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'SEQ',
      title: $t('materials-management.material-manager.text'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      field: 'NAMEMEMBER',
      title: $t('materials-management.material-manager.text'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      field: 'TEXTMEMBER',
      // title: $t('materials-management.material-manager.text'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      treeNode: true,
    },
    {
      field: 'VALUEMEMBER',
      title: $t('materials-management.material-manager.value'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      field: 'PARENTMEMBER',
      title: $t('materials-management.material-manager.parent'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      field: 'LOCATIONID',
      title: $t('materials-management.material-manager.locationid'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
  ];
}

// 定义位置数据类型
export interface LocationData {
  id: string;
  parentId: null | string;
  name: string;
  code: string;
  type: string;
  status: string;
  description: string;
  capacity: number;
  temperature: string;
  humidity: string;
  createdAt: string;
  updatedAt: string;
}

// 位置表单
export function useLocationSchema() {
  return [
    {
      component: 'Input',
      fieldName: 'LOCATIONCODE',
      label: $t('materials-management.location-manage.locationcode'),
      disabled: true,
    },
    {
      component: 'Input',
      fieldName: 'LOCATION_NAME',
      label: $t('materials-management.location-manage.location_name'),
      disabled: true,
    },
    {
      component: 'Input',
      fieldName: 'DESCRIPTION',
      label: $t('materials-management.location-manage.description'),
    },
    {
      component: 'Checkbox',
      fieldName: 'IS_STORABLE',
      label: $t('materials-management.location-manage.is_storable'),
    },
    {
      component: 'Checkbox',
      fieldName: 'IS_GXP',
      label: $t('materials-management.location-manage.is_gxp'),
    },
    {
      component: 'Input',
      fieldName: 'CONDITION',
      label: $t('materials-management.location-manage.condition'),
      disabled: true,
    },
    {
      component: 'Input',
      fieldName: 'TEMPERATURE',
      label: $t('materials-management.location-manage.temperature'),
      disabled: true,
    },
    {
      component: 'Input',
      fieldName: 'LUMINOSITY',
      label: $t('materials-management.location-manage.luminosity'),
      disabled: true,
    },
    {
      component: 'Input',
      fieldName: 'TEMPERATURE_MAX',
      label: $t('materials-management.location-manage.temperature_max'),
      disabled: true,
    },
    {
      component: 'Input',
      fieldName: 'OTHER',
      label: $t('materials-management.location-manage.other'),
      disabled: true,
    },
    {
      component: 'Input',
      fieldName: 'HUMIDITY',
      label: $t('materials-management.location-manage.humidity'),
      disabled: true,
    },
    {
      component: 'Input',
      fieldName: 'NAME',
      label: $t('materials-management.location-manage.name'),
      disabled: true,
    },
    {
      component: 'Input',
      fieldName: 'SEQUENCE',
      label: $t('materials-management.location-manage.sequence'),
      disabled: true,
    },
    {
      component: 'Input',
      fieldName: 'NUMBERING_METHOD',
      label: $t('materials-management.location-manage.numbering_method'),
      disabled: true,
    },
    {
      component: 'Input',
      fieldName: 'SUBLOCATION_SIZE',
      label: $t('materials-management.location-manage.sublocation_size'),
      disabled: true,
    },
  ];
}

// 站点表单
export function useDeptSchema() {
  return [
    {
      component: 'Input',
      fieldName: 'DEPTCODE',
      label: $t('materials-management.location-manage.deptcode'),
      disabled: true,
    },
    {
      component: 'Input',
      fieldName: 'DEPT',
      label: $t('materials-management.location-manage.dept'),
      disabled: true,
    },
  ];
}

// 建筑表单
export function useBuildingSchema() {
  return [
    {
      component: 'Input',
      fieldName: 'BUILDING_CODE',
      label: $t('materials-management.location-manage.building_code'),
      disabled: true,
    },
    {
      component: 'Input',
      fieldName: 'BUILDING_NAME',
      label: $t('materials-management.location-manage.building_name'),
      disabled: true,
    },
    {
      component: 'Input',
      fieldName: 'DESCRIPTION',
      label: $t('materials-management.location-manage.description'),
      disabled: true,
    },
  ];
}

// 房间表单
export function useRoomSchema() {
  return [
    {
      component: 'Input',
      fieldName: 'ROOM_CODE',
      label: $t('materials-management.location-manage.room_code'),
      disabled: true,
    },
    {
      component: 'Input',
      fieldName: 'ROOM_NAME',
      label: $t('materials-management.location-manage.room_name'),
      disabled: true,
    },
    {
      component: 'Input',
      fieldName: 'CLASS',
      label: $t('materials-management.location-manage.class'),
      disabled: true,
    },
    {
      component: 'Input',
      fieldName: 'DESCRIPTION',
      label: $t('materials-management.location-manage.description'),
      disabled: true,
    },
  ];
}

export function useStorageConditionColumns(): VxeTableGridOptions<LocationManageApi.StorageCondition>['columns'] {
  return [
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'STCONDITION_CODE',
      title: $t('materials-management.location-manage.stcondition_code'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'TEMPERATURE',
      title: $t('materials-management.location-manage.temperature'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'TEMPERATURE_MAX',
      title: $t('materials-management.location-manage.temperature_max'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'HUMIDITY',
      title: $t('materials-management.location-manage.humidity'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'LUMINOSITY',
      title: $t('materials-management.location-manage.luminosity'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'OTHER',
      title: $t('materials-management.location-manage.other'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}

export function useLayoutConfigColumns(): VxeTableGridOptions<LocationManageApi.LayoutConfig>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'NAME',
      title: $t('materials-management.location-manage.layoutname'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'PREFIX',
      title: $t('materials-management.location-manage.prefix'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'NUMBERING_METHOD',
      title: $t('materials-management.location-manage.numbering_method'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'LOCTYPE_SIZE',
      title: $t('materials-management.location-manage.loctype_size'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'LOCTYPE_ORDER',
      title: $t('materials-management.location-manage.loctype_order'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'IS_STORABLE',
      title: $t('materials-management.location-manage.is_storable'),
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}
