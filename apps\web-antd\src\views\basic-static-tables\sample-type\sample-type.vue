<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { SampleTypeApi } from '#/api/basic-static-tables/sample-type';

import { Page, useVbenModal } from '@vben/common-ui';

import { Button, message, Modal, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  $deleteSampleTypeApi,
  $getSampleTypeApi,
} from '#/api/basic-static-tables/sample-type';

import AddSampleTypeForm from './add-sample-type-form.vue';
import { useColumns, useFilterSchema } from './sample-type-data';

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddSampleTypeForm,
  destroyOnClose: true,
});

const gridOptions: VxeTableGridOptions<SampleTypeApi.SampleType> = {
  columns: useColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        return await $getSampleTypeApi();
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const formOptions: VbenFormProps = {
  schema: useFilterSchema(),
  submitOnChange: false,
  collapsed: true,
  showCollapseButton: true,
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: {},
  formOptions,
});

function hasEditStatus(row: SampleTypeApi.SampleType) {
  return gridApi.grid?.isEditByRow(row);
}

function editRowEvent(row: SampleTypeApi.SampleType) {
  gridApi.grid?.setEditRow(row);
}

async function saveRowEvent(_row: SampleTypeApi.SampleType) {
  await gridApi.grid?.clearEdit();

  gridApi.setLoading(true);
  // NICK TODO: 这里需要根据实际的API进行修改
  setTimeout(() => {
    gridApi.setLoading(false);
    message.success({
      content: `保存成功！`,
    });
  }, 600);
}

const cancelRowEvent = (_row: SampleTypeApi.SampleType) => {
  gridApi.grid?.clearEdit();
};

function onRefresh() {
  gridApi.query();
}

function onCreate() {
  formModalApi.setData(null).open();
}

async function onDelete() {
  const checkOrig: number =
    (gridApi.grid?.getRadioRecord()?.ORIGREC as number) ?? 0;
  if (checkOrig === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }
  await confirm('是否确认删除选中的数据？', '删除确认');
  await $deleteSampleTypeApi(checkOrig);
  message.success('删除成功！');
  onRefresh();
}

function confirm(content: string, title: string) {
  return new Promise((reslove, reject) => {
    Modal.confirm({
      content,
      onCancel() {
        reject(new Error('已取消'));
      },
      onOk() {
        reslove(true);
      },
      title,
    });
  });
}
</script>

<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <Grid>
      <template #toolbar-actions>
        <Space :size="[4, 0]" wrap>
          <Button type="primary" @click="onCreate">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="onDelete">
            {{ $t('ui.actionTitle.delete') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent(row)">保存</Button>
          <Button type="link" @click="cancelRowEvent(row)">取消</Button>
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">编辑</Button>
        </template>
      </template>
    </Grid>
  </Page>
</template>
