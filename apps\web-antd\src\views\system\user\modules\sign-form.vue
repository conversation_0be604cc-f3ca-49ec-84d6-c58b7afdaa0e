<script lang="ts" setup>
import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import {
  Modal as AModal,
  Card,
  CardMeta,
  Image,
  message,
  Space,
  Spin,
} from 'ant-design-vue';
import { Edit, Trash } from 'lucide-vue-next';

import { callServer, getUserInfoFromWitlab, UploadWitlabFile } from '#/api';
import { $t } from '#/locales';
import { isNullOrWhiteSpace } from '#/utils';

const emit = defineEmits<{
  success: [];
}>();

// 常量定义
const SIGN_TYPES = {
  CHS: 'chs',
  EN: 'en',
} as const;

const API_ENDPOINTS = {
  GET_IMAGE: 'RUNTIME_SUPPORT.GetImageFromSTARDOCBase64',
  UPDATE_USER: 'Common.Update',
  DELETE_LOGO: 'UserManagement.deleteLogo',
} as const;

const DB_FIELDS = {
  CHS_STARDOC_ID: 'STARDOC_ID',
  EN_STARDOC_ID: 'ENGSTARDOC_ID',
} as const;

type SignType = (typeof SIGN_TYPES)[keyof typeof SIGN_TYPES];

// 工具函数
function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.addEventListener('load', () => resolve(reader.result as string));
    reader.addEventListener('error', () => reject(reader.error));
  });
}

function validateImageFile(file: File): boolean {
  const maxSize = 5 * 1024 * 1024; // 5MB
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

  if (!allowedTypes.includes(file.type)) {
    message.error('请选择有效的图片格式 (JPEG, PNG, GIF, WebP)');
    return false;
  }

  if (file.size > maxSize) {
    message.error('图片大小不能超过 5MB');
    return false;
  }

  return true;
}

interface SignPicture {
  image: string;
  starDocId: string;
}

interface SignFormData {
  origrec: number;
  userName: string;
  realName: string;
  chsSignPic: SignPicture;
  enSignPic: SignPicture;
}

interface LoadingState {
  chs: boolean;
  en: boolean;
}

// 响应式状态
const formData = ref<SignFormData>();
const loadingState = ref<LoadingState>({
  chs: false,
  en: false,
});

// 计算属性
const getTitle = computed(() =>
  $t('ui.actionTitle.view', [$t('system.user.signPic')]),
);

// 工具函数 - 加载签名图片
async function loadSignImage(starDocId: string): Promise<string> {
  if (isNullOrWhiteSpace(starDocId)) {
    return '';
  }

  try {
    return await callServer(API_ENDPOINTS.GET_IMAGE, [starDocId]);
  } catch (error) {
    console.error('Failed to load sign image:', error);
    message.error('加载签名图片失败');
    return '';
  }
}

// 设置加载状态
function setLoadingState(type: SignType, loading: boolean) {
  loadingState.value[type] = loading;
}

// 初始化表单数据
async function initializeFormData(userData: {
  realName: string;
  userName: string;
}) {
  try {
    setLoadingState(SIGN_TYPES.CHS, true);
    setLoadingState(SIGN_TYPES.EN, true);

    const { origrec, starDocId, engStarDocId } = await getUserInfoFromWitlab(
      userData.userName,
    );

    // 初始化基础数据
    formData.value = {
      origrec,
      userName: userData.userName,
      realName: userData.realName,
      chsSignPic: {
        image: '',
        starDocId: starDocId || '',
      },
      enSignPic: {
        image: '',
        starDocId: engStarDocId || '',
      },
    };

    // 并行加载图片
    const [chsImage, enImage] = await Promise.all([
      loadSignImage(starDocId),
      loadSignImage(engStarDocId),
    ]);

    if (formData.value) {
      formData.value.chsSignPic.image = chsImage;
      formData.value.enSignPic.image = enImage;
    }
  } catch (error) {
    console.error('Failed to initialize form data:', error);
    message.error('初始化数据失败');
  } finally {
    setLoadingState(SIGN_TYPES.CHS, false);
    setLoadingState(SIGN_TYPES.EN, false);
  }
}

const [Modal, modalApi] = useVbenModal({
  footer: false,
  closeOnClickModal: false,
  async onConfirm() {
    modalApi.lock();
    try {
      modalApi.close();
      emit('success');
    } finally {
      modalApi.lock(false);
    }
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<{ realName: string; userName: string }>();
      await initializeFormData(data);
    }
  },
});

// 更新签名数据
async function handleUpdateSign(
  type: SignType,
  starDocId: string,
  newImageBase64?: string,
): Promise<boolean> {
  try {
    const fieldToUpdate =
      type === SIGN_TYPES.CHS
        ? DB_FIELDS.CHS_STARDOC_ID
        : DB_FIELDS.EN_STARDOC_ID;

    await callServer(API_ENDPOINTS.UPDATE_USER, [
      'USERS',
      fieldToUpdate,
      starDocId,
      formData.value?.origrec,
    ]);

    // 更新本地状态
    if (formData.value) {
      const signPic = {
        image: newImageBase64 || '',
        starDocId,
      };

      if (type === SIGN_TYPES.CHS) {
        formData.value.chsSignPic = signPic;
      } else {
        formData.value.enSignPic = signPic;
      }
    }

    message.success('更新成功');
    return true;
  } catch (error) {
    console.error('Update failed', error);
    message.error('更新失败');
    return false;
  }
}

// 创建文件选择器
function createFileInput(): HTMLInputElement {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = 'image/jpeg,image/png,image/gif,image/webp';
  return input;
}

// 处理文件上传
async function handleFileUpload(file: File, type: SignType): Promise<void> {
  if (!validateImageFile(file)) {
    return;
  }

  setLoadingState(type, true);

  try {
    const newStarDocId = await UploadWitlabFile(file);
    if (newStarDocId) {
      const newImageBase64 = await fileToBase64(file);
      await handleUpdateSign(type, newStarDocId, newImageBase64);
    }
  } catch (error) {
    console.error('Upload failed', error);
    message.error('上传失败');
  } finally {
    setLoadingState(type, false);
  }
}

// 编辑签名
const handleEdit = async (type: SignType) => {
  const input = createFileInput();

  input.addEventListener('change', async (e) => {
    const files = (e.target as HTMLInputElement).files;
    if (files && files.length > 0) {
      const file = files[0];
      if (file) {
        await handleFileUpload(file, type);
      }
    }
  });

  input.click();
};

// 删除签名
const handleDelete = async (type: SignType) => {
  const signPic =
    type === SIGN_TYPES.CHS
      ? formData.value?.chsSignPic
      : formData.value?.enSignPic;

  if (!signPic?.starDocId) {
    message.warning('没有可删除的签名');
    return;
  }

  // 显示确认对话框
  AModal.confirm({
    title: '确认删除',
    content: '确定要删除这个签名吗？此操作不可撤销。',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        setLoadingState(type, true);

        await callServer(API_ENDPOINTS.DELETE_LOGO, [
          formData.value?.userName,
          signPic.starDocId,
        ]);

        await handleUpdateSign(type, '');
      } catch (error) {
        console.error('Delete failed', error);
        message.error('删除失败');
      } finally {
        setLoadingState(type, false);
      }
    },
  });
};
</script>

<template>
  <Modal :title="getTitle" class="w-[720px]">
    <Space class="flex justify-center gap-4">
      <Card hoverable class="m-4 min-h-[420px] min-w-[300px]">
        <template #cover>
          <Spin :spinning="loadingState.chs">
            <Image
              :src="formData?.chsSignPic?.image"
              class="min-h-[300px] w-full"
              :preview="!!formData?.chsSignPic?.image"
              fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
            />
          </Spin>
        </template>
        <template #actions>
          <div class="flex w-full justify-center">
            <Edit
              key="edit"
              class="size-4 cursor-pointer hover:text-blue-500"
              @click="handleEdit(SIGN_TYPES.CHS)"
            />
          </div>
          <div class="flex w-full justify-center">
            <Trash
              key="delete"
              class="size-4 cursor-pointer hover:text-red-500"
              @click="handleDelete(SIGN_TYPES.CHS)"
            />
          </div>
        </template>
        <CardMeta
          :title="$t('system.user.chsSignPic')"
          :description="formData?.realName"
        />
      </Card>
      <Card hoverable class="m-4 min-h-[420px] min-w-[300px]">
        <template #cover>
          <Spin :spinning="loadingState.en">
            <Image
              :src="formData?.enSignPic?.image"
              class="min-h-[300px] w-full"
              :preview="!!formData?.enSignPic?.image"
              fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
            />
          </Spin>
        </template>
        <template #actions>
          <div class="flex w-full justify-center">
            <Edit
              key="edit"
              class="size-4 cursor-pointer hover:text-blue-500"
              @click="handleEdit(SIGN_TYPES.EN)"
            />
          </div>
          <div class="flex w-full justify-center">
            <Trash
              key="delete"
              class="size-4 cursor-pointer hover:text-red-500"
              @click="handleDelete(SIGN_TYPES.EN)"
            />
          </div>
        </template>
        <CardMeta
          :title="$t('system.user.enSignPic')"
          :description="formData?.userName"
        />
      </Card>
    </Space>
  </Modal>
</template>
