<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { $addStabilityPurposeApi } from '#/api/basic-static-tables';
import { $t } from '#/locales';

import { useSchema } from './data';

const emit = defineEmits(['success']);
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: useSchema(),
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    data.eventCode = 'addPurpose2';
    data.comment = '添加稳定性目的';
    modalApi.lock();
    try {
      const res = await $addStabilityPurposeApi(data);
      if (!res) {
        message.error($t('commons.exists'));
        return;
      }
      if (res) emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  title: $t('ui.actionTitle.create', [
    $t('business-static-tables.stabilityPurpose.stabilityPurpose'),
  ]),
});
</script>

<template>
  <Modal>
    <Form class="mx-4" />
  </Modal>
</template>
