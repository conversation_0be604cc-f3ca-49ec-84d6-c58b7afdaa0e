<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Transfer } from 'ant-design-vue';

import { callServer, getSelectedPlantMemberListByPlant } from '#/api';
import { $t } from '#/locales';

interface TransferItem {
  key: string;
  title: string;
  selected: boolean;
}
const emit = defineEmits(['success']);
const plant = ref<{ DEPT: string; PLANT: string }>({
  DEPT: '',
  PLANT: '',
});

const ds = ref<TransferItem[]>([]);

const targetKeys = ref<string[]>([]);

const pendingSubmitData = ref<{
  direction: string;
  moveKeys: string[];
  targetKeys: string[];
}>({
  direction: '',
  moveKeys: [],
  targetKeys: [],
});

const getData = async () => {
  try {
    modalApi.lock();
    const keys: string[] = [];
    const tData = await getSelectedPlantMemberListByPlant<{
      FULLNAME: string;
      Selected: string;
      USRNAM: string;
    }>(plant.value.DEPT, plant.value.PLANT);
    ds.value = tData.map(({ Selected, FULLNAME, USRNAM }) => {
      return {
        key: USRNAM,
        title: FULLNAME,
        selected: Selected === 'true',
      };
    });
    for (const item of ds.value) {
      if (item.selected) {
        keys.push(item.key);
      }
    }
    targetKeys.value = keys;
  } finally {
    modalApi.lock(false);
  }
};
const filterOption = (inputValue: string, item: TransferItem) => {
  return item.title.includes(inputValue);
};
const handleChange = async (
  targetKeys: string[],
  direction: string,
  moveKeys: string[],
) => {
  pendingSubmitData.value = {
    targetKeys,
    direction,
    moveKeys,
  };
};

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { targetKeys } = pendingSubmitData.value;
    try {
      modalApi.lock();
      await callServer('LABS.UPDATE_MEMBERS', [
        targetKeys,
        plant.value.DEPT,
        plant.value.PLANT,
      ]);
      modalApi.close();
      emit('success');
    } catch (error) {
      console.error(error);
      await getData();
    } finally {
      modalApi.lock(false);
    }
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<{ DEPT: string; PLANT: string }>();
      if (data) {
        plant.value = data;
        await getData();
      }
    }
  },
});
</script>

<template>
  <Modal
    :title="
      $t('ui.actionTitle.edit2', [
        $t('system.dept.serviceGroup.analystFullName'),
      ])
    "
    class="flex h-[720px] w-fit justify-center gap-4"
  >
    <Transfer
      class="mx-4 h-full"
      :row-key="(record) => record?.key || ''"
      :list-style="{ width: '300px', height: '100%' }"
      v-model:target-keys="targetKeys"
      :data-source="ds"
      show-search
      :filter-option="filterOption"
      :render="(item) => item.title"
      @change="handleChange"
    />
  </Modal>
</template>
