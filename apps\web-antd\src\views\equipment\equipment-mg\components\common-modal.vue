<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import { addEquipmentApi } from '#/api/equipment/equipment-mg';

import { deviceModalSchema } from '../equipment-mg-data';

const emit = defineEmits(['success']);
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: deviceModalSchema(),
  showDefaultActions: false,
});
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    console.warn(data);

    modalApi.lock();
    try {
      const Dept = '';
      const Method = '';
      const equipMode = ''; // 默认值为 '0'
      const params = [
        data.equipId,
        '',
        data.equipType,
        data.sOwner,
        Dept,
        Method,
        equipMode,
      ];
      const res = await addEquipmentApi(params);
      console.warn(res);
      await addEquipmentApi(params);
      emit('success');
      modalApi.close();
    } finally {
      modalApi.unlock();
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      // const data = modalApi.getData<SampleTypeApi.SampleType>();
    }
  },
});
</script>
<template>
  <Modal title="添加设备">
    <Form class="mx-4" />
  </Modal>
</template>
