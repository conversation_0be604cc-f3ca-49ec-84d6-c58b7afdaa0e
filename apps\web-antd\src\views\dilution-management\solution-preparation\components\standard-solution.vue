<script lang="ts" setup>
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { SolutionPreparationApi } from '#/api/dilution-management/solution-preparation';

import {
  Button,
  Space,
  TabPane,
  Tabs,
  message,
  Modal as AModal,
} from 'ant-design-vue';
import {
  standardColumns,
  historyColumns,
  fileColumns,
  soluteColumns,
} from '../solution preparation-data';

import {
  getStandSolutionApi,
  getTestsolutionRecordApi,
  updateProviderApi,
  getListAttachmentsApi,
  getSoluteApi,
  deleteSolutionApi,
  deleteSoluteApi,
} from '#/api/dilution-management/solution-preparation';
const formargs = [
  'SolutionConfiguration',
  'Draft',
  ' and STATUS = ?',
  "'Draft'",
];
import { useVbenModal } from '@vben/common-ui';
import CommitModal from './commit-modal.vue';
import PreparationModal from './preparation-modal.vue';
import AddSolutionModal from './add-solution-modal.vue';
import AddReserveModal from './add-reserve-modal.vue';

const [CommitFormModal, commitFormModalApi] = useVbenModal({
  connectedComponent: CommitModal,
  destroyOnClose: true,
});
const [PreparationFormModal, preparationFormModalApi] = useVbenModal({
  connectedComponent: PreparationModal,
  destroyOnClose: true,
});
const [AddSolutionFormModal, addSolutionFormModalApi] = useVbenModal({
  connectedComponent: AddSolutionModal,
  destroyOnClose: true,
});
const [AddReserveFormModal, addReserveFormModalApi] = useVbenModal({
  connectedComponent: AddReserveModal,
  destroyOnClose: true,
});

interface RowType {
  [key: string]: any;
}
import { ref } from 'vue';
const gridOptions: VxeTableGridOptions<SolutionPreparationApi.Form> = {
  columns: standardColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        const params = [formargs[1]];
        const data = await getStandSolutionApi(params);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
const historyGridOptions: VxeTableGridOptions<SolutionPreparationApi.Form> = {
  columns: historyColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!clickRow.value.SEQUENCY) {
          return [];
        }

        const data = await getTestsolutionRecordApi([clickRow.value.SEQUENCY]);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
const fileGridOptions: VxeTableGridOptions<SolutionPreparationApi.Form> = {
  columns: fileColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!clickRow.value.ORIGREC) {
          return [];
        }
        const params = ['DILUTION_STORE', clickRow.value.ORIGREC];
        const data = await getListAttachmentsApi(params);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
const soluteGridOptions: VxeTableGridOptions<SolutionPreparationApi.Form> = {
  columns: soluteColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!clickRow.value.SEQUENCY) {
          return [];
        }

        const data = await getSoluteApi([clickRow.value.SEQUENCY]);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const gridEvents: VxeGridListeners<SolutionPreparationApi.Form> = {
  currentChange: async ({ row }) => {
    if (row) {
      clickRow.value = row;
      const queryActions: Record<'历史记录' | '附件' | '溶质', () => void> = {
        历史记录: () => {
          historyGridApi.query();
        },
        附件: () => {
          fileGridApi.query();
        },
        溶质: () => {
          soluteGridApi.query();
        },
      };
      queryActions[activeKey.value as '历史记录' | '附件' | '溶质']();
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents,
});
const [HistoryGrid, historyGridApi] = useVbenVxeGrid({
  gridOptions: historyGridOptions,
  gridEvents: {},
});
const [FileGrid, fileGridApi] = useVbenVxeGrid({
  gridOptions: fileGridOptions,
  gridEvents: {},
});
const [SoluteGrid, soluteGridApi] = useVbenVxeGrid({
  gridOptions: soluteGridOptions,
  gridEvents: {},
});
const tabsChange = (key: string) => {
  if (activeKey.value === '历史记录') {
    historyGridApi.query();
  }
  if (activeKey.value === '附件') {
    fileGridApi.query();
  }
  if (activeKey.value === '溶质') {
    soluteGridApi.query();
  }
};
const clickRow = ref<RowType>({});
const solutionPreparation = () => {
  preparationFormModalApi.setData({ type: 'standard' }).open();
};

const submit = () => {
  commitFormModalApi
    .setData({ type: '标准溶液', clickRow: clickRow.value })
    .open();
};
const deleteSolution = () => {
  if (!clickRow.value.ORIGREC) {
    message.warning('未选择行，无法删除');
    return;
  }
  const oESig = false;
  if (oESig) {
    message.warning('请先进行电子签名');
    return;
  }
  AModal.confirm({
    title: '提示',
    content: '是否要删除当前溶液？',
    cancelText: '否',
    okText: '是',
    async onOk() {
      const params = [clickRow.value?.ORIGREC];
      await deleteSolutionApi(params);
      gridApi.query();
      refreshChildren();
    },
    onCancel() {},
  });
};
const activeKey = ref('历史记录');
const tabList = [
  {
    title: '历史记录',
  },
  {
    title: '附件',
  },
  {
    title: '溶质',
  },
];
const hasEditStatus = (row: RowType) => {
  return gridApi.grid?.isEditByRow(row);
};

const editRowEvent = (row: RowType) => {
  gridApi.grid?.setEditRow(row);
};

const saveRowEvent = async () => {
  await gridApi.grid?.clearEdit();
  if (!clickRow.value) {
    message.warning('未选择行，无法保存');
    return;
  }
  const row = clickRow.value;
  const rowList = Object.keys(row).map((key) => {
    const isNum = typeof row[key] === 'number' ? 'N' : 'S';
    return [key, row[key], isNum, ''];
  });
  const params = [
    'dgTitrsntSolution',
    'DILUTION_STORE',
    rowList,
    row.ORIGREC,
    null,
  ];
  await updateProviderApi(params);
};

const cancelRowEvent = (row: RowType) => {
  gridApi.grid?.clearEdit().then(() => {
    // 还原行数据
    gridApi.grid.revertData(row);
  });
};
const refreshChildren = () => {
  if (activeKey.value === '历史记录') {
    historyGridApi.query();
  }
  if (activeKey.value === '附件') {
    fileGridApi.query();
  }
  if (activeKey.value === '溶质') {
    soluteGridApi.query();
  }
};
const onRefresh = () => {
  gridApi.query();
  refreshChildren();
};
const addSolution = () => {
  if (!clickRow.value.ORIGREC) {
    message.warning('未选择溶液，无法添加');
    return;
  }
  if (clickRow.value.ISMIX === 'N') {
    message.warning('该溶液不是混标不能添加溶质');
    return;
  }

  addSolutionFormModalApi.setData({ standardRow: clickRow.value }).open();
};
const addStandardStockSolution = () => {
  if (!clickRow.value.ORIGREC) {
    message.warning('未选择溶液，无法添加');
    return;
  }
  if (clickRow.value.ISMIX === 'N') {
    message.warning('该溶液不是混标不能添加溶质');
    return;
  }
  if (clickRow.value.TYPE === '标准储备液') {
    message.warning('该溶液是标准储备液,不能添加储备液了！');
    return;
  }
  addReserveFormModalApi.setData({ standardRow: clickRow.value }).open();
};
const deleteDetailSolution = async () => {
  if (!clickRow.value.ORIGREC) {
    message.warning('未选择溶质，无法删除');
    return;
  }
  const res = await deleteSoluteApi(clickRow.value.ORIGREC);
  if (!res) {
    message.success('删除失败');
    return;
  }
  refreshChildren();
};
const addFiles = () => {
  //TODO：上传文件
};
const deleteFiles = () => {
  //TODO：删除文件
};
const viewFiles = () => {
  //TODO：查看文件
};
const takePhoto = () => {
  //TODO：拍照
};
const batchAddFiles = () => {
  //TODO：批量上传文件
};
const downloadFiles = () => {
  //TODO:下载文件
};
</script>
<template>
  <PreparationFormModal @success="onRefresh" />
  <CommitFormModal @success="onRefresh" />
  <AddSolutionFormModal @success="refreshChildren" />
  <AddReserveFormModal @success="refreshChildren" />

  <Grid class="h-2/5">
    <template #toolbar-actions>
      <Space :size="[8, 0]" wrap>
        <Button type="primary" @click="solutionPreparation">
          {{
            $t('dilution-management.solution-preparation.solutionPreparation')
          }}
        </Button>
        <Button type="primary" @click="submit">
          {{ $t('dilution-management.solution-preparation.submit') }}
        </Button>
        <Button type="primary" danger @click="deleteSolution">
          {{ $t('dilution-management.solution-preparation.delete') }}
        </Button>
      </Space>
    </template>
    <template #action="{ row }">
      <template v-if="hasEditStatus(row)">
        <Button type="link" @click="saveRowEvent()">
          {{ $t('business-static-tables.save') }}
        </Button>
        <Button type="link" @click="cancelRowEvent(row)">
          {{ $t('business-static-tables.cancel') }}
        </Button>
      </template>
      <template v-else>
        <Button type="link" @click="editRowEvent(row)">
          {{ $t('business-static-tables.edit') }}
        </Button>
      </template>
    </template>
  </Grid>
  <Tabs v-model:active-key="activeKey" class="w-full" @change="tabsChange">
    <TabPane v-for="item in tabList" :key="item.title" :tab="item.title" />
  </Tabs>
  <HistoryGrid class="h-2/5" v-show="activeKey === '历史记录'" />
  <FileGrid class="h-2/5" v-show="activeKey === '附件'">
    <template #toolbar-actions>
      <Space :size="[8, 0]" wrap>
    <Button type="primary" @click="addFiles">
          {{ $t('dilution-management.solution-preparation.add') }}
        </Button>
        <Button type="primary" @click="deleteFiles">
          {{ $t('dilution-management.solution-preparation.delete') }}
        </Button>
        <Button type="primary" danger @click="viewFiles">
          {{ $t('dilution-management.solution-preparation.view') }}
        </Button>
        <Button type="primary" @click="takePhoto">
          {{ $t('dilution-management.solution-preparation.takePhoto') }}
        </Button>
        <Button type="primary" @click="batchAddFiles">
          {{ $t('dilution-management.solution-preparation.batchAdd') }}
        </Button>
        <Button type="primary" @click="downloadFiles">
          {{ $t('dilution-management.solution-preparation.download') }}
        </Button>
      </Space>
    </template>
  </FileGrid>
  <SoluteGrid class="h-2/5" v-show="activeKey === '溶质'">
    <template #toolbar-actions>
      <Space :size="[8, 0]" wrap>
        <Button type="primary" @click="addSolution">
          {{ $t('dilution-management.solution-preparation.addSolute') }}
        </Button>
        <Button type="primary" @click="addStandardStockSolution">
          {{
            $t(
              'dilution-management.solution-preparation.addStandardStockSolution',
            )
          }}
        </Button>
        <Button type="primary" danger @click="deleteDetailSolution">
          {{ $t('dilution-management.solution-preparation.delete') }}
        </Button>
      </Space>
    </template>
  </SoluteGrid>
</template>
