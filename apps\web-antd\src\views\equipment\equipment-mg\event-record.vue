<script lang="ts" setup>
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { EquipmentMgApi } from '#/api/equipment/equipment-mg';

import { computed, ref, watch } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { Button, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  getCertificateApi,
  getMaintRecsApi,
} from '#/api/equipment/equipment-mg';
import { $t } from '#/locales';
import { useEquipmentStore } from '#/store';

import MaintainModel from './components/maintain-modal.vue';
import SearchModal from './components/search-modal.vue';
import StartDeviceModal from './components/start-device-modal.vue';
import StopDeviceModal from './components/stop-device-modal.vue';
import { certificateColumns, eventRecordColumns } from './equipment-mg-data';

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: MaintainModel,
  destroyOnClose: true,
});
const [StartDeviceFormModal, startDeviceModalApi] = useVbenModal({
  connectedComponent: StartDeviceModal,
  destroyOnClose: true,
});
const [StopDeviceFormModal, stopDeviceModalApi] = useVbenModal({
  connectedComponent: StopDeviceModal,
  destroyOnClose: true,
});

const [SearchDeviceFormModal] = useVbenModal({
  connectedComponent: SearchModal,
  destroyOnClose: true,
});
const equipmentStore = useEquipmentStore();
const currentRow: EquipmentMgApi.RowType = computed(
  () => equipmentStore.getCurrentRow,
);
interface RowType {
  [key: string]: any;
}
const currentEventRow = ref<EquipmentMgApi.RowType | null>({});
const isShowELNButton = computed(() => {
  return !(currentEventRow.value && currentEventRow.value.ELN_ID);
});
watch(
  currentRow,
  async (newRow: RowType) => {
    if (newRow) {
      gridApi.query();
    }
  },
  { deep: true },
);
watch(
  currentEventRow,
  async (newRow: RowType) => {
    if (newRow) {
      certificateGridApi.query();
    }
  },
  { deep: true },
);
const gridOptions: VxeTableGridOptions<EquipmentMgApi.MetaDataEquipment> = {
  columns: eventRecordColumns(),
  stripe: true,
  border: true,
  checkboxConfig: {
    highlight: true,
    range: true,
    labelField: 'select',
  },
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!currentRow.value) {
          return [];
        }
        const data = await getMaintRecsApi([currentRow.value.EQID]);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'ORIGREC',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
const gridEvents: VxeGridListeners<EquipmentMgApi.MetaDataEquipment> = {
  currentChange: async ({ row }) => {
    if (row) {
      isFinished.value = row.STATUS !== 'Not in Service';
      currentEventRow.value = row;
      console.warn(`当前行：${row}`);
    }
  },
};
const certificateOptions: VxeTableGridOptions<EquipmentMgApi.MetaDataEquipment> =
  {
    columns: certificateColumns(),
    stripe: true,
    border: true,
    editConfig: {
      mode: 'row',
      trigger: 'manual',
    },
    height: 'auto',
    pagerConfig: {},
    proxyConfig: {
      ajax: {
        query: async () => {
          if (
            !currentRow.value ||
            !currentEventRow.value ||
            !currentEventRow.value.ORIGREC
          ) {
            return [];
          }
          const data = await getCertificateApi([
            currentRow.value.EQID,
            currentEventRow.value.ORIGREC,
          ]);
          return data;
        },
      },
    },
    exportConfig: {},
    showOverflow: true,
    rowConfig: {
      keyField: 'origrec',
    },
    toolbarConfig: {
      custom: true,
      export: true,
      refresh: {},
      search: true,
      zoom: true,
    },
  };

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents,
});
const [CertificateGrid, certificateGridApi] = useVbenVxeGrid({
  gridOptions: certificateOptions,
  gridEvents: {},
});
const isFinished = ref<boolean>(true);

const hasEditStatus = (row: EquipmentMgApi.RowType) => {
  return gridApi.grid?.isEditByRow(row);
};
const editRowEvent = (row: EquipmentMgApi.RowType) => {
  gridApi.grid?.setEditRow(row);
};

const saveRowEvent = async (row: EquipmentMgApi.RowType) => {
  await gridApi.grid?.clearEdit();
  gridApi.setLoading(true);
  setTimeout(() => {
    gridApi.setLoading(false);
    console.warn(`保存成功！${row}`);
  }, 600);
};
const saveCertificateRowEvent = async (row: EquipmentMgApi.RowType) => {
  await certificateGridApi.grid?.clearEdit();
  certificateGridApi.setLoading(true);
  setTimeout(() => {
    certificateGridApi.setLoading(false);
    console.warn(`保存成功！${row}`);
  }, 600);
};
const hasCertificateEditStatus = (row: EquipmentMgApi.RowType) => {
  return certificateGridApi.grid?.isEditByRow(row);
};
const cancelCertificateRowEvent = (_row: EquipmentMgApi.RowType) => {
  certificateGridApi.grid?.clearEdit();
};
const editCertificateRowEvent = (row: EquipmentMgApi.RowType) => {
  certificateGridApi.grid?.setEditRow(row);
};

const Finish = () => {
  console.warn('Finish');
};
const viewELN = () => {
  console.warn('viewELN');
  // TODO: 查看ELN任务
};
const addCertificate = () => {
  console.warn('addCertificate');
};
const viewCertificate = () => {
  console.warn('viewCertificate');
};
const lauchELN = () => {
  console.warn('lauchELN');
  // TODO: 运行ELN任务
};
const onRefresh = () => {
  gridApi.query();
};
const openMainEvent = () => {
  formModalApi.setData(null).open();
};
const cancelRowEvent = (row: EquipmentMgApi.RowType) => {
  console.warn(`cancelRowEvent${row}`);
  // warn
};
const startDevice = () => {
  startDeviceModalApi.setData({ currentEventRow }).open();
};
const disableDevice = () => {
  stopDeviceModalApi.setData(null).open();
};
</script>
<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <StartDeviceFormModal @success="onRefresh" />
    <StopDeviceFormModal @success="onRefresh" />
    <SearchDeviceFormModal @success="onRefresh" />
    <Grid class="h-1/2">
      <template #toolbar-actions>
        <Space :size="[4, 0]" wrap>
          <Button type="primary" danger @click="openMainEvent">
            {{ $t('equipment.equipment-mg.openMainEvent') }}
          </Button>
          <Button type="primary" v-if="isFinished" disabled @click="Finish">
            {{ $t('equipment.equipment-mg.finished') }}
          </Button>
          <Button type="primary" v-else @click="startDevice">
            {{ $t('equipment.equipment-mg.activationDevice') }}
          </Button>
          <Button type="primary" :disabled="!isFinished" @click="disableDevice">
            {{ $t('equipment.equipment-mg.disable') }}
          </Button>
          <Button type="primary" @click="addCertificate">
            {{ $t('equipment.equipment-mg.addCertificate') }}
          </Button>
          <Button type="primary" @click="viewCertificate">
            {{ $t('equipment.equipment-mg.checkCertificate') }}
          </Button>
          <Button type="primary" :disabled="isShowELNButton" @click="lauchELN">
            {{ $t('equipment.equipment-mg.launchELN') }}
          </Button>
          <Button type="primary" :disabled="isShowELNButton" @click="viewELN">
            {{ $t('equipment.equipment-mg.inspectELN') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent(row)">
            {{ $t('basic-static-tables.save') }}
          </Button>
          <Button type="link" @click="cancelRowEvent(row)">
            {{ $t('equipment.cancel') }}
          </Button>
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">
            {{ $t('basic-static-tables.edit') }}
          </Button>
        </template>
      </template>
    </Grid>
    <CertificateGrid class="h-2/5">
      <template #action="{ row }">
        <template v-if="hasCertificateEditStatus(row)">
          <Button type="link" @click="saveCertificateRowEvent(row)">
            {{ $t('basic-static-tables.save') }}
          </Button>
          <Button type="link" @click="cancelCertificateRowEvent(row)">
            {{ $t('equipment.cancel') }}
          </Button>
        </template>
        <template v-else>
          <Button type="link" @click="editCertificateRowEvent(row)">
            {{ $t('basic-static-tables.edit') }}
          </Button>
        </template>
      </template>
    </CertificateGrid>
  </Page>
</template>

<style>
.ant-tabs-content {
  height: 100%;
}
</style>
