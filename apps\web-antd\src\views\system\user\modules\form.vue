<script lang="ts" setup>
import type { UploadFile } from 'ant-design-vue';

import type { Ref } from 'vue';

import type { SystemUserApi } from '#/api';

import { computed, ref, watch } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import { createUser, updateUser } from '#/api';
import { $t } from '#/locales';

import { useFormSchema } from '../data';

const emits = defineEmits(['success']);

const formData = ref<SystemUserApi.User & { fileList?: UploadFile[] }>();
const [Form, formApi] = useVbenForm({
  schema: useFormSchema(formData as Ref<SystemUserApi.User>),
  showDefaultActions: false,
});

watch(
  () => formData.value?.fileList,
  (fileList) => {
    if (formData.value) {
      formData.value.icon = fileList?.length
        ? fileList[0]?.url || (fileList[0]?.response as string)
        : undefined;
    }
  },
);

const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const values = await formApi.getValues();
    drawerApi.lock();

    const { fileList: _fileList, ...rest } = values;

    const payload = { ...rest, icon: formData.value?.icon };

    try {
      await (formData.value?.userId
        ? updateUser(formData.value.userId, payload as SystemUserApi.User)
        : createUser(payload as SystemUserApi.User));
      emits('success');
      drawerApi.close();
    } catch (error) {
      console.error('Failed to save user:', error);
    } finally {
      drawerApi.unlock();
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<SystemUserApi.User>();
      formApi.resetForm();
      if (data) {
        formData.value = { ...data, fileList: [] };
        if (data.icon) {
          formData.value.fileList = [
            {
              uid: '-1',
              name: 'avatar.png',
              status: 'done',
              url: data.icon,
            },
          ];
        }

        formApi.setValues(formData.value);
      } else {
        formData.value = undefined;
      }
    }
  },
});

const getDrawerTitle = computed(() => {
  return formData.value?.userId
    ? $t('ui.actionTitle.edit', [$t('system.user.name')])
    : $t('ui.actionTitle.create', [$t('system.user.name')]);
});
</script>
<template>
  <Drawer :title="getDrawerTitle">
    <Form />
  </Drawer>
</template>
