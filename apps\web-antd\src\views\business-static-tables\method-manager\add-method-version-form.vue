<script lang="ts" setup>
import type { MethodManagerApi } from '#/api/business-static-tables';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { $addTestMethodVersionApi } from '#/api/business-static-tables/method-manager';
import { $t } from '#/locales';

import { useMethodVersionSchema } from './data';

const emit = defineEmits(['success']);

const methodData = ref<MethodManagerApi.TestMethod>();
const getTitle = computed(() => {
  return $t('ui.actionTitle.create', [
    $t('business-static-tables.methodManager.method'),
  ]);
});

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: useMethodVersionSchema(),
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    modalApi.lock();
    try {
      data.METHOD = methodData.value?.METHOD;
      const res = await $addTestMethodVersionApi(data);
      if (res[0] === 'DraftVersionExists') {
        message.error(
          $t('business-static-tables.methodVersionManager.draftVersionExists'),
          1,
        );
        return;
      }
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<MethodManagerApi.TestMethod>();
      if (data) {
        methodData.value = data;
      }
    }
  },
});
</script>

<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
  </Modal>
</template>
