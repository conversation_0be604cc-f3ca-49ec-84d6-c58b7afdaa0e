import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { HolidaysApi } from '#/api/basic-static-tables/holidays';

import { h } from 'vue';

import { z } from '#/adapter/form';
import { $getSitesApi, $getYearsApi } from '#/api/basic-static-tables/holidays';
import { $t } from '#/locales';

export function useHolidayColumns(): VxeTableGridOptions<HolidaysApi.Holidays>['columns'] {
  return [
    {
      type: 'checkbox',
      align: 'center',
      width: 60,
    },
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      visible: true,
      width: 80,
    },
    {
      align: 'center',
      field: 'DEPT',
      title: $t('Holidays.Department'),
      visible: false,
    },
    {
      align: 'center',
      field: 'HOL_DATE',
      title: $t('commons.date'),
      visible: true,
      editRender: {
        name: 'Input',
        props: {},
      },
    },
    {
      align: 'center',
      field: 'HOL_NAME',
      title: $t('commons.name'),
      visible: true,
      editRender: {
        name: 'Input',
        props: {},
      },
    },
    {
      align: 'center',
      field: 'HOL_DESCRIPTION',
      title: $t('commons.description'),
      visible: true,
      editRender: {
        name: 'Input',
        props: {},
      },
    },
    {
      align: 'center',
      field: 'HOL_YEAR',
      title: $t('commons.Date'),
      visible: false,
    },
    {
      align: 'center',
      field: 'ISWORKDAY',
      title: $t('basic-static-tables.holidays.isWorkDay'),
      visible: true,
      editRender: {
        name: 'Input',
        props: {
          options: [
            {
              label: $t(
                'basic-static-tables.holidays.isWorkDayOptions.workDay',
              ),
              value: '1',
            },
            {
              label: $t(
                'basic-static-tables.holidays.isWorkDayOptions.holiday',
              ),
              value: '0',
            },
          ],
        },
      },
    },
  ];
}

export function useAddHolidaySchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        disabled: true,
      },
      fieldName: 'DEPT',
      label: $t('commons.dept'),
      rules: z.string().nullable(),
    },
    {
      component: 'DatePicker',
      fieldName: 'HOL_DATE',
      label: $t('commons.date'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'HOL_NAME',
      label: $t('commons.name'),
      rules: z.string().optional(),
    },
    {
      component: 'Checkbox',
      fieldName: 'ISWORKDAY',
      label: $t('basic-static-tables.holidays.isWorkDay'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'HOL_DESCRIPTION',
      label: $t('commons.description'),
      rules: z.string().optional(),
    },
  ];
}

export function useCopyHolidaySchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      componentProps: {
        api: $getSitesApi,
        afterFetch: (data: { DEPT: string }[]) => {
          return data.map((item: any) => ({
            label: item.DEPT,
            value: item.DEPT,
          }));
        },
      },
      fieldName: 'DEPT',
      label: $t('commons.dept'),
      rules: z.string().nullable(),
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: $getYearsApi,
        params: { dept: 'Site1', fromSysData: 'Y' },
        afterFetch: (data: { Text: string }[]) => {
          return data.map((item: any) => ({
            label: item.Text,
            value: item.Text,
          }));
        },
      },
      fieldName: 'YEAR',
      label: $t('commons.year'),
      rules: 'required',
    },
    {
      component: 'Divider',
      fieldName: '_divider',
      formItemClass: 'col-span-2',
      hideLabel: true,
      renderComponentContent: () => {
        return {
          default: () => h('div', '复制到'),
        };
      },
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: $getSitesApi,
        afterFetch: (data: { DEPT: string }[]) => {
          return data.map((item: any) => ({
            label: item.DEPT,
            value: item.DEPT,
          }));
        },
        params: 'Weekdays',
      },
      fieldName: 'TO_DEPT',
      label: $t('basic-static-tables.holidays.copyToDept'),
      rules: z.string().nullable(),
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: $getYearsApi,
        params: { dept: 'Site1', fromSysData: 'Y' },
        afterFetch: (data: { Text: string }[]) => {
          return data.map((item: any) => ({
            label: item.Text,
            value: item.Text,
          }));
        },
      },
      fieldName: 'TO_YEAR',
      label: $t('basic-static-tables.holidays.copyToYear'),
      rules: 'required',
    },
  ];
}
