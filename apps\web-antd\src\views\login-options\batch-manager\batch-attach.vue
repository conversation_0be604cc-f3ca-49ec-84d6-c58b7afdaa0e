<script setup lang="ts">
import type { BatcheManagerApi } from '#/api/login-options/batch-manager';

import { watch } from 'vue';

import { confirm } from '@vben/common-ui';

import { Button, message, Space } from 'ant-design-vue';

import {
  deleteDocumentation,
  getReportFile,
} from '#/api/login-options/batch-manager';
import { $t } from '#/locales';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import {
  useBatchAttachColumns,
  useBatchAttachFilterSchema,
} from './batch-manager-data';

const props = defineProps<{
  currentTestRow: BatcheManagerApi.Batches | null;
}>();

watch(
  () => props.currentTestRow,
  (_val) => {
    onRefresh();
  },
);
const colums = useBatchAttachColumns();
const filterSchema = useBatchAttachFilterSchema();
const queryData = async () => {
  if (!props.currentTestRow) return [];
  const data = await getReportFile(props.currentTestRow.ORIGREC);
  return data.items;
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};

const {
  Grid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
} = useLimsGridsConfig<BatcheManagerApi.BatchAttach>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

function onRefresh() {
  gridApi.query();
}

// 移除配方
async function onRemove() {
  // 获取选中行
  const doc = gridApi.grid?.getCurrentRecord();
  if (!doc) return;

  const sOrigrec = doc.ORIGREC;
  const sStarDocId = doc.STARDOC_ID;
  try {
    await confirm({
      title: '确认移除',
      content: `确定要移除选中的数据吗？`,
      icon: 'warning',
      centered: false,
    });

    await deleteDocumentation(sOrigrec, sStarDocId);

    message.success('移除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}
</script>

<template>
  <div class="h-[350px] w-full">
    <Grid>
      <template #toolbar-actions>
        <Space>
          <Button type="primary">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="onRemove">
            {{ $t('login-options.remove') }}
          </Button>
          <Button type="default">
            {{ $t('login-options.batchManager.viewFile') }}
          </Button>
          <Button type="default">
            {{ $t('login-options.batchManager.btnPicture') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent(row)">
            {{ $t('login-options.save') }}
          </Button>
          <Button type="link" @click="cancelRowEvent(row)">
            {{ $t('login-options.cancel') }}
          </Button>
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">
            {{ $t('login-options.edit') }}
          </Button>
        </template>
      </template>
    </Grid>
  </div>
</template>
