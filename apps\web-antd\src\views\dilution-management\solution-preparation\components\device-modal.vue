<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { SolutionPreparationApi } from '#/api/dilution-management/solution-preparation';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deviceColumns,
  deviceDetailColumns,
} from '../solution preparation-data';
import {
  getAllEquipmenttimeApi,
  deleteRowsApi,
  getEquipmentApi,
  getOtherEquipmentUsedtimeApi,
  editEquimentListApi,
  addOtherEquipmentUsedTimeApi,
  updateEndDateApi,
} from '#/api/dilution-management/solution-preparation';

import { Button, Space, Modal as AModal, CheckboxGroup } from 'ant-design-vue';
import { ref, computed } from 'vue';
const emit = defineEmits(['success']);
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    emit('success');
    modalApi.close();
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      clickRow.value = modalApi.getData().clickRow;
      gridApi.query();
    }
  },
});
const clickDeviceRow = ref<RowType>({});

const gridOptions: VxeTableGridOptions<SolutionPreparationApi.Form> = {
  columns: deviceColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!clickRow.value.BATCHNO) {
          return [];
        }

        const data = await getEquipmentApi([
          clickRow.value.BATCHNO,
          'DILUTION',
        ]);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
const deviceGridOptions: VxeTableGridOptions<SolutionPreparationApi.Form> = {
  columns: deviceDetailColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!clickDeviceRow.value.ORIGREC) {
          return [];
        }
        const data = await getOtherEquipmentUsedtimeApi([
          clickDeviceRow.value.ORIGREC,
        ]);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};
interface RowType {
  [key: string]: any;
}
const clickRow = ref<RowType>({});
const clickRecordRow = ref<RowType>({});

const gridEvents: VxeGridListeners<SolutionPreparationApi.Form> = {
  currentChange: async ({ row }) => {
    if (row) {
      clickDeviceRow.value = row;
      deviceGridApi.query();
    }
  },
};
const recordGridEvents: VxeGridListeners<SolutionPreparationApi.Form> = {
  currentChange: async ({ row }) => {
    if (row) {
      clickRecordRow.value = row;
    }
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: gridEvents,
});
const [DeviceGrid, deviceGridApi] = useVbenVxeGrid({
  gridOptions: deviceGridOptions,
  gridEvents: recordGridEvents,
});
const editDevice = async () => {
  const res = await getAllEquipmenttimeApi([
    clickRow.value.BATCHNO,
    'DILUTION',
  ]);
  options.value = res.map((item) => item.EQ_DISPLAY);
  open.value = true;
};
const removeDevice = () => {
  AModal.confirm({
    title: '询问',
    content: '是否确定要删除选定的记录?',
    cancelText: '否',
    okText: '是',
    async onOk() {
      try {
        // TODO:电子签名
        const params = ['OTHER_EQUIPMENT', [clickDeviceRow.value.ORIGREC]];
        await deleteRowsApi(params);
        gridApi.query();
        deviceGridApi.query();
      } catch {
        console.warn('删除失败');
      }
      console.warn('删除');
      // TODO：电子签名
    },
    onCancel() {},
  });
};
const addRecord = async () => {
  await addOtherEquipmentUsedTimeApi([
    clickDeviceRow.value.ORIGREC,
    clickDeviceRow.value.EQID,
  ]);
  deviceGridApi.query();
};
const addEndDate = async () => {
  await updateEndDateApi([
    clickRecordRow.value.ORIGREC,
    clickDeviceRow.value.EQID,
  ]);
  deviceGridApi.query();
};
const removeRecord = () => {};
const open = ref(false);
const handleOk = async () => {
  const list = checkedList.value.map((item) => item.trim());
  const params = [clickRow.value.BATCHNO.trim(), 'DILUTION', list, list, []];
  await editEquimentListApi(params);
  gridApi.query();
  deviceGridApi.query();
  open.value = false;
};
const options = ref<string[]>([]);
const checkedList = ref<string[]>([]);
const isShowEndDate = computed(() =>
  clickRecordRow.value.EQENDDATE ? false : true,
);
</script>

<template>
  <AModal
    v-model:open="open"
    title="选择设备ID"
    @ok="handleOk"
    width="30%"
    class="h-1/5 w-4/5"
  >
    <div class="h-1/5 overflow-auto">
      <CheckboxGroup
        v-model:value="checkedList"
        name="checkboxgroup"
        :options="options"
        class="h-96"
        style="display: grid"
      />
    </div>
  </AModal>
  <Modal title="实验设备" class="h-5/6 w-3/5">
    <div class="flex h-full justify-between">
      <div class="h-full w-1/2">
        <Grid>
          <template #toolbar-actions>
            <Space :size="[8, 0]" wrap>
              <Button type="primary" @click="editDevice">
                {{
                  $t(
                    'dilution-management.solution-preparation.addEditEquipmentId',
                  )
                }}
              </Button>
              <Button type="primary" @click="removeDevice">
                {{ $t('dilution-management.solution-preparation.remove') }}
              </Button>
            </Space></template
          ></Grid
        >
      </div>
      <div class="h-full w-1/2">
        <DeviceGrid>
          <template #toolbar-actions>
            <Space :size="[8, 0]" wrap>
              <Button type="primary" @click="addRecord">
                {{ $t('dilution-management.solution-preparation.add') }}
              </Button>
              <Button type="primary" @click="removeRecord">
                {{ $t('dilution-management.solution-preparation.remove') }}
              </Button>
              <Button type="primary" @click="addEndDate" v-show="isShowEndDate">
                {{ $t('dilution-management.solution-preparation.endDate') }}
              </Button>
            </Space>
          </template>
        </DeviceGrid>
      </div>
    </div>
  </Modal>
</template>
<style>
.list-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #ccc;
  transition: background-color 0.3s;
}

.list-item:hover {
  background-color: #f0f0f0;
}
</style>
