<script setup lang="ts">
import type { VxeGridListeners } from '#/adapter/vxe-table';
import type { SampleGroupsApi } from '#/api/business-static-tables/sample-groups';

import { ref, watch } from 'vue';

// import { confirm, useVbenModal } from '@vben/common-ui';
import { Col, Row } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getSamplingRequirementsSamplesProfiles } from '#/api/business-static-tables/sample-groups';
import { createGridOptions } from '#/utils/grid-option';

import SampleRequirementGrid from './requirements.vue';
import { useSampleReqProfileColumns } from './sample-groups-data';

const props = defineProps<{
  currentTestRow: null | SampleGroupsApi.IpSampleGroups;
}>();

// const isRowEditDisabled = ref(false);
const profile = ref<string>('');
const sampleGroupCode = ref<string>('');
const spCode = ref<number>(0);

const currentSample = ref<SampleGroupsApi.IpSampleGroupDetails>();

watch(
  () => props.currentTestRow,
  (_val) => {
    onSampleListRefresh();
    // onRefresh();
  },
);

// 测试计划组
const sampleListGridOptions = {
  ...createGridOptions<SampleGroupsApi.IpSampleGroupDetails>(),
  columns: useSampleReqProfileColumns(),
  rowConfig: {
    isCurrent: true,
  },
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!props.currentTestRow) return [];
        const data = await getSamplingRequirementsSamplesProfiles(
          props.currentTestRow.SAMPLEGROUPCODE,
          'false',
          'Create',
        );
        return data.items;
      },
    },
  },
};

const sampleListGridEvents: VxeGridListeners<SampleGroupsApi.IpSampleGroupDetails> =
  {
    currentRowChange: async ({ row }) => {
      currentSample.value = row;
      profile.value = row.PROFILE;
      sampleGroupCode.value = props.currentTestRow?.SAMPLEGROUPCODE || '';
      spCode.value = row.SP_CODE;
      // onRefresh();
    },
  };

const [SampleReqProfileGrid, sampleReqProfileGridApi] = useVbenVxeGrid({
  gridOptions: sampleListGridOptions,
  gridEvents: sampleListGridEvents,
});

function onSampleListRefresh() {
  sampleReqProfileGridApi.query();
}
</script>

<template>
  <div class="h-[350px] w-full">
    <!-- 使用 a-row 实现行布局 -->
    <Row :gutter="16" class="h-full flex-nowrap">
      <!-- 左侧新 Grid 区域 -->
      <Col :span="6" class="h-full overflow-hidden">
        <SampleReqProfileGrid />
      </Col>

      <!-- 右侧原有的 RecipeGrid -->
      <Col :span="18" class="h-full overflow-hidden">
        <SampleRequirementGrid
          :profile="profile"
          :sample-group-code="sampleGroupCode"
          :sp-code="spCode"
        />
      </Col>
    </Row>
  </div>
</template>
