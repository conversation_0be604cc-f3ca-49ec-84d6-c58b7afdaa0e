import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { SampleGroupsApi } from '#/api/business-static-tables/sample-groups';

import { $t } from '@vben/locales';

import dayjs from 'dayjs';

import { getAllRoles } from '#/api/business-static-tables/sample-groups';

export function useSampleGrpFilterSchema(): VbenFormSchema[] {
  return [];
}
export function useSampleGrpColumns(): VxeTableGridOptions<SampleGroupsApi.IpSampleGroups>['columns'] {
  return [
    // { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'SUBGROUPNAME',
      title: $t('business-static-tables.sampleGroups.subgroupname'),
      editRender: { name: 'input' },
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'PRODGROUP',
      title: $t('business-static-tables.sampleGroups.prodgroup'),
      editRender: { name: 'input' },
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      field: 'SAMPLEGROUPNAME',
      title: $t('business-static-tables.sampleGroups.samplegroupname'),
      editRender: { name: 'input' },
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SAMPLEGROUPCODE',
      title: $t('business-static-tables.sampleGroups.samplegroupcode'),
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'MATCODE',
      title: $t('business-static-tables.sampleGroups.matcode'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SAMPGRTYPE',
      title: $t('business-static-tables.sampleGroups.sampgrtype'),
      editRender: {
        name: 'select',
        options: [
          { value: 'RAWMAT', label: '原辅包' },
          { value: 'LOTTEMPLATE', label: '产品/中间体' },
          { value: 'EM', label: '环境样' },
        ],
      },
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'DEPT',
      title: $t('business-static-tables.sampleGroups.dept'),
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'PLANT',
      title: $t('business-static-tables.sampleGroups.plant'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'METADATA_TEMPLATE',
      title: $t('business-static-tables.sampleGroups.metadataTemplate'),
      editRender: { name: 'input' },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      // formatter: ({ cellValue }) => {
      //   return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      // },
    },
    {
      field: 'ACTIVE',
      title: $t('business-static-tables.sampleGroups.active'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('business-static-tables.operation'),
      minWidth: 140,
    },
  ];
}

export function useSampleGrpDetailFilterSchema(): VbenFormSchema[] {
  return [];
}

export function useSampleGrpDetailColumns(): VxeTableGridOptions<SampleGroupsApi.IpSampleGroupDetails>['columns'] {
  return [
    // { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'SP_CODE',
      title: $t('business-static-tables.sampleGroups.spcode'),
      minWidth: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SEQUENCE',
      title: $t('business-static-tables.sampleGroups,sequence'),
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      field: 'NO_SAMPLES_TO_LOGIN',
      title: $t('business-static-tables.sampleGroups.noSamplesToLogin'),
      minWidth: 110,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'PROGNAME',
      title: $t('business-static-tables.sampleGroups.progname'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'MATCODE',
      title: $t('business-static-tables.sampleGroups.matcode'),
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'VERSION',
      title: $t('business-static-tables.sampleGroups.version'),
      minWidth: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'DISPSTEPCODE',
      title: $t('business-static-tables.sampleGroups.dispstepcode'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'DISPSTATUS',
      title: $t('business-static-tables.sampleGroups.dispstatus'),
      minWidth: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'DATEFROM',
      title: $t('business-static-tables.sampleGroups.datefrom'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      field: 'DATETO',
      title: $t('business-static-tables.sampleGroups.dateto'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      field: 'PROFILE',
      title: $t('business-static-tables.sampleGroups.profile'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SPECIFICATION',
      title: $t('business-static-tables.sampleGroups.specification'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'AUTOADD',
      title: $t('business-static-tables.sampleGroups.autoadd'),
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SKIP_SAMPLING',
      title: $t('business-static-tables.sampleGroups.skipSampling'),
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SKIP_CR',
      title: $t('business-static-tables.sampleGroups.skipCr'),
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      minWidth: 170,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SKIP_RECEIVE',
      title: $t('business-static-tables.sampleGroups.skipReceive'),
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      minWidth: 170,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'AUTODISPOSITION',
      title: $t('business-static-tables.sampleGroups.autoDisposition'),
      editRender: {
        name: 'select',
        options: [
          { value: 'I', label: '自动发布已达标' },
          { value: 'A', label: '自动发布' },
          { value: 'M', label: '需手动发布' },
        ],
      },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'BATCHAUTORELEASE',
      title: $t('business-static-tables.sampleGroups.batchAutoRelease'),
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'INSPECTIONCYCLE',
      title: $t('business-static-tables.sampleGroups.inspectionCycle'),
      editRender: { name: 'input' },
      minWidth: 170,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SKIPBATCHINSPECTION',
      title: $t('business-static-tables.sampleGroups.skipBatchInspection'),
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'BATCHNUMBER',
      title: $t('business-static-tables.sampleGroups.batchNumber'),
      editRender: { name: 'input' },
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'ROLE_SAMPLING',
      title: $t('business-static-tables.sampleGroups.roleSampling'),
      editRender: {
        name: 'select',
        options: rolesOptions,
      },
      minWidth: 170,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('business-static-tables.operation'),
      minWidth: 140,
    },
  ];
}

const rolesResult = await getAllRoles();
const rolesOptions = rolesResult.items.map(
  (item: { Text: string; Value: string }) => ({
    label: item.Text,
    value: item.Value,
  }),
);

export function useSpecsFilterSchema(): VbenFormSchema[] {
  return [];
}

export function useSpecsColumns(): VxeTableGridOptions<SampleGroupsApi.Specs>['columns'] {
  return [
    // { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'PRIMARY_SPEC',
      title: $t('business-static-tables.sampleGroups.primarySpec'),
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SPECIFICATION',
      title: $t('business-static-tables.sampleGroups.specification'),
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SPECNO',
      title: $t('business-static-tables.sampleGroups.specNo'),
      editRender: { name: 'input' },
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      field: 'SPECIFICATION_O',
      title: $t('business-static-tables.sampleGroups.specificationO'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      field: 'VERSION',
      title: $t('business-static-tables.sampleGroups.version'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'INSPECTION_STANDARD',
      title: $t('business-static-tables.sampleGroups.inspectionStandard'),
      editRender: { name: 'input' },
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('business-static-tables.operation'),
      minWidth: 140,
    },
  ];
}

export function useSpTestsFilterSchema(): VbenFormSchema[] {
  return [];
}

export function useSpTestsColumns(): VxeTableGridOptions<SampleGroupsApi.SpTests>['columns'] {
  return [
    // { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'PROFILE',
      title: $t('business-static-tables.sampleGroups.profile'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      field: 'SPTESTSORTER',
      title: $t('business-static-tables.sampleGroups.spTestsSorter'),
      minWidth: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SP_TESTNO',
      title: $t('business-static-tables.sampleGroups.spTestNo'),
      editRender: { name: 'input' },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'TESTNO',
      title: $t('business-static-tables.sampleGroups.testNo'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SP_CODE',
      title: $t('business-static-tables.sampleGroups.spCode'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      field: 'TESTCODE',
      title: $t('business-static-tables.sampleGroups.testCode'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      field: 'RETEST_FLAG',
      title: $t('business-static-tables.sampleGroups.retestFlag'),
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SCENE_FLAG',
      title: $t('business-static-tables.sampleGroups.sceneFlag'),
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('business-static-tables.operation'),
      minWidth: 140,
    },
  ];
}

export function useSpAnalytesFilterSchema(): VbenFormSchema[] {
  return [];
}

export function useSpAnalytesColumns(): VxeTableGridOptions<SampleGroupsApi.SpAnalytes>['columns'] {
  return [
    // { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'SP_SYNONYM',
      title: $t('business-static-tables.sampleGroups.spSynonym'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'ANALYTE',
      title: $t('business-static-tables.sampleGroups.analyte'),
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'NOREP',
      title: $t('business-static-tables.sampleGroups.noRep'),
      editRender: { name: 'input' },
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SORTER',
      title: $t('business-static-tables.sampleGroups.sorter'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('business-static-tables.operation'),
      minWidth: 140,
    },
  ];
}

export function useMethodsRelSpTestsFilterSchema(): VbenFormSchema[] {
  return [];
}

export function useMethodsRelSpTestsColumns(): VxeTableGridOptions<SampleGroupsApi.MethodsRelSpTests>['columns'] {
  return [
    // { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'DEFAULTMETHOD',
      title: $t('business-static-tables.sampleGroups.defaultMethod'),
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'METHOD',
      title: $t('business-static-tables.sampleGroups.method'),
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SP_CODE',
      title: $t('business-static-tables.sampleGroups.sp_code'),
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      field: 'TESTCODE',
      title: $t('business-static-tables.sampleGroups.testcode'),
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      field: 'PROFILE',
      title: $t('business-static-tables.sampleGroups.profile'),
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      field: 'DRAWNO',
      title: $t('business-static-tables.sampleGroups.drawNo'),
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      field: 'DEPT',
      title: $t('business-static-tables.sampleGroups.dept'),
      editRender: { name: 'input' },
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SERVGRP',
      title: $t('business-static-tables.sampleGroups.servGrp'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'ELNREFERENCE',
      title: $t('business-static-tables.sampleGroups.elnReference'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'ELN_ID',
      title: $t('business-static-tables.sampleGroups.elnId'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'TEST_USRNAM',
      title: $t('business-static-tables.sampleGroups.testUsrnam'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('business-static-tables.operation'),
      minWidth: 140,
    },
  ];
}

export function useMethodsRelSpTestsEqTypeFilterSchema(): VbenFormSchema[] {
  return [];
}

export function useMethodsRelSpTestsEqTypeColumns(): VxeTableGridOptions<SampleGroupsApi.MethodsRelSpTestsEqType>['columns'] {
  return [
    // { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'EQTYPE',
      title: $t('business-static-tables.sampleGroups.eqType'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}

export function useMethodsRelSpTestsMetFilterSchema(): VbenFormSchema[] {
  return [];
}

export function useMethodsRelSpTestsMetColumns(): VxeTableGridOptions<SampleGroupsApi.MethodsRelSpTestsMet>['columns'] {
  return [
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'MATCODE',
      title: $t('business-static-tables.sampleGroups.matCode'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'MATNAME',
      title: $t('business-static-tables.sampleGroups.matName'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}

export function useRecipeFilterSchema(): VbenFormSchema[] {
  return [];
}

export function useRecipeColumns(): VxeTableGridOptions<SampleGroupsApi.Recipes>['columns'] {
  return [
    // { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'REF_ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'STATUS',
      title: $t('business-static-tables.sampleGroups.status'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      field: 'RECIPECODE',
      title: $t('business-static-tables.sampleGroups.recipecode'),
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'RECIPENAME',
      title: $t('business-static-tables.sampleGroups.recipename'),
      editRender: { name: 'input' },
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'STARTDDATE',
      title: $t('business-static-tables.sampleGroups.startddate'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      field: 'EXPDATE',
      title: $t('business-static-tables.sampleGroups.expdate'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      field: 'RETIREDDAT',
      title: $t('business-static-tables.sampleGroups.retireddat'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      field: 'SAMPLEGROUPCODE',
      title: $t('business-static-tables.sampleGroups.samplegroupcode'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('business-static-tables.operation'),
      minWidth: 140,
    },
  ];
}

export function useSampleReqProfileColumns(): VxeTableGridOptions<SampleGroupsApi.IpSampleGroupDetails>['columns'] {
  return [
    // { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'PROGNAME',
      title: $t('business-static-tables.sampleGroups.progname'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'VERSION',
      title: $t('business-static-tables.sampleGroups.version'),
      minWidth: 80,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'PROFILE',
      title: $t('business-static-tables.sampleGroups.profile'),
      minWidth: 80,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SP_CODE',
      title: $t('business-static-tables.sampleGroups.spcode'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
  ];
}

export function useSampleRequirementFilterSchema(): VbenFormSchema[] {
  return [];
}

export function useSampleRequirementColumns(): VxeTableGridOptions<SampleGroupsApi.SamplingRequirements>['columns'] {
  return [
    // { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'SAMPLINGPOSITION',
      title: $t('business-static-tables.sampleGroups.samplingposition'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      field: 'SAMPLESIZE',
      title: $t('business-static-tables.sampleGroups.samplesize'),
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'NUMBEROFCONTAINERS',
      title: $t('business-static-tables.sampleGroups.numberofcontainers'),
      editRender: { name: 'input' },
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SAMPLE_TYPE',
      title: $t('business-static-tables.sampleGroups.sampleType'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'CONTAINERQTY',
      title: $t('business-static-tables.sampleGroups.containerqty'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'CONTAINER_UNITS',
      title: $t('business-static-tables.sampleGroups.containerUnits'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'FORLAB',
      title: $t('business-static-tables.sampleGroups.forlab'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },

    {
      field: 'PURPOSE',
      title: $t('business-static-tables.sampleGroups.purpose'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'CONDITION',
      title: $t('business-static-tables.sampleGroups.condition'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'CREATEINVENTORYID',
      title: $t('business-static-tables.sampleGroups.createInventoryId'),
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'ALIQUOT',
      title: $t('business-static-tables.sampleGroups.aliquot'),
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'ID',
      title: $t('business-static-tables.sampleGroups.id'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('business-static-tables.operation'),
      minWidth: 200,
    },
  ];
}

export function useSampleRequirementTestFilterSchema(): VbenFormSchema[] {
  return [];
}

export function useSampleRequirementTestColumns(): VxeTableGridOptions<SampleGroupsApi.SamplingRequirementsTests>['columns'] {
  return [
    // { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'ID',
      title: $t('business-static-tables.sampleGroups.id'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      field: 'TESTCODE',
      title: $t('business-static-tables.sampleGroups.testcode'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      field: 'TESTNO',
      title: $t('business-static-tables.sampleGroups.testno'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('business-static-tables.operation'),
      minWidth: 200,
    },
  ];
}
