<script lang="ts" setup>
import type { NotifySamplersApi } from '#/api/notify-samplers/notify-samplers';

import { ref } from 'vue'; // 添加了 onMounted 和 reactive 的导入

// 添加了 onMounted 和 reactive 的导入
import { Page } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Button, message, Space, TabPane, Tabs } from 'ant-design-vue';

import {
  getSamplesSelectNew,
  logSamples,
  sendSampTask,
} from '#/api/notify-samplers/notify-samplers';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import {
  useNotifySamplersColumns,
  useNotifySamplersFilterSchema,
} from './notify-samplers-data';
import SampleRequirement from './sample-requirement.vue';

const activeKey = ref('tpRequirement');
const colums = useNotifySamplersColumns();
const filterSchema = useNotifySamplersFilterSchema();
const queryData = async () => {
  return getSamplesSelectNew();
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
};

const {
  Grid,
  gridApi,
  hasEditStatus,
  editRowEvent,
  saveRowEvent,
  cancelRowEvent,
  CurrentRow,
} = useLimsGridsConfig<NotifySamplersApi.NotifySamplers>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

function onRefresh() {
  gridApi.query(); // 主动触发一次查询
}

// 取样
async function onSampling() {
  const aSelectedRows = gridApi.grid?.getCheckboxRecords();
  if (aSelectedRows.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }
  const sSampDateErr = ref();
  const sActualVolErr = ref();

  for (const item of aSelectedRows) {
    const dSampdate = item.SAMPDATE;
    const nActualVol = item.ACTUALVOL;
    if (dSampdate === null) {
      sSampDateErr.value = 'SampdateEmpty';
    } else {
      if (
        new Date(dSampdate).setHours(0, 0, 0, 0) <
        new Date().setHours(0, 0, 0, 0)
      ) {
        sSampDateErr.value = 'SampdateNotPast';
        break;
      }
    }

    if (nActualVol === null || nActualVol === '' || nActualVol <= 0) {
      sActualVolErr.value = 'ActualVolEmptyWarn';
    }
  }

  if (sSampDateErr.value === 'SampdateNotPast') {
    message.warn($t('notify-samplers.SampdateNotPast'));
    return;
  }
  if (sActualVolErr.value === '') {
    message.warn($t('notify-samplers.ActualVolEmptyWarn'));
    return;
  }

  if (sSampDateErr.value === 'SampdateEmpty') {
    message.warn($t('notify-samplers.SampdateEmpty'));
    return;
  }

  const ordnos = aSelectedRows.map((row) => `'${row.ORDNO}'`);
  const sOrderNoList = `(${ordnos.join(',')})`;
  await logSamples(sOrderNoList, 'COMMIT');
  message.success('取样成功');
  onRefresh();
}
// 取样任务下发
async function onSampleTask() {
  // 获取选中行
  const sample = gridApi.grid?.getCurrentRecord();
  if (!sample) return;
  const requestId = sample.REQUESTID;
  const ordNo = sample.ORDNO;
  const batchId = sample.BATCHID;
  if (requestId !== null && requestId !== '') {
    const aRet = await sendSampTask(ordNo, requestId, '', batchId);
    if (aRet) {
      message.success('发送成功');
    } else {
      message.warning('发送失败');
    }
  }
}
</script>
<template>
  <Page auto-content-height>
    <div class="h-[420px] w-full">
      <Grid>
        <template #toolbar-actions>
          <Space>
            <Button type="primary" @click="onSampling">
              {{ $t('notify-samplers.sampling') }}
            </Button>
            <Button type="default">
              {{ $t('notify-samplers.printLabels') }}
            </Button>
            <Button type="default" @click="onSampleTask">
              {{ $t('notify-samplers.sampleTask') }}
            </Button>
            <Button type="default">
              {{ $t('notify-samplers.runELN') }}
            </Button>
            <Button type="default">
              {{ $t('notify-samplers.viewELN') }}
            </Button>
          </Space>
        </template>
        <template #action="{ row }">
          <template v-if="hasEditStatus(row)">
            <Button type="link" @click="saveRowEvent(row)">
              {{ $t('notify-samplers.save') }}
            </Button>
            <Button type="link" @click="cancelRowEvent(row)">
              {{ $t('notify-samplers.cancel') }}
            </Button>
          </template>
          <template v-else>
            <Button type="link" @click="editRowEvent(row)">
              {{ $t('notify-samplers.edit') }}
            </Button>
          </template>
        </template>
      </Grid>
    </div>
    <Tabs v-model:active-key="activeKey">
      <TabPane key="tpRequirement" tab="取样要求">
        <SampleRequirement :current-test-row="CurrentRow" />
      </TabPane>
    </Tabs>
  </Page>
</template>
