import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { CheckListApi } from '#/api/basic-static-tables/check-list';

import { $getCheckListItemTypeApi } from '#/api/basic-static-tables/check-list';
import { $t } from '#/locales';

export function useColumns(): VxeTableGridOptions<CheckListApi.CheckList>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'CHECKSORT',
      title: $t('commons.sort'),
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      editRender: { name: 'input' },
    },
    {
      align: 'center',
      field: 'TYPE',
      title: $t('basic-static-tables.checkList.type'),
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      editRender: { name: 'input' },
    },
    {
      align: 'center',
      field: 'TYPE_EN',
      width: 200,
      title: $t('basic-static-tables.checkList.typeEn'),
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'CONTENT',
      width: 200,
      title: $t('basic-static-tables.checkList.content'),
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      field: 'CONTENT_EN',
      width: 200,
      title: $t('basic-static-tables.checkList.contentEn'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      editRender: { name: 'input' },
    },
    {
      align: 'center',
      field: 'ITEM',
      title: $t('basic-static-tables.checkList.item'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('commons.action'),
      width: 140,
    },
  ];
}

export function useSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'TYPE',
      label: $t('basic-static-tables.checkList.type'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'CONTENT',
      label: $t('basic-static-tables.checkList.content'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'CHECKSORT',
      label: $t('commons.sort'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: $getCheckListItemTypeApi,
        labelField: 'TEXT',
        valueField: 'VALUE',
        class: 'w-full',
      },
      fieldName: 'ITEM',
      label: $t('basic-static-tables.checkList.item'),
      rules: 'required',
    },
  ];
}

export function useFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'TYPE',
      label: $t('basic-static-tables.checkList.type'),
    },
    {
      component: 'Input',
      fieldName: 'CONTENT',
      label: $t('basic-static-tables.checkList.content'),
    },
    {
      component: 'Input',
      fieldName: 'ITEM',
      label: $t('basic-static-tables.checkList.item'),
    },
  ];
}
