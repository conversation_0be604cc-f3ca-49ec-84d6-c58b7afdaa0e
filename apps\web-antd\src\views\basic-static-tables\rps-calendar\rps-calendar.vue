<script lang="ts" setup>
import type { CalendarMode } from 'ant-design-vue/es/calendar/generateCalendar';

import type { Recordable } from '@vben/types';

import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { RpswaCalendarApi } from '#/api/basic-static-tables/rps-calendar';

import { reactive, ref } from 'vue';

import { Page, useVbenModal, VbenCheckButtonGroup } from '@vben/common-ui';

import {
  Badge,
  Button,
  Calendar,
  Card,
  Col,
  FormItem,
  message,
  Modal,
  Row,
  Space,
  TimePicker,
} from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  $deleteCalendarApi,
  $getCalendarEventsApi,
  $getCalendarTreeApi,
  $getCalendarWorkWeekApi,
  $updateCalendarApi,
} from '#/api/basic-static-tables/rps-calendar';
import { $t } from '#/locales';

import AddAdditionalHours from './add-additional-hours.vue';
import AddRpsCalendar from './add-rps-calendar.vue';
import { useColumns } from './rps-calendar-data';

const RPSWA_TABLENAME = 'RPSWA_CALENDAR';
const currentValue = ref<Dayjs>(dayjs());

const selectedCalendar = ref<RpswaCalendarApi.RpswaCalendar>();
const startTime = ref<Dayjs>();
const endTime = ref<Dayjs>();
const checkValue = ref<string[]>([]);
const currentCanalendar = ref<RpswaCalendarApi.RpswaCalendarTree>();

const getListData = (
  value: Dayjs,
): {
  content: string;
  type: 'default' | 'error' | 'processing' | 'success' | 'warning';
}[] => {
  let listData: {
    content: string;
    type: 'default' | 'error' | 'processing' | 'success' | 'warning';
  }[] = [];

  if (!currentCanalendar.value) {
    return listData;
  }
  if (calendarEvents.value && calendarEvents.value.length > 0) {
    listData = calendarEvents.value
      .filter((item) => {
        return (
          dayjs(item.START_DATETIME).format('YYYY-MM-DD') ===
          value.format('YYYY-MM-DD')
        );
      })
      .map((item) => {
        return {
          content:
            `${dayjs(item.START_DATETIME).format(
              'YYYY-MM-DD HH:mm:ss',
            )} - ${dayjs(item.END_DATETIME).format('YYYY-MM-DD HH:mm:ss')}` +
            ` ${item.SUBJECT}`,
          type: 'default',
        };
      });
  }

  return listData;
};

const getMonthData = (value: Dayjs) => {
  if (!currentCanalendar.value) {
    return 0;
  }
  let listData: any[] = [];
  if (calendarEvents.value && calendarEvents.value.length > 0) {
    listData = calendarEvents.value
      .filter((item) => {
        return (
          dayjs(item.START_DATETIME).format('YYYY-MM') ===
          value.format('YYYY-MM')
        );
      })
      .map((item) => {
        return {
          content:
            `${dayjs(item.START_DATETIME).format(
              'YYYY-MM-DD HH:mm:ss',
            )} - ${dayjs(item.END_DATETIME).format('YYYY-MM-DD HH:mm:ss')}` +
            ` ${item.SUBJECT}`,
          type: 'default',
        };
      });
  }
  return listData.length;
};

const compProps = reactive({
  beforeChange: async (val: string, check: boolean) => {
    const updateRes = await updateFields(val, check ? 'Y' : 'N');
    return updateRes;
  },
  disabled: false,
  gap: 5,
  showIcon: true,
  size: 'middle',
} as Recordable<any>);

const updateFields = async (field: string, val: string) => {
  if (currentCanalendar.value && selectedCalendar.value) {
    const fields = [RPSWA_TABLENAME, field, val, currentCanalendar.value.Value];
    const res = await $updateCalendarApi(fields);
    return !!res;
  } else {
    message.error($t('basic-static-tables.rpswaCalendar.selectCalendar'));
    return false;
  }
};

const options = [
  { label: '周日', value: 'SUN' },
  { label: '周一', value: 'MON' },
  { label: '周二', value: 'TUE' },
  { label: '周三', value: 'WED' },
  { label: '周四', value: 'THU' },
  { label: '周五', value: 'FRI' },
  { label: '周六', value: 'SAT' },
];

const gridOptions: VxeTableGridOptions<RpswaCalendarApi.RpswaCalendarTree> = {
  columns: useColumns(),
  stripe: true,
  border: true,
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        return await $getCalendarTreeApi();
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
  },
  treeConfig: {
    parentField: 'Parent',
    rowField: 'Value',
    transform: true,
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
};

const gridEvents: VxeGridListeners<RpswaCalendarApi.RpswaCalendarTree> = {
  currentChange: async (row) => {
    currentCanalendar.value = row.row;
    const workWeek = await $getCalendarWorkWeekApi(row.row.Value);
    selectedCalendar.value = workWeek.items[0];
    const selected: string[] = [];
    if (selectedCalendar.value) {
      if (selectedCalendar.value.SUN === 'Y') {
        selected.push('SUN');
      }
      if (selectedCalendar.value.MON === 'Y') {
        selected.push('MON');
      }
      if (selectedCalendar.value.TUE === 'Y') {
        selected.push('TUE');
      }
      if (selectedCalendar.value.WED === 'Y') {
        selected.push('WED');
      }
      if (selectedCalendar.value.THU === 'Y') {
        selected.push('THU');
      }
      if (selectedCalendar.value.FRI === 'Y') {
        selected.push('FRI');
      }
      if (selectedCalendar.value.SAT === 'Y') {
        selected.push('SAT');
      }
    }
    checkValue.value = selected;
    startTime.value = dayjs(selectedCalendar.value?.D_START_TIME);
    endTime.value = dayjs(selectedCalendar.value?.D_END_TIME);
    refreshCalendarEvents();
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents,
});

const startTimeChange = async (value: Dayjs | null | string) => {
  if (value && dayjs.isDayjs(value)) {
    await updateFields('D_START_TIME', value.format('YYYY-MM-DD HH:mm:ss'));
  }
};
const endTimeChange = async (value: Dayjs | null | string) => {
  if (value && dayjs.isDayjs(value)) {
    await updateFields('D_END_TIME', value.format('YYYY-MM-DD HH:mm:ss'));
  }
};

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddRpsCalendar,
  destroyOnClose: true,
});

const [AdditionalHoursModal, additionalHoursModalApi] = useVbenModal({
  connectedComponent: AddAdditionalHours,
  destroyOnClose: true,
});

function onCreate() {
  formModalApi.setData(null).open();
}

async function onDelete() {
  // const checkOirgrec: string = gridApi.grid?.getRadioRecord()?.Value as string;
  const checkOirgrec = currentCanalendar.value?.Value;
  if (!checkOirgrec) {
    message.warning('请至少选择一条数据！');
    return;
  }
  await confirm('是否确认删除选中的数据？', '删除确认');
  await $deleteCalendarApi(checkOirgrec);
  message.success('删除成功！');
  onRefresh();
}

function confirm(content: string, title: string) {
  return new Promise((reslove, reject) => {
    Modal.confirm({
      content,
      onCancel() {
        reject(new Error('已取消'));
      },
      onOk() {
        reslove(true);
      },
      title,
    });
  });
}

function onRefresh() {
  gridApi.query();
}

function additionalHours(type: string) {
  if (!currentCanalendar.value) {
    message.error($t('basic-static-tables.rpswaCalendar.selectCalendar'));
    return false;
  }
  additionalHoursModalApi
    .setData({
      calendarId: currentCanalendar.value.Value,
      type,
    })
    .open();
}
const currentMode = ref<CalendarMode>('month');
const calendarRange = ref<{ end?: Dayjs; start?: Dayjs }>({});
const calendarEvents = ref<RpswaCalendarApi.RpswaCalendarEvent[]>([]); // 新增：存储事件数据
const onlChange = async (value: Dayjs | string) => {
  const current = dayjs(value);
  let end: Dayjs, start: Dayjs;
  if (currentMode.value === 'month') {
    const firstDayOfMonth = current.startOf('month');
    const lastDayOfMonth = current.endOf('month');
    start = firstDayOfMonth;
    end = lastDayOfMonth;
  } else {
    start = current.startOf('year');
    end = current.endOf('year');
  }
  if (
    calendarRange.value.start?.format('YYYY-MM-DD') !==
      start.format('YYYY-MM-DD') ||
    calendarRange.value.end?.format('YYYY-MM-DD') !== end.format('YYYY-MM-DD')
  ) {
    refreshCalendarEvents();
  }
};

const refreshCalendarEvents = async () => {
  const current = currentValue.value;
  let end: Dayjs, start: Dayjs;
  if (currentMode.value === 'month') {
    // 当前月第一天
    const firstDayOfMonth = current.startOf('month');
    // 当前月最后一天
    const lastDayOfMonth = current.endOf('month');
    start = firstDayOfMonth;
    end = lastDayOfMonth;
  } else {
    // 年视图，通常显示12个月
    start = current.startOf('year');
    end = current.endOf('year');
  }
  calendarRange.value = { start, end };
  if (currentCanalendar.value) {
    const events = await $getCalendarEventsApi({
      calendarId: currentCanalendar.value.Value,
      start,
      end,
    });
    calendarEvents.value = events;
  } else {
    calendarEvents.value = [];
  }
};

const onPanelChange = (_: Dayjs | string, mode: CalendarMode) => {
  currentMode.value = mode;
};
</script>

<template>
  <FormModal @success="onRefresh" />
  <AdditionalHoursModal />
  <Row :gutter="5">
    <!-- 左侧日历列表 -->
    <Col span="6">
      <Page auto-content-height>
        <Grid>
          <template #toolbar-actions>
            <Space :size="[4, 0]" wrap>
              <Button type="primary" @click="onCreate">
                {{ $t('ui.actionTitle.create') }}
              </Button>
              <Button type="primary" danger @click="onDelete">
                {{ $t('ui.actionTitle.delete') }}
              </Button>
            </Space>
          </template>
        </Grid>
      </Page>
    </Col>
    <!-- 右侧日历详情 -->
    <Col span="18">
      <Page auto-content-height>
        <Space direction="vertical" size="large">
          <Card title="工作时间">
            <Space direction="horizontal" size="middle" style="width: 100%">
              <div class="mt-2 flex flex-col gap-2">
                <VbenCheckButtonGroup
                  v-model="checkValue"
                  multiple
                  :options="options"
                  v-bind="compProps"
                />
                <Space direction="horizontal" size="middle" style="width: 100%">
                  <FormItem label="开始时间">
                    <TimePicker
                      format="HH:mm"
                      :allow-clear="false"
                      v-model:value="startTime"
                      @change="startTimeChange"
                    />
                  </FormItem>
                  <FormItem label="结束时间">
                    <TimePicker
                      format="HH:mm"
                      :allow-clear="false"
                      v-model:value="endTime"
                      @change="endTimeChange"
                    />
                  </FormItem>
                  <FormItem>
                    <Button type="primary" @click="additionalHours('WORK')">
                      额外的工作时间
                    </Button>
                  </FormItem>
                  <FormItem>
                    <Button type="primary" @click="additionalHours('NOWORK')">
                      额外的非工作时间
                    </Button>
                  </FormItem>
                </Space>
              </div>
            </Space>
          </Card>
          <Card title="日历详情">
            <Calendar
              v-model:value="currentValue"
              @change="onlChange"
              @panel-change="onPanelChange"
            >
              <template #dateCellRender="{ current }">
                <ul class="events">
                  <li v-for="item in getListData(current)" :key="item.content">
                    <Badge :status="item.type" :text="item.content" />
                  </li>
                </ul>
              </template>
              <template #monthCellRender="{ current }">
                <div v-if="getMonthData(current)" class="notes-month">
                  <section>{{ getMonthData(current) }}</section>
                  <span>Backlog number</span>
                </div>
              </template>
            </Calendar>
          </Card>
        </Space>
      </Page>
    </Col>
  </Row>
</template>
