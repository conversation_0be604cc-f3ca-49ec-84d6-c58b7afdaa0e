import type { Dayjs } from 'dayjs';

import { callServer, getDataSet, getDataSetNoPage } from '#/api/core/witlab';

export namespace RpswaCalendarApi {
  export interface RpswaCalendarTree {
    [key: string]: any;
    Value: string;
    Text: string;
    Name: string;
    Parent: number;
    AUTHOR: string;
  }

  export interface RpswaCalendar {
    [key: string]: any;
    ORIGREC: number;
    D_END_TIME: Date;
    D_START_TIME: Date;
    FRI: string;
    MON: string;
    SAT: string;
    SUN: string;
    THU: string;
    TUE: string;
    WED: string;
    PARENT_ORIGREC: number | string;
    CALENDAR_NAME: string;
  }

  export interface RpswaCalendarHours {
    [key: string]: any;
    ORIGREC: number;
    WEEKDAY: string;
    D_START_TIME: Date;
    D_END_TIME: Date;
    DESCRIPTION: string;
    CALENDAR_ORIGREC: number;
    TYPE: string;
  }

  export interface RpswaCalendarEvent {
    [key: string]: any;
    ORIGREC: number;
    DETAILS: string;
    END_DATETIME: Dayjs;
    START_DATETIME: Dayjs;
    SHOW_AS: string;
    LOCATION: string;
    SUBJECT: string;
  }
}

/**
 * 获取日历树
 */
async function $getCalendarTreeApi() {
  return getDataSet('RpsCalendar.getCalendarTree', []);
}

async function $getCalendarSelectTreeApi() {
  const data = await $getCalendarTreeApi();
  return data.items;
}

/**
 * 获取工作周
 * @param origrec - 原始记录
 * @returns RpswaCalendarWorkWeek
 * @description 获取工作周
 */
async function $getCalendarWorkWeekApi(origrec: number | string) {
  return getDataSet('RpsCalendar.getCalendarWorkWeek', [origrec]);
}

/**
 * 更新日历
 * @param fields - 字段数组
 * @returns boolean
 * @description 更新日历
 */
async function $updateCalendarApi(fields: string[]) {
  return callServer('RpsCalendar.updateField', fields);
}

async function $addCalendarApi(
  selection: string,
  data: Omit<RpswaCalendarApi.RpswaCalendar, 'ORIGREC'>,
) {
  return callServer('RpsCalendar.createCalendar', [
    selection,
    data.PARENT_ORIGREC,
    data.CALENDAR_NAME,
  ]);
}

/**
 * 删除日历
 * @param origrec - 编号
 * @returns boolean
 */
async function $deleteCalendarApi(origrec: string) {
  return callServer('RpsCalendar.deleteCalendar', [origrec]);
}

async function $getAdditionalHoursApi(origrec: number | string, type: string) {
  return getDataSet('RpsCalendar.getAdditionalHours', [origrec, type]);
}

async function $addAdditionalHoursApi(
  data: Omit<RpswaCalendarApi.RpswaCalendarHours, 'ORIGREC'>,
) {
  return callServer('RpsCalendar.addAdditionalHours', [
    data.CALENDAR_ORIGREC,
    data.TYPE,
    data.WEEKDAY,
    data.DESCRIPTION,
    data.D_START_TIME,
    data.D_END_TIME,
  ]);
}

// RpsCalendar.deleteAdditionalHours
async function $deleteAdditionalHoursApi(origrec: number[] | string[]) {
  return callServer('RpsCalendar.deleteAdditionalHours', [origrec]);
}

async function $getCalendarEventsApi(data: {
  calendarId: number | string;
  end: Dayjs | string;
  start: Dayjs | string;
}) {
  return await getDataSetNoPage('RpsCalendar.getEvents', [
    data.calendarId,
    data.start,
    data.end,
  ]);
}

export {
  $addAdditionalHoursApi,
  $addCalendarApi,
  $deleteAdditionalHoursApi,
  $deleteCalendarApi,
  $getAdditionalHoursApi,
  $getCalendarEventsApi,
  $getCalendarSelectTreeApi,
  $getCalendarTreeApi,
  $getCalendarWorkWeekApi,
  $updateCalendarApi,
};
