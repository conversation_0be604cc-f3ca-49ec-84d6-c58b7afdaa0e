import { defineStore } from 'pinia';

export const useEquipmentStore = defineStore('equipment', {
  state: () => ({
    currentRow: null,
  }),
  actions: {
    setCurrentRow(row: any) {
      this.currentRow = row;
    },
  },

  getters: {
    getCurrentRow: (state) => state.currentRow,
  },
});
export const useMainFormStore = defineStore('mainForm', {
  state: () => ({
    currentRow: null,
    eventRow: null,
  }),
  actions: {
    setCurrentRow(row: any) {
      this.currentRow = row;
    },
    setEventRow(row: any) {
      this.eventRow = row;
    },
  },

  getters: {
    getCurrentRow: (state) => state.currentRow,
    getEventRow: (state) => state.eventRow,
  },
});
