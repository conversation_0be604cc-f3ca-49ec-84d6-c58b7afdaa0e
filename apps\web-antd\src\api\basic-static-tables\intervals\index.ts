import { callServer, getDataSet, getDataSetNoPage } from '#/api/core';

export namespace XIntervalsApi {
  export interface XInterval {
    [key: string]: any;
    ORIGREC: number;
    INTERVAL: string;
    DEPT: string;
    PULLWINDOW: number;
    UNIT: string;
    PULLWINDOW_UNIT: string;
    PULLWINDOW_LONGTERM: number;
    PULLWINDOW_HURRY: number;
    SORTER: number;
  }

  export interface DeptTree {
    DEPT: string;
    SORTER: number;
    /**
     * antd组件必须要这个属性 实际是没有这个属性的
     */
    key: string;
    parentId: number;
    children?: DeptTree[];
  }

  // XINTERVALTAT
  export interface XIntervalTat {
    [key: string]: any;
    ORIGREC: number;
    DEPT: string;
    INTERVAL: string;
    UNIT: string;
    TATBEFORE: number;
    TATAFTER: number;
    TATBEFORE_UNIT: string;
    TATAFTER_UNIT: string;
    SORTER1: number;
    DEFAULT_TAT: string;
  }
}

const $delXIntervalsApi = async ({ origrecs }: { origrecs: number[] }) => {
  return await callServer('INTERVALS.DEL_INTERVAL', [origrecs]);
};
const $getXIntervalsApi = async ({ deptId }: { deptId: string }) => {
  return await getDataSet('INTERVALS.DS_INTERVALS', [deptId]);
};

const $addXIntervalsApi = async (
  data: Omit<XIntervalsApi.XInterval, 'ORIGREC'>,
) => {
  return await callServer('INTERVALS.ADD_INTERVAL', [
    data.INTERVAL,
    data.PULLWINDOW,
    data.UNIT,
    data.DEPT,
    data.PULLWINDOW_UNIT,
    data.eventCode,
    data.comment,
  ]);
};

const $getSitesApi = async () => {
  return await getDataSetNoPage('INTERVALS.getSitesAndDefault', []);
};

const $getIntervalTatApi = async ({ deptId }: { deptId: string }) => {
  return await getDataSet('INTERVALS.getIntervalTat', [deptId]);
};

const $addSampleDueDateCalcApi = async (
  data: Omit<XIntervalsApi.XIntervalTat, 'ORIGREC'>,
) => {
  return await callServer('INTERVALS.addSampleDueDateCalc', [
    data.DEPT,
    data.INTERVAL,
    data.UNIT,
    data.TATBEFORE,
    data.TATBEFORE_UNIT,
    data.TATAFTER,
    data.TATAFTER_UNIT,
    data.DEFAULT_TAT,
    data.eventCode,
    data.comment,
  ]);
};

const $delSampleDueDateCalcApi = async ({
  origrecs,
}: {
  origrecs: number[];
}) => {
  return await callServer('INTERVALS.delSampleDueDateCalc', [origrecs]);
};

const $getUnitsApi = async () => {
  return await getDataSetNoPage('INTERVALS.getUnits', []);
};

const $getSampleCalcIntervalsApi = async ({ deptId }: { deptId: string }) => {
  return await getDataSetNoPage('INTERVALS.getSampleCalcIntervals', [deptId]);
};

// [nOrigrec, sSite, sInterval, sUnit, sOrigInterval, sOrigUnit
const $updateSampleCalcIntervalApi = async ({
  nOrigrec,
  sSite,
  sInterval,
  sUnit,
  sOrigInterval,
  sOrigUnit,
}: {
  nOrigrec: number;
  sInterval: string;
  sOrigInterval: string;
  sOrigUnit: string;
  sSite: string;
  sUnit: string;
}) => {
  return await callServer('INTERVALS.updateSampleCalcInterval', [
    nOrigrec,
    sSite,
    sInterval,
    sUnit,
    sOrigInterval,
    sOrigUnit,
  ]);
};

const $getSitesAndDefault_Ssl = async (type: string) => {
  return await getDataSetNoPage('INTERVALS.getSitesAndDefault', [type]);
};

const $copyFromSiteApi = async ({
  fromDept,
  toDept,
}: {
  fromDept: string;
  toDept: string;
}) => {
  return await callServer('INTERVALS.copyFromSite', [fromDept, toDept]);
};

export {
  $addSampleDueDateCalcApi,
  $addXIntervalsApi,
  $copyFromSiteApi,
  $delSampleDueDateCalcApi,
  $delXIntervalsApi,
  $getIntervalTatApi,
  $getSampleCalcIntervalsApi,
  $getSitesAndDefault_Ssl,
  $getSitesApi,
  $getUnitsApi,
  $getXIntervalsApi,
  $updateSampleCalcIntervalApi,
};
