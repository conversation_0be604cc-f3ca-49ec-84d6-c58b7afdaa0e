import { backendRequestClient } from '#/api/request';

import { callServer, getDataSetNoPage } from '../core';

export namespace SystemDeptApi {
  export interface SystemDept {
    [key: string]: any;
    children?: SystemDept[];
    id: string;
    name: string;
    code: string;
    pid: string;
    remark?: string;
    status: 0 | 1;
  }
}

/**
 * 获取部门列表数据
 */
async function getDeptList() {
  return backendRequestClient.get<Array<SystemDeptApi.SystemDept>>(
    '/platform/depts',
  );
}

/**
 * 创建部门
 * @param data 部门数据
 */
async function createDept(
  data: Omit<SystemDeptApi.SystemDept, 'children' | 'id'>,
) {
  return backendRequestClient.post('/platform/depts', data);
}

/**
 * 更新部门
 *
 * @param id 部门 ID
 * @param data 部门数据
 */
async function updateDept(
  id: string,
  data: Omit<SystemDeptApi.SystemDept, 'children' | 'id'>,
) {
  return backendRequestClient.put(`/platform/depts/${id}`, data);
}

/**
 * 删除部门
 * @param id 部门 ID
 */
async function deleteDept(id: string) {
  return backendRequestClient.delete(`/platform/depts/${id}`);
}

async function getServiceGroupListByDept<T>(deptCode: string) {
  return getDataSetNoPage<T>('LABS.DS_TEAMS', [deptCode]);
}

async function getSelectedServiceGroupListByDept<T>(deptCode: string) {
  return getDataSetNoPage<T>('LABS.DS_SELECT_TEAMS', [deptCode]);
}

async function getAnalystListByServiceGroup<T>(
  deptCode: string,
  serviceGroup: string,
) {
  return getDataSetNoPage<T>('LABS.DS_ANALYSTS', [deptCode, serviceGroup]);
}

async function getSelectedAnalystListByServiceGroup<T>(
  deptCode: string,
  serviceGroup: string,
) {
  return getDataSetNoPage<T>('LABS.DS_SELECT_ANALYSTS', [
    deptCode,
    serviceGroup,
  ]);
}

async function getPlantListByDept<T>(deptCode: string) {
  return getDataSetNoPage<T>('LABS.DS_PLANTS', [deptCode]);
}

async function getPlantMemberListByPlant<T>(deptCode: string, plant: string) {
  return getDataSetNoPage<T>('LABS.DS_PLANTS_MEMBERS', [deptCode, plant]);
}

async function getSelectedPlantMemberListByPlant<T>(
  deptCode: string,
  plant: string,
) {
  return getDataSetNoPage<T>('LABS.DS_SELECT_PLANTMEMBERS', [deptCode, plant]);
}

async function deletePlant(deptCode: string, plant: string) {
  return callServer('LABS.DEL_PLANT', [deptCode, plant]);
}

async function getSiteAreas(deptCode: string) {
  return getDataSetNoPage<any>('Areas.getSiteAreas', [deptCode]);
}

async function createPlant(data: Record<string, any>) {
  return callServer('LABS.ADD_PLANT', [
    data.DEPT,
    data.PLANT,
    data.DESCRIPTION,
    data.AREA_NAME,
  ]);
}

async function getBuildingListByDept<T>(deptCode: string) {
  return getDataSetNoPage<T>('LABS.dsDepartmentBuildingSearch', [deptCode]);
}

async function getRoomListByBuilding<T>(buildingId: number) {
  return getDataSetNoPage<T>('LABS.dsBuildingRoomSearch', [buildingId]);
}

async function deleteBuilding(buildingId: number) {
  return callServer('LABS.scBuildingDelete', [buildingId]);
}

async function deleteRoom(roomId: number) {
  return callServer('LABS.scRoomDelete', [roomId]);
}

async function createBuilding(data: Record<string, any>) {
  return callServer('LABS.scBuildingAdd', [
    data.DEPT,
    data.BUILDING_CODE,
    data.BUILDING_NAME,
    data.DESCRIPTION,
  ]);
}

async function createRoom(data: Record<string, any>) {
  return callServer('LABS.scRoomAdd', [
    data.BUILDING_ID,
    data.ROOM_CODE,
    data.ROOM_NAME,
    data.DESCRIPTION,
    data.CLASS,
  ]);
}

export {
  createBuilding,
  createDept,
  createPlant,
  createRoom,
  deleteBuilding,
  deleteDept,
  deletePlant,
  deleteRoom,
  getAnalystListByServiceGroup,
  getBuildingListByDept,
  getDeptList,
  getPlantListByDept,
  getPlantMemberListByPlant,
  getRoomListByBuilding,
  getSelectedAnalystListByServiceGroup,
  getSelectedPlantMemberListByPlant,
  getSelectedServiceGroupListByDept,
  getServiceGroupListByDept,
  getSiteAreas,
  updateDept,
};
