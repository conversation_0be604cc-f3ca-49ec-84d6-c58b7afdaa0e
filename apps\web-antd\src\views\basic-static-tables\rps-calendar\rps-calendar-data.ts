import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { RpswaCalendarApi } from '#/api/basic-static-tables/rps-calendar';

import dayjs from 'dayjs';

import { z } from '#/adapter/form';
import { $getLookupValuesSimple } from '#/api/basic-static-tables/generic-meta-data/lookup';
import { $getCalendarSelectTreeApi } from '#/api/basic-static-tables/rps-calendar';
import { $t } from '#/locales';

export function useColumns(): VxeTableGridOptions<RpswaCalendarApi.RpswaCalendarTree>['columns'] {
  return [
    {
      align: 'center',
      field: 'Value',
      title: $t('commons.value'),
      visible: false,
    },
    {
      align: 'center',
      field: 'Text',
      title: $t('basic-static-tables.rpswaCalendar.calendarName'),
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      treeNode: true,
    },
    {
      align: 'center',
      field: 'Parent',
      title: $t('basic-static-tables.rpswaCalendar.parent'),
      visible: false,
    },
    {
      align: 'center',
      field: 'AUTHOR',
      title: $t('commons.creator'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}

export function useAddtionalColumns(
  selectOptions: { label: string; value: string }[] = [],
): VxeTableGridOptions<RpswaCalendarApi.RpswaCalendarHours>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 50 },
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('Commons.OrigRec'),
      visible: false,
    },
    {
      align: 'center',
      field: 'WEEKDAY',
      title: $t('basic-static-tables.rpswaCalendar.weekday'),
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return (
          selectOptions.find((cur) => cur.value === cellValue)?.label ||
          cellValue
        );
        // return weekdaysMap[cellValue] || cellValue;
      },
    },
    {
      align: 'center',
      field: 'D_START_TIME',
      title: $t('basic-static-tables.rpswaCalendar.startTime'),
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        const dispValue = dayjs(cellValue).format('HH:mm');
        return dispValue;
      },
    },
    {
      align: 'center',
      field: 'D_END_TIME',
      width: 200,
      title: $t('basic-static-tables.rpswaCalendar.endTime'),
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        const dispValue = dayjs(cellValue).format('HH:mm');
        return dispValue;
      },
    },
    {
      align: 'center',
      field: 'DESCRIPTION',
      title: $t('commons.description'),
      width: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}

export function useSchema(): VbenFormSchema[] {
  return [
    {
      component: 'RadioGroup',
      fieldName: 'CreateOption',
      label: $t('basic-static-tables.rpswaCalendar.calendarCreateOption'),
      componentProps: {
        options: [
          {
            label: $t('ui.actionTitle.create'),
            value: 'NEW',
          },
          {
            label: $t('commons.selector'),
            value: 'Select',
          },
        ],
      },
      defaultValue: 'NEW',
    },
    {
      component: 'ApiTreeSelect',
      componentProps: {
        allowClear: true,
        api: $getCalendarSelectTreeApi,
        class: 'w-full',
        labelField: 'Text',
        valueField: 'Value',
        childrenField: 'children',
      },
      fieldName: 'PARENT_ORIGREC',
      label: $t('basic-static-tables.rpswaCalendar.parentCalendar'),
      dependencies: {
        show(values) {
          return values.CreateOption === 'Select';
        },
        triggerFields: ['CreateOption'],
      },
    },
    {
      component: 'Input',
      fieldName: 'CALENDAR_NAME',
      label: $t('basic-static-tables.rpswaCalendar.calendarName'),
      rules: z
        .string()
        .min(
          1,
          $t('ui.formRules.minLength', [
            $t('basic-static-tables.rpswaCalendar.calendarName'),
            1,
          ]),
        )
        .max(
          20,
          $t('ui.formRules.maxLength', [
            $t('basic-static-tables.rpswaCalendar.calendarName'),
            20,
          ]),
        ),
    },
  ];
}

export function useAdditionalHoursSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: { TEXT: string; VALUE: string }[]) => {
          return data.map((item: any) => ({
            label: item.TEXT,
            value: item.VALUE,
          }));
        },
        api: $getLookupValuesSimple,
        params: 'Weekdays',
      },
      fieldName: 'WEEKDAY',
      label: $t('basic-static-tables.rpswaCalendar.weekday'),
      rules: 'required',
    },
    {
      component: 'TimePicker',
      componentProps: {
        format: 'HH:mm',
      },
      fieldName: 'D_START_TIME',
      label: $t('basic-static-tables.rpswaCalendar.startTime'),
      rules: 'required',
    },
    {
      component: 'TimePicker',
      componentProps: {
        format: 'HH:mm',
      },
      fieldName: 'D_END_TIME',
      label: $t('basic-static-tables.rpswaCalendar.endTime'),
      rules: 'required',
    },
    {
      component: 'Textarea',
      fieldName: 'DESCRIPTION',
      label: $t('commons.description'),
      rules: z.string().max(500).optional(),
    },
  ];
}
