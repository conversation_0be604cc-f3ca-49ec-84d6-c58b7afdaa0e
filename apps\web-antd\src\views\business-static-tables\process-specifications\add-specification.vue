<script lang="ts" setup>
import type { ComponentPublicInstance } from 'vue';

import type { ProcessSpecificationsApi } from '#/api/business-static-tables/process-specifications';

import { computed, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { message, Step, Steps } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  addSpecification,
  copySpecification,
  getGrouping,
  getSpecCategory,
  IsSpecificationNameExists,
} from '#/api/business-static-tables/process-specifications';
import { $t } from '#/locales';

import LimitsGrid from './limits.vue';

const emit = defineEmits(['success']);
const currentTab = ref(0);
const specNo = ref(0);
const status = ref<string>('Draft'); // 初始状态为 Draft
const mode = ref<string>('ADD');
const fromSpecNo = ref(0);

async function onFirstSubmit(values: Record<string, any>) {
  const specifications = values as ProcessSpecificationsApi.Specifications;

  // 检查规格名称是否已存在
  const exists = await IsSpecificationNameExists(specifications.SPECIFICATION);
  if (exists) {
    message.warn(
      $t('business-static-tables.process-specifications.specification-exists'),
    );
    return;
  }

  if (mode.value === 'COPY') {
    specifications.SPECNO = fromSpecNo.value;
    await copySpecification(specifications);
  } else {
    const sSpecNo = await addSpecification(specifications);
    // const addTestCode = 999;
    if (sSpecNo[0]) {
      specNo.value = sSpecNo[2] as number;
      // console.log(sSpecNo[2]);
      currentTab.value = 1;
      modalApi.setState({ confirmDisabled: false });
    } else {
      message.error(
        $t('business-static-tables.process-specifications.add-fail'),
      );
    }
  }
}

const [SpecificationForm] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  handleSubmit: onFirstSubmit,
  layout: 'horizontal',
  resetButtonOptions: {
    show: false,
  },
  schema: [
    {
      component: 'Input',
      fieldName: 'SPECIFICATION',
      label: $t('business-static-tables.process-specifications.specification'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'SPECIFICATION_ENG',
      label: $t(
        'business-static-tables.process-specifications.specification_eng',
      ),
    },
    {
      component: 'Input',
      fieldName: 'INSPECTION_STANDARD',
      label: $t(
        'business-static-tables.process-specifications.inspection_standard',
      ),
    },
    {
      component: 'Input',
      fieldName: 'LIMITFILE',
      label: $t('business-static-tables.process-specifications.limitfile'),
    },
    {
      component: 'Input',
      fieldName: 'MATCODE',
      label: $t('business-static-tables.process-specifications.matcode'),
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'ApiSelect',
      // 对应组件的参数
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: async () => {
          const res = await getSpecCategory();
          return res.items.map(
            (item: { SPECCATEGORY: string; Text: string }) => ({
              label: item.Text,
              value: item.SPECCATEGORY,
            }),
          );
        },
        // autoSelect: 'first',
        immediate: true,
      },
      // 字段名
      fieldName: 'SPECCATEGORY',
      // 界面显示的label
      label: $t('business-static-tables.process-specifications.speccategory'),
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'ApiSelect',
      // 对应组件的参数
      componentProps: {
        allowClear: true,
        filterOption: true,
        showSearch: true,
        api: async () => {
          const res = await getGrouping();
          return res.items.map((item: { TEXT: string; VALUE: string }) => ({
            label: item.TEXT,
            value: item.VALUE,
          }));
        },
        // autoSelect: 'first',
        immediate: true,
      },
      // 字段名
      fieldName: 'GROUPING',
      // 界面显示的label
      label: $t('business-static-tables.process-specifications.grouping'),
    },
    {
      component: 'Textarea',
      fieldName: 'FOOTNOTES',
      label: $t('business-static-tables.process-specifications.footnotes'),
    },
  ],
  submitButtonOptions: {
    content: '下一步',
  },
  wrapperClass: 'grid-cols-1 md:grid-cols-1 lg:grid-cols-1',
});

const getTitle = computed(() => {
  return $t('ui.actionTitle.create', [tabTitleMap[currentTab.value]]);
});
const tabTitleMap = ['质量标准', '限值'];
const limitRef = ref<ComponentPublicInstance<{
  HasLimitData: () => Promise<boolean>;
}> | null>(null);
const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  showConfirmButton: true,
  confirmDisabled: true,
  onConfirm: async () => {
    let hasValue = false;
    if (limitRef.value) {
      hasValue = await limitRef.value.HasLimitData();
    }
    if (!hasValue) {
      message.error('请至少添加一个检测项目');
      return;
    }
    emit('success');
    modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData<Record<string, any>>();
      if (data) {
        mode.value = data.MODE;
        fromSpecNo.value = data.SPECNO;
      }
    }
  },
});
</script>

<template>
  <Modal :title="getTitle" class="w-[1000px]">
    <Page>
      <div class="mx-auto">
        <Steps :current="currentTab" class="steps">
          <Step :title="tabTitleMap[0]" />
          <Step :title="tabTitleMap[1]" />
        </Steps>
        <div class="p-0">
          <SpecificationForm v-show="currentTab === 0" class="p-10" />
          <LimitsGrid
            v-if="currentTab === 1"
            ref="limitRef"
            :spec-no="specNo"
            :status="status"
          />
        </div>
      </div>
    </Page>
  </Modal>
</template>
