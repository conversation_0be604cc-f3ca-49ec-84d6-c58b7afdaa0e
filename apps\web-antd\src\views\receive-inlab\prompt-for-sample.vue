<script lang="ts" setup>
import type { ReceiveInLabApi } from '#/api/receive-inlab/receive-inlab'; // 导入ReceiveInLabApi接口

import { computed, ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, Col, message, Row } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  dsSubLocationGet,
  getLocationList,
} from '#/api/materials-management/location-management';
import {
  convertSampleNoToInventoryId,
  getInventoryId,
  getPromptForSample,
  receiveInLabMulti,
  updReceiveAll,
} from '#/api/receive-inlab/receive-inlab';
import { $t } from '#/locales';
import { createGridOptions } from '#/utils/grid-option';

import {
  usePendingItemsColumns,
  useSelectedItemsColumns,
} from './receive-inlab-data';

// 定义组件事件
const emit = defineEmits(['success']);

const formData = ref();
const ReceiveMethod = ref<string>('SAMPLE');
const sOpeningMode = ref<string>('RECEIVEINCR');
// 树形数据处理
const treeData = ref<any[]>([]);
const isLoading = ref(false);

// 数据状态管理
const originalPendingData = ref<ReceiveInLabApi.ReceiveOrders[]>([]); // 原始左侧数据
const selectedItemData = ref<ReceiveInLabApi.ReceiveOrders[]>([]); // 右侧已选择数据

// 计算过滤后的左侧数据（排除已选择的数据）
const filteredPendingData = computed(() => {
  const selectedValues = new Set(
    selectedItemData.value.map((item) => item.Value),
  );
  return originalPendingData.value.filter(
    (item) => !selectedValues.has(item.Value),
  );
});

// 监听右侧数据变化，自动刷新左侧表格
watch(
  selectedItemData,
  () => {
    refreshPendingGrid();
  },
  { deep: true },
);

const [TopForm, topFormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  // handleSubmit: onSubmit,
  handleValuesChange: async (changedValues: any) => {
    // 获取变化的字段名
    const changedFields = Object.keys(changedValues)[0];
    // 判断是否是 radioGroupValue 字段发生了变化
    if (changedFields === 'ReceiveMethod') {
      const newValue = changedValues[changedFields];
      ReceiveMethod.value = newValue;
      const data = await getPromptForSample(sOpeningMode.value, newValue);
      pendingGridApi.setGridOptions({
        data: data.items,
      });
    }
  },
  layout: 'vertical',
  schema: [
    {
      component: 'RadioGroup',
      rules: 'required',
      componentProps: {
        options: [
          {
            label: '按样品',
            value: 'SAMPLE',
          },
          {
            label: '按库存',
            value: 'INVENTORY',
          },
          {
            label: '稳定性',
            value: 'Stability',
          },
        ],
      },
      fieldName: 'ReceiveMethod',
      label: '请选择接收方式',
    },
    {
      component: 'Input',
      fieldName: 'SCANBARCODE',
      label: $t('receive-inlab.scanbarcode'),
      componentProps: {
        onKeypress: async (value: { key: string }) => {
          if (value.key === 'Enter') {
            const data = await topFormApi.getValues();
            const locationName = data.LOCATION;
            const newValue = data.SCANBARCODE;
            const sInvId = await convertSampleNoToInventoryId(newValue);
            if (!sInvId) return;
            const selectedRows = await getInventoryId(
              sOpeningMode.value,
              'N',
              sInvId.toString(),
              ReceiveMethod.value === 'Stability' ? 'Y' : 'N',
            );
            if (selectedRows && selectedRows.items.length > 0) {
              const newRows = selectedRows.items.filter(
                (row: { Value: number }) => {
                  // 双重检查，确保不会添加重复数据
                  return !selectedItemData.value.some(
                    (item) => item.Value === row.Value,
                  );
                },
              );

              if (newRows.length === 0) {
                message.info('所选数据已添加过,请选择不同的数据！');
                return;
              }

              // 添加新数据到右侧
              selectedItemData.value = [...selectedItemData.value, ...newRows];

              selectedItemData.value = selectedItemData.value.map((item) => {
                if (!item.LOCATION_NAME) {
                  return {
                    ...item,
                    LOCATION_NAME: locationName, // 替换为你想赋的值
                  };
                }
                return item;
              });

              // 刷新右侧表格
              selectedGridApi.setGridOptions({
                data: selectedItemData.value,
              });
            }
          }
        },
      },
    },
    {
      component: 'Input',
      fieldName: 'LOCATION',
      label: $t('receive-inlab.location'),
    },
  ],
  wrapperClass: 'grid-cols-1 md:grid-cols-3 gap-x-8',
  showDefaultActions: false,
});

const [BottomForm, bottomFormApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // handleSubmit: onSubmit,
  layout: 'horizontal',
  schema: [
    {
      component: 'DatePicker',
      fieldName: 'COMPDAT',
      label: $t('receive-inlab.compdat'),
      rules: 'required',
    },
    {
      component: 'ApiTreeSelect',
      fieldName: 'LOCATIONCODE',
      label: $t('receive-inlab.locationcode'),
      rules: 'required',
      componentProps: {
        allowClear: true,
        placeholder: '请选择位置',
        showSearch: true,
        treeDefaultExpandAll: false,
        treeDefaultExpandedKeys: [],
        showIcon: true,
        // API配置
        // api: loadLocationData,
        treeData,
        immediate: true,

        // 字段映射配置 - 使用转换后的标准字段
        labelField: 'label',
        valueField: 'value',
        childrenField: 'children',

        // TreeSelect特有配置
        fieldNames: {
          label: 'label',
          value: 'value',
          children: 'children',
        },

        // 搜索配置 - 使用转换后的标准字段
        treeNodeFilterProp: 'label',
        filterTreeNode: (inputValue: string, treeNode: any) => {
          if (!inputValue) return true;
          const label = treeNode.label || treeNode.title || '';
          return label.toLowerCase().includes(inputValue.toLowerCase());
        },

        // 动态加载配置
        loadData: async (treeNode: any) => {
          // 动态加载子节点
          // console.log('treeNode.value', treeNode.value);
          if (treeNode.children && treeNode.children.length > 0) {
            // console.log('treeNode.children', treeNode.children);
            if (treeNode.children[0].label.includes('expand')) {
              const str = treeNode.value;
              const sId = str.split('*')[0];
              const childrenData = await dsSubLocationGet(sId);
              const result = transformToTreeData(childrenData.items);
              treeNode.children.push(...result);
            } else {
              // 已有子节点，不需要加载
            }
          } else {
            try {
              const str = treeNode.value;
              const sId = str.split('*')[0];
              const childrenData = await dsSubLocationGet(sId);
              const result = transformToTreeData(childrenData.items);
              treeNode.children.push(...result);
              // treeNode.children = children;
            } catch (error) {
              console.error('动态加载子节点失败:', error);
            }
          }
        },

        // 事件处理
        onSelect: (value: string) => {
          const allData = selectedGridApi.grid?.getData();
          if (allData.length === 0) return;
          selectedItemData.value = allData.map((item) => {
            if (!item.LOCATION_NAME) {
              return {
                ...item,
                LOCATION_NAME: value, // 替换为你想赋的值
              };
            }
            return item;
          });

          // 刷新右侧表格
          selectedGridApi.setGridOptions({
            data: selectedItemData.value,
          });
        },

        // 加载状态
        loading: isLoading,
      },
    },
  ],
  wrapperClass: 'grid-cols-1 md:grid-cols-3 gap-x-8',
  showDefaultActions: false,
});

// 数据转换函数：将API返回的扁平数据转换为树形结构
const transformToTreeData = (flatData: any[]) => {
  if (!flatData || flatData.length === 0) {
    console.warn('transformToTreeData: 输入数据为空');
    return [];
  }

  // console.log('transformToTreeData: 原始数据', flatData);

  const map = new Map();
  const roots: any[] = [];

  // 第一遍遍历：创建所有节点
  flatData.forEach((item) => {
    const node = {
      // 确保字段名称正确映射
      label: item.TEXTMEMBER || '未知位置',
      value: item.NAMEMEMBER || item.ORIGREC,
      key: item.NAMEMEMBER || item.ORIGREC,
      title: item.TEXTMEMBER || '未知位置', // TreeSelect 可能需要 title 字段
      children: [],

      // 保留原始数据用于调试
      _original: item,
      _parentId: item.PARENTMEMBER,
    };

    // 使用 NAMEMEMBER 作为 map 的键
    if (node.value) {
      map.set(node.value, node);
    } else {
      console.warn('transformToTreeData: 节点缺少有效的 value', item);
    }
  });

  // 第二遍遍历：建立父子关系
  flatData.forEach((item) => {
    const nodeValue = item.NAMEMEMBER || item.ORIGREC;
    const parentValue = item.PARENTMEMBER;

    const node = map.get(nodeValue);
    if (!node) return;

    // 修复父子关系判断逻辑
    if (parentValue && parentValue !== '' && map.has(parentValue)) {
      // 有父节点，添加到父节点的children中
      const parent = map.get(parentValue);
      if (parent && parent.children) {
        parent.children.push(node);
      }
    } else {
      // 没有父节点或父节点不存在，作为根节点
      roots.push(node);
    }
  });

  // console.log('transformToTreeData: 转换后的树形数据', roots);

  // 验证数据结构
  const validateTreeData = (nodes: any[], level = 0) => {
    nodes.forEach((node) => {
      if (node.children && node.children.length > 0) {
        validateTreeData(node.children, level + 1);
      }
    });
  };

  if (roots.length > 0) {
    // console.log('=== 树形数据结构验证 ===');
    validateTreeData(roots);
  }

  return roots;
};

// 刷新左侧表格的方法
const refreshPendingGrid = () => {
  if (pendingGridApi.grid) {
    pendingGridApi.setGridOptions({
      data: filteredPendingData.value,
    });
  }
};

const pendingGridOptions = {
  ...createGridOptions<ReceiveInLabApi.ReceiveOrders>(),
  columns: usePendingItemsColumns(),
  proxyConfig: {
    ajax: {
      query: async () => {
        const result = await getPromptForSample(
          sOpeningMode.value,
          ReceiveMethod.value,
        );
        // 保存原始数据
        originalPendingData.value = result.items || [];
        // 返回过滤后的数据
        return {
          items: filteredPendingData.value,
          total: filteredPendingData.value.length,
        };
      },
    },
  },
};

// 第一个表格配置
const [PendingGrid, pendingGridApi] = useVbenVxeGrid({
  gridOptions: pendingGridOptions,
});

const selectedGridOptions = {
  ...createGridOptions<ReceiveInLabApi.ReceiveOrders>(),
  columns: useSelectedItemsColumns(),
  data: [],
};

// 第二个表格配置
const [SelectedGrid, selectedGridApi] = useVbenVxeGrid({
  gridOptions: selectedGridOptions,
});

// 新增数据到右侧
async function onCreate() {
  const selectedRows =
    pendingGridApi.grid?.getCheckboxRecords() as ReceiveInLabApi.ReceiveOrders[];

  if (selectedRows.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  // 由于左侧已经过滤了重复数据，这里直接添加即可
  const newRows = selectedRows.filter((row) => {
    // 双重检查，确保不会添加重复数据
    return !selectedItemData.value.some((item) => item.Value === row.Value);
  });

  if (newRows.length === 0) {
    message.info('所选数据已添加过,请选择不同的数据！');
    return;
  }

  // 添加新数据到右侧
  selectedItemData.value = [...selectedItemData.value, ...newRows];

  const data = await topFormApi.getValues();
  const locationName = data.LOCATION;
  selectedItemData.value = selectedItemData.value.map((item) => {
    if (!item.LOCATION_NAME) {
      return {
        ...item,
        LOCATION_NAME: locationName, // 替换为你想赋的值
      };
    }
    return item;
  });

  // 刷新右侧表格
  selectedGridApi.setGridOptions({
    data: selectedItemData.value,
  });

  // 清除左侧表格的选择状态
  pendingGridApi.grid?.clearCheckboxRow();

  // message.success(`成功添加 ${newRows.length} 条数据`);
}

// 从右侧删除数据
async function onDelete() {
  const selectedRows =
    selectedGridApi.grid?.getCheckboxRecords() as ReceiveInLabApi.ReceiveOrders[];

  if (!selectedRows || selectedRows.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  // 记录删除的数据数量
  // const deleteCount = selectedRows.length;

  // 过滤掉被选中的行
  selectedItemData.value = selectedItemData.value.filter(
    (item) => !selectedRows.some((row) => row.Value === item.Value),
  );

  // 刷新右侧表格
  selectedGridApi.setGridOptions({
    data: selectedItemData.value,
  });

  // 清除右侧表格的选择状态
  selectedGridApi.grid?.clearCheckboxRow();

  // message.success(`成功移除 ${deleteCount} 条数据`);
}

const aSampleData = ref<string[][]>([]);

// 辅助方法：重置所有数据
const resetAllData = () => {
  originalPendingData.value = [];
  selectedItemData.value = [];
  pendingGridApi.grid?.clearCheckboxRow();
  selectedGridApi.grid?.clearCheckboxRow();
};

// 辅助方法：刷新所有表格
// const refreshAllGrids = () => {
//   // 刷新左侧表格
//   refreshPendingGrid();
//   // 刷新右侧表格
//   selectedGridApi.setGridOptions({
//     data: recipeMaterialData.value,
//   });
// };

// 保存修改
async function saveChanges() {
  try {
    // 1. 获取表格中所有行数据

    await topFormApi.validate();
    await bottomFormApi.validate();

    const allData = selectedGridApi.grid?.getData();

    if (!allData || allData.length === 0) {
      message.warning('没有可接收的数据');
      return;
    }
    aSampleData.value = allData.map((item) => [
      item.ORDNO,
      item.Value,
      item.SAMPLEDBY,
      item.SAMPDATE,
      item.LOCATION_NAME,
      item.FOLDERNO,
    ]);
    const sReceiveMode = ref();
    sReceiveMode.value =
      ReceiveMethod.value === 'SAMPLE' || ReceiveMethod.value === 'Stability'
        ? 'SAMPLE'
        : ReceiveMethod.value;

    const data = await bottomFormApi.getValues();
    const receiveDate = data.COMPDAT;

    const aORDNOs = await receiveInLabMulti(
      sReceiveMode.value,
      aSampleData.value,
      receiveDate,
      sOpeningMode.value,
    );
    if (aORDNOs) {
      await updReceiveAll(aORDNOs);
    }
    // 实现保存逻辑
    message.success('接收成功');
    emit('success');
    // 关闭Modal
    modalApi.close();
  } catch (error) {
    console.warn('接收失败:', error);
    message.error('接收失败');
  }
}

// 异步加载位置数据
const loadLocationData = async (parentValue?: string) => {
  try {
    isLoading.value = true;
    const result = await getLocationList('SITE1');

    if (result && result.items) {
      if (parentValue) {
        // 动态加载子节点
        return transformToTreeData(
          result.items.filter((item: any) => item.NAMEMEMBER === parentValue),
        );
      } else {
        // 初始加载所有数据
        const transformedData = transformToTreeData(result.items);
        treeData.value = transformedData;
        // console.log('transformedData', transformedData);
        return transformedData;
      }
    }
    return [];
  } catch (error) {
    console.error('加载位置数据失败:', error);
    message.error('加载位置数据失败');
    return [];
  } finally {
    isLoading.value = false;
  }
};

// 获取Modal实例
const [Modal, modalApi] = useVbenModal({
  title: '扫码接收',
  onCancel() {
    modalApi.close();
  },
  onConfirm: saveChanges,
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      formData.value = modalApi.getData<Record<string, any>>();
      // await nextTick();

      await loadLocationData();

      // 重置数据状态
      resetAllData();
      // 重新加载左侧数据
      pendingGridApi.query();
    }
  },
});
</script>

<template>
  <Modal class="h-[800px] w-[1350px]">
    <TopForm />
    <Row :gutter="16">
      <Col :span="11.5" class="h-[515px] overflow-hidden">
        <!-- 左侧 Grid -->
        <PendingGrid />
      </Col>
      <!-- 按钮区域 -->
      <Col :span="1" style="text-align: center">
        <div class="button-container">
          <Button type="primary" @click="onCreate">
            {{ $t('receive-inlab.right') }}
          </Button>
          <Button
            type="primary"
            danger
            :style="{ marginTop: '20px' }"
            @click="onDelete"
          >
            {{ $t('receive-inlab.left') }}
          </Button>
        </div>
      </Col>
      <Col :span="11.5" class="h-[515px] overflow-hidden">
        <!-- 右侧Grid -->
        <SelectedGrid />
      </Col>
    </Row>
    <BottomForm />
  </Modal>
</template>

<style scoped>
.button-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}
</style>
