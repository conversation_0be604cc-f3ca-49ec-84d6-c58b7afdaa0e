<script lang="ts" setup>
import type { EquipmentMgApi } from '#/api/equipment/equipment-mg';

import { computed } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import { addOutOfServiceMaintenanceApi } from '#/api/equipment/equipment-mg';
import { useEquipmentStore } from '#/store';

import { stopDeviceModalSchema } from '../equipment-mg-data';

const emit = defineEmits(['success']);
const equipmentStore = useEquipmentStore();
const currentRow: EquipmentMgApi.RowType = computed(
  () => equipmentStore.getCurrentRow,
);
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: stopDeviceModalSchema(),
  showDefaultActions: false,
});
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    console.warn(data);
    try {
      modalApi.lock();

      const params = [currentRow.value.EQID, data.OPENREASON];
      await addOutOfServiceMaintenanceApi(params);
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      // const data = modalApi.getData<SampleTypeApi.SampleType>();
    }
  },
});
</script>
<template>
  <Modal title="已停用">
    <Form class="mx-4" />
  </Modal>
</template>
