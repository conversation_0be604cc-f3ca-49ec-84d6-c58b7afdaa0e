<script lang="ts" setup>
import type { VxeColumnPropTypes } from 'vxe-table';

import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { WordTemplateApi } from '#/api/business-static-tables/word-template';

import { onMounted, reactive } from 'vue'; // 添加了 onMounted 和 reactive 的导入

import { confirm, Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Button, message, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteWordTemplate,
  getFunctionName,
  getWordTemplateList,
} from '#/api/business-static-tables/word-template';
import { getLookupValues } from '#/api/common';

import AddWordTemplateForm from './add-word-template.vue';
import {
  useWordTemplateColumns,
  useWordTemplateFilterSchema,
} from './word-template-data';

function createEditRender(): VxeColumnPropTypes.EditRender {
  return {
    name: 'select',
    options: [],
  };
}

const languageEditRender = reactive(createEditRender());

const functionNameEditRender = reactive(createEditRender());

onMounted(async () => {
  // 绑定语言版本数据
  const languageResult = await getLookupValues('ReportLanguage');
  languageEditRender.options = languageResult.items.map((item) => ({
    label: item.TEXT,
    value: item.VALUE,
  }));

  // 绑定执行脚本数据
  const functionNameResult = await getFunctionName('');
  functionNameEditRender.options = functionNameResult.items.map((item) => ({
    label: item.Text,
    value: item.Value,
  }));
});

const gridOptions: VxeTableGridOptions<WordTemplateApi.WordTemplate> = {
  columns: useWordTemplateColumns(languageEditRender, functionNameEditRender),
  stripe: true,
  keepSource: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getWordTemplateList();
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const formOptions: VbenFormProps = {
  schema: useWordTemplateFilterSchema(),
  submitOnChange: false,
  collapsed: true,
  showCollapseButton: true,
  submitButtonOptions: {
    content: '查询',
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  formOptions,
});

function hasEditStatus(row: WordTemplateApi.WordTemplate) {
  return gridApi.grid?.isEditByRow(row);
}

function editRowEvent(row: WordTemplateApi.WordTemplate) {
  gridApi.grid?.setEditRow(row);
}

async function saveRowEvent() {
  await gridApi.grid?.clearEdit();
  // addQcType(row);
  gridApi.setLoading(true);
  setTimeout(() => {
    gridApi.setLoading(false);
    message.success({
      content: `保存成功！`,
    });
  }, 600);
}

const cancelRowEvent = (row: WordTemplateApi.WordTemplate) => {
  gridApi.grid?.clearEdit().then(() => {
    // 还原行数据
    gridApi.grid.revertData(row);
  });
};

function onRefresh() {
  gridApi.query();
}

// 添加QC类型
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddWordTemplateForm,
});

async function onCreate() {
  formModalApi.setData(null).open();
}

// 删除QC类型
async function onDelete() {
  // 获取选中行
  const aOrigrec: number[] = gridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.ORIGREC);

  if (aOrigrec.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${aOrigrec.length} 条数据吗？`,
      icon: 'warning',
      centered: false,
    });

    await deleteWordTemplate(aOrigrec);

    message.success('删除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}
</script>
<template>
  <Page auto-content-height>
    <FormDrawer @success="onRefresh" />
    <FormModal @success="onRefresh" />
    <Grid>
      <template #toolbar-actions>
        <Space>
          <Button type="primary" @click="onCreate">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button type="primary" danger @click="onDelete">
            {{ $t('ui.actionTitle.delete') }}
          </Button>
        </Space>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(row)">
          <Button type="link" @click="saveRowEvent()">
            {{ $t('business-static-tables.save') }}
          </Button>
          <Button type="link" @click="cancelRowEvent(row)">
            {{ $t('business-static-tables.cancel') }}
          </Button>
        </template>
        <template v-else>
          <Button type="link" @click="editRowEvent(row)">
            {{ $t('business-static-tables.edit') }}
          </Button>
        </template>
      </template>
    </Grid>
  </Page>
</template>
