<script lang="ts" setup>
import type { PreBatchesApi } from '#/api/login-options/interface-notification';

import { ref } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';

import { getPreTests } from '#/api/login-options/interface-notification';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import {
  usePreTestsColumns,
  usePreTestsFilterSchema,
} from './request-notification-data';

const requestId = ref<string>('');

const colums = usePreTestsColumns();
const filterSchema = usePreTestsFilterSchema();
const queryData = async () => {
  return await getPreTests(requestId.value);
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
};

const { Grid } = useLimsGridsConfig<PreBatchesApi.PreTests>(
  colums,
  filterSchema,
  queryData,
  girdOption,
);

const [Drawer, drawerApi] = useVbenDrawer({
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<PreBatchesApi.PreBatches>();
      if (data) {
        requestId.value = data.REQUESTID;
        // onRefresh();
      } else {
        // gridApi?.reload();
      }
    }
  },
  showCancelButton: false,
  showConfirmButton: false,
});
</script>

<template>
  <Drawer class="w-full max-w-[800px]" title="检项">
    <Page auto-content-height>
      <Grid />
    </Page>
  </Drawer>
</template>
