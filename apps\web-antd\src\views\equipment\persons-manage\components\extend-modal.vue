<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import { ref } from 'vue';
import { extendModalSchema } from '../persons-manage-data';
import dayjs from 'dayjs';
import { batchUpdateCertApi } from '#/api/equipment/persons-manage';
import { message } from 'ant-design-vue';
const emit = defineEmits(['success']);
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: extendModalSchema(),
  showDefaultActions: false,
});
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    console.warn(data);

    modalApi.lock();
    try {
      const clickRow = modalApi.getData().clickRow || [];
      const data = await formApi.getValues();

      //电子签名
      const params = [
        type.value,
        clickRow.USRNAM,
        data.authorizationDate,
        data.lostDate,
      ];
      const res = await batchUpdateCertApi(params);
      if (res[0]) {
        emit('success');
      } else {
        message.warning('扩期失败请重新选择');
      }

      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      type.value = modalApi.getData().type;
      title.value=type.value==='METHOD'?'选择方法授权失效日期':'选择设备授权失效日期';
      formApi.setFieldValue('authorizationDate', dayjs());
      formApi.setFieldValue('lostDate', dayjs());
    }
  },
});
const type = ref('METHOD');
const title =ref("选择方法授权失效日期")
</script>
<template>
  <Modal :title="title">
    <Form class="mx-4" />
  </Modal>
</template>
