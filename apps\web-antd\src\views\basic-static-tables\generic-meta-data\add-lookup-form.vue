<script lang="ts" setup>
import type { LookupTableApi } from '#/api/basic-static-tables/generic-meta-data';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { addLookupApi } from '#/api/basic-static-tables/generic-meta-data';
import { $t } from '#/locales';

import { useSchema } from './lookup-data';

const emit = defineEmits(['success']);

const formData = ref<LookupTableApi.MetaDataLookups>();
const getTitle = computed(() => {
  return $t('ui.actionTitle.create', [$t('basicStatic.lookups.title')]);
});

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: useSchema(),
  showDefaultActions: false,
});

function resetForm() {
  formApi.resetForm();
  formApi.setValues(formData.value || {});
}

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    modalApi.lock();
    try {
      // const validData: Omit<LookupTableApi.MetaDataLookups, 'origrec'> = {
      //   lookupName: data.lookupName,
      //   category: data.category,
      //   description: data.description,
      //   ...data, // Include other properties if necessary
      // };
      await addLookupApi(data);
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<LookupTableApi.MetaDataLookups>();
      if (data) {
        formData.value = data;
        formApi.setValues(formData.value);
      }
    }
  },
});
</script>

<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
    <template #prepend-footer>
      <div class="flex-auto">
        <Button type="primary" danger @click="resetForm">
          {{ $t('common.reset') }}
        </Button>
      </div>
    </template>
  </Modal>
</template>
