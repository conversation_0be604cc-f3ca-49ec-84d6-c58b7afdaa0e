<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';

import { preparationModalSchema } from '../solution preparation-data';

import {
  Modal as AModal,
  Input,
  Button,
  Table,
  Select,
  SelectOption,
} from 'ant-design-vue';
import {
  getUsersApi,
  addTestSolutionApi,
  getMeasureTypeApi,
  getMeasureUnitApi,
  getSolutionNameApi,
  addStandSolutionApi,
  getSoultionNameApi,
  addTitarantSelfSolutionApi,
} from '#/api/dilution-management/solution-preparation';
import { ref } from 'vue';
import { Ellipsis } from 'lucide-vue-next';
import type { TableProps } from 'ant-design-vue';
import dayjs from 'dayjs';

const emit = defineEmits(['success']);
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: preparationModalSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    console.warn(data);
    data.dcbCaliBrationTime = '';
    data.dcbAfterTime = '';
    data.scbAfterMan = '';
    data.scbAfterManCode = '';

    modalApi.lock();
    try {
      if (type.value === 'test') {
        const params = [
          data.solutionName,
          data.solutionType,
          data.storageCondition,
          data.storageLocation,
          data.concentration,
          data.position,
          data.isMixedLabel,
          data.solvent,
          data.F_value,
          data.concentrationUnit,
        ];
        await addTestSolutionApi(params);
      }
      if (type.value === 'standard') {
        const params = [
          data.solutionName.trim(),
          data.solutionType.trim(),
          data.storageCondition.trim(),
          data.storageLocation.trim(),
          data.concentration.trim(),
          data.position.trim(),
          data.isMixedLabel.trim(),
          data.solvent.trim(),
          data.sTextF.trim(),
          data.concentrationUnit.trim(),
          -1, //statlims意义不明的参数
        ];
        await addStandSolutionApi(params);
      }
      if (type.value === 'titration') {
        const courseNameRef = formApi.getFieldComponentRef('recalibrator');
        const options = courseNameRef.getOptions();
        const recalibratorName = options.find(
          (item: any) => item.value === data.recalibrator,
        ).label;
        const params = [
          data.solutionName,
          data.solutionType,
          data.storageCondition,
          data.storageLocation,
          data.concentration,
          data.position,
          data.isMixedLabel,
          data.solvent,
          data.F_value,
          data.concentrationUnit,
          data.calibrationDate,
          data.recalibrationDate,
          recalibratorName,
          data.recalibrator,
          -1, //statlims意义不明的参数
        ];
        await addTitarantSelfSolutionApi(params);
      }
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      type.value = modalApi.getData().type;
      if (type.value === 'standard') {
        formApi.updateSchema([
          { fieldName: 'isMixedLabel', formItemClass: '' },
        ]);
      }
      if (type.value === 'titration') {
        formApi.updateSchema([
          { fieldName: 'F_value', formItemClass: '' },
          { fieldName: 'calibrationDate', formItemClass: '' },
          { fieldName: 'recalibrator', formItemClass: '' },
          { fieldName: 'recalibrationDate', formItemClass: '' },
        ]);
        await formApi.setFieldValue('calibrationDate', dayjs());
        await formApi.setFieldValue('recalibrationDate', dayjs());
      }
      const res = await getUsersApi();
      const options = res.map((item: any) => {
        return {
          label: item.Text,
          value: item.Value,
        };
      });
      formApi.updateSchema([
        {
          fieldName: 'approver',
          componentProps: {
            options: options,
          },
        },
      ]);
      const typeObj = {
        test: 'tbTestSolution',
        standard: 'tbStandardSolution',
        titration: 'titrant',
      };
      const typeRes = await getSoultionNameApi({
        type: typeObj[type.value as 'test' | 'standard' | 'titration'],
      });
      typeOptions.value = typeRes.map((item) => {
        return {
          value: item.Value,
          label: item.Text,
        };
      });
    }
  },
});
const type = ref('test');

interface Item {
  value: string;
  label: string;
}
const viewSolutionName = async () => {
  const res = await getSolutionNameApi();
  selectItems.value = res.map((item) => {
    return {
      value: item.Value,
      label: item.Text,
    };
  });
  open.value = true;
};
const open = ref(false);
const handleOk = () => {
  open.value = false;
  formApi.setFieldValue('solutionName', selectedItem.value.label);
};
const handleUnitOk = () => {
  searchOpen.value = false;
  formApi.setFieldValue('concentrationUnit', clickRowKeys.value[0]?.UNIT_NAME);
};
const clickItem = (item: Item) => {
  selectedItem.value = item;
};
const selectedItem = ref<Item>({
  value: '',
  label: '',
});
const selectItems = ref<Item[]>([]);
const searchOpen = ref(false);
const search = async () => {
  const typeRes = await getMeasureTypeApi();
  typeData.value = typeRes;
  const unitRes = await getMeasureUnitApi([typeRes[0].MEASURE_TYPE]);
  unitData.value = unitRes;
  searchOpen.value = true;
};
const typeColumns = ref([
  {
    title: '测量类型',
    dataIndex: 'MEASURE_TYPE',
  },
]);
const typeData = ref<any[]>([]);
const unitColumns = ref([
  {
    title: '单位代码',
    dataIndex: 'UNIT_NAME',
  },
  {
    title: '技术代码',
    dataIndex: 'UNIT_CODE',
  },
]);
const unitData = ref<any[]>([]);
const rowClick = (record: any) => {
  return {
    onClick: async () => {
      const unitRes = await getMeasureUnitApi([record.MEASURE_TYPE]);
      unitData.value = unitRes;
    },
  };
};

const clickRowKeys = ref<string[]>([]);

const rowSelection: TableProps['rowSelection'] = {
  onChange: (selectedRowKeys: any, selectedRows: any) => {
    console.warn(selectedRowKeys);
    clickRowKeys.value = selectedRows;
  },
  hideSelectAll: true,
  type: 'radio',
};
const mixedOptions = ref([
  {
    value: 'Y',
    label: '是',
  },
  {
    value: 'N',
    label: '否',
  },
]);
const isDisabled = ref(false);

import type { Ref } from 'vue';
const typeOptions: Ref<Array<{ value: any; label: any }>> = ref([]);
const viewChange = (value: any) => {
  if (value === 'N') {
    isDisabled.value = false;
  } else {
    formApi.setFieldValue('concentrationUnit', '');
    formApi.setFieldValue('concentration', '');
    isDisabled.value = true;
  }
};
</script>
<template>
  <AModal
    v-model:open="searchOpen"
    title="选择"
    @ok="handleUnitOk"
    width="30%"
    class="h-1/5 w-4/5"
  >
    <div class="flex h-[500px] justify-around overflow-auto">
      <div class="h-full w-2/5">
        <Table
          :sticky="true"
          :scroll="{ y: 300 }"
          :pagination="false"
          :columns="typeColumns"
          :data-source="typeData"
          bordered
          :customRow="rowClick"
          :rowKey="
            (record) => {
              return record.MEASURE_TYPE;
            }
          "
        >
          <template #title>测量类型</template>
        </Table>
      </div>
      <div class="h-full w-2/5">
        <Table
          :sticky="true"
          :scroll="{ y: 300 }"
          :pagination="false"
          :columns="unitColumns"
          :data-source="unitData"
          bordered
          :row-selection="rowSelection"
          :rowKey="
            (record) => {
              return record.UNIT_NAME;
            }
          "
        >
          <template #title>单位</template>
        </Table>
      </div>
    </div>
  </AModal>
  <AModal v-model:open="open" title="选择" @ok="handleOk" class="h-1/5">
    <div class="h-[500px] overflow-auto">
      <ul class="w-full overflow-auto">
        <li
          v-for="item in selectItems"
          :key="item.label"
          class="list-item hover:bg-blue-200"
          :style="{
            backgroundColor:
              selectedItem.label === item.label ? '#f0f0f0' : 'white',
          }"
          @click="clickItem(item)"
        >
          {{ item.label }}
        </li>
      </ul>
    </div>
  </AModal>
  <Modal title="审核页面" class="w-2/5">
    <Form class="mx-4">
      <template #solutionName="slotProps">
        <Input v-bind="slotProps" placeholder="">
          <template #suffix>
            <Ellipsis class="ml-auto h-6 w-6" @click="viewSolutionName()" />
          </template>
        </Input>
      </template>
      <template #solutionType="slotProps">
        <Select v-bind="slotProps" style="width: 100%">
          <SelectOption
            v-for="item in typeOptions"
            :value="item.value"
            :key="item.value"
          >
            {{ item.label }}
          </SelectOption>
        </Select>
      </template>
      <template #concentration="slotProps">
        <Input v-bind="slotProps" placeholder="" :disabled="isDisabled">
        </Input>
      </template>
      <template #concentrationUnit="slotProps">
        <Input v-bind="slotProps" placeholder="" :disabled="isDisabled">
        </Input>
        <Button type="primary" @click="search" :disabled="isDisabled">
          {{ $t('dilution-management.solution-preparation.search') }}
        </Button>
      </template>
      <template #isMixedLabel="slotProps">
        <Select v-bind="slotProps" style="width: 100%" @change="viewChange">
          <SelectOption
            v-for="item in mixedOptions"
            :value="item.value"
            :key="item.value"
          >
            {{ item.label }}
          </SelectOption>
        </Select>
      </template>
    </Form>
  </Modal>
</template>
<style>
.list-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #ccc;
  transition: background-color 0.3s;
}

.list-item:hover {
  background-color: #f0f0f0;
}
</style>
