<script setup lang="ts">
import type { XFolderApi } from '#/api/business-static-tables';

import { onMounted, ref } from 'vue';

import { Search } from '@vben/icons';

import {
  Button,
  Checkbox,
  CheckboxGroup,
  Empty,
  InputSearch,
  Skeleton,
  Tree,
} from 'ant-design-vue';

import {
  $getConditionsPositionsApi,
  $getIntervalsApi,
} from '#/api/business-static-tables';
import { flatToTree } from '#/utils/utils';

interface FormArgs {
  bDisableConditions?: boolean;
  nMaxIntervals?: number;
  openingMode?: string;
  stabNo?: number;
}

const props = withDefaults(
  defineProps<{
    formArgs?: FormArgs;
    showSearch?: boolean;
  }>(),
  {
    showSearch: true,
    formArgs: () => ({
      bDisableConditions: false,
      nMaxIntervals: 0,
      openingMode: '',
      stabNo: -1,
    }),
  },
);

const selectConditions = ref<string[]>([]);
const checkIntervals = ref<string[]>([]);

const searchValue = ref<string>('');
/** 骨架屏加载 */
const showTreeSkeleton = ref<boolean>(true);
const showIntervalTreeSkeleton = ref<boolean>(true);
const conditionTreeArray = ref<XFolderApi.ConditionTree[]>([]);
const intervalList = ref<XFolderApi.Intervals[]>([]);
async function handleReload() {
  await loadTree();
}

onMounted(() => {
  loadTree();
  loadIntervals();
});

async function loadTree() {
  showTreeSkeleton.value = true;
  searchValue.value = '';
  const stabNo = props.formArgs.stabNo?.toString();
  const ret = await $getConditionsPositionsApi({ stabNo });
  ret?.forEach((item) => {
    item.key = `${item.PARENT}-${item.VALUE}`;
  });
  const retTree = flatToTree(ret, 'VALUE', 'PARENT');
  const checkConditions = ret
    .filter((item) => item.SELECTED === 'true')
    .map((item) => item.key);
  selectConditions.value = checkConditions;
  conditionTreeArray.value = retTree;
  showTreeSkeleton.value = false;
}

const loadIntervals = async () => {
  showIntervalTreeSkeleton.value = true;
  const stabNo = props.formArgs.stabNo?.toString();
  const ret = await $getIntervalsApi({ stabNo, dept: 'Site1' });
  ret?.forEach((item: { INTERVAL: any; label: any; value: any }) => {
    item.label = item.INTERVAL;
    item.value = item.INTERVAL;
  });
  intervalList.value = ret;
  checkIntervals.value = ret
    .filter((item: { CHECKED: string }) => item.CHECKED === 'true')
    .map((item: { INTERVAL: any }) => item.INTERVAL);
  showIntervalTreeSkeleton.value = false;
};

function checkCondition(checkedKeys: any) {
  selectConditions.value = checkedKeys;
}

function getSelectConditions() {
  // 拼接成PARENT - VALUE的字符串格式
  const selectConditionsArray = [];
  for (let i = 0; i < conditionTreeArray.value.length; i++) {
    const item = conditionTreeArray.value[i];
    if (item && item.children) {
      // 有子节点则取子节点
      for (let j = 0; j < item.children.length; j++) {
        const child = item.children[j];
        if (child && selectConditions.value.includes(child.key)) {
          selectConditionsArray.push(child);
        }
      }
    } else {
      // 没有子节点则取当前节点
      if (item && selectConditions.value.includes(item.key)) {
        selectConditionsArray.push(item);
      }
    }
  }
  const selectConditionsStr = selectConditionsArray.map(
    (item: { PARENT: any; VALUE: any }) => {
      return item.PARENT ? `${item.PARENT} - ${item.VALUE}` : `${item.VALUE}`;
    },
  );
  return selectConditionsStr;
}
defineExpose({
  getSelectConditions,
  checkIntervals,
});
</script>
<template>
  <div style="display: flex; height: 500px; padding: 8px; overflow-y: auto">
    <Skeleton
      :loading="showTreeSkeleton"
      :paragraph="{ rows: 8 }"
      active
      class="p-[8px]"
    >
      <div
        class="bg-background flex h-full w-1/2 flex-col overflow-y-auto rounded-lg"
      >
        <div
          v-if="showSearch"
          class="bg-background z-100 sticky left-0 top-0 p-[8px]"
        >
          <InputSearch
            v-model:value="searchValue"
            :placeholder="$t('commons.search')"
            size="small"
          >
            <template #enterButton>
              <Button @click="handleReload">
                <Search class="text-primary" />
              </Button>
            </template>
          </InputSearch>
        </div>
        <div class="h-full overflow-x-hidden px-[8px]">
          <Tree
            v-bind="$attrs"
            v-if="conditionTreeArray.length > 0"
            v-model:checked-keys="selectConditions"
            :class="$attrs.class"
            :field-names="{ title: 'TEXT', key: 'key' }"
            :show-line="{ showLeafIcon: false }"
            :tree-data="conditionTreeArray"
            :virtual="false"
            checkable
            :selectable="false"
            @check="checkCondition"
          >
            <template #title="{ TEXT }">
              <span v-if="TEXT.indexOf(searchValue) > -1">
                {{ TEXT.substring(0, TEXT.indexOf(searchValue)) }}
                <span style="color: #f50">{{ searchValue }}</span>
                {{
                  TEXT.substring(TEXT.indexOf(searchValue) + searchValue.length)
                }}
              </span>
              <span v-else>{{ TEXT }}</span>
            </template>
          </Tree>
          <div v-else class="mt-5">
            <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE" description="无数据" />
          </div>
        </div>
      </div>
    </Skeleton>
    <Skeleton
      :loading="showIntervalTreeSkeleton"
      :paragraph="{ rows: 8 }"
      active
      class="p-[8px]"
    >
      <div
        class="bg-background flex h-full w-1/2 flex-col overflow-y-auto rounded-lg"
      >
        <div class="h-full overflow-x-hidden px-[8px]">
          <CheckboxGroup
            v-if="intervalList.length > 0"
            v-model:value="checkIntervals"
            style="display: flex; flex-direction: column; width: 100%"
          >
            <template v-for="item in intervalList" :key="item.INTERVAL">
              <Checkbox
                v-model:value="item.INTERVAL"
                :checked="item.CHECKED === 'true'"
                class="w-full"
              >
                {{ item.INTERVAL }}
              </Checkbox>
            </template>
          </CheckboxGroup>
          <div v-else class="mt-5">
            <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE" description="无数据" />
          </div>
        </div>
      </div>
    </Skeleton>
  </div>
</template>
