<script lang="ts" setup>
import { computed, onMounted, watch } from 'vue';

import { Button, Card, Space } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { getFileBasedApi } from '#/api/equipment/equipment-mg';
import { useEquipmentStore } from '#/store';

import { fileSchema } from '../equipment-mg-data';

const equipmentStore = useEquipmentStore();
interface RowType {
  [key: string]: any;
}
const currentRow = computed<null | RowType>(
  () => equipmentStore.getCurrentRow as null | RowType,
);
onMounted(() => {
  getData();
});
watch(
  currentRow,
  () => {
    getData();
  },
  { deep: true },
);
const getData = async () => {
  if (currentRow.value && currentRow.value.EQID) {
    const data = await getFileBasedApi([currentRow.value.EQID]);
    formApi.setFieldValue('DCUMETHOD', data[0].DCUMETHOD);
    formApi.setFieldValue('DCUFOLDER', data[0].DCUFOLDER);
    formApi.setFieldValue('DCUFILETYPE', data[0].DCUFILETYPE);
  }
};

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: fileSchema(),
  showDefaultActions: false,
  // wrapperClass: 'grid-cols-3',
});

const searchMethod = () => {
  // TODO:脚本查找
};
const editMethod = () => {
  // TODO:脚本编辑
};
</script>
<template>
  <Card title="基于文件的工具" class="h-2/3">
    <Form class="mx-4" />
    <Space :size="[4, 0]">
      <Button type="primary" @click="searchMethod"> 查找方法 </Button>
      <Button type="primary" danger @click="editMethod"> 修改脚本 </Button>
    </Space>
  </Card>
</template>
