import { callServer, getDataSet, getDataSetNoPage } from '#/api/core/witlab';

export namespace SampleGroupsApi {
  export interface IpSampleGroups {
    ORIGREC: number;
    SAMPLEGROUPCODE: string;
    SAMPLEGROUPNAME: string;
    PRODGROUP: string;
    SAMPGRTYPE: string;
    DEPT: string;
    MATCODE: string;
    PLANT: string;
    SUBGROUPNAME: string;
    METADATA_TEMPLATE: string;
    ACTIVE: string;
  }

  export interface IpSampleGroupDetails {
    ORIGREC: number;
    SP_CODE: number;
    SEQUENCE: number;
    NO_SAMPLES_TO_LOGIN: number;
    PROGNAME: string;
    MATCODE: string;
    VERSION: number;
    DISPSTEPCODE: string;
    DISPSTATUS: string;
    DATEFROM: Date;
    DATETO: Date;
    PROFILE: string;
    SPECIFICATION: string;
    AUTOADD: string;
    SKIP_SAMPLING: string;
    SKIP_CR: string;
    SKIP_RECEIVE: string;
    AUTODISPOSITION: string;
    BATCHAUTORELEASE: string;
    INSPECTIONCYCLE: number;
    SKIPBATCHINSPECTION: string;
    BATCHNUMBER: string;
    ROLE_SAMPLING: string;
    SAMPLEGROUPCODE: string;
    PRODGROUP: string;
  }

  export interface Specs {
    ORIGREC: number;
    PRIMARY_SPEC: string;
    SPECIFICATION: string;
    SPECNO: number;
    SPECIFICATION_O: number;
    VERSION: number;
    INSPECTION_STANDARD: string;
  }

  export interface SpTests {
    ORIGREC: number;
    PROFILE: string;
    SPTESTSORTER: number;
    SP_TESTNO: string;
    TESTNO: string;
    SP_CODE: number;
    TESTCODE: number;
    RETEST_FLAG: string;
    SCENE_FLAG: string;
    DRAWNO: number;
  }

  export interface SpAnalytes {
    ORIGREC: number;
    SP_SYNONYM: string;
    ANALYTE: string;
    NOREP: number;
    SORTER: number;
  }

  export interface MethodsRelSpTests {
    ORIGREC: number;
    DEFAULTMETHOD: string;
    METHOD: string;
    DEPT: string;
    SERVGRP: string;
    ELNREFERENCE: string;
    STARTDATE: Date;
    TODATE: Date;
    ELN_ID: string;
    TEST_USRNAM: string;
    SP_CODE: number;
    TESTCODE: number;
    PROFILE: string;
    DRAWNO: number;
  }

  export interface MethodsRelSpTestsEqType {
    ORIGREC: number;
    EQTYPE: string;
    SP_CODE: number;
    TESTCODE: number;
    METHOD: string;
    PROFILE: string;
    DRAWNO: string;
  }

  export interface MethodsRelSpTestsMet {
    ORIGREC: number;
    MATCODE: string;
    MATNAME: string;
    SP_CODE: number;
    TESTCODE: number;
    METHOD: string;
    DEPT: string;
    PROFILE: string;
    SERVGRP: string;
    DRAWNO: string;
  }

  export interface Recipes {
    ORIGREC: number;
    REF_ORIGREC: number;
    STATUS: string;
    RECIPECODE: number;
    RECIPENAME: string;
    STARTDDATE: Date;
    EXPDATE: Date;
    RETIREDDAT: Date;
    MATCODE: string;
    SAMPLEGROUPCODE: string;
  }

  export interface RecipesDetail {
    ORIGREC: number;
    RECIPECODE: string;
    MATCODE: string;
    MATNAME: string;
  }

  export interface SamplingRequirements {
    ORIGREC: number;
    SAMPLINGPOSITION: string;
    SAMPLESIZE: string;
    NUMBEROFCONTAINERS: number;
    SAMPLE_TYPE: string;
    CONTAINERQTY: number;
    CONTAINER_UNITS: string;
    FORLAB: string;
    PURPOSE: string;
    CONDITION: string;
    CREATEINVENTORYID: string;
    ALIQUOT: string;
    ID: number;
    SP_CODE: number;
    DRAWNO: number;
    SAMPLEGROUPCODE: string;
    PROFILE: string;
  }

  export interface SamplingRequirementsTests {
    ORIGREC: number;
    ID: number;
    TESTCODE: number;
    TESTNO: string;
  }
}

/**
 * 获取样品模板组
 */
async function getTestPlanGroups(sOpeningMode: string) {
  return getDataSet('SampleGroups.getTestPlanGroups', [sOpeningMode]);
}

/**
 * 获取样品模板组列表
 */
async function getSampleGrpsList(sTestGroup: string, sOpeningMode: string) {
  return getDataSet('SampleGroups.SampleGrp_dg', [sTestGroup, sOpeningMode]);
}

/**
 * 获取测试计划组列表
 */
async function getSampleGrpDetailList(
  sSampleGrpCode: string,
  isShowAllTestPlan: string,
  sOpeningMode: string,
) {
  return getDataSet('SampleGroups.SampleGrpDetails_dgSSL', [
    sSampleGrpCode,
    isShowAllTestPlan,
    sOpeningMode,
  ]);
}

/**
 * 获取角色
 */
async function getAllRoles() {
  return getDataSet('Common.DS_AllRoles', []);
}

/**
 * 获取车间
 */
async function getPlants(sDept: string) {
  return getDataSet('SampleGroups.cbGetPlants', [sDept]);
}

/**
 * 获取样品列表
 */
async function getTestPlanList(sTestPlanGroup: string, sSampGrpCode: string) {
  return getDataSet('SampleGroups.getTestPlanList', [
    sTestPlanGroup,
    sSampGrpCode,
  ]);
}

/**
 * 获取质量标准
 */
async function getSpecsList(
  sTable: string,
  FieldValue: string,
  sFolderno: string,
  bFilterSeason: boolean,
  sField: string,
) {
  return getDataSet('ProcessSpecifications.getSpecsForSpCode', [
    sTable,
    FieldValue,
    sFolderno,
    bFilterSeason,
    sField,
  ]);
}

/**
 * 获取测试类别
 */
async function getTestCat() {
  return getDataSet('TEST_PLAN_MANAGER.CB_TEST_CAT', []);
}

/**
 * 编辑测试
 */
async function getTestByMultiChoice(
  spCode: number,
  drawNo: number,
  sTestChoice: string,
  profile: string,
) {
  return getDataSet('TEST_PLAN_MANAGER.DS_TESTS_MULTICHOICE', [
    spCode,
    drawNo,
    sTestChoice,
    profile,
  ]);
}

/**
 * 获取质量标准下的检测项目
 */
async function getTestBySpecs(spCode: number, specNo: number) {
  return getDataSet('TEST_PLAN_MANAGER.DS_TESTS_SPECS', [spCode, specNo]);
}

/**
 * 获取方案测试
 */
async function getProfiles(sSpCode: number, sDrawNo: number) {
  return getDataSet('SampleGroups.getProfiles', [sSpCode, sDrawNo]);
}

/**
 * 获取分析项
 */
async function getAnalytes(
  sSpCode: number,
  sDrawNo: number,
  sProfile: string,
  sTestCode: number,
) {
  return getDataSet('SampleGroups.getAnalytes', [
    sSpCode,
    sDrawNo,
    sProfile,
    sTestCode,
  ]);
}

/**
 * 获取分析项
 */
async function getMethods(
  sSpCode: number,
  sTestCode: number,
  sProfile: string,
) {
  return getDataSet('SampleGroups.getMethods', [sSpCode, sTestCode, sProfile]);
}

/**
 * 获取设备
 */
async function GetMethodEquiType(sMethodOrigrec: number) {
  return getDataSet('SampleGroups.GetMethodEquiType', [sMethodOrigrec]);
}

/**
 * 获取材料
 */
async function GetMethodMet(sMethodOrigrec: number) {
  return getDataSet('SampleGroups.GetMethodMet', [sMethodOrigrec]);
}

/**
 * 获取配方列表
 */
async function getRecipeList(sMatCode: string, sSampleGrpCode: string) {
  return getDataSet('SampleGroups.Recipes_dg', [sMatCode, sSampleGrpCode]);
}

/**
 * 获取配方详情列表
 */
async function getRecipeDetailList(sRecipeCode: string) {
  return getDataSet('SampleGroups.RecipeDetails_dg', [sRecipeCode]);
}

/**
 * 获取取样要求左侧测试计划
 */
async function getSamplingRequirementsSamplesProfiles(
  sSampleGrpCode: string,
  isShowAllTestPlan: string,
  sOpeningMode: string,
) {
  return getDataSet('SampleGroups.SamplingRequirementsSamplesProfiles', [
    sSampleGrpCode,
    isShowAllTestPlan,
    sOpeningMode,
  ]);
}

/**
 * 获取取样要求
 */
async function getSampleReqList(
  sSpCode: number,
  sSampleGrpCode: string,
  sProfile: string,
) {
  return getDataSet('SampleGroups.SamplingReq_dg', [
    sSpCode,
    sSampleGrpCode,
    sProfile,
  ]);
}

/**
 * 获取取样位置
 */
async function getSamplePosition() {
  return await getDataSetNoPage('SampleGroups.cbCollectFrom', []);
}

/**
 * 获取容器类型
 */
async function getSampleSize() {
  return await getDataSetNoPage('SampleGroups.cbCollectSize', []);
}

/**
 * 获取样品类型
 */
async function getSampleType() {
  return await getDataSetNoPage('SampleGroups.cbSampleType', []);
}

/**
 * 获取取样单位
 */
async function getSampleReqUnit() {
  return await getDataSetNoPage('SampleGroups.getUnitsSamplingReq', []);
}

/**
 * 获取样品用于
 */
async function getSampleForLab(sDept: string) {
  return await getDataSetNoPage('Common.CB_SERVGRP', [sDept]);
}

/**
 * 获取贮存条件
 */
async function getCondition() {
  return await getDataSetNoPage('SampleGroups.getConditions', []);
}

/**
 * 获取关联测试列表
 */
async function getSampleReqTests(sId: number) {
  return getDataSet('SampleGroups.SamplingRequirementsTests', [sId]);
}

/**
 * 获取选择关联测试
 */
async function getChooseSampleReqTests(sSpCode: number, aTestCode: string[]) {
  return getDataSet('SampleGroups.ChooseSamplingRequirementsTests', [
    sSpCode,
    aTestCode,
    '',
    1,
  ]);
}

/**
 * 根据质量标准添加测试项目
 */
async function insertTestsByProfile(
  sSpCode: number,
  sSpecNo: number,
  sDrawNo: number,
  profile: string,
) {
  return await callServer('TEST_PLAN_MANAGER.insertTestsByProfile', [
    sSpCode,
    sSpecNo,
    sDrawNo,
    profile,
    'Y',
    'Add',
  ]);
}

/**
 * 编辑测试列表
 */
async function insertTests(
  sSpCode: number,
  sDrawNo: number,
  profile: string,
  aTestCode: string[],
) {
  return await callServer('TEST_PLAN_MANAGER.INSERT_TESTS', [
    sSpCode,
    sDrawNo,
    profile,
    aTestCode,
    'Y',
    'Edit',
  ]);
}
/**
 * 同步方法失效日期
 */
async function updateExpirationDate(sSpCode: number) {
  return await callServer('SampleGroups.updateExpirationDate', [
    sSpCode,
    'UpdateExpirationDate',
  ]);
}

/**
 * 同步方法
 */
async function updateDefaultMethodsRelSpTest(
  sSpCode: number,
  sTestCode: number,
  sProfile: string,
) {
  return await callServer('TEST_PLAN_MANAGER.defaultMethodsRelSpTest', [
    sSpCode,
    sTestCode,
    sProfile,
  ]);
}

/**
 * 查询分析项
 */
async function getAnalytesByMultiChoice(
  sSpCode: number,
  sDrawNo: number,
  sTestCode: number,
  profile: string,
) {
  return getDataSet('TEST_PLAN_MANAGER.DS_ANALYTES_MULTICHOICE', [
    sSpCode,
    sDrawNo,
    sTestCode,
    profile,
  ]);
}

/**
 * 编辑分析项
 */
async function editAnalytes(
  sSpCode: number,
  sDrawNo: number,
  sTestCode: number,
  profile: string,
  aAnalytes: string[],
) {
  return await callServer('TEST_PLAN_MANAGER.EDIT_ANALYTES', [
    sSpCode,
    sDrawNo,
    sTestCode,
    profile,
    aAnalytes,
  ]);
}

/**
 * 查询设备类型
 */
async function getEqTypes(
  sSpCode: number,
  sTestCode: number,
  sMethod: string,
  sDept: string,
  sProfile: string,
  sServgrp: string,
  sDrawNo: number,
  sEqtypes: string,
) {
  return getDataSet('SampleGroups.mcEquipTypes', [
    sSpCode,
    sTestCode,
    sMethod,
    sDept,
    sProfile,
    sServgrp,
    sDrawNo,
    '',
    sEqtypes,
  ]);
}

/**
 * 保存设备类型
 */
async function updateMethodEquType(
  sSpCode: number,
  sTestCode: number,
  sMethod: string,
  sDept: string,
  sProfile: string,
  sServgrp: string,
  sDrawNo: number,
  aEqtypes: string[],
) {
  return await callServer('SampleGroups.UpdateMethodEquiType', [
    sSpCode,
    sTestCode,
    sMethod,
    sDept,
    sProfile,
    sServgrp,
    sDrawNo,
    aEqtypes,
  ]);
}

/**
 * 查询设备类型
 */
async function getMetTypes(
  sSpCode: number,
  sTestCode: number,
  sMethod: string,
  sDept: string,
  sProfile: string,
  sServgrp: string,
  sDrawNo: number,
  sMatCode: string,
) {
  return getDataSet('SampleGroups.mcMetTypes', [
    sSpCode,
    sTestCode,
    sMethod,
    sDept,
    sProfile,
    sServgrp,
    sDrawNo,
    '',
    sMatCode,
  ]);
}

/**
 * 保存材料类型
 */
async function updateMethodMetType(
  sSpCode: number,
  sTestCode: number,
  sMethod: string,
  sDept: string,
  sProfile: string,
  sServgrp: string,
  sDrawNo: number,
  aMetType: string[],
) {
  return await callServer('SampleGroups.UpdateMethodMetType', [
    sSpCode,
    sTestCode,
    sMethod,
    sDept,
    sProfile,
    sServgrp,
    sDrawNo,
    aMetType,
  ]);
}

/**
 * 新增样品模板
 */
async function addSampleGroupTestPlanGrp(data: SampleGroupsApi.IpSampleGroups) {
  return await callServer('SampleGroups.addSampleGroupTestPlanGrp', [
    data.PRODGROUP,
    data.SAMPGRTYPE,
    data.MATCODE,
    data.SAMPLEGROUPNAME,
    data.PLANT,
  ]);
}

/**
 * 删除样品模板
 */
async function delSampGroup(sSampleGrpCode: string) {
  return await callServer('SampleGroups.DelSampGroup', [sSampleGrpCode]);
}

/**
 * 新增测试计划
 */
async function addTestPlan(data: SampleGroupsApi.IpSampleGroupDetails) {
  return await callServer('SampleGroups.addTestPlan', [
    data.SAMPLEGROUPCODE,
    data.PROGNAME,
    data.PRODGROUP,
    data.MATCODE,
    'Sample',
    'Single',
  ]);
}

/**
 * 选择质量标准
 */
async function addSpecToTemplate(
  sTable: string,
  sFieldValue: string,
  aSpecNo: number[],
  sFolderNo: string,
) {
  return await callServer('ProcessSpecifications.addSpecToTemplate', [
    sTable,
    sFieldValue,
    aSpecNo,
    sFolderNo,
  ]);
}

/**
 * 删除选择的质量标准
 */
async function deleteSpecFromSpCode(
  sTable: string,
  sFieldValue: string,
  speco: number,
  sSpecNo: number,
) {
  return await callServer('ProcessSpecifications.deleteSpecFromSpCode', [
    sTable,
    sFieldValue,
    speco,
    sSpecNo,
  ]);
}

/**
 * 删除测试计划组
 */
async function deleteTestPlan(sSampleGrpCode: string, sSpCode: number) {
  return await callServer('SampleGroups.deleteTestPlan', [
    sSampleGrpCode,
    sSpCode,
  ]);
}

/**
 * 新建版本
 */
async function createNewVersionTestPlan(
  sSpCode: number,
  sSampleGrpCode: string,
) {
  return await callServer('SampleGroups.createNewVersionTestPlan', [
    sSpCode,
    sSampleGrpCode,
  ]);
}
/**
 * 新增取样要求
 */
async function addSamplingRequirements(
  data: SampleGroupsApi.SamplingRequirements,
) {
  return await callServer('SampleGroups.AddSamplingRequirements', [
    data.SP_CODE,
    data.DRAWNO,
    data.SAMPLEGROUPCODE,
    data.SAMPLINGPOSITION,
    data.SAMPLESIZE,
    data.NUMBEROFCONTAINERS,
    data.FORLAB,
    data.CONTAINERQTY,
    data.CONTAINER_UNITS,
    data.CREATEINVENTORYID,
    data.CONDITION,
    data.ALIQUOT,
    '',
    data.PROFILE,
    '',
    '',
    '',
    '',
    data.SAMPLE_TYPE,
    data.PURPOSE,
  ]);
}

/**
 * 删除取样要求
 */
async function delSamplingReq(sOrigrec: number) {
  return await callServer('SampleGroups.delSamplingReq', [sOrigrec]);
}

/**
 * 复制取样要求
 */
async function copySamplingReq(sOrigrec: number, sNum: number) {
  return await callServer('SampleGroups.copyRequirementSampling', [
    sOrigrec,
    sNum,
  ]);
}

/**
 * 新增配方
 */
async function addRecipe(data: SampleGroupsApi.Recipes) {
  return await callServer('SampleGroups.ADD_RECIPE', [
    data.MATCODE,
    data.RECIPENAME,
    data.SAMPLEGROUPCODE,
    data.RECIPECODE,
  ]);
}

/**
 * 删除配方
 */
async function deleteRecipe(sOrigrec: number) {
  return await callServer('SampleGroups.deleteRecipe', [sOrigrec]);
}

/**
 * 废弃配方
 */
async function retireRecipe(sRecipeCode: number) {
  return await callServer('MATERIAL_MANAGER.RECIPE_RETIRE', [sRecipeCode]);
}

export {
  addRecipe,
  addSampleGroupTestPlanGrp,
  addSamplingRequirements,
  addSpecToTemplate,
  addTestPlan,
  copySamplingReq,
  createNewVersionTestPlan,
  deleteRecipe,
  deleteSpecFromSpCode,
  deleteTestPlan,
  delSampGroup,
  delSamplingReq,
  editAnalytes,
  getAllRoles,
  getAnalytes,
  getAnalytesByMultiChoice,
  getChooseSampleReqTests,
  getCondition,
  getEqTypes,
  GetMethodEquiType,
  GetMethodMet,
  getMethods,
  getMetTypes,
  getPlants,
  getProfiles,
  getRecipeDetailList,
  getRecipeList,
  getSampleForLab,
  getSampleGrpDetailList,
  getSampleGrpsList,
  getSamplePosition,
  getSampleReqList,
  getSampleReqTests,
  getSampleReqUnit,
  getSampleSize,
  getSampleType,
  getSamplingRequirementsSamplesProfiles,
  getSpecsList,
  getTestByMultiChoice,
  getTestBySpecs,
  getTestCat,
  getTestPlanGroups,
  getTestPlanList,
  insertTests,
  insertTestsByProfile,
  retireRecipe,
  updateDefaultMethodsRelSpTest,
  updateExpirationDate,
  updateMethodEquType,
  updateMethodMetType,
};
