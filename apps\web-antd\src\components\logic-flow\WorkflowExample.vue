<script setup lang="ts">
import type {
  Approver,
  ValidationResult,
  WorkflowData,
  WorkflowEdge,
  WorkflowNode,
} from './types/workflow';

import { reactive, ref } from 'vue';

import { Button, message, Modal, Space, Table, Tabs } from 'ant-design-vue';

import { WORKFLOW_TEMPLATES } from './config/nodes';
import WorkflowDesigner from './WorkflowDesigner.vue';
// 响应式数据
const workflowRef = ref();
const mode = ref<'edit' | 'view'>('edit');
const showCode = ref(false);
const selectedNode = ref<null | WorkflowNode>(null);
const selectedEdge = ref<null | WorkflowEdge>(null);

// 工作流数据
const flowData = reactive<WorkflowData>({
  nodes: [
    {
      id: 'start-1',
      type: 'start',
      x: 200,
      y: 200,
      properties: {
        name: '开始',
      },
      text: {
        x: 200,
        y: 240,
        value: '开始',
      },
    },
    {
      id: 'end-1',
      type: 'end',
      x: 600,
      y: 200,
      properties: {
        name: '结束',
      },
      text: {
        x: 600,
        y: 240,
        value: '结束',
      },
    },
  ],
  edges: [],
});

// 审批人数据源
const approverDataSource: Approver[] = [
  {
    id: '1',
    name: '张三',
    email: '<EMAIL>',
    department: '质控部',
    role: '质控员',
  },
  {
    id: '2',
    name: '李四',
    email: '<EMAIL>',
    department: '质控部',
    role: '质控主管',
  },
  {
    id: '3',
    name: '王五',
    email: '<EMAIL>',
    department: '质控部',
    role: '质控经理',
  },
  {
    id: '4',
    name: '赵六',
    email: '<EMAIL>',
    department: '生产部',
    role: '生产主管',
  },
  {
    id: '5',
    name: '钱七',
    email: '<EMAIL>',
    department: '研发部',
    role: '研发经理',
  },
];

// 事件处理
const handleNodeSelect = (node: null | WorkflowNode) => {
  selectedNode.value = node;
  selectedEdge.value = null;
};

const handleEdgeSelect = (edge: null | WorkflowEdge) => {
  selectedEdge.value = edge;
  selectedNode.value = null;
};

const handleValidate = (result: ValidationResult) => {
  if (result.valid) {
    message.success('工作流验证通过');
  } else {
    const errorCount = result.errors.filter((e) => e.type === 'error').length;
    const warningCount = result.errors.filter(
      (e) => e.type === 'warning',
    ).length;
    message.warning(`发现 ${errorCount} 个错误，${warningCount} 个警告`);
  }
};

const switchMode = () => {
  mode.value = mode.value === 'edit' ? 'view' : 'edit';
  message.info(`已切换到${mode.value === 'edit' ? '编辑' : '查看'}模式`);
};

const loadTemplate = () => {
  const template = WORKFLOW_TEMPLATES[1]; // 加载条件审批模板
  Object.assign(flowData, template.data);
  message.success(`已加载模板：${template.name}`);
};

// API文档数据
const propsColumns = [
  { title: '属性', dataIndex: 'name', key: 'name' },
  { title: '类型', dataIndex: 'type', key: 'type' },
  { title: '默认值', dataIndex: 'default', key: 'default' },
  { title: '说明', dataIndex: 'description', key: 'description' },
];

const propsData = [
  {
    name: 'flowData',
    type: 'WorkflowData',
    default: '{}',
    description: '工作流数据',
  },
  {
    name: 'mode',
    type: "'edit' | 'view'",
    default: "'edit'",
    description: '工作模式',
  },
  {
    name: 'readonly',
    type: 'boolean',
    default: 'false',
    description: '是否只读',
  },
  { name: 'config', type: 'Object', default: '{}', description: '画布配置' },
  {
    name: 'approverDataSource',
    type: 'Approver[]',
    default: '[]',
    description: '审批人数据源',
  },
];

const eventsColumns = [
  { title: '事件', dataIndex: 'name', key: 'name' },
  { title: '参数', dataIndex: 'params', key: 'params' },
  { title: '说明', dataIndex: 'description', key: 'description' },
];

const eventsData = [
  {
    name: 'update:flowData',
    params: 'data: WorkflowData',
    description: '数据变更时触发',
  },
  {
    name: 'node-select',
    params: 'node: WorkflowNode | null',
    description: '节点选择时触发',
  },
  {
    name: 'edge-select',
    params: 'edge: WorkflowEdge | null',
    description: '连线选择时触发',
  },
  {
    name: 'validate',
    params: 'result: ValidationResult',
    description: '验证时触发',
  },
];

const methodsColumns = [
  { title: '方法', dataIndex: 'name', key: 'name' },
  { title: '参数', dataIndex: 'params', key: 'params' },
  { title: '返回值', dataIndex: 'return', key: 'return' },
  { title: '说明', dataIndex: 'description', key: 'description' },
];

const methodsData = [
  {
    name: 'getFlowData',
    params: '-',
    return: 'WorkflowData',
    description: '获取工作流数据',
  },
  {
    name: 'setFlowData',
    params: 'data: WorkflowData',
    return: 'void',
    description: '设置工作流数据',
  },
  {
    name: 'validate',
    params: '-',
    return: 'ValidationResult',
    description: '验证工作流',
  },
  {
    name: 'exportData',
    params: 'format?: string',
    return: 'string',
    description: '导出数据',
  },
  { name: 'fitView', params: '-', return: 'void', description: '适应画布' },
];
</script>

<template>
  <div class="workflow-example">
    <div class="example-header">
      <Space>
        <Button @click="switchMode">
          {{ mode === 'edit' ? '切换到查看模式' : '切换到编辑模式' }}
        </Button>
        <Button @click="loadTemplate">加载示例模板</Button>
        <Button @click="showCode = true">查看代码</Button>
      </Space>
    </div>

    <div class="example-content">
      <WorkflowDesigner
        ref="workflowRef"
        v-model:flow-data="flowData"
        :mode="mode"
        :readonly="mode === 'view'"
        :approver-data-source="approverDataSource"
        @node-select="handleNodeSelect"
        @edge-select="handleEdgeSelect"
        @validate="handleValidate"
      />
    </div>

    <!-- 代码查看模态框 -->
    <Modal
      v-model:open="showCode"
      title="使用示例代码"
      width="80%"
      :footer="null"
    >
      <Tabs>
        <Tabs.TabPane key="api" tab="API文档">
          <div class="api-docs">
            <h4>Props</h4>
            <Table
              :columns="propsColumns"
              :data-source="propsData"
              size="small"
              :pagination="false"
            />

            <h4>Events</h4>
            <Table
              :columns="eventsColumns"
              :data-source="eventsData"
              size="small"
              :pagination="false"
            />

            <h4>Methods</h4>
            <Table
              :columns="methodsColumns"
              :data-source="methodsData"
              size="small"
              :pagination="false"
            />
          </div>
        </Tabs.TabPane>
      </Tabs>
    </Modal>
  </div>
</template>

<style scoped>
.workflow-example {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f0f2f5;
}

.example-header {
  padding: 24px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
}

.example-header h2 {
  margin: 0 0 8px;
  color: #262626;
}

.example-header p {
  margin: 0 0 16px;
  color: #8c8c8c;
}

.example-content {
  position: relative;
  flex: 1;
}

.example-status {
  position: fixed;
  top: 120px;
  right: 24px;
  z-index: 1000;
  width: 200px;
}

.code-block {
  padding: 16px;
  overflow: auto;
  font-size: 12px;
  line-height: 1.5;
  background: #f5f5f5;
  border-radius: 4px;
}

.api-docs h4 {
  margin: 24px 0 16px;
  color: #262626;
}

.api-docs h4:first-child {
  margin-top: 0;
}
</style>
