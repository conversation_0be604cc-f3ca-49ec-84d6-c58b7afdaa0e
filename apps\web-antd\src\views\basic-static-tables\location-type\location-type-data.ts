import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { LocationTypeApi } from '#/api/';

import { $t } from '#/locales';

export function useLocationTypeColumns(): VxeTableGridOptions<LocationTypeApi.LocationType>['columns'] {
  return [
    { field: 'select', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'LOCATION_TYPE_ID',
      title: $t('basic-static-tables.location-types.location_type_id'),
      visible: false,
    },
    {
      align: 'center',
      field: 'TYPE_NAME',
      title: $t('basic-static-tables.location-type.type_name'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'DESCRIPTION',
      title: $t('basic-static-tables.location-type.description'),
      minWidth: 200,
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'IMAGEREF',
      title: $t('basic-static-tables.location-type.imageref'),
      visible: false,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('basic-static-tables.operation'),
      minWidth: 140,
    },
  ];
}

export function useLocationTypeFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        // placeholder: 'Please enter code',
      },
      fieldName: 'TYPE_NAME',
      label: $t('basic-static-tables.location-type.type_name'),
    },
    {
      component: 'Input',
      fieldName: 'DESCRIPTION',
      componentProps: {
        // placeholder: 'Please enter name',
      },
      label: $t('basic-static-tables.location-type.description'),
    },
  ];
}

export function useLocationTypeSublocEventColumns(): VxeTableGridOptions<LocationTypeApi.LocationTypeSubLoc>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 100,
      visible: false,
    },
    {
      align: 'center',
      field: 'LOCATION_TYPES_SUBLOC_ID',
      title: $t(
        'basic-static-tables.location-type_subloc.location_type_subloc_id',
      ),
      visible: false,
    },
    {
      align: 'center',
      field: 'LOCATION_TYPE_ID',
      title: $t('basic-static-tables.location-type_subloc.location_type_id'),
      visible: false,
    },
    {
      align: 'center',
      field: 'NAME',
      title: $t('basic-static-tables.location-type_subloc.name'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'PREFIX',
      title: $t('basic-static-tables.location-type_subloc.prefix'),
      editRender: { name: 'input' },
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'LOCTYPE_SIZE',
      title: $t('basic-static-tables.location-type_subloc.loctype_size'),
      editRender: { name: 'input' },
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'LOCTYPE_ORDER',
      title: $t('basic-static-tables.location-type_subloc.loctype_order'),
      editRender: { name: 'input' },
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'NUMBERING_METHOD',
      title: $t('basic-static-tables.location-type_subloc.numbering_method'),
      editRender: { name: 'input' },
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'IS_STORABLE',
      title: $t('basic-static-tables.location-type_subloc.is_storable'),
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('basic-static-tables.operation'),
      minWidth: 140,
    },
  ];
}
