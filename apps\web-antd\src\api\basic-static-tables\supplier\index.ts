import { callServer, getDataSet } from '#/api/core/witlab';
import { requestClient } from '#/api/request';

export namespace SupplierApi {
  export interface PageFetchParams {
    [key: string]: any;
    page: number;
    pageSize: number;
  }

  export interface Supplier {
    ORIGREC: number;
    SUPTYPE: string;
    SUPPCODE: string;
    SUPPNAM: string;
    SUPPADD: string;
    SUPPADD_A: string;
    SUPPCITY: string;
    SUPPSTNAM: string;
    SUPPST: string;
    SUPZIP: string;
    COUNTRYNAME: string;
    PRIMARYPHONE: string;
    PRIMARYFAX: string;
    DEFAULTCONTACT: string;
    EMAIL: string;
    URL: string;
    COMMENTS: string;
    CITY_CODE: string;
    COUNTRY: string;
  }
}

/**
 * 获取厂商列表数据
 */
async function getSupplierList() {
  return getDataSet('SUPPLIERS.DS_DGDSUPPLIERS_GRID', []);
}

/**
 * 添加厂商
 * @param data 厂商数据
 */
async function addSupplier(data: SupplierApi.Supplier) {
  return await callServer('SUPPLIERS.ADD_SUPPLIER', [
    [
      data.SUPPCODE,
      data.SUPPNAM,
      data.SUPPADD,
      data.SUPPADD_A,
      data.CITY_CODE,
      data.SUPPST,
      data.SUPZIP,
      data.COUNTRY,
      data.PRIMARYPHONE,
      data.PRIMARYFAX,
      data.DEFAULTCONTACT,
      data.EMAIL,
      data.URL,
      data.COMMENTS,
      data.SUPTYPE,
    ],
    'AddSupplier',
  ]);
}

/**
 * 更新厂商
 *
 * @param data 厂商数据
 * @returns boolean
 */
async function updateSupplier(data: SupplierApi.Supplier) {
  return requestClient.post<boolean>('/supplier/updateSupplier', data);
}

/**
 * 删除厂商
 *
 * @param origrec 厂商数据
 * @returns boolean
 */
async function deleteSupplier(sSuppcode: string[], sOrigrec: number[]) {
  return await callServer('SUPPLIERS.DEL_SUPPLIER', [sSuppcode, sOrigrec]);
}

export { addSupplier, deleteSupplier, getSupplierList, updateSupplier };
