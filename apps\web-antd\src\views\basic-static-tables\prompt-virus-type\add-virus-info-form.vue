<script lang="ts" setup>
import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import { $addVirusInfoApi } from '#/api/basic-static-tables/prompt-virus-type';
import { $t } from '#/locales';

import { useInfoSchema } from './prompt-virus-type-data';

const emit = defineEmits(['success']);
const getTitle = computed(() => {
  return $t('ui.actionTitle.create', [$t('basicStatic.regions.title')]);
});
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: useInfoSchema(),
  showDefaultActions: false,
});
const virusTypeName = ref('');
const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    modalApi.lock();
    try {
      data.TYPENAME = virusTypeName.value;
      await $addVirusInfoApi(data, 'AddVirusType');
      emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData<{ virusType: string }>();
      virusTypeName.value = data?.virusType || '';
    }
  },
});
</script>
<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
  </Modal>
</template>
