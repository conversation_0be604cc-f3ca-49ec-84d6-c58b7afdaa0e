<script lang="ts" setup>
import type { Recordable } from '@vben/types';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Transfer } from 'ant-design-vue';

import {
  $getSelctedElnApi,
  $updateElnIdApi,
} from '#/api/business-static-tables';
import { $t } from '#/locales';

interface FormArgs {
  elnId: string;
  origrec: number;
}
const emit = defineEmits(['success']);
const elnList = ref<Recordable<string>[]>([]);
const targetKeys = ref<string[]>([]);
const formArgs = ref<FormArgs>();
const getElnList = async () => {
  const eData = formArgs.value
    ? await $getSelctedElnApi({ elnId: formArgs.value.elnId })
    : [];
  const selectKeys = eData
    .filter((item) => item.SELECTED === 'Y')
    .map((item) => item.VALUE);
  elnList.value = eData;
  targetKeys.value = selectKeys;
};

const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    if (!formArgs.value) return;
    await $updateElnIdApi({
      elnId: targetKeys.value.join(','),
      origrec: formArgs.value.origrec,
    });
    emit('success');
    modalApi.close();
  },
  onCancel: () => {
    modalApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      // 当模态框打开时，重新获取数据
      const data = modalApi.getData<FormArgs>();
      if (data) {
        formArgs.value = data;
        getElnList();
      }
    }
  },
  title: $t('business-static-tables.testManager.selectEln'),
});
</script>
<template>
  <Modal>
    <div
      class="flex h-full w-full items-center justify-center"
      style="width: 100%; min-height: 500px; padding: 20px"
    >
      <Transfer
        v-model:target-keys="targetKeys"
        :data-source="elnList"
        show-search
        :list-style="{
          width: '100%',
          height: '500px',
        }"
        :render="(item) => `${item.TEXT}`"
        style="width: 100%"
        :row-key="(record) => record.VALUE"
      >
        <template #notFoundContent>
          <span>没数据</span>
        </template>
      </Transfer>
    </div>
  </Modal>
</template>
