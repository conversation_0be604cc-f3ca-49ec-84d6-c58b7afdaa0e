<script setup lang="ts">
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { MainFormApi } from '#/api/equipment/main-form';

import { computed, ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Modal as AModal, Button, message, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  delPointValueApi,
  getCalibrationPointApi,
  getTrueValueApi,
} from '#/api/equipment/main-form';
import { useMainFormStore } from '#/store';

import { checkColumns, weightColumns } from '../main-form-data';
import AddTargetModal from './add-target-modal.vue';

interface RowType {
  [key: string]: any;
}
const currentCheckRow = ref<RowType>();
const mainFormStore = useMainFormStore();
// const currentRow = computed<RowType>(
//   () => mainFormStore.getCurrentRow as unknown as RowType,
// );
const eventRow = computed<RowType>(
  () => mainFormStore.getEventRow as unknown as RowType,
);
watch(
  currentCheckRow,
  (newRow) => {
    if (newRow) {
      recordGridApi.query();
    }
  },
  { deep: true },
);

watch(
  eventRow,
  (newRow) => {
    if (newRow) {
      targetGridApi.query();
    }
  },
  { deep: true },
);
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddTargetModal,
  destroyOnClose: true,
});

const gridEvents: VxeGridListeners<MainFormApi.MainForm> = {
  currentChange: async ({ row }) => {
    if (row) {
      currentCheckRow.value = row;
    }
  },
};
const gridOptions: VxeTableGridOptions<MainFormApi.MainForm> = {
  columns: checkColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!eventRow.value) {
          return [];
        }
        const data = await getCalibrationPointApi([eventRow.value?.ORIGREC]);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: {},
    search: true,
    zoom: true,
  },
};
const weightGridOptions: VxeTableGridOptions<MainFormApi.MainForm> = {
  columns: weightColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'manual',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (!currentCheckRow.value) {
          return [];
        }
        const data = await getTrueValueApi([currentCheckRow.value?.ORIGREC]);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: {},
    search: true,
    zoom: true,
  },
};
const [TargetGrid, targetGridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents,
  tableTitle: '校验点',
});
const [RecordGrid, recordGridApi] = useVbenVxeGrid({
  gridOptions: weightGridOptions,
  gridEvents: {},
  tableTitle: '称量记录',
});
const addTargetPoint = () => {
  formModalApi.setData({ type: 'target' }).open();
};
const addCheckPoint = () => {
  formModalApi.setData({ type: 'check' }).open();
};
const connectBalance = () => {};
const deleteItem = () => {
  AModal.confirm({
    title: '询问',
    content: '确实要删除所选数据吗?',
    cancelText: '否',
    okText: '是',
    async onOk() {
      if (!currentCheckRow.value) {
        message.warning('请选择要删除的数据！');
        return;
      }
      const params = [currentCheckRow.value.ORIGREC];
      await delPointValueApi(params);
      message.success('删除成功！');
      onRefresh();
    },
  });
};
const onRefresh = () => {
  targetGridApi.query();
};
</script>
<template>
  <div class="flex h-full w-full flex-row">
    <FormModal @success="onRefresh" />

    <TargetGrid class="w-1/2" height="auto">
      <template #toolbar-actions>
        <Space :size="[8, 0]" wrap>
          <Button type="primary" @click="addTargetPoint">
            {{ $t('equipment.main-form.addTargetPoint') }}
          </Button>
          <Button type="primary" @click="addCheckPoint">
            {{ $t('equipment.main-form.addCheckPoint') }}
          </Button>
          <Button type="primary" danger @click="deleteItem">
            {{ $t('equipment.main-form.delete') }}
          </Button>
          <Button type="primary" @click="connectBalance">
            {{ $t('equipment.main-form.connectBalance') }}
          </Button>
        </Space>
      </template>
    </TargetGrid>
    <RecordGrid class="w-1/2" height="auto" />
  </div>
</template>
