import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { StabilityPurposesApi } from '#/api/basic-static-tables';

import { $t } from '#/locales';
import { createColumn } from '#/utils/lims-grids-config';

export function useColumns(): VxeTableGridOptions<StabilityPurposesApi.XStabilityPurpose>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 60 },
    createColumn({
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 80,
    }),
    createColumn({
      field: 'PURPOSE',
      title: $t('basic-static-tables.stabilityPurpose.stabilityPurpose'),
      minWidth: 120,
    }),
    createColumn({
      field: 'DESCRIPTION',
      title: $t('commons.description'),
      editRender: { name: 'textarea' },
    }),
    {
      field: 'action',
      title: $t('commons.action'),
      minWidth: 150,
      slots: { default: 'action' },
    },
  ];
}

export function useSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'PURPOSE',
      label: $t('basic-static-tables.stabilityPurpose.stabilityPurpose'),
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'DESCRIPTION',
      label: $t('commons.description'),
    },
  ];
}

export function useFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'PURPOSE',
      label: $t('basic-static-tables.stabilityPurpose.stabilityPurpose'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'DESCRIPTION',
      label: $t('commons.description'),
    },
  ];
}
