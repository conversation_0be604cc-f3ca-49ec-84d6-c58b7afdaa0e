{"title": "business static data", "operation": "Operation", "add": "Add", "delete": "Delete", "edit": "Edit", "save": "Save", "cancel": "Cancel", "detail": "Detail", "removed": "Removed", "copy": "Copy", "release": "Release", "retire": "Retire", "process-specifications": {"title": "Process Specifications", "grouping": "Grouping", "specification": "Specification", "specification_eng": "Specification Eng", "version": "Version", "inspection_standard": "Inspection Standard", "limitfile": "Limit File", "status": "Status", "startddate": "Start Date", "expdate": "Exp Date", "retireddat": "Retired <PERSON><PERSON>", "matcode": "Mat Code", "specno": "Spec No", "speccategory": "Spec Category", "testno": "Test No", "analyte": "Analyte", "printflag": "Printflag", "synonim": "Synonim", "specsorter": "Spec Sorter", "schemaname": "Schema Name", "picture": "Picture", "lowa": "LowA", "lowb": "LowB", "lowc": "LowC", "highc": "HighC", "highb": "HighB", "higha": "HighA", "units": "Units", "charlimits": "Charlimits", "charlimits_i": "Charlimits I", "pass_text": "Pass Text", "pass_text_eng": "Pass Text Eng", "analtype": "Analtype", "testcode": "Testcode", "edit-testno": "Edit Test No", "edit-analyte": " Edit Analyte", "footnotes": "Footnotes", "specification-exists": "Specification Exists", "addversion": "Add Version"}, "word-static-fields": {"title": "Word Static Fields", "group_by": "Group By", "marking_name": "Marking Name", "replace_name": "Replace Name", "sorter": "Sorter"}, "word-template": {"title": "Word Template", "status": "Status", "type": "Type", "temp_code": "Temp Code", "sorter": "Sorter", "name": "Name", "language": "Language", "mattypes": "Mattypes", "clients": "Clients", "function_name": "Function Name", "stardoc_id": "Stardoc Id", "filepath": "File Path", "pic_size": "<PERSON><PERSON>", "remark": "Remark", "deptname": "Deptname", "createdby": "Created By", "createdate": "Created Date"}, "sample-Groups": {"title": "Sample Groups"}}