import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { XIntervalsApi } from '#/api/basic-static-tables/intervals';

import { $getUnitsApi } from '#/api/basic-static-tables/intervals';
import { $t } from '#/locales';
import { createColumn } from '#/utils/lims-grids-config';
import { traslateValue } from '#/utils/utils';

export function useColumns(): VxeTableGridOptions<XIntervalsApi.XInterval>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 60 },
    createColumn({
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 80,
    }),
    createColumn({
      field: 'INTERVAL',
      title: $t('basic-static-tables.intervals.interval'),
      width: 120,
    }),
    createColumn({
      field: 'UNIT',
      title: $t('basic-static-tables.intervals.unit'),
      width: 120,
    }),
    createColumn({
      field: 'PULLWINDOW_LONGTERM',
      title: $t('basic-static-tables.intervals.pullwindow_longterm'),
      width: 180,
      editRender: {
        name: 'InputNumber',
      },
    }),
    createColumn({
      field: 'PULLWINDOW_HURRY',
      title: $t('basic-static-tables.intervals.pullwindow_hurry'),
      width: 180,
      editRender: {
        name: 'InputNumber',
      },
    }),
    createColumn({
      field: 'PULLWINDOW',
      title: $t('basic-static-tables.intervals.pullwindow'),
      width: 180,
      editRender: {
        name: 'InputNumber',
      },
    }),
    createColumn({
      field: 'PULLWINDOW_UNIT',
      title: $t('basic-static-tables.intervals.pullwindow_unit'),
      width: 180,
      editRender: {
        name: 'ApiSelectEdit',
        props: {
          api: $getUnitsApi,
          labelField: 'UNIT',
          valueField: 'UNIT',
        },
      },
    }),
    {
      field: 'action',
      title: $t('commons.action'),
      minWidth: 150,
      slots: { default: 'action' },
    },
  ];
}

export function useSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        class: 'w-full',
      },
      fieldName: 'INTERVAL',
      label: $t('basic-static-tables.intervals.interval'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: $getUnitsApi,
        class: 'w-full',
        afterFetch: (data: { DEPT: string }[]) => {
          return data.map((item: any) => ({
            label: traslateValue(item.UNIT),
            value: item.UNIT,
          }));
        },
      },
      fieldName: 'UNIT',
      label: $t('basic-static-tables.intervals.unit'),
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        class: 'w-full',
      },
      fieldName: 'PULLWINDOW',
      label: $t('basic-static-tables.intervals.pullwindow'),
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: $getUnitsApi,
        class: 'w-full',
        afterFetch: (data: { DEPT: string }[]) => {
          return data.map((item: any) => ({
            label: traslateValue(item.UNIT),
            value: item.UNIT,
          }));
        },
      },
      fieldName: 'PULLWINDOW_UNIT',
      label: $t('basic-static-tables.intervals.pullwindow_unit'),
    },
    {
      fieldName: 'DEPT',
      label: $t('commons.dept'),
      component: 'Input',
      componentProps: {
        class: 'w-full',
      },
      rules: 'required',
    },
  ];
}

export function useFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'INTERVAL',
      label: $t('basic-static-tables.intervals.interval'),
    },
  ];
}

export function useTatColumns(): VxeTableGridOptions<XIntervalsApi.XIntervalTat>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 60 },
    createColumn({
      field: 'DEPT',
      title: $t('commons.dept'),
      width: 120,
    }),
    createColumn({
      field: 'INTERVAL',
      title: $t('basic-static-tables.intervals.interval'),
      width: 120,
    }),
    createColumn({
      field: 'UNIT',
      title: $t('basic-static-tables.intervals.unit'),
      width: 120,
    }),
    createColumn({
      field: 'DEFAULT_TAT',
      title: $t('basic-static-tables.intervals.default_tat'),
      width: 120,
      editRender: {
        name: 'SwitchEdit',
        props: {
          trueValue: 'Y',
          falseValue: 'N',
        },
      },
    }),
    createColumn({
      field: 'TATBEFORE',
      title: $t('basic-static-tables.intervals.tatbefore'),
      width: 180,
      editRender: {
        name: 'InputNumber',
      },
    }),
    createColumn({
      field: 'TATBEFORE_UNIT',
      title: $t('basic-static-tables.intervals.tatbefore_unit'),
      width: 180,
      editRender: {
        name: 'ApiSelectEdit',
        props: {
          api: $getUnitsApi,
          labelField: 'UNIT',
          valueField: 'UNIT',
        },
      },
    }),
    createColumn({
      field: 'TATAFTER',
      title: $t('basic-static-tables.intervals.tatafter'),
      width: 180,
      editRender: {
        name: 'InputNumber',
      },
    }),
    createColumn({
      field: 'TATAFTER_UNIT',
      title: $t('basic-static-tables.intervals.tatafter_unit'),
      width: 180,
      editRender: {
        name: 'ApiSelectEdit',
        props: {
          api: $getUnitsApi,
          labelField: 'UNIT',
          valueField: 'UNIT',
        },
      },
    }),
    {
      field: 'action',
      title: $t('commons.action'),
      minWidth: 150,
      slots: { default: 'action' },
    },
  ];
}

export function useTatFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'INTERVAL',
      label: $t('basic-static-tables.intervals.interval'),
    },
  ];
}

export function useAddTatSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Select',
      componentProps: {
        class: 'w-full',
        options: [{ INTERVAL: '1' }],
        fieldNames: {
          label: 'INTERVAL',
          value: 'INTERVAL',
        },
      },
      fieldName: 'INTERVAL',
      label: $t('basic-static-tables.intervals.interval'),
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        class: 'w-full',
      },
      fieldName: 'TATBEFORE',
      label: $t('basic-static-tables.intervals.tatbefore'),
      rules: 'required',
    },
    {
      fieldName: 'TATBEFORE_UNIT',
      label: $t('basic-static-tables.intervals.tatbefore_unit'),
      component: 'ApiSelect',
      componentProps: {
        api: $getUnitsApi,
        class: 'w-full',
        afterFetch: (data: { UNIT: string }[]) => {
          return data.map((item: any) => ({
            label: traslateValue(item.UNIT),
            value: item.UNIT,
          }));
        },
      },
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        class: 'w-full',
      },
      fieldName: 'TATAFTER',
      label: $t('basic-static-tables.intervals.tatafter'),
      rules: 'required',
    },
    {
      fieldName: 'TATAFTER_UNIT',
      label: $t('basic-static-tables.intervals.tatafter_unit'),
      component: 'ApiSelect',
      componentProps: {
        api: $getUnitsApi,
        class: 'w-full',
        afterFetch: (data: { UNIT: string }[]) => {
          return data.map((item: any) => ({
            label: traslateValue(item.UNIT),
            value: item.UNIT,
          }));
        },
      },
      rules: 'required',
    },
    {
      component: 'Switch',
      componentProps: {
        checkedValue: 'Y',
        unCheckedValue: 'N',
      },
      fieldName: 'DEFAULT_TAT',
      label: $t('basic-static-tables.intervals.default_tat'),
    },
  ];
}
