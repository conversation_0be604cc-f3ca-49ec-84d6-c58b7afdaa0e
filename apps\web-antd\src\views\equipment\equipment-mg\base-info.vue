<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { Button, Input, Modal, Space, TabPane, Tabs } from 'ant-design-vue';
import { Ellipsis } from 'lucide-vue-next';

import { useVbenForm } from '#/adapter/form';
import {
  getEquipManagerListApi,
  getEquipmentDetailsApi,
} from '#/api/equipment/equipment-mg';
import { $t } from '#/locales';
import { useEquipmentStore } from '#/store';

import Position from './base-info-components/position.vue';
import Work from './base-info-components/work.vue';
import PersonModal from './components/person-modal.vue';
import { useSchema } from './equipment-mg-data';

const equipmentStore = useEquipmentStore();
interface RowType {
  EQID?: string;
  [key: string]: any;
}
const currentRow = computed<RowType>(
  () => equipmentStore.getCurrentRow as unknown as RowType,
);
onMounted(() => {
  getData();
});
const getData = async () => {
  if (currentRow.value && currentRow.value.EQID) {
    const res = await getEquipManagerListApi();
    console.warn(res);
    const data = await getEquipmentDetailsApi([currentRow.value.EQID]);
    Object.keys(data.items[0]).forEach((key) => {
      if (data.items[0][key]) {
        formApi.setFieldValue(key, data.items[0][key]);
      }
    });
  }
  formApi.setState({ commonConfig: { disabled: true } });
};
watch(
  currentRow,
  async (newRow: RowType) => {
    if (newRow) {
      getData();
    }
  },
  { deep: true },
);
const activeKey = ref('定期维护保养工作');
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: PersonModal,
  destroyOnClose: true,
});
const [Form, formApi] = useVbenForm({
  layout: 'horizontal',
  schema: useSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3',
});
const tabList = [
  {
    title: '定期维护保养工作',
    page: Work,
  },
  {
    title: '岗位',
    page: Position,
  },
];
const openEdit = () => {
  formApi.setState({ commonConfig: { disabled: false } });
  Modal.info({
    title: '信息',
    okText: '确定',
    content: '已打开可编辑状态',
  });
};
const modalType = ref('person');
const closeEdit = () => {
  formApi.setState({ commonConfig: { disabled: true } });
  // TODO: 关闭编辑状态时，需要保存数据
};
onMounted(() => {
  closeEdit();
});
const key = ref('');
const openPersonSelect = async (type: string, name: string) => {
  const state = await formApi.getState();
  if (state?.commonConfig?.disabled) return;
  modalType.value = type;
  key.value = name;
  formModalApi
    .setData({
      modalType: modalType.value,
    })
    .open();
};
const editPerson = async (data: any) => {
  // TODO: Implement edit person logic
  formApi.setFieldValue(key.value, data.TEXT);
};
</script>
<template>
  <Page auto-content-height>
    <FormModal @success="editPerson" />
    <Space :size="[4, 0]">
      <Button type="primary" primary @click="openEdit">
        {{ $t('equipment.edit') }}
      </Button>
      <Button @click="closeEdit">
        {{ $t('equipment.coloseEdit') }}
      </Button>
    </Space>
    <Form class="mx-4">
      <template #EQUIPMANAGER="slotProps">
        <Input v-bind="slotProps" placeholder="">
          <template #suffix>
            <Ellipsis
              class="ml-auto h-6 w-6"
              @click="openPersonSelect('person', 'EQUIPMANAGER')"
            />
          </template>
        </Input>
      </template>
      <template #EQUIPMANAGER_SECOND="slotProps">
        <Input v-bind="slotProps" placeholder="">
          <template #suffix>
            <Ellipsis
              class="ml-auto h-6 w-6"
              @click="openPersonSelect('person', 'EQUIPMANAGER_SECOND')"
            />
          </template>
        </Input>
      </template>
      <template #RELATIONEQID="slotProps">
        <Input v-bind="slotProps" placeholder="Basic usage">
          <template #suffix>
            <Ellipsis
              class="ml-auto h-6 w-6"
              @click="openPersonSelect('equipment', 'RELATIONEQID')"
            />
          </template>
        </Input>
      </template>
    </Form>
    <Tabs v-model:active-key="activeKey" class="h-1/2">
      <TabPane v-for="item in tabList" :key="item.title" :tab="item.title">
        <component
          :is="tabList.find((item) => item.title === activeKey)?.page || Work"
        />
      </TabPane>
    </Tabs>
  </Page>
</template>

<style>
.ant-tabs-content {
  height: 100%;
}
</style>
