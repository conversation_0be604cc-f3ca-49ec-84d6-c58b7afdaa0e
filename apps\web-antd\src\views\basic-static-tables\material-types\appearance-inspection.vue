<script lang="ts" setup>
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { MaterialTypesApi } from '#/api/basic-static-tables/material-types';

import { computed, ref } from 'vue';

import { confirm, Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { Button, Divider, message, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addAppearanceItemResult,
  deleteAppearanceItem,
  deleteAppearanceItemResult,
  getAppearanceItemList,
  getAppearanceItemResultList,
} from '#/api/basic-static-tables/material-types';

import AddAppearanceItemFormModal from './add-appearance-item.vue';
import {
  materialAppearanceItemColumns,
  materialAppearanceItemResultColumns,
} from './data';

const matTypeData = ref<MaterialTypesApi.MaterialTypes>();

const sOrigrec = ref<number>();

const gridOptions: VxeTableGridOptions<MaterialTypesApi.AppearanceItem> = {
  columns: materialAppearanceItemColumns(),
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        const data = drawerApi.getData<MaterialTypesApi.MaterialTypes>();
        if (!data) {
          return;
        }
        const mattype = data.MATTYPE;
        const dept = data.DEPT;
        return await getAppearanceItemList(mattype, dept);
      },
    },
  },
  showOverflow: true,
  exportConfig: {},
  rowConfig: {
    keyField: 'ORIGREC',
    isCurrent: true,
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const gridEvents: VxeGridListeners<MaterialTypesApi.AppearanceItem> = {
  currentRowChange: async ({ row }) => {
    if (row) {
      sOrigrec.value = row.ORIGREC;
      // 详细资料
      await loadDetailData();
    }
  },
};

async function loadDetailData() {
  const origrec = sOrigrec.value;
  if (origrec === undefined) {
    return;
  }
  const resDetail = await getAppearanceItemResultList(origrec);
  const data = Array.isArray(resDetail) ? resDetail : resDetail.items || [];
  if (Array.isArray(data)) {
    detailGridApi.setGridOptions({
      data,
    });
  }
}

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddAppearanceItemFormModal,
  destroyOnClose: true,
});

function hasEditStatus(row: MaterialTypesApi.AppearanceItem) {
  return gridApi.grid?.isEditByRow(row);
}

function editRowEvent(row: MaterialTypesApi.AppearanceItem) {
  gridApi.grid?.setEditRow(row);
}

async function saveRowEvent() {
  await gridApi.grid?.clearEdit();
  gridApi.setLoading(true);
  // updateLocationTypeSubLoc(row);
  setTimeout(() => {
    gridApi.setLoading(false);
    message.success({
      content: `保存成功！`,
    });
  }, 600);
}

const cancelRowEvent = (_row: MaterialTypesApi.AppearanceItem) => {
  gridApi.grid?.clearEdit();
};

const [Drawer, drawerApi] = useVbenDrawer({
  onConfirm: onSubmit,
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<MaterialTypesApi.MaterialTypes>();
      if (data) {
        matTypeData.value = data;
        // gridApi?.query();
      } else {
        // gridApi?.reload();
      }
    }
  },
  showCancelButton: false,
  showConfirmButton: false,
});

async function onSubmit() {
  drawerApi.close();
}

const getDrawerTitle = computed(() => {
  if (matTypeData.value) {
    return `查看 ${matTypeData.value.MATTYPE}`;
  }
  return '外观验收标准';
});

function onCreate() {
  formModalApi
    .setData({
      MATTYPE: matTypeData.value?.MATTYPE,
      DEPT: matTypeData.value?.DEPT,
    })
    .open();
}

async function onDelete() {
  // 获取选中行
  const aOrigrec: number[] = gridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.ORIGREC);

  if (aOrigrec.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  if (aOrigrec.length > 1) {
    message.warning('只能选择一条数据！');
    return;
  }

  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${aOrigrec.length} 条数据吗？`,
      icon: 'warning',
      centered: false,
    });

    const sOrigrec = aOrigrec[0] as number;
    await deleteAppearanceItem(sOrigrec);

    message.success('删除成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

function onRefresh() {
  gridApi.query();
}

async function addDetailRow() {
  const origrec = sOrigrec.value;
  if (origrec === undefined) {
    return;
  }
  await addAppearanceItemResult(origrec);
  message.success('添加成功');
  loadDetailData();
}

// 详情
const DetailGridOptions: VxeTableGridOptions<MaterialTypesApi.AppearanceItemResult> =
  {
    columns: materialAppearanceItemResultColumns(),
    stripe: true,
    border: true,
    editConfig: {
      mode: 'row',
      trigger: 'manual',
    },
    height: 'auto',
    pagerConfig: {},
    // data: detailData.value,
    proxyConfig: {
      ajax: {
        query: async () => {
          const origrec = sOrigrec.value;
          if (origrec === undefined) {
            return;
          }
          return await getAppearanceItemResultList(origrec);
        },
      },
    },
    exportConfig: {},
    showOverflow: true,
    rowConfig: {
      keyField: 'ORIGREC',
    },
    toolbarConfig: {
      custom: true,
      export: true,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  };

const [DetailGrid, detailGridApi] = useVbenVxeGrid({
  gridOptions: DetailGridOptions,
});

function hasDetailEditStatus(row: MaterialTypesApi.AppearanceItemResult) {
  return detailGridApi.grid?.isEditByRow(row);
}

function editDetailRowEvent(row: MaterialTypesApi.AppearanceItemResult) {
  detailGridApi.grid?.setEditRow(row);
}

// 删除选中的细项
async function deleteDetailRows() {
  // 获取选中行
  const aOrigrec: number[] = detailGridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.ORIGREC);

  if (aOrigrec.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  if (aOrigrec.length > 1) {
    message.warning('只能选择一条数据！');
    return;
  }

  try {
    await confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${aOrigrec.length} 条数据吗？`,
      icon: 'warning',
      centered: false,
    });

    const sOrigrec = aOrigrec[0] as number;
    await deleteAppearanceItemResult(sOrigrec);

    message.success('删除成功');
    loadDetailData();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

// 保存细项编辑
async function saveDetailEdit() {
  await detailGridApi.grid?.clearEdit();

  detailGridApi.setLoading(true);
  setTimeout(() => {
    detailGridApi.setLoading(false);
    message.success({
      content: `保存成功！`,
    });
  }, 600);
}

// 取消细项编辑
const cancelDetailEdit = (_row: MaterialTypesApi.AppearanceItemResult) => {
  detailGridApi.grid?.clearEdit();
};
</script>

<template>
  <Drawer class="w-full max-w-[900px]" :title="getDrawerTitle">
    <FormModal @success="onRefresh" />
    <Page auto-content-height>
      <div class="vp-raw h-[400px] w-full">
        <Grid>
          <template #toolbar-actions>
            <Space>
              <Button type="primary" @click="onCreate"> 新增验收项 </Button>
              <Button type="primary" danger @click="onDelete">
                删除验收项
              </Button>
            </Space>
          </template>
          <template #action="{ row }">
            <template v-if="hasEditStatus(row)">
              <Button type="link" @click="saveRowEvent()">
                {{ $t('basic-static-tables.save') }}
              </Button>
              <Button type="link" @click="cancelRowEvent(row)">
                {{ $t('basic-static-tables.cancel') }}
              </Button>
            </template>
            <template v-else>
              <Button type="link" @click="editRowEvent(row)">
                {{ $t('basic-static-tables.edit') }}
              </Button>
            </template>
          </template>
        </Grid>
      </div>
      <!-- 分割线 -->
      <Divider />
      <!-- 第二个表格部分 -->
      <div class="h-[400px] w-full">
        <DetailGrid>
          <template #action="{ row }">
            <template v-if="hasDetailEditStatus(row)">
              <Button type="link" @click="saveDetailEdit()">
                {{ $t('basic-static-tables.save') }}
              </Button>
              <Button type="link" @click="cancelDetailEdit(row)">
                {{ $t('basic-static-tables.cancel') }}
              </Button>
            </template>
            <template v-else>
              <Button type="link" @click="editDetailRowEvent(row)">
                {{ $t('basic-static-tables.edit') }}
              </Button>
            </template>
          </template>
          <template #toolbar-actions>
            <Space>
              <Button type="primary" @click="addDetailRow">
                {{
                  $t(
                    'basic-static-tables.material-appearance-item-result.addappearanceitemresult',
                  )
                }}
              </Button>
              <Button type="primary" danger @click="deleteDetailRows">
                {{
                  $t(
                    'basic-static-tables.material-appearance-item-result.deleteappearanceitemresult',
                  )
                }}
              </Button>
            </Space>
          </template>
        </DetailGrid>
      </div>
    </Page>
  </Drawer>
</template>
