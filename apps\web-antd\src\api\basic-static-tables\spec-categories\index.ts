import { callServer, getDataSet } from '#/api/core/witlab';

export namespace SpecCategoriesApi {
  export interface PageFetchParams {
    [key: string]: any;
    page: number;
    pageSize: number;
  }

  export interface SpecCategorries {
    ORIGREC: number;
    SPECCATEGORY: string;
    REGIONCODE: string;
  }
}

/**
 * 获取质量标准分类列表数据
 */
async function getSpecCategoryList() {
  return getDataSet('SPEC_CATEGORIES.DS_SPECCATS_GRID', []);
}

/**
 * 添加质量标准分类
 * @param data 质量标准分类数据
 */
async function addSpecCategory(data: SpecCategoriesApi.SpecCategorries) {
  return await callServer('SPEC_CATEGORIES.ADD_CATEGORY', [
    data.SPECCATEGORY,
    'AddCategory',
  ]);
}

/**
 * 删除质量标准分类
 *
 * @param origrec 质量标准分类数据
 * @returns boolean
 */
async function deleteSpecCategory(specCategories: string[]) {
  return await callServer('SPEC_CATEGORIES.DEL_CATEGORY', [specCategories]);
}

export { addSpecCategory, deleteSpecCategory, getSpecCategoryList };
