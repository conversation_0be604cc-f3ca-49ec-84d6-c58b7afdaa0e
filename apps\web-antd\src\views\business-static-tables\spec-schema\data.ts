import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { SpecSchemasApi } from '#/api/business-static-tables';

import {
  $getComboSpecSchemaCalcItemApi,
  $getComboSpecSchemaCalcStatusApi,
  $getComboSpecSchemaFiledDispPropertyApi,
} from '#/api/business-static-tables';
import { $t } from '#/locales';
import { createColumn } from '#/utils/lims-grids-config';

export function useColumns(): VxeTableGridOptions<SpecSchemasApi.SpecGroups>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 60 },
    createColumn({
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 80,
    }),
    createColumn({
      field: 'SPECSCHEMAGROUP',
      title: $t('business-static-tables.specSchema.specSchemaGroup'),
      minWidth: 120,
    }),
  ];
}

export function useSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'SPECSCHEMAGROUP',
      label: $t('business-static-tables.specSchema.specSchemaGroup'),
      rules: 'required',
    },
  ];
}

export function useFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'SPECSCHEMAGROUP',
      label: $t('business-static-tables.specSchema.specSchemaGroup'),
    },
  ];
}

// useSpecSchemasColumns, useSpecSchemasFilterSchema
export function useSpecSchemasColumns(): VxeTableGridOptions<SpecSchemasApi.SpecSchemas>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 60 },
    createColumn({
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 80,
    }),
    createColumn({
      field: 'SCHEMANAME',
      title: $t('business-static-tables.specSchema.schemaName'),
      minWidth: 120,
    }),
    createColumn({
      field: 'SPECSCHEMAGROUP',
      title: $t('business-static-tables.specSchema.specSchemaGroup'),
      minWidth: 120,
      visible: false,
    }),
    createColumn({
      field: 'STARTDDATE',
      title: $t('commons.startDate'),
      minWidth: 120,
      formatter: 'formatDate',
      editRender: {
        name: 'input',
        attrs: {
          type: 'date',
        },
      },
    }),
    createColumn({
      field: 'EXPDATE',
      title: $t('commons.expDate'),
      formatter: 'formatDate',
      minWidth: 120,
      editRender: {
        name: 'input',
        attrs: {
          type: 'date',
        },
      },
    }),
    createColumn({
      field: 'VER',
      title: $t('commons.version'),
      minWidth: 80,
    }),
    createColumn({
      field: 'SPECSCHEMACODE',
      title: $t('business-static-tables.specSchema.specSchemaCode'),
      minWidth: 120,
    }),
    {
      field: 'action',
      title: $t('commons.action'),
      minWidth: 150,
      slots: { default: 'action' },
    },
  ];
}

// useSpecSchemasFilterSchema
export function useSpecSchemasFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'SCHEMANAME',
      label: $t('business-static-tables.specSchema.schemaName'),
    },
    {
      component: 'DatePicker',
      componentProps: {},
      fieldName: 'STARTDDATE',
      label: $t('commons.startDate'),
    },
    {
      component: 'DatePicker',
      componentProps: {},
      fieldName: 'EXPDATE',
      label: $t('commons.expDate'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'VER',
      label: $t('commons.version'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'SPECSCHEMACODE',
      label: $t('business-static-tables.specSchema.specSchemaCode'),
    },
  ];
}

// useSpecSchemasSchema
export function useSpecSchemasSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'SCHEMANAME',
      label: $t('business-static-tables.specSchema.schemaName'),
      rules: 'required',
    },
  ];
}

// useSpecSchemasCalcsColumns
export function useSpecSchemasCalcsColumns(): VxeTableGridOptions<SpecSchemasApi.SpecSchemaCalcs>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 60 },
    createColumn({
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      width: 80,
    }),
    createColumn({
      field: 'TYPEOFITEM',
      title: $t('business-static-tables.specSchema.typeOfItem'),
      minWidth: 120,
      editRender: {
        name: 'ApiSelectEdit',
        props: {
          api: $getComboSpecSchemaCalcItemApi,
          labelField: 'CALC_ITEM',
          valueField: 'CALC_ITEM',
        },
      },
    }),
    createColumn({
      field: 'CALCULATION',
      title: $t('business-static-tables.specSchema.calculation'),
      minWidth: 120,
      editRender: {
        name: 'input',
      },
    }),
    createColumn({
      field: 'STATUS',
      title: $t('commons.status'),
      minWidth: 120,
      editRender: {
        name: 'ApiSelectEdit',
        props: {
          api: $getComboSpecSchemaCalcStatusApi,
          labelField: 'STATUS',
          valueField: 'STATUS',
        },
      },
    }),
    {
      field: 'action',
      title: $t('commons.action'),
      minWidth: 100,
      slots: { default: 'action' },
    },
  ];
}

// useSpecSchemasCalcsSchema
export function useSpecSchemasCalcsSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      componentProps: {
        api: $getComboSpecSchemaCalcItemApi,
        labelField: 'CALC_ITEM',
        valueField: 'CALC_ITEM',
        class: 'w-full',
      },
      fieldName: 'TYPEOFITEM',
      label: $t('business-static-tables.specSchema.typeOfItem'),
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: $getComboSpecSchemaCalcStatusApi,
        labelField: 'STATUS',
        valueField: 'STATUS',
        class: 'w-full',
      },
      fieldName: 'STATUS',
      label: $t('commons.status'),
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'CALCULATION',
      label: $t('business-static-tables.specSchema.calculation'),
    },
  ];
}

// useSpecSchemasCalcsFilterSchema
export function useSpecSchemasCalcsFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'TYPEOFITEM',
      label: $t('business-static-tables.specSchema.typeOfItem'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'CALCULATION',
      label: $t('business-static-tables.specSchema.calculation'),
    },
  ];
}

// useSpecSchemasFieldColumns
export function useSpecSchemasFieldColumns(): VxeTableGridOptions<SpecSchemasApi.SpecSchemaFields>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 60 },
    createColumn({
      field: 'FIELDNAME',
      title: $t('business-static-tables.specSchema.fieldName'),
      minWidth: 120,
    }),
    createColumn({
      field: 'FIELDCAPTION',
      title: $t('business-static-tables.specSchema.fieldCaption'),
      editRender: {
        name: 'input',
      },
    }),
    createColumn({
      field: 'DISP_PROPERTY',
      title: $t('business-static-tables.specSchema.dispProperty'),
      minWidth: 120,
      editRender: {
        name: 'ApiSelectEdit',
        props: {
          api: $getComboSpecSchemaFiledDispPropertyApi,
          labelField: 'PROPERTY',
          valueField: 'PROPERTY',
        },
      },
      formatter: 'formatTranslate',
    }),
    createColumn({
      field: 'FIELDWIDTH',
      title: $t('business-static-tables.specSchema.fieldWidth'),
      minWidth: 120,
      editRender: {
        name: 'input',
        attrs: {
          type: 'number',
        },
      },
    }),
    createColumn({
      field: 'TABLENAME',
      title: $t('business-static-tables.specSchema.tableName'),
      minWidth: 120,
    }),
    {
      field: 'action',
      title: $t('commons.action'),
      minWidth: 80,
      slots: { default: 'action' },
      fixed: 'right',
    },
  ];
}

// useSpecSchemasFieldFilterSchema,
export function useSpecSchemasFieldFilterSchema(
  selectOptions: any[],
): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'FIELDNAME',
      label: $t('business-static-tables.specSchema.fieldName'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'FIELDCAPTION',
      label: $t('business-static-tables.specSchema.fieldCaption'),
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'TABLENAME',
      label: $t('business-static-tables.specSchema.tableName'),
    },
    {
      component: 'Select',
      componentProps: {
        options: selectOptions,
      },
      fieldName: 'DISP_PROPERTY',
      label: $t('business-static-tables.specSchema.dispProperty'),
    },
  ];
}
