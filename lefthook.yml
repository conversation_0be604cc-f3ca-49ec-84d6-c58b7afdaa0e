# EXAMPLE USAGE:
#
#   Refer for explanation to following link:
#   https://lefthook.dev/configuration/
#
# pre-push:
#   jobs:
#     - name: packages audit
#       tags:
#         - frontend
#         - security
#       run: yarn audit
#
#     - name: gems audit
#       tags:
#         - backend
#         - security
#       run: bundle audit
#
# pre-commit:
#   parallel: true
#   jobs:
#     - run: yarn eslint {staged_files}
#       glob: "*.{js,ts,jsx,tsx}"
#
#     - name: rubocop
#       glob: "*.rb"
#       exclude:
#         - config/application.rb
#         - config/routes.rb
#       run: bundle exec rubocop --force-exclusion {all_files}
#
#     - name: govet
#       files: git ls-files -m
#       glob: "*.go"
#       run: go vet {files}
#
#     - script: "hello.js"
#       runner: node
#
#     - script: "hello.go"
#       runner: go run

pre-commit:
  parallel: true
  commands:
    code-workspace:
      run: pnpm vsh code-workspace --auto-commit
    lint-md:
      run: pnpm prettier --cache --ignore-unknown --write {staged_files}
      glob: '*.md'
    lint-vue:
      run: pnpm prettier --write {staged_files} && pnpm eslint --cache --fix {staged_files} && pnpm stylelint --fix --allow-empty-input {staged_files}
      glob: '*.vue'
    lint-js:
      run: pnpm prettier --cache --ignore-unknown --write {staged_files} && pnpm eslint --cache --fix {staged_files}
      glob: '*.{js,jsx,ts,tsx}'
    lint-style:
      run: pnpm prettier --cache --ignore-unknown --write {staged_files} && pnpm stylelint --fix --allow-empty-input {staged_files}
      glob: '*.{scss,less,styl,html,vue,css}'
    lint-package:
      run: pnpm prettier --cache --write {staged_files}
      glob: 'package.json'
    lint-json:
      run: pnpm prettier --cache --write --parser json {staged_files}
      glob: '{!(package)*.json,*.code-snippets,.!(browserslist)*rc}'

post-merge:
  commands:
    install:
      run: pnpm install

commit-msg:
  commands:
    commitlint:
      run: pnpm exec commitlint --edit $1
