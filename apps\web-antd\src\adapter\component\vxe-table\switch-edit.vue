<script lang="ts" setup>
import type { VxeGlobalRendererHandles, VxeTableDefines } from 'vxe-table';

import type { PropType } from 'vue';

import { computed, ref } from 'vue';

import { Switch } from 'ant-design-vue';

const props = defineProps({
  renderParams: {
    type: Object as PropType<VxeGlobalRendererHandles.RenderTableEditParams>,
    default: () => ({}),
  },
  trueValue: {
    type: [String, Number, Boolean],
    default: true,
  },
  falseValue: {
    type: [String, Number, Boolean],
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});
const currRow = ref();
const currColumn = ref<VxeTableDefines.ColumnInfo>();

const load = () => {
  const { renderParams } = props;
  const { row, column } = renderParams;
  currRow.value = row;
  currColumn.value = column;
};

load();

const checked = computed({
  get() {
    return currRow.value && currColumn.value
      ? currRow.value[currColumn.value.field] === props.trueValue
      : false;
  },
  set(val: boolean) {
    if (currRow.value && currColumn.value) {
      currRow.value[currColumn.value.field] = val
        ? props.trueValue
        : props.falseValue;
    }
  },
});
</script>

<template>
  <div v-if="currRow && currColumn" class="edit-down-table">
    <Switch v-model:checked="checked" :disabled="props.disabled" />
  </div>
</template>
