<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { $addXPackagingTypeApi } from '#/api/basic-static-tables/packaging-type';
import { $t } from '#/locales';

import { useSchema } from './data';

const emit = defineEmits(['success']);
const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: useSchema(),
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    data.eventCode = 'addPackagingType';
    data.comment = '添加包装类型';
    modalApi.lock();
    try {
      const res = await $addXPackagingTypeApi(data);
      if (!res) {
        message.error($t('commons.exists'));
        return;
      }
      if (res) emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  title: $t('ui.actionTitle.create', [
    $t('basic-static-tables.xpackagingtype.packagingTypeCode'),
  ]),
});
</script>

<template>
  <Modal>
    <Form class="mx-4" />
  </Modal>
</template>
