import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { ProcessSpecificationsApi } from '#/api/business-static-tables/process-specifications';

import { $t } from '@vben/locales';

import dayjs from 'dayjs';

export function useSpecificationColumns(): VxeTableGridOptions<ProcessSpecificationsApi.Specifications>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'GROUPING',
      title: $t('business-static-tables.process-specifications.grouping'),
      editRender: { name: 'input' },
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SPECIFICATION',
      title: $t('business-static-tables.process-specifications.specification'),
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SPECIFICATION_ENG',
      title: $t(
        'business-static-tables.process-specifications.specification_eng',
      ),
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'VERSION',
      title: $t('business-static-tables.process-specifications.version'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'INSPECTION_STANDARD',
      title: $t(
        'business-static-tables.process-specifications.inspection_standard',
      ),
      editRender: { name: 'input' },
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'LIMITFILE',
      title: $t('business-static-tables.process-specifications.limitfile'),
      editRender: { name: 'input' },
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'STATUS',
      title: $t('business-static-tables.process-specifications.status'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'STARTDDATE',
      title: $t('business-static-tables.process-specifications.startddate'),
      editRender: { name: 'input', attrs: { type: 'date' } },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      field: 'EXPDATE',
      title: $t('business-static-tables.process-specifications.expdate'),
      editRender: { name: 'input', attrs: { type: 'date' } },
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      field: 'RETIREDDAT',
      title: $t('business-static-tables.process-specifications.retireddat'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      formatter: ({ cellValue }) => {
        return cellValue ? dayjs(cellValue).format('YYYY-MM-DD') : '';
      },
    },
    {
      field: 'MATCODE',
      title: $t('business-static-tables.process-specifications.matcode'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SPECNO',
      title: $t('business-static-tables.process-specifications.specno'),
      minWidth: 150,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SPECCATEGORY',
      title: $t('business-static-tables.process-specifications.speccategory'),
      editRender: { name: 'input' },
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('business-static-tables.operation'),
      minWidth: 140,
    },
  ];
}

export function useSpecAnalyteFilterSchema(): VbenFormSchema[] {
  return [];
}

export function useSpecAnalyteColumns(): VxeTableGridOptions<ProcessSpecificationsApi.SpecAnalytes>['columns'] {
  return [
    {
      field: 'ORIGREC',
      title: $t('commons.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'TESTNO',
      title: $t('business-static-tables.process-specifications.testno'),
      editRender: { name: 'input' },
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'ANALYTE',
      title: $t('business-static-tables.process-specifications.analyte'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'PRINTFLAG',
      title: $t('business-static-tables.process-specifications.printflag'),
      editRender: {
        name: 'select',
        options: [
          { value: 'Y', label: '是' },
          { value: 'N', label: '否' },
        ],
      },
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SYNONIM',
      title: $t('business-static-tables.process-specifications.synonim'),
      editRender: { name: 'input' },
      minWidth: 180,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SPECSORTER',
      title: $t('business-static-tables.process-specifications.specsorter'),
      editRender: { name: 'input' },
      minWidth: 160,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'SCHEMANAME',
      title: $t('business-static-tables.process-specifications.schemaname'),
      editRender: { name: 'input' },
      minWidth: 130,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'PICTURE',
      title: $t('business-static-tables.process-specifications.picture'),
      editRender: { name: 'input' },
      minWidth: 130,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'LOWA',
      title: $t('business-static-tables.process-specifications.lowa'),
      editRender: { name: 'input' },
      minWidth: 130,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'LOWB',
      title: $t('business-static-tables.process-specifications.lowb'),
      editRender: { name: 'input' },
      minWidth: 130,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'LOWC',
      title: $t('business-static-tables.process-specifications.lowc'),
      editRender: { name: 'input' },
      minWidth: 130,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'HIGHC',
      title: $t('business-static-tables.process-specifications.highc'),
      editRender: { name: 'input' },
      minWidth: 130,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'HIGHB',
      title: $t('business-static-tables.process-specifications.highb'),
      editRender: { name: 'input' },
      minWidth: 130,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'HIGHA',
      title: $t('business-static-tables.process-specifications.higha'),
      editRender: { name: 'input' },
      minWidth: 130,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'UNITS',
      title: $t('business-static-tables.process-specifications.units'),
      editRender: { name: 'input' },
      minWidth: 130,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'CHARLIMITS',
      title: $t('business-static-tables.process-specifications.charlimits'),
      editRender: { name: 'input' },
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'CHARLIMITS_I',
      title: $t('business-static-tables.process-specifications.charlimits_i'),
      editRender: { name: 'input' },
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'PASS_TEXT',
      title: $t('business-static-tables.process-specifications.pass_text'),
      editRender: { name: 'input' },
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'PASS_TEXT_ENG',
      title: $t('business-static-tables.process-specifications.pass_text_eng'),
      editRender: { name: 'input' },
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'ANALTYPE',
      title: $t('business-static-tables.process-specifications.analtype'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
    {
      field: 'TESTCODE',
      title: $t('business-static-tables.process-specifications.testcode'),
      minWidth: 120,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('commons.action'),
      minWidth: 140,
    },
  ];
}
