<script lang="ts" setup>
import type { CalendarMode } from 'ant-design-vue/es/calendar/generateCalendar';
import type { DefaultOptionType, SelectValue } from 'ant-design-vue/es/select';

import type { Recordable } from '@vben/types';

import type { HolidaysApi } from '#/api/basic-static-tables/holidays';

import { computed, onMounted, reactive, ref } from 'vue';

import { Page, useVbenModal, VbenCheckButtonGroup } from '@vben/common-ui';

import {
  Badge,
  Button,
  Calendar,
  Card,
  message,
  Modal,
  Select,
  SelectOption,
  Space,
  TabPane,
  Tabs,
} from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  $deleteHoliday,
  $getHolidaysApi,
  $getNonWorkingDaysApi,
  $getSitesApi,
} from '#/api/basic-static-tables/holidays';

import AddHolidayForm from './add-holiday-form.vue';
import CopyHolidayForm from './copy-holiday-form.vue';
import { useHolidayColumns } from './holidays-calendar-data';

const [ListViewGrid, listViewGridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useHolidayColumns(),
    stripe: true,
    showOverflow: true,
    border: true,
    height: 'auto',
    editConfig: {
      mode: 'row',
      trigger: 'manual',
    },
    pagerConfig: {
      enabled: false,
    },
    sortConfig: {
      multiple: true,
    },
    toolbarConfig: {
      custom: true,
      export: true,
      refresh: true,
      zoom: true,
    },
    proxyConfig: {
      ajax: {
        query: async () => {
          if (!selectedDept.value) {
            return [];
          }
          return await $getHolidaysApi({ site: selectedDept.value });
        },
      },
    },
  },
});

const checkValue = ref<string[]>([]);

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: AddHolidayForm,
  destroyOnClose: true,
});

const [CopyFormModal, copyFormModalApi] = useVbenModal({
  connectedComponent: CopyHolidayForm,
  destroyOnClose: true,
});

const onCreate = () => {
  const deptId = selectedDept.value;
  if (!deptId) {
    message.warning('请先选择站点！');
    return;
  }
  formModalApi.setData({ DEPT: deptId }).open();
};
async function onDelete() {
  const checkOirgrec: string[] = listViewGridApi.grid
    .getCheckboxRecords()
    .map((row) => row.ORIGREC) as string[];
  if (!checkOirgrec || checkOirgrec.length === 0) {
    message.warning('请至少选择一条数据！');
    return;
  }

  await confirm('是否确认删除选中的数据？', '删除确认');
  await $deleteHoliday(checkOirgrec);
  message.success('删除成功！');
  onRefresh();
}

function confirm(content: string, title: string) {
  return new Promise((reslove, reject) => {
    Modal.confirm({
      content,
      onCancel() {
        reject(new Error('已取消'));
      },
      onOk() {
        reslove(true);
      },
      title,
    });
  });
}
const onRefresh = async () => {
  await listViewGridApi.reload();
  const newEvents: HolidaysApi.Holidays[] = [];
  listViewGridApi.grid.getData().forEach((row) => {
    newEvents.push(row);
  });
  calendarEvents.value = newEvents;
};

const deptOptions = ref<Recordable<string>[]>([]);
const getDeptList = async () => {
  const res = await $getSitesApi();
  deptOptions.value = res;
};
onMounted(async () => {
  await getDeptList();
});

const activeKey = ref('1');

const options = [
  { label: '周日', value: 'SUN' },
  { label: '周一', value: 'MON' },
  { label: '周二', value: 'TUE' },
  { label: '周三', value: 'WED' },
  { label: '周四', value: 'THU' },
  { label: '周五', value: 'FRI' },
  { label: '周六', value: 'SAT' },
];
const compProps = reactive({
  beforeChange: async (val: string, check: boolean) => {
    const updateRes = await updateFields(val, check ? 'Y' : 'N');
    return updateRes;
  },
  disabled: false,
  gap: 10,
  showIcon: true,
  size: 'small',
} as Recordable<any>);

const currentMode = ref<CalendarMode>('month');
const calendarEvents = ref<HolidaysApi.Holidays[]>([]);

const getListData = (
  value: Dayjs,
): {
  content: string;
  type: 'default' | 'error' | 'processing' | 'success' | 'warning';
}[] => {
  let listData: {
    content: string;
    type: 'default' | 'error' | 'processing' | 'success' | 'warning';
  }[] = [];

  if (!selectedDept.value) {
    return listData;
  }
  if (calendarEvents.value && calendarEvents.value.length > 0) {
    listData = calendarEvents.value
      .filter((item) => {
        return (
          dayjs(item.HOL_DATE).format('YYYY-MM-DD') ===
          value.format('YYYY-MM-DD')
        );
      })
      .map((item) => {
        return {
          content:
            `${dayjs(item.HOL_DATE).format('YYYY-MM-DD HH:mm:ss')}` +
            ` ${item.HOL_NAME}`,
          type: 'default',
        };
      });
  }
  return listData;
};

const getMonthData = (value: Dayjs) => {
  if (!selectedDept.value) {
    return 0;
  }
  let listData: any[] = [];
  if (calendarEvents.value && calendarEvents.value.length > 0) {
    listData = calendarEvents.value
      .filter((item) => {
        return (
          dayjs(item.HOL_DATE).format('YYYY-MM') === value.format('YYYY-MM')
        );
      })
      .map((item) => {
        return {
          content:
            `${dayjs(item.HOL_DATE).format('YYYY-MM-DD HH:mm:ss')}` +
            ` ${item.HOL_NAME}`,
          type: 'default',
        };
      });
  }
  return listData.length;
};

const onPanelChange = (_: Dayjs | string, mode: CalendarMode) => {
  currentMode.value = mode;
};

const onCopy = () => {
  /**	var sYear;
	
	if(dgdHolidays.RowCount > 0)
		sYear = dgdHolidays.GetCurrentRowData("HOL_YEAR");
	
	var sDept = cboSite.SelectedValue;
	
	var aRet = await form.ShowModalDialog(lims.GetFormSource("Holidays.copyHolidays"),[sDept, sYear]); */
  const deptId = selectedDept.value;
  const checkData =
    listViewGridApi.grid.getCheckboxRecords() as HolidaysApi.Holidays[];
  let year = '';
  if (checkData && checkData.length > 0) {
    year = checkData[0]?.HOL_YEAR ?? '';
  }
  copyFormModalApi.setData({ DEPT: deptId, YEAR: year }).open();
};

const selectedDept = ref<string>();

const onSelectChange = (
  _value: SelectValue,
  _option: DefaultOptionType | DefaultOptionType[],
) => {
  onRefresh();
  reloadNonWorkDay();
};

const reloadNonWorkDay = async () => {
  if (!selectedDept.value) {
    return;
  }
  const nonWorkWeek = await $getNonWorkingDaysApi({ site: selectedDept.value });

  const selected: string[] = [];
  if (nonWorkWeek.length > 0) {
    const newNonWorkWeek = nonWorkWeek[0];
    if (newNonWorkWeek && newNonWorkWeek.SUN === 'Y') {
      selected.push('SUN');
    }
    if (newNonWorkWeek && newNonWorkWeek.MON === 'Y') {
      selected.push('MON');
    }
    if (newNonWorkWeek && newNonWorkWeek.TUE === 'Y') {
      selected.push('TUE');
    }
    if (newNonWorkWeek && newNonWorkWeek.WED === 'Y') {
      selected.push('WED');
    }
    if (newNonWorkWeek && newNonWorkWeek.THU === 'Y') {
      selected.push('THU');
    }
    if (newNonWorkWeek && newNonWorkWeek.FRI === 'Y') {
      selected.push('FRI');
    }
    if (newNonWorkWeek && newNonWorkWeek.SAT === 'Y') {
      selected.push('SAT');
    }
  }
  checkValue.value = selected;
};

const disabledGridBtn = computed(() => {
  return activeKey.value !== '1';
});
</script>
<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <CopyFormModal @success="onRefresh" />
    <template #title>
      <Space direction="vertical">
        <Select
          placeholder="请选择站点"
          style="width: 200px"
          v-model:value="selectedDept"
          @change="onSelectChange"
        >
          <SelectOption
            v-for="item in deptOptions"
            :key="item.DEPT"
            :value="item.DEPT"
          >
            {{ item.DEPT }}
          </SelectOption>
        </Select>
        <Card title="非工作日">
          <Space direction="horizontal" size="middle">
            <div class="mt-2 flex flex-col gap-2">
              <VbenCheckButtonGroup
                v-model="checkValue"
                multiple
                :options="options"
                v-bind="compProps"
              />
            </div>
          </Space>
        </Card>
        <Space>
          <Button type="primary" @click="onCreate">
            {{ $t('ui.actionTitle.create') }}
          </Button>
          <Button
            type="primary"
            danger
            @click="onDelete"
            :disabled="disabledGridBtn"
          >
            {{ $t('ui.actionTitle.delete') }}
          </Button>
          <Button type="default" @click="onCopy" :disabled="disabledGridBtn">
            {{ $t('commons.copy') }}
          </Button>
        </Space>
      </Space>
    </template>
    <Tabs v-model:active-key="activeKey" class="h-full">
      <TabPane key="1" tab="列表视图" class="flex flex-1 flex-col">
        <ListViewGrid />
      </TabPane>
      <TabPane key="2" tab="日历视图">
        <Calendar
          @panel-change="onPanelChange"
          style="height: 100%; overflow: scroll"
        >
          <template #dateCellRender="{ current }">
            <ul class="events">
              <li v-for="item in getListData(current)" :key="item.content">
                <Badge :status="item.type" :text="item.content" />
              </li>
            </ul>
          </template>
          <template #monthCellRender="{ current }">
            <div v-if="getMonthData(current)" class="notes-month">
              <section>{{ getMonthData(current) }}</section>
              <span>Backlog number</span>
            </div>
          </template>
        </Calendar>
      </TabPane>
    </Tabs>
  </Page>
</template>

<style scoped>
.ant-tabs-content {
  height: 100%;
}
</style>
