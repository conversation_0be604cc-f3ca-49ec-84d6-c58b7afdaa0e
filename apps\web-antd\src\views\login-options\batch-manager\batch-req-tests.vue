<script lang="ts" setup>
import type { BatcheManagerApi } from '#/api/login-options/batch-manager';

import { ref } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';

import { Button, Space } from 'ant-design-vue';

import { getSampleReqTestsList } from '#/api/login-options/batch-manager';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import {
  useBatchReqTestsColumns,
  useBatchReqTestsFilterSchema,
} from './batch-manager-data';
import TransferModal from './edit-batch-req-tests.vue';

const requirement = ref<BatcheManagerApi.BatchSamplingRequirement | null>(null);

const transferModalRef = ref();

const colums = useBatchReqTestsColumns();
const filterSchema = useBatchReqTestsFilterSchema();
const queryData = async () => {
  const data = drawerApi.getData<BatcheManagerApi.BatchSamplingRequirement>();
  if (!data) {
    return;
  }
  const sOrdNo = data.ORDNO;
  const inventoryId = data.INVENTORYID;
  const dataResult = await getSampleReqTestsList(sOrdNo, inventoryId);
  return dataResult.items;
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
  pagerConfig: {
    enabled: false,
  },
};

const { Grid, gridApi } =
  useLimsGridsConfig<BatcheManagerApi.BatchSamplingRequTests>(
    colums,
    filterSchema,
    queryData,
    girdOption,
  );

function onRefresh() {
  gridApi.query();
}

const [Drawer, drawerApi] = useVbenDrawer({
  onConfirm: onSubmit,
  onOpenChange(isOpen) {
    if (isOpen) {
      const data =
        drawerApi.getData<BatcheManagerApi.BatchSamplingRequirement>();
      if (data) {
        requirement.value = data;
        // gridApi?.query();
      } else {
        // gridApi?.reload();
      }
    }
  },
  showCancelButton: false,
  showConfirmButton: false,
});

async function onSubmit() {
  drawerApi.close();
}

function openTransferModal() {
  transferModalRef.value?.open();
}
</script>

<template>
  <Drawer class="w-full max-w-[800px]" title="关联测试">
    <TransferModal
      ref="transferModalRef"
      :current-test-row="requirement"
      @success="onRefresh"
    />
    <Page auto-content-height>
      <Grid>
        <template #toolbar-actions>
          <Space>
            <Button type="primary" @click="openTransferModal"> 编辑 </Button>
          </Space>
        </template>
      </Grid>
    </Page>
  </Drawer>
</template>
