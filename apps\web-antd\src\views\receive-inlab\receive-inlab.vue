<script lang="ts" setup>
import type { ReceiveInLabApi } from '#/api/receive-inlab/receive-inlab';

import { ref } from 'vue'; // 添加了 onMounted 和 reactive 的导入

// 添加了 onMounted 和 reactive 的导入
import { confirm, Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Button, message, Space, TabPane, Tabs } from 'ant-design-vue';

import {
  getPendingReceiptdg,
  returnToLogin,
} from '#/api/receive-inlab/receive-inlab';
import { useLimsGridsConfig } from '#/utils/lims-grids-config';

import ReceiveByScanCodeForm from './prompt-for-sample.vue';
import {
  useReceiveInLabColumns,
  useReceiveInLabFilterSchema,
} from './receive-inlab-data';
import SampleRequirement from './sample-req.vue';
import SampleTests from './sample-test.vue';
import SelectDateAndLocationForm from './select-receive-date-location.vue';

const activeKey = ref('tpRequirement');
const colums = useReceiveInLabColumns();
const filterSchema = useReceiveInLabFilterSchema();
const queryData = async () => {
  return getPendingReceiptdg();
};
const girdOption = {
  formConfig: {
    enabled: false,
  },
};

const { Grid, gridApi, CurrentRow } =
  useLimsGridsConfig<ReceiveInLabApi.ReceiveOrders>(
    colums,
    filterSchema,
    queryData,
    girdOption,
  );

function onRefresh() {
  gridApi.query(); // 主动触发一次查询
}

// 接收
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: SelectDateAndLocationForm,
});

async function onReceive() {
  // 获取选中行
  const aOrigrec: number[] = gridApi.grid
    ?.getCheckboxRecords()
    .map((row) => row.ORIGREC);

  if (aOrigrec.length === 0) {
    message.warning('请先选择要接收的数据');
    return;
  }

  formModalApi.setData({ AORIGREC: aOrigrec }).open();
  // onRefresh();
}
// 退回
async function onGoBack() {
  // 获取选中行
  const sampler = gridApi.grid?.getCurrentRecord();
  if (!sampler) return;

  const sOrdNo = sampler.ORDNO;
  try {
    await confirm({
      title: '确认退回',
      content: `确定要退回选中的数据吗？`,
      icon: 'warning',
      centered: false,
    });

    const ret = await returnToLogin(sOrdNo);
    if (ret !== null && !ret[0] && ret[1] === 100) {
      message.warn($t('receive-inlab.otherSamplecantreturn'));
      return;
    }

    message.success('退回成功');
    onRefresh();
  } catch (error) {
    if (error) {
      // message.error(`删除失败：${(error as Error).message}`);
    }
  }
}

// 扫码接收
const [ReceiveFormModal, receiveFormModalApi] = useVbenModal({
  connectedComponent: ReceiveByScanCodeForm,
  destroyOnClose: true,
});

function onReceiveByScanCode() {
  receiveFormModalApi.setData({ MODE: '' }).open();
}
</script>
<template>
  <FormModal @success="onRefresh" />
  <ReceiveFormModal @success="onRefresh" />
  <Page auto-content-height>
    <div class="h-[420px] w-full">
      <Grid>
        <template #toolbar-actions>
          <Space>
            <Button type="primary" @click="onReceive">
              {{ $t('receive-inlab.receive') }}
            </Button>
            <Button type="primary" danger @click="onGoBack">
              {{ $t('receive-inlab.goback') }}
            </Button>
            <Button type="default" @click="onReceiveByScanCode">
              {{ $t('receive-inlab.receiveByScanCode') }}
            </Button>
          </Space>
        </template>
      </Grid>
    </div>
    <Tabs v-model:active-key="activeKey">
      <TabPane key="tpRequirement" tab="取样要求">
        <SampleRequirement :current-test-row="CurrentRow" />
      </TabPane>
      <TabPane key="tpTest" tab="测试">
        <SampleTests :current-test-row="CurrentRow" />
      </TabPane>
    </Tabs>
  </Page>
</template>
