import { callServer, getDataSet, getDataSetNoPage } from '#/api/core/witlab';

export namespace CheckListApi {
  export interface CheckList {
    [key: string]: any;
    ORIGREC: number;
    CHECKSORT: number;
    TYPE: string;
    TYPE_EN: string;
    CONTENT: string;
    CONTENT_EN: string;
    ITEM: string;
  }
}

const $getCheckListApi = async () => {
  const data = await getDataSet('CHECK_LIST.dgCheckList', []);
  return data;
};

const $addCheckListApi = async (
  data: Omit<CheckListApi.CheckList, 'ORIGREC'>,
) => {
  const res = await callServer('CHECK_LIST.ADD_CHECK', [
    data.TYPE,
    data.CONTENT,
    data.CHECKSORT,
    data.ITEM,
  ]);
  return res;
};
const $deleteCheckListApi = async (origrec: number) => {
  const res = await callServer('CHECK_LIST.DEL_CHECK', [origrec]);
  return res;
};

const $getCheckListItemTypeApi = async () => {
  const data = await getDataSetNoPage('CHECK_LIST.GetCheckListItemType', []);
  return data;
};

export {
  $addCheckListApi,
  $deleteCheckListApi,
  $getCheckListApi,
  $getCheckListItemTypeApi,
};
