<script setup lang="ts">
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { FormApi } from '#/api/equipment/persons-manage';
import { ref, onMounted } from 'vue';
import { Page, useVbenModal } from '@vben/common-ui';

import {
  Button,
  Select,
  SelectOption,
  Space,
  TabPane,
  Tabs,
  Input,
} from 'ant-design-vue';
import BaseInfo from './components/base-info.vue';
import DeviceAuthorization from './components/device-authorization.vue';
import MethodAuthorization from './components/method-authorization.vue';
import QualificationOnboard from './components/qualification-onboard.vue';
import TrainingExperience from './components/training-experience.vue';
import {
  getDeptListApi,
  getPersonnelNewApi,
  getSearchDataApi,
} from '#/api/equipment/persons-manage';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { $t } from '#/locales';
import { personColumns } from './persons-manage-data';
import EditModal from './components/edit-modal.vue';
import ExtendModal from './components/extend-modal.vue';

interface RowType {
  [key: string]: any;
}
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: EditModal,
  destroyOnClose: true,
});

const [ExtendFormModal, extendModalApi] = useVbenModal({
  connectedComponent: ExtendModal,
  destroyOnClose: true,
});

const formargs = ['Person', 'Search', ' and STATUS = ?', "'Search'"]; //TODO:URL携带的参数
const clickRow = ref<RowType | null>(null);
onMounted(async () => {
  const res = await getDeptListApi();
  options.value = res.map((item: { TEXT: string; VALUE: string }) => {
    return {
      value: item.TEXT,
      label: item.VALUE,
    };
  });
  laboratory.value = options.value[0]?.value;
});
const gridEvents: VxeGridListeners<FormApi.Form> = {
  currentChange: async ({ row }) => {
    if (row) {
      clickRow.value = row;
    }
  },
};

const gridOptions: VxeTableGridOptions<FormApi.Form> = {
  columns: personColumns(),
  stripe: true,
  border: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        if (searchType.value === 'person' && person.value) {
          const params = [person.value, formargs[1]];
          const data = await getSearchDataApi(params);
          return data;
        }
        if (searchType.value === 'laboratory' && laboratory.value) {
          const params = [laboratory.value];
          const data = await getPersonnelNewApi(params);
          return data;
        }
        const data = await getPersonnelNewApi([]);
        return data;
      },
    },
  },
  exportConfig: {},
  showOverflow: true,
  rowConfig: {
    keyField: 'origrec',
    isCurrent: true,
  },
  keepSource: true,
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: { code: 'query' },
    search: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: gridEvents,
  tableTitle: '实验室',
});

const laboratory = ref();
const options = ref<{ label: string; value: string }[]>([]);
const laboratoryChange = () => {
  searchType.value = 'laboratory';
  gridApi.query();
};
const searchType = ref('');
const person = ref('');
const activeKey = ref('基本信息');

const tabList = ref([
  {
    title: '基本信息',
    page: BaseInfo,
  },
  {
    title: '方法授权',
    page: MethodAuthorization,
  },
  {
    title: '设备授权',
    page: DeviceAuthorization,
  },
  {
    title: '培训经历',
    page: TrainingExperience,
  },
  {
    title: '上岗资质',
    page: QualificationOnboard,
  },
]);
const search = () => {
  searchType.value = 'person';
  gridApi.query();
};
const onRefresh = () => {
  gridApi.query();
};
const edit = () => {
  formModalApi
    .setData({
      tableData: gridApi.grid.getTableData().tableData,
      laboratory: laboratory.value,
    })
    .open();
};
const oneClickMethodExtension = () => {
  extendModalApi
    .setData({
      clickRow: clickRow.value,
      type: 'METHOD',
    })
    .open();
};
const oneClickInstrumentExtension = () => {
  extendModalApi
    .setData({
      clickRow: clickRow.value,
      type: 'EQ',
    })
    .open();
};
</script>
<template>
  <Page auto-content-height>
    <FormModal @success="onRefresh" />
    <ExtendFormModal @success="onRefresh" />
    <div class="flex h-full flex-col">
      <div class="p-3">
        <Space :size="[8, 0]" wrap>
          <div>输入人员编码、姓名或岗位查询：</div>
          <Input v-model:value="person" placeholder="" />
          <Button type="primary" @click="search">
            {{ $t('equipment.persons-manage.search') }}
          </Button>
        </Space>
      </div>
      <div class="flex h-[95%] w-full">
        <Grid>
          <template #toolbar-actions>
            <Space :size="[8, 0]" wrap>
              <Select
                v-model:value="laboratory"
                style="width: 150px"
                @change="laboratoryChange"
              >
                <SelectOption
                  v-for="item in options"
                  :value="item.value"
                  :key="item.value"
                >
                  {{ item.label }}
                </SelectOption>
              </Select>
              <Button type="primary" @click="edit">
                {{ $t('equipment.persons-manage.edit') }}
              </Button>
              <Button type="primary" @click="oneClickMethodExtension">
                {{ $t('equipment.persons-manage.oneClickMethodExtension') }}
              </Button>
              <Button type="primary" @click="oneClickInstrumentExtension">
                {{ $t('equipment.persons-manage.oneClickInstrumentExtension') }}
              </Button>
            </Space>
          </template>
        </Grid>
        <div class="flex-1">
          <Tabs v-model:active-key="activeKey">
            <TabPane
              v-for="item in tabList"
              :key="item.title"
              :tab="item.title"
            >
            </TabPane>
          </Tabs>
          <component
            :is="
              tabList.find((item) => item.title === activeKey)?.page || BaseInfo
            "
            :clickRow="clickRow"
            class="h-full"
          />
        </div>
      </div>
    </div>
  </Page>
</template>
