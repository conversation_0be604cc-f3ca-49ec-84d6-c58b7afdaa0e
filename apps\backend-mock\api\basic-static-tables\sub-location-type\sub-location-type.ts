import { faker } from '@faker-js/faker';
import { usePageResponseSuccess } from '~/utils/response';

function generateMockDataList(count: number) {
  const dataList = [];

  for (let i = 0; i < count; i++) {
    const dataItem: Record<string, any> = {
      ORIGREC: faker.number.int({ min: 1, max: 1000 }),
      NAME: faker.commerce.product(),
      PREFIX: faker.commerce.product(),
      NUMBERING_METHOD: faker.commerce.productMaterial(),
      SUBLOCATIONS_TEMPLATE_ID: faker.number.int({ min: 1, max: 1000 }),
    };

    dataList.push(dataItem);
  }

  return dataList;
}

const mockData = generateMockDataList(100);

export default eventHandler(async (event) => {
  const { page, pageSize, sortBy, sortOrder } = getQuery(event);
  const listData = structuredClone(mockData);
  if (sortBy && Reflect.has(listData[0], sortBy as string)) {
    listData.sort((a, b) => {
      if (sortOrder === 'asc') {
        return a[sortBy as string] > b[sortBy as string] ? 1 : -1;
      } else {
        return a[sortBy as string] < b[sortBy as string] ? 1 : -1;
      }
    });
  }
  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
