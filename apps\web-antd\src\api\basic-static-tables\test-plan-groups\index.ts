import { callServer, getDataSet, getDataSetNoPage } from '#/api/core/witlab';

export namespace TestPlanGroupsApi {
  export interface PageFetchParams {
    [key: string]: any;
    page: number;
    pageSize: number;
  }

  export interface TestPlanGroup {
    ORIGREC: number;
    PRODGROUP: string;
    PRODGRPDESC: string;
    PRODTYPE: string;
    DEPT: string;
  }
}

/**
 * 获取测试计划组列表数据
 */
async function getTestPlanGroupList() {
  return getDataSet('TEST_PLAN_GROUPS.DS_TESTPLAN_GROUP_GRID', []);
}

/**
 * 添加测试计划组
 * @param data 测试计划组数据
 */
async function addTestPlanGroup(data: TestPlanGroupsApi.TestPlanGroup) {
  return await callServer('TEST_PLAN_GROUPS.ADD_TESTPLAN_GROUP', [
    data.PRODGROUP,
    data.PRODGRPDESC,
    data.PRODTYPE,
    'AddTestPlanGroup',
  ]);
}

async function getSites(sProdGroup: string) {
  return getDataSet('TEST_PLAN_GROUPS.dgdSites', [sProdGroup]);
}

async function mcSelectSites(sProdGroup: string) {
  return getDataSetNoPage('TEST_PLAN_GROUPS.mcSelectSites', [sProdGroup]);
}

async function editSiteList(sProdGroup: string, aSite: string[]) {
  return await callServer('TEST_PLAN_GROUPS.EditSiteList', [sProdGroup, aSite]);
}

/**
 * 删除测试计划组
 *
 * @param origrec 测试计划组数据
 * @returns boolean
 */
async function deleteTestPlanGroup(sProdGroup: string[]) {
  return await callServer('TEST_PLAN_GROUPS.DEL_TESTPLAN_GROUP', [sProdGroup]);
}

export {
  addTestPlanGroup,
  deleteTestPlanGroup,
  editSiteList,
  getSites,
  getTestPlanGroupList,
  mcSelectSites,
};
