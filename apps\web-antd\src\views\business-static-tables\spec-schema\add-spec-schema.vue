<script lang="ts" setup>
import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { $addSchemaNameApi } from '#/api/business-static-tables/spec-schemas';
import { $t } from '#/locales';

import { useSpecSchemasSchema } from './data';

const emit = defineEmits(['success']);

const getTitle = computed(() => {
  return $t('ui.actionTitle.create', [
    $t('business-static-tables.specSchema.schemaName'),
  ]);
});

const [Form, formApi] = useVbenForm({
  layout: 'vertical',
  schema: useSpecSchemasSchema(),
  showDefaultActions: false,
});
const specSchemaGroup = ref<string>('');

const [Modal, modalApi] = useVbenModal({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const data = await formApi.getValues();
    modalApi.lock();
    try {
      data.SPECSCHEMAGROUP = specSchemaGroup.value;
      const res = await $addSchemaNameApi(data);
      if (!res) {
        message.error($t(''));
        return;
      }
      if (res) emit('success');
      modalApi.close();
    } finally {
      modalApi.lock(false);
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData();
      if (data) {
        specSchemaGroup.value = data.specSchemaGroup;
      }
    }
  },
});
</script>

<template>
  <Modal :title="getTitle">
    <Form class="mx-4" />
  </Modal>
</template>
