import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { $t } from '#/locales';

export function useCategoryFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        // placeholder: 'Please enter code',
      },
      fieldName: 'CATEGORY',
      label: $t('basic-static-tables.client-categories.category'),
    },
  ];
}

// 客户分类列表
export function clientCategoriesColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'select',
      type: 'checkbox',
      title: $t('basic-static-tables.select'),
      width: 200,
    },
    {
      field: 'origrec',
      title: $t('basic-static-tables.origrec'),
      minWidth: 100,
      visible: false,
    },
    {
      field: 'CATEGORY',
      title: $t('basic-static-tables.client-categories.category'),
      minWidth: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}
