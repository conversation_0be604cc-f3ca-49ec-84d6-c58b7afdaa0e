<script lang="ts" setup>
import type { VbenFormSchema } from '@vben/common-ui';
import type { BasicOption } from '@vben/types';

import { computed, ref } from 'vue';

import { AuthenticationLogin, z } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { useAuthStore } from '#/store';

defineOptions({ name: 'Login' });

const authStore = useAuthStore();

// 登录阶段：1-账号密码验证，2-部门角色选择
const loginStep = ref(1);
// 存储第一阶段验证成功后的用户信息
const firstStepUserInfo = ref<any>(null);

const MOCK_USER_OPTIONS: BasicOption[] = [
  {
    label: 'Super',
    value: 'vben',
  },
  {
    label: 'Admin',
    value: 'admin',
  },
  {
    label: 'User',
    value: 'jack',
  },
];

// 模拟部门选项
const deptOptions = ref<BasicOption[]>([
  { label: '研发部', value: 'RD' },
  { label: '质量保证部', value: 'QA' },
  { label: '生产部', value: 'PROD' },
  { label: '市场部', value: 'MKT' },
]);

// 模拟角色选项
const roleOptions = ref<BasicOption[]>([
  { label: '管理员', value: 'ADMIN' },
  { label: '普通用户', value: 'USER' },
  { label: '审核员', value: 'REVIEWER' },
]);

const formSchema = computed((): VbenFormSchema[] => {
  if (loginStep.value === 1) {
    // 第一阶段：账号密码
    return [
      {
        component: 'VbenInput',
        componentProps: {
          placeholder: $t('authentication.usernameTip'),
        },
        dependencies: {
          trigger(values, form) {
            if (values.selectAccount) {
              const findUser = MOCK_USER_OPTIONS.find(
                (item) => item.value === values.selectAccount,
              );
              if (findUser) {
                form.setValues({
                  password: '123456',
                  username: findUser.value,
                });
              }
            }
          },
          triggerFields: ['selectAccount'],
        },
        fieldName: 'username',
        label: $t('authentication.username'),
        rules: z.string().min(1, { message: $t('authentication.usernameTip') }),
      },
      {
        component: 'VbenInputPassword',
        componentProps: {
          placeholder: $t('authentication.password'),
        },
        fieldName: 'password',
        label: $t('authentication.password'),
        rules: z.string().min(1, { message: $t('authentication.passwordTip') }),
      },
    ];
  }

  // 第二阶段：部门角色选择
  return [
    {
      component: 'VbenSelect',
      componentProps: {
        options: deptOptions,
        placeholder: '请选择登录部门',
      },
      fieldName: 'deptCode',
      label: '登录部门',
      rules: z.string().min(1, { message: '请选择登录部门' }),
    },
    {
      component: 'VbenSelect',
      componentProps: {
        options: roleOptions,
        placeholder: '请选择登录角色',
      },
      fieldName: 'roleCode',
      label: '登录角色',
      rules: z.string().min(1, { message: '请选择登录角色' }),
    },
  ];
});

// 处理登录提交
async function handleLogin(params: any) {
  if (loginStep.value === 1) {
    // 第一阶段：验证账号密码
    try {
      const result = await authStore.authLoginFirstStep(params);
      if (result?.continueLogin) {
        firstStepUserInfo.value = result.userInfo;
        deptOptions.value = result.userDepts.map((dept: any) => ({
          label: dept.name,
          value: dept.code,
        }));
        roleOptions.value = result.userRoles.map((role: any) => ({
          label: role.name,
          value: role.code,
        }));
        loginStep.value = 2;
      }
    } catch (error) {
      console.error('登录失败:', error);
    }
  } else {
    // 第二阶段：选择部门角色完成登录
    const finalParams = {
      ...firstStepUserInfo.value,
      deptCode: params.deptCode,
      roleCode: params.roleCode,
    };
    await authStore.authLoginSecondStep(finalParams);
  }
}
</script>

<template>
  <AuthenticationLogin
    :form-schema="formSchema"
    :loading="authStore.loginLoading"
    :show-remember-me="loginStep === 1"
    :show-code-login="false"
    :show-qrcode-login="false"
    :show-third-party-login="false"
    :show-register="false"
    :show-forget-password="false"
    :title="loginStep === 1 ? '登录系统' : '选择部门和角色'"
    :sub-title="
      loginStep === 1 ? '请输入您的账号和密码' : '请选择您要登录的部门和角色'
    "
    :submit-button-text="loginStep === 1 ? '登录' : '进入系统'"
    @submit="handleLogin"
  />
</template>
