{"title": "工作流设计器", "description": "基于LogicFlow的审批工作流设计器", "toolbar": {"undo": "撤销", "redo": "重做", "zoomIn": "放大", "zoomOut": "缩小", "fitView": "适应画布", "miniMap": "缩略图", "validate": "验证", "export": "导出", "viewData": "查看数据", "clear": "清空", "switchMode": "切换模式", "loadTemplate": "加载模板", "viewCode": "查看代码"}, "mode": {"edit": "编辑模式", "view": "查看模式", "switchToEdit": "切换到编辑模式", "switchToView": "切换到查看模式"}, "nodePanel": {"title": "节点面板", "categories": {"basic": "基础节点", "process": "流程节点", "control": "控制节点"}}, "nodes": {"start": {"name": "开始", "description": "工作流开始节点"}, "end": {"name": "结束", "description": "工作流结束节点"}, "approval": {"name": "审批", "description": "审批节点，需要指定审批人"}, "condition": {"name": "条件", "description": "条件判断节点，根据条件分支"}, "parallel": {"name": "并行", "description": "并行处理节点，同时执行多个分支"}, "merge": {"name": "合并", "description": "合并节点，等待所有分支完成"}}, "properties": {"title": "属性设置", "basic": {"name": "节点名称", "namePlaceholder": "请输入节点名称", "description": "节点描述", "descriptionPlaceholder": "请输入节点描述"}, "approval": {"title": "审批设置", "approvers": "审批人", "approversPlaceholder": "请选择审批人", "approvalType": "审批类型", "approvalTypes": {"single": "单人审批", "all": "全部审批", "majority": "多数审批"}, "timeLimit": "审批时限（小时）", "autoApprove": "超时自动审批"}, "condition": {"title": "条件设置", "rules": "条件规则", "field": "字段名", "fieldPlaceholder": "字段名", "operator": "操作符", "operatorPlaceholder": "操作符", "value": "值", "valuePlaceholder": "值", "addCondition": "添加条件"}, "parallel": {"title": "并行设置", "branches": "分支设置", "branchPlaceholder": "分支", "addBranch": "添加分支"}, "edge": {"label": "连线标签", "labelPlaceholder": "请输入连线标签", "condition": "条件表达式", "conditionPlaceholder": "请输入条件表达式"}, "actions": {"reset": "重置", "save": "保存"}}, "templates": {"title": "工作流模板", "simple": "简单审批流程", "conditional": "条件审批流程", "parallel": "并行审批流程", "applied": "已应用模板"}, "validation": {"title": "验证结果", "passed": "验证通过", "failed": "发现问题", "passedDesc": "工作流配置正确", "failedDesc": "发现 {count} 个问题", "error": "错误", "warning": "警告", "rules": {"noNodes": "工作流至少需要包含一个节点", "noStartNode": "工作流必须包含一个开始节点", "multipleStartNodes": "工作流只能包含一个开始节点", "noEndNode": "建议添加结束节点", "noNodeName": "建议为节点设置名称", "noApprovers": "审批节点必须设置审批人", "invalidTimeLimit": "审批时限必须大于0", "incompleteApprover": "审批人信息不完整", "noConditions": "条件节点必须设置判断条件", "incompleteCondition": "条件配置不完整", "insufficientBranches": "并行节点至少需要2个分支", "invalidConnection": "连线配置错误", "startNodeAsTarget": "开始节点不能作为连线的终点", "endNodeAsSource": "结束节点不能作为连线的源节点", "selfConnection": "节点不能连接到自身", "isolatedNode": "发现孤立节点", "noIncomingEdge": "节点没有输入连线", "noOutgoingEdge": "节点没有输出连线", "circularDependency": "工作流中存在循环依赖", "unreachableNode": "节点从开始节点不可达"}}, "dataViewer": {"title": "工作流数据", "json": "JSON格式", "nodes": "节点列表", "edges": "连线列表", "columns": {"id": "ID", "type": "类型", "name": "名称", "x": "X坐标", "y": "Y坐标", "sourceNode": "源节点", "targetNode": "目标节点"}}, "status": {"title": "当前状态", "mode": "模式", "nodeCount": "节点数量", "edgeCount": "连线数量", "selectedNode": "选中节点", "selectedEdge": "选中连线"}, "messages": {"modeSwitch": "已切换到{mode}模式", "templateLoaded": "已加载模板：{name}", "validationPassed": "工作流验证通过", "validationFailed": "发现 {errorCount} 个错误，{warningCount} 个警告", "saveSuccess": "保存成功", "resetSuccess": "已重置", "exportSuccess": "导出成功", "connectionNotAllowed": "连接不被允许"}, "operators": {"eq": "等于", "ne": "不等于", "gt": "大于", "gte": "大于等于", "lt": "小于", "lte": "小于等于", "in": "包含", "not_in": "不包含", "contains": "包含文本", "starts_with": "开头是", "ends_with": "结尾是"}}