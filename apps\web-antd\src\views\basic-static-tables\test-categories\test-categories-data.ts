import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { TestCategoriesApi } from '#/api/basic-static-tables/test-categories';

import { $t } from '#/locales';

export function useTestCategoryColumns(): VxeTableGridOptions<TestCategoriesApi.TestCategory>['columns'] {
  return [
    { field: 'checkbox', type: 'checkbox', width: 80 },
    {
      align: 'center',
      field: 'origrec',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'TESTCATCODE',
      title: $t('basic-static-tables.test-categories.testcatcode'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      field: 'TESTCATDESC',
      title: $t('basic-static-tables.test-categories.testcatdesc'),
      editRender: { name: 'input' },
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }], // { sType: 'include', isSensitive: false, sVal: '' }
      sortable: true,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      fixed: 'right',
      title: $t('basic-static-tables.operation'),
      width: 210,
    },
  ];
}

export function useTestCategoryFilterSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        // placeholder: 'Please enter code',
      },
      fieldName: 'TESTCATCODE',
      label: $t('basic-static-tables.test-categories.testcatcode'),
    },
  ];
}

export function useDeptFilterSchema(): VbenFormSchema[] {
  return [];
}

export function useDeptColumns(): VxeTableGridOptions<TestCategoriesApi.TestCategory>['columns'] {
  return [
    {
      align: 'center',
      field: 'origrec',
      title: $t('commons.origrec'),
      width: 100,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
      visible: false,
    },
    {
      align: 'center',
      field: 'DEPT',
      title: $t('basic-static-tables.test-categories.dept'),
      minWidth: 200,
      filterRender: {
        name: 'TableFilterInput',
      },
      filters: [{ data: '' }],
      sortable: true,
    },
  ];
}
